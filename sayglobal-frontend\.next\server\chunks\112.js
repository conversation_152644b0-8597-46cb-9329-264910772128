"use strict";exports.id=112,exports.ids=[112],exports.modules={8819:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9005:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10577:(e,t,a)=>{a.d(t,{A:()=>m});var r=a(60687),i=a(43210),s=a(88920),l=a(26001),n=a(11860),d=a(96882),c=a(16023),o=a(88233),u=a(8819);let m=({isOpen:e,onClose:t,onSave:a,editingVariant:m,hidePvCvSp:g=!1,colorScheme:x="red"})=>{let[p,h]=(0,i.useState)(null),[b,f]=(0,i.useState)(null),y={ring:"blue"===x?"focus:ring-blue-500":"focus:ring-red-500",button:"blue"===x?"bg-blue-600 hover:bg-blue-700":"bg-red-600 hover:bg-red-700"};if((0,i.useEffect)(()=>{if(e&&m){let e={...m};if(!e.pricing.points){let{price:t,ratios:a}=e.pricing;e.pricing.points={pv:Math.round(t*(a.pvRatio/100)),cv:Math.round(t*(a.cvRatio/100)),sp:Math.round(t*(a.spRatio/100))}}h(e)}else h(null),f(null)},[e,m]),(0,i.useEffect)(()=>{if(p&&p.pricing.price>0){let{price:e,ratios:t}=p.pricing,a=Math.round(e*(t.pvRatio/100)),r=Math.round(e*(t.cvRatio/100)),i=Math.round(e*(t.spRatio/100));p.pricing.points&&p.pricing.points.pv===a&&p.pricing.points.cv===r&&p.pricing.points.sp===i||h(e=>e?{...e,pricing:{...e.pricing,points:{pv:a,cv:r,sp:i}}}:null)}},[p?.pricing.price,p?.pricing.ratios.pvRatio,p?.pricing.ratios.cvRatio,p?.pricing.ratios.spRatio]),(0,i.useEffect)(()=>{if(e){let e=window.innerWidth-document.documentElement.clientWidth;document.body.style.overflow="hidden",document.body.style.paddingRight=`${e}px`}else document.body.style.overflow="unset",document.body.style.paddingRight="0px";return()=>{document.body.style.overflow="unset",document.body.style.paddingRight="0px"}},[e]),!p)return null;let v=(e,t)=>{h(a=>a?{...a,pricing:{...a.pricing,[e]:t}}:null)},j=(e,t)=>{h(a=>a?{...a,pricing:{...a.pricing,ratios:{...a.pricing.ratios,[e]:t}}}:null)},k=e=>{h(t=>{if(!t)return null;let a=t.images.filter((t,a)=>a!==e);return t.images[e].isMain&&a.length>0&&(a[0].isMain=!0),{...t,images:a}})},N=e=>{h(t=>t?{...t,images:t.images.map((t,a)=>({...t,isMain:a===e}))}:null)},w=e=>e.pricing.price<=0?(f("Fiyat 0'dan b\xfcy\xfck olmalıdır"),!1):!(e.pricing.stock<0)||(f("Stok miktarı negatif olamaz"),!1);return(0,r.jsx)(s.N,{children:e&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50",children:(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},transition:{duration:.2},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:"Varyant Detayları"}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Varyantın fiyat, stok ve g\xf6rsellerini d\xfczenleyin."})]}),(0,r.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(n.A,{className:"h-6 w-6"})})]}),b&&(0,r.jsx)("div",{className:"p-4 bg-red-50 border-b border-red-200 flex-shrink-0",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:b})}),(0,r.jsx)("div",{className:"p-6 overflow-y-auto flex-grow",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Varyant Bilgisi"}),(0,r.jsx)("p",{className:"text-gray-800 font-semibold text-xl",children:p.name}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:p.featureDetails.map(e=>`${e.featureName}: ${e.featureValue}`).join(", ")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Fiyat ve Stok"}),(0,r.jsxs)("div",{className:`grid grid-cols-1 gap-4 ${g?"md:grid-cols-2":"md:grid-cols-3"}`,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Fiyat (₺) *"}),(0,r.jsx)("input",{type:"number",value:isNaN(p.pricing.price)?"":p.pricing.price,onChange:e=>v("price",parseFloat(e.target.value)),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ${y.ring} focus:border-transparent text-black`,placeholder:"0.00"})]}),!g&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"İndirim (%)"}),(0,r.jsx)("input",{type:"number",value:isNaN(p.pricing.extraDiscount)?"":p.pricing.extraDiscount,onChange:e=>v("extraDiscount",parseInt(e.target.value)),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ${y.ring} focus:border-transparent text-black`,placeholder:"0"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Stok Miktarı *"}),(0,r.jsx)("input",{type:"number",value:isNaN(p.pricing.stock)?"":p.pricing.stock,onChange:e=>v("stock",parseInt(e.target.value)),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ${y.ring} focus:border-transparent text-black`,placeholder:"0"})]})]})]}),!g&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Puan Oranları"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"PV Oranı (%)"}),(0,r.jsx)("input",{type:"number",value:isNaN(p.pricing.ratios.pvRatio)?"":p.pricing.ratios.pvRatio,onChange:e=>j("pvRatio",parseInt(e.target.value)),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ${y.ring} focus:border-transparent text-black`,placeholder:"0"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CV Oranı (%)"}),(0,r.jsx)("input",{type:"number",value:isNaN(p.pricing.ratios.cvRatio)?"":p.pricing.ratios.cvRatio,onChange:e=>j("cvRatio",parseInt(e.target.value)),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ${y.ring} focus:border-transparent text-black`,placeholder:"0"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"SP Oranı (%)"}),(0,r.jsx)("input",{type:"number",value:isNaN(p.pricing.ratios.spRatio)?"":p.pricing.ratios.spRatio,onChange:e=>j("spRatio",parseInt(e.target.value)),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ${y.ring} focus:border-transparent text-black`,placeholder:"0"})]})]}),(0,r.jsxs)("div",{className:"mt-4 p-3 bg-gray-100 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-800 mb-2",children:"Hesaplanan Puanlar:"}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center text-sm",children:[(0,r.jsxs)("div",{className:"font-semibold text-blue-600",children:["PV: ",p.pricing.points?.pv||0]}),(0,r.jsxs)("div",{className:"font-semibold text-green-600",children:["CV: ",p.pricing.points?.cv||0]}),(0,r.jsxs)("div",{className:"font-semibold text-purple-600",children:["SP: ",p.pricing.points?.sp||0]})]})]})]}),g&&(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(d.A,{className:"h-5 w-5 text-blue-400"})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsxs)("p",{className:"text-sm text-blue-700",children:[(0,r.jsx)("strong",{children:"Not:"})," PV, CV, SP oranları ve indirim y\xfczdesi admin tarafından belirlenecektir."]})})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Varyant G\xf6rselleri"}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsxs)("label",{htmlFor:"variant-images",className:"flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center pt-5 pb-6",children:[(0,r.jsx)(c.A,{className:"w-8 h-8 mb-4 text-gray-500"}),(0,r.jsx)("p",{className:"mb-2 text-sm text-gray-500",children:(0,r.jsx)("span",{className:"font-semibold",children:"G\xf6rsel y\xfckle"})}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"PNG, JPG (MAX. 5MB)"})]}),(0,r.jsx)("input",{id:"variant-images",type:"file",multiple:!0,accept:"image/*",onChange:e=>{let t=e.target.files;if(t&&p){let e=Array.from(t).map((e,t)=>({url:URL.createObjectURL(e),isMain:0===p.images.length&&0===t,sortOrder:p.images.length+t,file:e}));h(t=>t?{...t,images:[...t.images,...e]}:null)}},className:"hidden"})]})}),p.images.length>0&&(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:p.images.map((e,t)=>(0,r.jsxs)("div",{className:"relative group aspect-square",children:[(0,r.jsx)("img",{src:e.url,alt:`Varyant g\xf6rseli ${t+1}`,className:"w-full h-full object-cover rounded-lg"}),e.isMain&&(0,r.jsx)("div",{className:"absolute top-1 left-1 bg-red-600 text-white text-xs px-2 py-1 rounded",children:"Ana"}),(0,r.jsxs)("div",{className:"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center space-x-2",children:[!e.isMain&&(0,r.jsx)("button",{type:"button",onClick:()=>N(t),className:"px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700",children:"Ana Yap"}),(0,r.jsx)("button",{type:"button",onClick:()=>k(t),className:"p-1 bg-red-600 text-white rounded-full hover:bg-red-700",children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})]})]},t))})]})]})}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 flex-shrink-0",children:[(0,r.jsx)("button",{onClick:t,className:"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"İptal"}),(0,r.jsxs)("button",{onClick:()=>{if(!p)return;let e={...p.pricing,price:isNaN(p.pricing.price)?0:p.pricing.price,stock:isNaN(p.pricing.stock)?0:p.pricing.stock,extraDiscount:isNaN(p.pricing.extraDiscount)?0:p.pricing.extraDiscount,ratios:{...p.pricing.ratios,pvRatio:isNaN(p.pricing.ratios.pvRatio)?0:p.pricing.ratios.pvRatio,cvRatio:isNaN(p.pricing.ratios.cvRatio)?0:p.pricing.ratios.cvRatio,spRatio:isNaN(p.pricing.ratios.spRatio)?0:p.pricing.ratios.spRatio}},r={...p,pricing:e};w(r)&&(a(r),t())},className:`px-6 py-2 ${y.button} text-white rounded-lg transition-colors flex items-center`,children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Değişiklikleri Kaydet"]})]})]})})})}},11860:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},13964:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14952:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},16023:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},21852:(e,t,a)=>{a.d(t,{A:()=>g});var r=a(60687),i=a(43210),s=a.n(i),l=a(88920),n=a(26001),d=a(14952),c=a(13964),o=a(11860),u=a(45583),m=a(64298);let g=({isOpen:e,onClose:t,onSelect:a,initialData:g,colorScheme:x="red"})=>{let[p,h]=(0,i.useState)(1),[b,f]=(0,i.useState)(!1),[y,v]=(0,i.useState)(null),j={spinner:"blue"===x?"border-blue-600":"border-red-600",button:"blue"===x?"bg-blue-600 hover:bg-blue-700":"bg-red-600 hover:bg-red-700",selected:"blue"===x?"border-blue-500 bg-blue-50":"border-red-500 bg-red-50",checkButton:"blue"===x?"border-blue-500 bg-blue-50 text-blue-700":"border-red-500 bg-red-50 text-red-700"},[k,N]=(0,i.useState)([]),[w,A]=(0,i.useState)([]),[C,R]=(0,i.useState)([]),[I,M]=(0,i.useState)([]),[D,S]=(0,i.useState)({}),[z,V]=(0,i.useState)(0),[$,E]=(0,i.useState)(0),[O,F]=(0,i.useState)(0),[P,B]=(0,i.useState)({}),[U,H]=(0,i.useState)(""),[L,W]=(0,i.useState)(""),[q,G]=(0,i.useState)(""),K=s().useCallback((e=!1)=>{h(1),V(0),E(0),F(0),B({}),v(null),e||t()},[t]),T=s().useCallback(async()=>{try{f(!0),v(null);let e=await m.jU.getBrands();e.success?N(e.data):v(e.error||"Markalar y\xfcklenirken bir hata oluştu")}catch{v("Markalar y\xfcklenirken bir hata oluştu")}finally{f(!1)}},[]);(0,i.useEffect)(()=>{e&&(g&&g.brandId>0?(V(g.brandId),E(g.categoryId),F(g.subCategoryId),B(g.selectedFeatures||{}),console.log("\uD83C\uDFAF ProductCategorySelector initialData.selectedFeatures:",g.selectedFeatures),g.subCategoryId>0?h(4):g.categoryId>0?h(3):h(2)):K(!0),T())},[e,g,K,T]);let Z=s().useCallback(async e=>{try{f(!0),v(null);let t=await m.jU.getSubCategoryFeatures(e);t.success&&(M(t.data),t.data.forEach(e=>{Y(e.featureDefinitionId)}))}catch(e){console.error("\xd6zellik tanımları y\xfcklenirken hata:",e),v("\xd6zellik tanımları y\xfcklenirken bir hata oluştu.")}finally{f(!1)}},[]),J=s().useCallback(async e=>{try{f(!0),v(null);let t=await m.jU.getCategoriesByBrand(e);t.success?A(t.data):v(t.error||"Kategoriler y\xfcklenirken bir hata oluştu")}catch{v("Kategoriler y\xfcklenirken bir hata oluştu")}finally{f(!1)}},[]),X=s().useCallback(async e=>{try{f(!0),v(null);let t=await m.jU.getSubCategories(e);t.success?R(t.data):v(t.error||"Alt kategoriler y\xfcklenirken bir hata oluştu")}catch{v("Alt kategoriler y\xfcklenirken bir hata oluştu")}finally{f(!1)}},[]);(0,i.useEffect)(()=>{O>0&&Z(O)},[O,Z]),(0,i.useEffect)(()=>{z>0&&J(z)},[z,J]),(0,i.useEffect)(()=>{$>0&&X($)},[$,X]),(0,i.useEffect)(()=>{if(e){let e=window.innerWidth-document.documentElement.clientWidth;document.body.style.overflow="hidden",document.body.style.paddingRight=`${e}px`}else document.body.style.overflow="unset",document.body.style.paddingRight="0px";return()=>{document.body.style.overflow="unset",document.body.style.paddingRight="0px"}},[e]);let Y=async e=>{try{let t=await m.jU.getFeatureValues(e);t.success&&S(a=>({...a,[e]:t.data}))}catch(e){console.error("\xd6zellik değerleri y\xfcklenirken hata:",e)}},_=e=>{let t=k.find(t=>t.id===e);V(e),H(t?.name||""),g&&g.brandId===e||(E(0),F(0),B({})),h(2)},Q=e=>{let t=w.find(t=>t.id===e);E(e),W(t?.name||""),g&&g.categoryId===e||(F(0),B({})),h(3)},ee=async e=>{let t=C.find(t=>t.id===e);F(e),G(t?.name||""),g&&g.subCategoryId===e||B({}),v(null),f(!0);let a=await m.jU.getSubCategoryFeatures(e);f(!1),a.success&&a.data.length>0?(M(a.data),a.data.forEach(e=>{Y(e.featureDefinitionId)}),h(4)):(a.success&&0===a.data.length?v("Bu alt kategoriye ait \xf6zellik bulunmamaktadır."):v(a.error||"\xd6zellikler y\xfcklenirken bir hata oluştu."),M([]),S({}),h(3))},et=(e,t)=>{B(a=>{let r=a[e]||[],i=r.includes(t);return{...a,[e]:i?r.filter(e=>e!==t):[...r,t]}})},ea=()=>{let e=Object.entries(P).filter(([,e])=>e.length>0);return 0===e.length?[{id:Date.now(),name:"Default",selectedFeatures:{},featureDetails:[],features:[],pricing:{price:0,stock:0,extraDiscount:0,ratios:{pvRatio:0,cvRatio:0,spRatio:0},points:{pv:0,cv:0,sp:0}},images:[],isActive:!0}]:e.map(([e,t])=>t.map(t=>({featureDefinitionId:parseInt(e),valueId:t}))).reduce((e,t)=>e.flatMap(e=>t.map(t=>[...e,t])),[[]]).map((e,t)=>{let a=e.map(e=>{let t=I.find(t=>t.featureDefinitionId===e.featureDefinitionId),a=D[e.featureDefinitionId]?.find(t=>t.id===e.valueId);return{featureDefinitionId:e.featureDefinitionId,featureValueId:e.valueId,featureName:t?.featureDefinition?.name||"",featureValue:a?.value||""}}),r=a.map(e=>e.featureValue).join(" - "),i={};return a.forEach(e=>{i[e.featureDefinitionId]=[e.featureValueId]}),{id:Date.now()+t,name:r,selectedFeatures:i,featureDetails:a,features:a,pricing:{price:0,stock:0,extraDiscount:0,ratios:{pvRatio:0,cvRatio:0,spRatio:0},points:{pv:0,cv:0,sp:0}},images:[],isActive:!0}})},er=(()=>{let e=Object.values(P).map(e=>e.length).filter(e=>e>0);return 0===e.length?1:e.reduce((e,t)=>e*t,1)})();return(0,r.jsx)(l.N,{children:e&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50",children:(0,r.jsxs)(n.P.div,{className:"bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:1===p?"Marka Se\xe7in":2===p?"Kategori Se\xe7in":3===p?"Alt Kategori Se\xe7in":"Varyant Oluştur"}),(0,r.jsx)("button",{onClick:()=>K(),className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(o.A,{className:"h-6 w-6"})})]}),y&&(0,r.jsx)("div",{className:"p-4 bg-red-50 border-b border-red-200",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:y})}),(0,r.jsx)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-200px)]",children:(()=>{if(b)return(0,r.jsx)("div",{className:"flex justify-center items-center p-12",children:(0,r.jsx)("div",{className:`animate-spin rounded-full h-8 w-8 border-b-2 ${j.spinner}`})});let e=(e,t,a,i="name")=>(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.map(e=>(0,r.jsx)("button",{onClick:()=>a(e.id),className:`p-4 border rounded-lg text-left hover:bg-gray-50 transition-colors text-black ${t===e.id?j.selected:"border-gray-200"}`,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:String(e[i])}),(0,r.jsx)(d.A,{className:"h-5 w-5 text-gray-400"})]})},e.id))});switch(p){case 1:return e(k,z,_);case 2:return e(w,$,Q);case 3:return e(C,O,ee);case 4:return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Varyant Oluşturma Talimatları"}),(0,r.jsxs)("div",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Her \xf6zellik grubundan"})," istediğiniz kadar se\xe7enek se\xe7ebilirsiniz"]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Se\xe7tiğiniz t\xfcm kombinasyonlar"})," otomatik olarak varyant haline gelecek"]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"\xd6rnek:"})," 2 renk + 3 beden = 6 farklı varyant oluşur"]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Hi\xe7bir \xf6zellik se\xe7mezseniz"})," tek bir varsayılan varyant oluşturulur"]})]})]})]})}),I.map(e=>(0,r.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:e.featureDefinition?.name||"\xd6zellik"}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:[P[e.featureDefinitionId]?.length||0," se\xe7ili"]})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2",children:D[e.featureDefinitionId]?.map(t=>(0,r.jsxs)("button",{onClick:()=>et(e.featureDefinitionId,t.id),className:`p-2 border rounded text-sm transition-colors flex items-center justify-between ${P[e.featureDefinitionId]?.includes(t.id)?j.checkButton:"border-gray-200 hover:bg-gray-50 text-black"}`,children:[(0,r.jsx)("span",{children:t.value}),P[e.featureDefinitionId]?.includes(t.id)&&(0,r.jsx)(c.A,{className:"h-4 w-4"})]},t.id))})]},e.id)),Object.values(P).some(e=>e.length>0)&&(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-green-900 mb-2",children:"Oluşturulacak Varyantlar \xd6nizlemesi"}),(0,r.jsxs)("div",{className:"text-sm text-green-800",children:[(0,r.jsxs)("p",{className:"mb-2",children:[(0,r.jsxs)("strong",{children:[er," adet varyant"]})," oluşturulacak:"]}),(0,r.jsx)("div",{className:"space-y-1",children:Object.entries(P).filter(([,e])=>e.length>0).map(([e,t])=>{let a=I.find(t=>t.featureDefinitionId===parseInt(e)),i=t.map(t=>D[parseInt(e)]?.find(e=>e.id===t)?.value).filter(Boolean);return(0,r.jsxs)("p",{children:[(0,r.jsxs)("strong",{children:[a?.featureDefinition?.name||"\xd6zellik",":"]})," ",i.join(", ")]},e)})})]})]})]});default:return null}})()}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-200 mt-auto bg-gray-50",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{children:p>1&&(0,r.jsx)("button",{onClick:()=>h(p-1),className:"px-4 py-2 border border-gray-300 rounded-lg text-black hover:bg-gray-50",children:"Geri"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[4===p&&(0,r.jsxs)("div",{className:"flex items-center text-sm text-blue-600 font-medium",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 mr-2"}),Object.values(P).every(e=>0===e.length)?(0,r.jsx)("span",{children:"Varyant se\xe7ilmedi"}):(0,r.jsxs)("span",{children:[er," varyant oluşturulacak"]})]}),(0,r.jsx)("button",{onClick:()=>K(),className:"px-4 py-2 border border-gray-300 rounded-lg text-black hover:bg-gray-50",children:"İptal"}),4===p?(0,r.jsx)("button",{onClick:()=>{let e=ea(),r=[];Object.entries(P).forEach(([e,t])=>{let a=I.find(t=>t.featureDefinitionId===parseInt(e));t.forEach(t=>{let i=D[parseInt(e)]?.find(e=>e.id===t);a&&i&&r.push({featureName:a.featureDefinition?.name||"",featureValue:i.value})})}),a({brandId:z,categoryId:$,subCategoryId:O,brandName:U,categoryName:L,subCategoryName:q,generatedVariants:e,selectedFeatures:P,selectedFeatureDetails:r}),t()},disabled:Object.values(P).every(e=>0===e.length),className:`px-4 py-2 ${j.button} text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed`,children:"Se\xe7imi Tamamla"}):(0,r.jsx)("button",{onClick:()=>h(p+1),disabled:1===p?!z:2===p?!$:!O,className:`px-4 py-2 ${j.button} text-white rounded-lg disabled:opacity-50`,children:"Devam Et"})]})]})})]})})})}},28559:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},37360:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},45583:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},62688:(e,t,a)=>{a.d(t,{A:()=>u});var r=a(43210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),l=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:a=2,absoluteStrokeWidth:i,className:s="",children:l,iconNode:o,...u},m)=>(0,r.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:i?24*Number(a)/Number(t):a,className:n("lucide",s),...!l&&!d(u)&&{"aria-hidden":"true"},...u},[...o.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(l)?l:[l]])),u=(e,t)=>{let a=(0,r.forwardRef)(({className:a,...s},d)=>(0,r.createElement)(o,{ref:d,iconNode:t,className:n(`lucide-${i(l(e))}`,`lucide-${e}`,a),...s}));return a.displayName=l(e),a}},70440:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var r=a(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},88233:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},96882:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},99891:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};