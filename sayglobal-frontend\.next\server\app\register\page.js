(()=>{var e={};e.id=454,e.ids=[454],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8548:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>c});var t=s(60687),a=s(85814),i=s.n(a),l=s(26001),o=s(43210),n=s(15908),d=s(16189),m=s(75068);function c(){let[e,r]=(0,o.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",phoneNumber:"",referansCode:"",role:"customer",terms:!1}),[s,a]=(0,o.useState)({}),[c,p]=(0,o.useState)(""),{register:u,isLoading:x}=(0,n.A)();(0,d.useRouter)();let{openRegisterSuccessModal:g}=(0,m.QR)(),h=e=>{let{name:t,value:i,type:l}=e.target,o=e.target.checked;r(e=>({...e,[t]:"checkbox"===l?o:i})),s[t]&&a(e=>({...e,[t]:""})),p("")},b=()=>{let r={};if(e.firstName||(r.firstName="İsim gereklidir"),e.lastName||(r.lastName="Soyisim gereklidir"),e.email?/\S+@\S+\.\S+/.test(e.email)||(r.email="Ge\xe7erli bir e-posta adresi giriniz"):r.email="E-posta gereklidir",e.password){let s=[];e.password.length<6&&s.push("en az 6 karakter"),/[A-Z]/.test(e.password)||s.push("en az 1 b\xfcy\xfck harf"),/[a-z]/.test(e.password)||s.push("en az 1 k\xfc\xe7\xfck harf"),/[0-9]/.test(e.password)||s.push("en az 1 rakam"),/[^a-zA-Z0-9]/.test(e.password)||s.push("en az 1 \xf6zel karakter (!@#$%^&*)"),s.length>0&&(r.password=`Şifre ${s.join(", ")} i\xe7ermelidir`)}else r.password="Şifre gereklidir";return e.password!==e.confirmPassword&&(r.confirmPassword="Şifreler eşleşmiyor"),e.terms||(r.terms="Şartları ve koşulları kabul etmelisiniz"),a(r),0===Object.keys(r).length},f=async r=>{r.preventDefault(),p(""),b()&&(await u({firstName:e.firstName,lastName:e.lastName,email:e.email,password:e.password,confirmPassword:e.confirmPassword,phoneNumber:e.phoneNumber||void 0,referansCode:e.referansCode||void 0,role:e.role})?g():p("Kayıt işlemi başarısız! L\xfctfen bilgilerinizi kontrol edin ve tekrar deneyin."))};return(0,t.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,t.jsx)("div",{className:"max-w-lg mx-auto",children:(0,t.jsx)(l.P.div,{className:"bg-white rounded-2xl shadow-lg overflow-hidden",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,t.jsxs)("div",{className:"p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-2 text-gray-800",children:"Hesap Oluştur"}),(0,t.jsx)("p",{className:"text-gray-700",children:"Say Global ailesine katılarak sağlıklı yaşama adım atın"})]}),c&&(0,t.jsx)(l.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg",children:c}),(0,t.jsxs)("form",{onSubmit:f,children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-5 mb-5",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:"İsim"}),(0,t.jsx)("input",{id:"firstName",name:"firstName",type:"text",value:e.firstName,onChange:h,disabled:x,className:`w-full px-4 py-3 rounded-lg border ${s.firstName?"border-red-500":"border-gray-300"} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100`,placeholder:"İsminizi girin"}),s.firstName&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s.firstName})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Soyisim"}),(0,t.jsx)("input",{id:"lastName",name:"lastName",type:"text",value:e.lastName,onChange:h,disabled:x,className:`w-full px-4 py-3 rounded-lg border ${s.lastName?"border-red-500":"border-gray-300"} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100`,placeholder:"Soyisminizi girin"}),s.lastName&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s.lastName})]})]}),(0,t.jsxs)("div",{className:"space-y-5",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"E-posta Adresi"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",value:e.email,onChange:h,disabled:x,className:`w-full px-4 py-3 rounded-lg border ${s.email?"border-red-500":"border-gray-300"} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100`,placeholder:"<EMAIL>"}),s.email&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"referansCode",className:"block text-sm font-medium text-gray-700 mb-1",children:"Referans Kodu (Opsiyonel)"}),(0,t.jsx)("input",{id:"referansCode",name:"referansCode",type:"text",value:e.referansCode,onChange:h,disabled:x,className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100",placeholder:"Referans kodunu girin"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:"Telefon"}),(0,t.jsx)("input",{id:"phone",name:"phoneNumber",type:"tel",value:e.phoneNumber,onChange:h,disabled:x,className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100",placeholder:"+90 ************"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700 mb-1",children:"Hesap T\xfcr\xfc"}),(0,t.jsx)("select",{id:"role",name:"role",value:e.role,onChange:h,disabled:x,className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 disabled:bg-gray-100",children:(0,t.jsx)("option",{value:"customer",children:"M\xfcşteri"})}),(0,t.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Distrib\xfct\xf6r olmak i\xe7in \xfcyelik seviyenizi y\xfckseltmeniz, satıcı olmak i\xe7in başvuru yapmanız gerekir."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Şifre"}),(0,t.jsx)("input",{id:"password",name:"password",type:"password",value:e.password,onChange:h,disabled:x,autoComplete:"new-password",className:`w-full px-4 py-3 rounded-lg border ${s.password?"border-red-500":"border-gray-300"} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100`,placeholder:"En az 6 karakter"}),s.password&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s.password})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Şifre Tekrar"}),(0,t.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",value:e.confirmPassword,onChange:h,disabled:x,autoComplete:"new-password",className:`w-full px-4 py-3 rounded-lg border ${s.confirmPassword?"border-red-500":"border-gray-300"} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100`,placeholder:"Şifrenizi tekrar girin"}),s.confirmPassword&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s.confirmPassword})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex items-center h-5",children:(0,t.jsx)("input",{id:"terms",name:"terms",type:"checkbox",checked:e.terms,onChange:h,disabled:x,className:`h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded ${s.terms?"border-red-500":""} disabled:bg-gray-100`})}),(0,t.jsxs)("div",{className:"ml-3 text-sm",children:[(0,t.jsx)("label",{htmlFor:"terms",className:"font-medium text-gray-700",children:(0,t.jsxs)("span",{className:"text-gray-700",children:[(0,t.jsx)(i(),{href:"/terms",className:"text-purple-600 hover:text-purple-800",children:"Şartlar ve koşulları"})," ","okudum ve kabul ediyorum"]})}),s.terms&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s.terms})]})]}),(0,t.jsx)(l.P.button,{whileHover:{scale:x?1:1.02},whileTap:{scale:x?1:.98},type:"submit",disabled:x,className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center",children:x?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Kayıt Oluşturuluyor..."]}):"Kayıt Ol"})]})]}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["Zaten bir hesabınız var mı?"," ",(0,t.jsx)(i(),{href:"/login",className:"text-purple-600 font-medium hover:text-purple-800 transition",children:"Giriş Yap"})]})})]})})})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10849:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>c,pages:()=>m,routeModule:()=>p,tree:()=>d});var t=s(65239),a=s(48088),i=s(88170),l=s.n(i),o=s(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);s.d(r,n);let d={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,94530)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\register\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,m=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\register\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33081:(e,r,s)=>{Promise.resolve().then(s.bind(s,94530))},33873:e=>{"use strict";e.exports=require("path")},46233:(e,r,s)=>{Promise.resolve().then(s.bind(s,8548))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94530:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\register\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,181,658,85],()=>s(10849));module.exports=t})();