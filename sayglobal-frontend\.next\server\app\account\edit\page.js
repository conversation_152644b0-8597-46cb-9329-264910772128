(()=>{var e={};e.id=947,e.ids=[947],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6007:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\account\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\account\\edit\\page.tsx","default")},9104:(e,r,t)=>{"use strict";t.d(r,{d:()=>i});var s=t(12850);class a{static{this.DEFAULT_WEBP_OPTIONS={maxSizeMB:1,maxWidthOrHeight:800,quality:.8,fileType:"image/webp",useWebWorker:!0}}static{this.PROFILE_WEBP_OPTIONS={maxSizeMB:.5,maxWidthOrHeight:400,quality:.85,fileType:"image/webp",useWebWorker:!0}}static{this.PRODUCT_WEBP_OPTIONS={maxSizeMB:1.5,maxWidthOrHeight:1200,quality:.9,fileType:"image/webp",useWebWorker:!0}}static async convertToWebP(e,r=this.DEFAULT_WEBP_OPTIONS){let t=performance.now();console.log("\uD83D\uDDBC️ WebP d\xf6n\xfcşt\xfcrme başlıyor...",{originalName:e.name,originalSize:`${(e.size/1024/1024).toFixed(2)}MB`,originalType:e.type});try{let a={maxSizeMB:r.maxSizeMB||1,maxWidthOrHeight:r.maxWidthOrHeight||800,useWebWorker:!1!==r.useWebWorker,fileType:r.fileType||"image/webp",quality:r.quality||.8,alwaysKeepResolution:!1,initialQuality:r.quality||.8},i=await (0,s.A)(e,a),l=performance.now()-t,n={file:i,originalSize:e.size,compressedSize:i.size,compressionRatio:(1-i.size/e.size)*100,processingTime:l};return console.log("✅ WebP d\xf6n\xfcşt\xfcrme tamamlandı!",{compressedName:i.name,compressedSize:`${(i.size/1024/1024).toFixed(2)}MB`,compressedType:i.type,compressionRatio:`${n.compressionRatio.toFixed(1)}%`,processingTime:`${l.toFixed(1)}ms`}),n}catch(e){throw console.error("❌ WebP d\xf6n\xfcşt\xfcrme hatası:",e),Error("Resim d\xf6n\xfcşt\xfcr\xfcl\xfcrken hata oluştu")}}static async convertProfilePictureToWebP(e){return this.convertToWebP(e,this.PROFILE_WEBP_OPTIONS)}static async convertProductImageToWebP(e){return this.convertToWebP(e,this.PRODUCT_WEBP_OPTIONS)}static validateImageFile(e,r=10){return e.type.startsWith("image/")?e.size>1024*r*1024?{isValid:!1,error:`Dosya boyutu ${r}MB'dan k\xfc\xe7\xfck olmalıdır`}:["image/jpeg","image/jpg","image/png","image/webp","image/gif"].includes(e.type.toLowerCase())?{isValid:!0}:{isValid:!1,error:"Desteklenen formatlar: JPEG, PNG, WebP, GIF"}:{isValid:!1,error:"L\xfctfen bir resim dosyası se\xe7in"}}static createPreviewUrl(e){return new Promise((r,t)=>{let s=new FileReader;s.onloadend=()=>r(s.result),s.onerror=t,s.readAsDataURL(e)})}static async convertMultipleToWebP(e,r=this.DEFAULT_WEBP_OPTIONS){console.log(`🖼️ ${e.length} resim d\xf6n\xfcşt\xfcr\xfcl\xfcyor...`);let t=await Promise.allSettled(e.map(e=>this.convertToWebP(e,r))),s=[],a=[];return t.forEach((r,t)=>{"fulfilled"===r.status?s.push(r.value):a.push(`${e[t].name}: ${r.reason.message}`)}),a.length>0&&console.warn("⚠️ Bazı resimler işlenirken hata oluştu:",a),console.log(`✅ ${s.length}/${e.length} resim başarıyla d\xf6n\xfcşt\xfcr\xfcld\xfc`),s}static formatFileSize(e){if(0===e)return"0 Bytes";let r=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,r)).toFixed(2))+" "+["Bytes","KB","MB","GB"][r]}static formatCompressionRatio(e){return`${e.toFixed(1)}%`}}let i=a},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20573:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var s=t(60687),a=t(43210),i=t(26001),l=t(85814),n=t.n(l),o=t(16189),d=t(15908),c=t(10535),u=t(9104),m=t(73638),p=t(78259),h=t(8693),x=t(87979);function f(){let e,r,t=(0,o.useRouter)(),{user:l,isLoading:f}=(0,d.A)(),{data:g,isLoading:b}=(0,x.dS)(),y=(0,h.jE)(),[v,j]=(0,a.useState)({firstName:"",lastName:"",phone:"",location:"",birthDate:"",gender:""}),[N,w]=(0,a.useState)({firstName:"",lastName:"",phone:"",location:"",birthDate:"",gender:""}),[P,k]=(0,a.useState)(!1),[S,z]=(0,a.useState)(null),[W,T]=(0,a.useState)(null),[D,C]=(0,a.useState)(!1),[F,q]=(0,a.useState)({}),[B,A]=(0,a.useState)(null),[E,M]=(0,a.useState)(!1),[O,L]=(0,a.useState)(!1),_=e=>{let{name:r,value:t}=e.target;j(e=>({...e,[r]:t})),F[r]&&q(e=>({...e,[r]:""}))},R=async e=>{try{let r=u.d.validateImageFile(e,10);if(!r.isValid)return void q(e=>({...e,image:r.error}));F.image&&q(e=>({...e,image:""})),C(!0);let t=await u.d.convertProfilePictureToWebP(e);z(t.file),setImageProcessingResult(t);let s=await u.d.createPreviewUrl(t.file);T(s)}catch(e){q(r=>({...r,image:e instanceof Error?e.message:"Resim işlenirken hata oluştu"}))}finally{C(!1)}},U=()=>{let e={};return v.firstName.trim()||(e.firstName="Ad alanı zorunludur"),v.lastName.trim()||(e.lastName="Soyad alanı zorunludur"),v.phone&&!/^\+?\d{10,15}$/.test(v.phone.replace(/\s+/g,""))&&(e.phone="Ge\xe7erli bir telefon numarası girin"),q(e),0===Object.keys(e).length},I=()=>v.firstName!==N.firstName||v.lastName!==N.lastName||v.phone!==N.phone||v.location!==N.location||v.birthDate!==N.birthDate||v.gender!==N.gender||null!==S||O,$=async e=>{if(e.preventDefault(),U()){if(!I())return void t.push("/account");k(!0);try{q({}),A(null);let e=[],r=v.firstName!==N.firstName||v.lastName!==N.lastName||v.phone!==N.phone||v.location!==N.location||v.birthDate!==N.birthDate||v.gender!==N.gender;if(r){let r={};if(v.firstName&&v.firstName.trim()&&(r.firstName=v.firstName.trim()),v.lastName&&v.lastName.trim()&&(r.lastName=v.lastName.trim()),v.phone&&v.phone.trim()&&(r.phoneNumber=v.phone.trim()),v.location&&v.location.trim()&&(r.location=v.location.trim()),v.birthDate&&(r.dateOfBirth=new Date(v.birthDate).toISOString()),v.gender){let e={erkek:p.w7.Male,kadın:p.w7.Female,diğer:p.w7.Other};e[v.gender]&&(r.gender=e[v.gender])}Object.keys(r).length>0&&e.push(c.y.updateProfile(r))}S&&e.push(c.y.updateProfilePicture(S)),O&&e.push(c.y.deleteProfilePicture()),await Promise.all(e),y.invalidateQueries({queryKey:x.ZF.user()}),y.invalidateQueries({queryKey:x.ZF.profileInfo()}),await Promise.all([y.refetchQueries({queryKey:x.ZF.user()}),y.refetchQueries({queryKey:x.ZF.profileInfo()})]),S||O?r?A("Profil bilgileriniz ve fotoğrafınız başarıyla g\xfcncellendi!"):A("Profil fotoğrafınız başarıyla g\xfcncellendi!"):A("Profil bilgileriniz başarıyla g\xfcncellendi!"),z(null),T(null),setImageProcessingResult(null),L(!1),setTimeout(()=>{A(null)},3e3),setTimeout(()=>{t.push("/account")},1500)}catch(e){q({submit:e instanceof Error&&"response"in e&&e.response&&"object"==typeof e.response&&"data"in e.response&&e.response.data&&"object"==typeof e.response.data&&"message"in e.response.data?String(e.response.data.message):"Profil g\xfcncellenirken bir hata oluştu"})}finally{k(!1)}}};if(f||b)return(0,s.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsx)("div",{className:"bg-white rounded-2xl shadow-lg overflow-hidden p-8",children:(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-300 rounded mb-6 w-64"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)("div",{className:"h-10 bg-gray-300 rounded"}),(0,s.jsx)("div",{className:"h-10 bg-gray-300 rounded"}),(0,s.jsx)("div",{className:"h-10 bg-gray-300 rounded"}),(0,s.jsx)("div",{className:"h-10 bg-gray-300 rounded"})]})]})})})});if(!l)return null;let H=W||(O?null:g?.profilePictureUrl);return(0,s.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsx)("div",{className:"bg-white rounded-2xl shadow-lg overflow-hidden",children:(0,s.jsxs)("div",{className:"p-6 md:p-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8 pb-6 border-b border-gray-200",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl md:text-3xl font-bold text-gray-800 mb-2",children:"Profili D\xfczenle"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Profil bilgilerinizi g\xfcncelleyebilirsiniz"})]}),(0,s.jsx)(n(),{href:"/account",children:(0,s.jsxs)(i.P.button,{className:"text-gray-500 hover:text-gray-700 transition-colors flex items-center",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Geri D\xf6n"]})})]}),F.submit&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"h-5 w-5 text-red-500 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("span",{className:"text-red-700",children:F.submit})]})}),B&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"h-5 w-5 text-green-500 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("span",{className:"text-green-700",children:B})]})}),(0,s.jsxs)("form",{onSubmit:$,className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("div",{className:"w-32 h-32 rounded-full bg-gradient-to-br from-purple-600 to-indigo-600 flex items-center justify-center overflow-hidden",children:[H?(0,s.jsx)("img",{src:H,alt:"Profile",className:"w-full h-full object-cover",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling.style.display="flex"}}):null,(0,s.jsx)("span",{className:`text-white text-3xl font-bold ${H?"hidden":"flex"} items-center justify-center w-full h-full`,children:(e=g?.firstName||l.firstName,r=g?.lastName||l.lastName,`${e.charAt(0).toUpperCase()}${r.charAt(0).toUpperCase()}`)})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.P.button,{type:"button",onClick:()=>{M(!E)},disabled:D,className:`absolute bottom-0 right-0 rounded-full p-2 shadow-lg transition-all ${D?"bg-gray-400 cursor-not-allowed":"bg-purple-600 hover:bg-purple-700"} text-white`,whileHover:D?{}:{scale:1.1},whileTap:D?{}:{scale:.9},children:D?(0,s.jsxs)("svg",{className:"h-4 w-4 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 13a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,s.jsx)(m.A,{isOpen:E,onClose:()=>{M(!1)},onFileSelect:R,hasProfilePicture:!!H&&!S&&!O,onDeleteSuccess:()=>{L(!0),z(null),T(null),setImageProcessingResult(null),A("Profil fotoğrafınız silmek \xfczere işaretlendi. Değişiklikleri kaydetmeyi unutmayın!"),setTimeout(()=>{A(null)},3e3)},onDeleteError:e=>{q(r=>({...r,image:e}))},isDeleting:D,mode:"edit"})]})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:D?"Resim WebP formatına d\xf6n\xfcşt\xfcr\xfcl\xfcyor...":"Profil fotoğrafını değiştirmek i\xe7in tıklayın"}),F.image&&(0,s.jsx)("p",{className:"text-sm text-red-600 mt-1",children:F.image})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Temel Bilgiler"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Ad *"}),(0,s.jsx)("input",{type:"text",id:"firstName",name:"firstName",value:v.firstName,onChange:_,className:`w-full px-4 py-2 rounded-lg border transition text-black ${F.firstName?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 focus:ring-purple-500 focus:border-transparent"} focus:outline-none focus:ring-2`,required:!0}),F.firstName&&(0,s.jsx)("p",{className:"text-sm text-red-600 mt-1",children:F.firstName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Soyad *"}),(0,s.jsx)("input",{type:"text",id:"lastName",name:"lastName",value:v.lastName,onChange:_,className:`w-full px-4 py-2 rounded-lg border transition text-black ${F.lastName?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 focus:ring-purple-500 focus:border-transparent"} focus:outline-none focus:ring-2`,required:!0}),F.lastName&&(0,s.jsx)("p",{className:"text-sm text-red-600 mt-1",children:F.lastName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:"Telefon"}),(0,s.jsx)("input",{type:"tel",id:"phone",name:"phone",value:v.phone,onChange:_,className:`w-full px-4 py-2 rounded-lg border transition text-black ${F.phone?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 focus:ring-purple-500 focus:border-transparent"} focus:outline-none focus:ring-2`,placeholder:"+90 ************"}),F.phone&&(0,s.jsx)("p",{className:"text-sm text-red-600 mt-1",children:F.phone})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Kişisel Bilgiler"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"birthDate",className:"block text-sm font-medium text-gray-700 mb-1",children:"Doğum Tarihi"}),(0,s.jsx)("input",{type:"date",id:"birthDate",name:"birthDate",value:v.birthDate,onChange:_,className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"gender",className:"block text-sm font-medium text-gray-700 mb-1",children:"Cinsiyet"}),(0,s.jsxs)("select",{id:"gender",name:"gender",value:v.gender,onChange:_,className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",children:[(0,s.jsx)("option",{value:"",children:"Se\xe7iniz"}),(0,s.jsx)("option",{value:"erkek",children:"Erkek"}),(0,s.jsx)("option",{value:"kadın",children:"Kadın"}),(0,s.jsx)("option",{value:"diğer",children:"Diğer"})]})]}),(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("label",{htmlFor:"location",className:"block text-sm font-medium text-gray-700 mb-1",children:"Konum"}),(0,s.jsx)("input",{type:"text",id:"location",name:"location",value:v.location,onChange:_,className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",placeholder:"Şehir, \xdclke"})]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200",children:[(0,s.jsx)(i.P.button,{type:"submit",disabled:P||!I(),className:`flex-1 py-3 px-6 rounded-lg font-medium transition-all duration-300 ${P||!I()?"bg-gray-400 text-gray-600 cursor-not-allowed":"bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:shadow-lg"}`,whileHover:!P&&I()?{scale:1.02}:{},whileTap:!P&&I()?{scale:.98}:{},children:P?"Kaydediliyor...":"Değişiklikleri Kaydet"}),(0,s.jsx)(n(),{href:"/account",className:"flex-1",children:(0,s.jsx)(i.P.button,{type:"button",disabled:P,className:`w-full py-3 px-6 rounded-lg font-medium transition-all duration-300 ${P?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,whileHover:P?{}:{scale:1.02},whileTap:P?{}:{scale:.98},children:"İptal"})})]})]})]})})})})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29759:(e,r,t)=>{Promise.resolve().then(t.bind(t,20573))},33873:e=>{"use strict";e.exports=require("path")},47911:(e,r,t)=>{Promise.resolve().then(t.bind(t,6007))},55431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),l=t.n(i),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(r,o);let d={children:["",{children:["account",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6007)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\account\\edit\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\account\\edit\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/account/edit/page",pathname:"/account/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73638:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var s=t(60687),a=t(43210),i=t(88920),l=t(26001),n=t(10535),o=t(8693),d=t(87979);let c=({isOpen:e,onClose:r,onFileSelect:t,hasProfilePicture:c,onDeleteSuccess:u,onDeleteError:m,isDeleting:p=!1,mode:h="immediate"})=>{let x=(0,a.useRef)(null),f=(0,a.useRef)(null),g=(0,o.jE)(),[b,y]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let t=e=>{f.current&&!f.current.contains(e.target)&&r()};return e&&document.addEventListener("mousedown",t),()=>{document.removeEventListener("mousedown",t)}},[e,r]),(0,a.useEffect)(()=>{let t=e=>{"Escape"===e.key&&r()};return e&&document.addEventListener("keydown",t),()=>{document.removeEventListener("keydown",t)}},[e,r]);let v=async()=>{if(c){if("edit"===h){u?.(),r();return}y(!0);try{await n.y.deleteProfilePicture(),g.invalidateQueries({queryKey:d.ZF.user()}),g.invalidateQueries({queryKey:d.ZF.profileInfo()}),await Promise.all([g.refetchQueries({queryKey:d.ZF.user()}),g.refetchQueries({queryKey:d.ZF.profileInfo()})]),u?.(),r()}catch(e){m?.(e.response?.data?.message||"Profil fotoğrafı silinirken bir hata oluştu")}finally{y(!1)}}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.N,{children:e&&(0,s.jsx)(l.P.div,{ref:f,initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},transition:{duration:.1},className:"absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50",children:(0,s.jsxs)("div",{className:"p-3 space-y-2",children:[(0,s.jsxs)(l.P.button,{type:"button",onClick:()=>{x.current?.click(),r()},className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-2 px-3 rounded-lg text-sm font-medium flex items-center justify-center hover:shadow-lg transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,s.jsxs)("svg",{className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 13a3 3 0 11-6 0 3 3 0 016 0z"})]}),"Fotoğraf Y\xfckle"]}),(0,s.jsx)(l.P.button,{type:"button",onClick:v,disabled:!c||b||p,className:`w-full py-2 px-3 rounded-lg text-sm flex items-center justify-center transition-all duration-300 ${!c||b||p?"text-gray-400 cursor-not-allowed bg-gray-50":"text-red-600 hover:bg-red-50 hover:text-red-700"}`,whileHover:!c||b||p?{}:{scale:1.02},whileTap:!c||b||p?{}:{scale:.98},children:b||p?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"h-4 w-4 mr-2 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Siliniyor..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("svg",{className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Profil Fotoğrafını Kaldır"]})})]})})}),(0,s.jsx)("input",{ref:x,type:"file",accept:"image/*",onChange:e=>{let r=e.target.files?.[0];r&&t(r),x.current&&(x.current.value="")},className:"hidden"})]})}},74075:e=>{"use strict";e.exports=require("zlib")},78259:(e,r,t)=>{"use strict";t.d(r,{F:()=>a,Sz:()=>l,vn:()=>s,w7:()=>i});var s=function(e){return e[e.Default=0]="Default",e[e.PriceAsc=1]="PriceAsc",e[e.PriceDesc=2]="PriceDesc",e[e.RatingDesc=3]="RatingDesc",e}({}),a=function(e){return e[e.None=0]="None",e[e.Baslangic=1]="Baslangic",e[e.Girisimci=2]="Girisimci",e[e.Bronz=3]="Bronz",e[e.Gumus=4]="Gumus",e[e.Altin=5]="Altin",e[e.Platin=6]="Platin",e[e.PlatinMax=7]="PlatinMax",e}({}),i=function(e){return e[e.Unspecified=0]="Unspecified",e[e.Male=1]="Male",e[e.Female=2]="Female",e[e.Other=3]="Other",e}({}),l=function(e){return e[e.Pending=0]="Pending",e[e.Accepted=1]="Accepted",e[e.Rejected=2]="Rejected",e}({})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,181,658,850,85],()=>t(55431));module.exports=s})();