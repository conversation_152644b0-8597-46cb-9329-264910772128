(()=>{var e={};e.id=944,e.ids=[944],e.modules={1965:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>o,pages:()=>m,routeModule:()=>x,tree:()=>c});var a=s(65239),r=s(48088),i=s(88170),l=s.n(i),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let c={children:["",{children:["become-dealer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,38182)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\become-dealer\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,m=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\become-dealer\\page.tsx"],o={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/become-dealer/page",pathname:"/become-dealer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4267:(e,t,s)=>{Promise.resolve().then(s.bind(s,38182))},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10022:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16023:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},17313:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},19080:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38182:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\become-dealer\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\become-dealer\\page.tsx","default")},41312:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41550:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},48340:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(43210);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let m=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:r,className:i="",children:l,iconNode:m,...o},x)=>(0,a.createElement)("svg",{ref:x,...c,width:t,height:t,stroke:e,strokeWidth:r?24*Number(s)/Number(t):s,className:n("lucide",i),...!l&&!d(o)&&{"aria-hidden":"true"},...o},[...m.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(l)?l:[l]])),o=(e,t)=>{let s=(0,a.forwardRef)(({className:s,...i},d)=>(0,a.createElement)(m,{ref:d,iconNode:t,className:n(`lucide-${r(l(e))}`,`lucide-${e}`,s),...i}));return s.displayName=l(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68899:(e,t,s)=>{"use strict";s.d(t,{CM:()=>r,nA:()=>i,vM:()=>a});let a=[{id:1,userId:6,userName:"Mehmet Yılmaz",userEmail:"<EMAIL>",applicationData:{firstName:"Mehmet",lastName:"Yılmaz",email:"<EMAIL>",phone:"+90 ************",mainProductCategory:"Elektronik",subProductCategories:["Telefon","Bilgisayar","Aksesuar"],estimatedProductCount:"50-100 adet",sampleProductListUrl:"https://example.com/products",companyName:"Yılmaz Elektronik Ltd. Şti.",taxNumber:"1234567890",taxOffice:"İstanbul Vergi Dairesi",companyAddress:"Atat\xfcrk Cad. No:123 Kadık\xf6y/İstanbul",authorizedPersonName:"Mehmet Yılmaz",authorizedPersonTcId:"12345678901",alternativeContactNumber:"+90 ************",userAgreementAccepted:!0,dealershipAgreementAccepted:!0,privacyPolicyAccepted:!0},status:"pending",submittedAt:"2024-01-15T10:30:00Z"},{id:2,userId:7,userName:"Ayşe Kaya",userEmail:"<EMAIL>",applicationData:{firstName:"Ayşe",lastName:"Kaya",email:"<EMAIL>",phone:"+90 ************",mainProductCategory:"Ev & Yaşam",subProductCategories:["Mutfak","Dekorasyon"],estimatedProductCount:"20-50 adet",companyName:"Kaya Ev Tekstili",taxNumber:"0987654321",taxOffice:"Ankara Vergi Dairesi",companyAddress:"Kızılay Cad. No:456 \xc7ankaya/Ankara",authorizedPersonName:"Ayşe Kaya",authorizedPersonTcId:"10987654321",alternativeContactNumber:"+90 ************",userAgreementAccepted:!0,dealershipAgreementAccepted:!0,privacyPolicyAccepted:!0},status:"approved",submittedAt:"2024-01-10T14:20:00Z",reviewedAt:"2024-01-12T09:15:00Z",reviewedBy:1,adminNotes:"Başvuru uygun bulunmuştur."}],r=["Elektronik","Ev & Yaşam","Giyim & Aksesuar","Spor & Outdoor","Kozmetik & Kişisel Bakım","Kitap & Kırtasiye","Oyuncak & Hobi","Otomotiv","Anne & Bebek","Diğer"],i=["1-10 adet","11-25 adet","26-50 adet","51-100 adet","101-250 adet","250+ adet"]},70334:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87235:(e,t,s)=>{Promise.resolve().then(s.bind(s,89572))},89572:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var a=s(60687),r=s(43210),i=s(15908),l=s(16189),n=s(26001),d=s(68899),c=s(88920),m=s(11860),o=s(5336),x=s(41312),u=s(70334);let p=({isOpen:e,onClose:t,onGoToAccount:s})=>(0,a.jsx)(c.N,{children:e&&(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-black/20 backdrop-blur-sm",onClick:t}),(0,a.jsxs)(n.P.div,{initial:{opacity:0,scale:.9,y:-20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:-20},transition:{type:"spring",duration:.5},className:"relative bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,a.jsx)("button",{onClick:t,className:"absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600 transition-colors z-10",children:(0,a.jsx)(m.A,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-100 p-8 text-center",children:[(0,a.jsx)(n.P.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:200,damping:10,delay:.2},className:"inline-flex items-center justify-center w-20 h-20 bg-green-500 rounded-full mb-6",children:(0,a.jsx)(o.A,{className:"h-10 w-10 text-white"})}),(0,a.jsx)(n.P.h2,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4},className:"text-2xl font-bold text-gray-900 mb-3",children:"Başvurunuz Alındı! \uD83C\uDF89"}),(0,a.jsx)(n.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},className:"text-gray-600 mb-6 text-sm leading-relaxed",children:"Satıcı başvurunuz başarıyla g\xf6nderildi. Başvurunuz inceleme s\xfcrecine alınmış olup, en kısa s\xfcrede size geri d\xf6n\xfcş yapılacaktır."}),(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},className:"space-y-3 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center bg-white bg-opacity-60 rounded-lg p-3 text-left",children:[(0,a.jsx)("div",{className:"bg-blue-100 p-2 rounded-full mr-3",children:(0,a.jsx)(x.A,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-800",children:"İnceleme S\xfcreci"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Başvurunuz 1-3 iş g\xfcn\xfc i\xe7inde değerlendirilecektir"})]})]}),(0,a.jsxs)("div",{className:"flex items-center bg-white bg-opacity-60 rounded-lg p-3 text-left",children:[(0,a.jsx)("div",{className:"bg-purple-100 p-2 rounded-full mr-3",children:(0,a.jsx)(u.A,{className:"h-4 w-4 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-800",children:"Sonu\xe7 Bildirimi"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"E-posta ve hesap paneli \xfczerinden bilgilendirileceksiniz"})]})]})]}),(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.7},className:"flex flex-col space-y-3",children:[(0,a.jsxs)("button",{onClick:s,className:"w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center justify-center space-x-2",children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Hesabıma Git"})]}),(0,a.jsx)("button",{onClick:t,className:"w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-6 rounded-lg transition-colors",children:"Kapat"})]})]})]})]})});var g=s(19080),h=s(17313),y=s(16023),b=s(58869),N=s(10022),j=s(13861),f=s(93613),v=s(41550),k=s(48340);let A=()=>{let{user:e,isLoading:t}=(0,i.A)(),s=(0,l.useRouter)(),[c,x]=(0,r.useState)({firstName:"",lastName:"",email:"",phone:"",mainProductCategory:"",subProductCategories:[],estimatedProductCount:"",sampleProductListUrl:"",companyName:"",taxNumber:"",taxOffice:"",companyAddress:"",taxCertificate:null,tradeRegistryGazette:null,signatureCircular:null,additionalDocuments:[],authorizedPersonName:"",authorizedPersonTcId:"",alternativeContactNumber:"",userAgreementAccepted:!1,dealershipAgreementAccepted:!1,privacyPolicyAccepted:!1}),[A,w]=(0,r.useState)(1),[z,P]=(0,r.useState)(!1),[C,M]=(0,r.useState)({}),[B,S]=(0,r.useState)(!1);(0,r.useEffect)(()=>{e&&x(t=>({...t,firstName:e.firstName,lastName:e.lastName,email:e.email,phone:e.phone,authorizedPersonName:`${e.firstName} ${e.lastName}`}))},[e]),(0,r.useEffect)(()=>{t||e||s.push("/login?redirect=/become-dealer")},[e,t,s]);let G=(e,t)=>{x(s=>({...s,[e]:t})),C[e]&&M(t=>({...t,[e]:""}))},D=(e,t,s)=>{if(t.size>5242880){alert("Dosya boyutu 5MB'dan b\xfcy\xfck olamaz"),s&&(s.value="");return}if(!["image/jpeg","image/png","image/jpg","application/pdf"].includes(t.type)){alert("Sadece JPEG, PNG ve PDF dosyaları y\xfckleyebilirsiniz"),s&&(s.value="");return}G(e,t),s&&(s.value="")},$=e=>{G(e,null)},E=(e,t)=>{if(e.size>5242880){alert("Dosya boyutu 5MB'dan b\xfcy\xfck olamaz"),t&&(t.value="");return}if(!["image/jpeg","image/png","image/jpg","application/pdf"].includes(e.type)){alert("Sadece JPEG, PNG ve PDF dosyaları y\xfckleyebilirsiniz"),t&&(t.value="");return}x(t=>({...t,additionalDocuments:[...t.additionalDocuments||[],e]})),t&&(t.value="")},K=e=>{x(t=>({...t,additionalDocuments:t.additionalDocuments?.filter((t,s)=>s!==e)||[]}))},T=e=>{let t={};switch(e){case 1:c.mainProductCategory||(t.mainProductCategory="Ana \xfcr\xfcn kategorisi se\xe7melisiniz"),c.estimatedProductCount||(t.estimatedProductCount="Tahmini \xfcr\xfcn sayısı se\xe7melisiniz");break;case 2:c.companyName||(t.companyName="Şirket adı gereklidir"),c.taxNumber||(t.taxNumber="Vergi kimlik numarası gereklidir"),c.taxOffice||(t.taxOffice="Vergi dairesi gereklidir"),c.companyAddress||(t.companyAddress="Şirket adresi gereklidir");break;case 3:c.taxCertificate||(t.taxCertificate="Vergi levhası gereklidir");break;case 4:c.authorizedPersonName||(t.authorizedPersonName="Yetkili kişi adı gereklidir"),c.authorizedPersonTcId||(t.authorizedPersonTcId="T.C. kimlik numarası gereklidir"),c.alternativeContactNumber||(t.alternativeContactNumber="Alternatif iletişim numarası gereklidir");break;case 5:c.userAgreementAccepted||(t.userAgreementAccepted="Kullanıcı s\xf6zleşmesini kabul etmelisiniz"),c.dealershipAgreementAccepted||(t.dealershipAgreementAccepted="Satıcı s\xf6zleşmesini kabul etmelisiniz"),c.privacyPolicyAccepted||(t.privacyPolicyAccepted="Gizlilik politikasını kabul etmelisiniz")}return M(t),0===Object.keys(t).length},q=async()=>{if(T(6)&&e){P(!0);try{let t={id:Date.now(),userId:e.id,userName:`${e.firstName} ${e.lastName}`,userEmail:e.email,applicationData:c,status:"pending",submittedAt:new Date().toISOString()};d.vM.push(t),await new Promise(e=>setTimeout(e,2e3)),S(!0)}catch(e){alert("Başvuru g\xf6nderilirken bir hata oluştu. L\xfctfen tekrar deneyin.")}finally{P(!1)}}};if(t)return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})});if(!e)return null;let O=[{number:1,title:"\xdcr\xfcn Bilgileri",icon:g.A},{number:2,title:"Şirket Bilgileri",icon:h.A},{number:3,title:"Belgeler",icon:y.A},{number:4,title:"Yetkili Kişi",icon:b.A},{number:5,title:"S\xf6zleşmeler",icon:N.A},{number:6,title:"\xd6nizleme",icon:j.A}];return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8",children:[(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Satıcı Ol"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Say Global platformunda satıcı olmak i\xe7in aşağıdaki formu doldurun"})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("div",{className:"flex justify-center items-center",children:(0,a.jsx)("div",{className:"flex items-center space-x-4",children:O.map((e,t)=>{let s=e.icon,r=A===e.number,i=A>e.number;return(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("div",{className:`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-colors ${i?"bg-green-500 border-green-500 text-white":r?"bg-purple-600 border-purple-600 text-white":"bg-white border-gray-300 text-gray-400"}`,children:i?(0,a.jsx)(o.A,{className:"h-6 w-6"}):(0,a.jsx)(s,{className:"h-6 w-6"})}),(0,a.jsx)("div",{className:"mt-2 text-center",children:(0,a.jsx)("p",{className:`text-xs font-medium whitespace-nowrap ${r?"text-purple-600":i?"text-green-600":"text-gray-400"}`,children:e.title})})]}),t<O.length-1&&(0,a.jsx)("div",{className:`w-8 md:w-16 h-0.5 mx-2 ${i?"bg-green-500":"bg-gray-300"}`})]},e.number)})})})}),(0,a.jsxs)(n.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:"bg-white rounded-xl shadow-lg p-6 mb-6",children:[1===A&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(g.A,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"\xdcr\xfcn Bilgileri"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ana \xdcr\xfcn Kategorisi *"}),(0,a.jsxs)("select",{value:c.mainProductCategory,onChange:e=>G("mainProductCategory",e.target.value),className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-600 ${C.mainProductCategory?"border-red-500":"border-gray-300"}`,children:[(0,a.jsx)("option",{value:"",children:"Kategori se\xe7in"}),d.CM.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]}),C.mainProductCategory&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:C.mainProductCategory})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tahmini \xdcr\xfcn Sayısı *"}),(0,a.jsxs)("select",{value:c.estimatedProductCount,onChange:e=>G("estimatedProductCount",e.target.value),className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-600 ${C.estimatedProductCount?"border-red-500":"border-gray-300"}`,children:[(0,a.jsx)("option",{value:"",children:"\xdcr\xfcn sayısı se\xe7in"}),d.nA.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]}),C.estimatedProductCount&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:C.estimatedProductCount})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xd6rnek \xdcr\xfcn Listesi / URL (İsteğe Bağlı)"}),(0,a.jsx)("input",{type:"url",value:c.sampleProductListUrl||"",onChange:e=>G("sampleProductListUrl",e.target.value),placeholder:"https://example.com/products",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"})]})]}),2===A&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(h.A,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Şirket Bilgileri"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Şirket Adı / Unvanı *"}),(0,a.jsx)("input",{type:"text",value:c.companyName,onChange:e=>G("companyName",e.target.value),className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${C.companyName?"border-red-500":"border-gray-300"}`,placeholder:"Şirket adınızı girin"}),C.companyName&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:C.companyName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Vergi Kimlik Numarası *"}),(0,a.jsx)("input",{type:"text",value:c.taxNumber,onChange:e=>G("taxNumber",e.target.value),className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${C.taxNumber?"border-red-500":"border-gray-300"}`,placeholder:"1234567890"}),C.taxNumber&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:C.taxNumber})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Vergi Dairesi *"}),(0,a.jsx)("input",{type:"text",value:c.taxOffice,onChange:e=>G("taxOffice",e.target.value),className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${C.taxOffice?"border-red-500":"border-gray-300"}`,placeholder:"İstanbul Vergi Dairesi"}),C.taxOffice&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:C.taxOffice})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Şirket Adresi *"}),(0,a.jsx)("textarea",{value:c.companyAddress,onChange:e=>G("companyAddress",e.target.value),rows:3,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${C.companyAddress?"border-red-500":"border-gray-300"}`,placeholder:"Tam şirket adresini girin"}),C.companyAddress&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:C.companyAddress})]})]})]}),3===A&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(y.A,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Belgeler"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Vergi Levhası *",(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"(JPEG, PNG, PDF - Max 5MB)"})]}),(0,a.jsx)("div",{className:`border-2 border-dashed rounded-lg p-4 ${C.taxCertificate?"border-red-500 bg-red-50":"border-gray-300"}`,children:c.taxCertificate?(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:c.taxCertificate.name}),(0,a.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(",(c.taxCertificate.size/1024/1024).toFixed(2)," MB)"]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>$("taxCertificate"),className:"text-red-500 hover:text-red-700",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})]}):(0,a.jsxs)("label",{className:"cursor-pointer block",children:[(0,a.jsx)("input",{type:"file",accept:".jpg,.jpeg,.png,.pdf",className:"hidden",onChange:e=>{let t=e.target.files?.[0];t&&D("taxCertificate",t,e.target)}}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(y.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Vergi levhanızı y\xfcklemek i\xe7in tıklayın"})]})]})}),C.taxCertificate&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:C.taxCertificate})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Ticaret Sicil Gazetesi (İsteğe Bağlı)",(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"(JPEG, PNG, PDF - Max 5MB)"})]}),(0,a.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-4",children:c.tradeRegistryGazette?(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:c.tradeRegistryGazette.name}),(0,a.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(",(c.tradeRegistryGazette.size/1024/1024).toFixed(2)," MB)"]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>$("tradeRegistryGazette"),className:"text-red-500 hover:text-red-700",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})]}):(0,a.jsxs)("label",{className:"cursor-pointer block",children:[(0,a.jsx)("input",{type:"file",accept:".jpg,.jpeg,.png,.pdf",className:"hidden",onChange:e=>{let t=e.target.files?.[0];t&&D("tradeRegistryGazette",t,e.target)}}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(y.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Ticaret sicil gazetenizi y\xfcklemek i\xe7in tıklayın"})]})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["İmza Sirk\xfcleri (İsteğe Bağlı)",(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"(JPEG, PNG, PDF - Max 5MB)"})]}),(0,a.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-4",children:c.signatureCircular?(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:c.signatureCircular.name}),(0,a.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(",(c.signatureCircular.size/1024/1024).toFixed(2)," MB)"]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>$("signatureCircular"),className:"text-red-500 hover:text-red-700",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})]}):(0,a.jsxs)("label",{className:"cursor-pointer block",children:[(0,a.jsx)("input",{type:"file",accept:".jpg,.jpeg,.png,.pdf",className:"hidden",onChange:e=>{let t=e.target.files?.[0];t&&D("signatureCircular",t,e.target)}}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(y.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"İmza sirk\xfclernizi y\xfcklemek i\xe7in tıklayın"})]})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Ek Belgeler (İsteğe Bağlı)",(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"(JPEG, PNG, PDF - Max 5MB)"})]}),c.additionalDocuments&&c.additionalDocuments.length>0&&(0,a.jsx)("div",{className:"space-y-2 mb-4",children:c.additionalDocuments.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.name}),(0,a.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(",(e.size/1024/1024).toFixed(2)," MB)"]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>K(t),className:"text-red-500 hover:text-red-700",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})]},t))}),(0,a.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-4",children:(0,a.jsxs)("label",{className:"cursor-pointer block",children:[(0,a.jsx)("input",{type:"file",accept:".jpg,.jpeg,.png,.pdf",className:"hidden",onChange:e=>{let t=e.target.files?.[0];t&&E(t,e.target)}}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(y.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Ek belge eklemek i\xe7in tıklayın"})]})]})})]})]}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(f.A,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-1",children:"Belge Y\xfckleme Bilgileri"}),(0,a.jsxs)("p",{className:"text-sm text-blue-700",children:["• Vergi levhası zorunludur",(0,a.jsx)("br",{}),"• Diğer belgeler isteğe bağlıdır ancak başvuru s\xfcrecini hızlandırır",(0,a.jsx)("br",{}),"• Dosya boyutu maksimum 5MB olmalıdır",(0,a.jsx)("br",{}),"• Desteklenen formatlar: JPEG, PNG, PDF"]})]})]})})]}),4===A&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(b.A,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Yetkili Kişi Bilgileri"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Yetkili Kişi Adı Soyadı *"}),(0,a.jsx)("input",{type:"text",value:c.authorizedPersonName,onChange:e=>G("authorizedPersonName",e.target.value),className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${C.authorizedPersonName?"border-red-500":"border-gray-300"}`,placeholder:"Yetkili kişinin adı soyadı"}),C.authorizedPersonName&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:C.authorizedPersonName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T.C. Kimlik Numarası *"}),(0,a.jsx)("input",{type:"text",value:c.authorizedPersonTcId,onChange:e=>G("authorizedPersonTcId",e.target.value),className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${C.authorizedPersonTcId?"border-red-500":"border-gray-300"}`,placeholder:"12345678901",maxLength:11}),C.authorizedPersonTcId&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:C.authorizedPersonTcId})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alternatif İletişim Numarası *"}),(0,a.jsx)("input",{type:"tel",value:c.alternativeContactNumber,onChange:e=>G("alternativeContactNumber",e.target.value),className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${C.alternativeContactNumber?"border-red-500":"border-gray-300"}`,placeholder:"+90 ************"}),C.alternativeContactNumber&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:C.alternativeContactNumber})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-3",children:"Hesap Bilgileri (Otomatik Dolduruldu)"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Ad Soyad:"}),(0,a.jsxs)("span",{className:"ml-2 font-medium text-gray-700",children:[e.firstName," ",e.lastName]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,a.jsx)("span",{className:"text-gray-600",children:"E-posta:"}),(0,a.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:e.email})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Telefon:"}),(0,a.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:e.phone})]})]})]})]}),5===A&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(N.A,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"S\xf6zleşme ve Politika Onayları"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:`border rounded-lg p-4 ${C.userAgreementAccepted?"border-red-500 bg-red-50":"border-gray-200"}`,children:[(0,a.jsxs)("label",{className:"flex items-start cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:c.userAgreementAccepted,onChange:e=>G("userAgreementAccepted",e.target.checked),className:"mt-1 mr-3 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Kullanıcı S\xf6zleşmesi Onayı *"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[(0,a.jsx)("a",{href:"#",className:"text-purple-600 hover:underline",children:"Kullanıcı S\xf6zleşmesi"}),"'ni okudum ve kabul ediyorum."]})]})]}),C.userAgreementAccepted&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-2",children:C.userAgreementAccepted})]}),(0,a.jsxs)("div",{className:`border rounded-lg p-4 ${C.dealershipAgreementAccepted?"border-red-500 bg-red-50":"border-gray-200"}`,children:[(0,a.jsxs)("label",{className:"flex items-start cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:c.dealershipAgreementAccepted,onChange:e=>G("dealershipAgreementAccepted",e.target.checked),className:"mt-1 mr-3 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Satıcı S\xf6zleşmesi Onayı *"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[(0,a.jsx)("a",{href:"#",className:"text-purple-600 hover:underline",children:"Satıcı S\xf6zleşmesi"}),"'ni okudum ve kabul ediyorum."]})]})]}),C.dealershipAgreementAccepted&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-2",children:C.dealershipAgreementAccepted})]}),(0,a.jsxs)("div",{className:`border rounded-lg p-4 ${C.privacyPolicyAccepted?"border-red-500 bg-red-50":"border-gray-200"}`,children:[(0,a.jsxs)("label",{className:"flex items-start cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:c.privacyPolicyAccepted,onChange:e=>G("privacyPolicyAccepted",e.target.checked),className:"mt-1 mr-3 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Gizlilik Politikası Onayı *"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[(0,a.jsx)("a",{href:"#",className:"text-purple-600 hover:underline",children:"Gizlilik Politikası"}),"'nı okudum ve kabul ediyorum."]})]})]}),C.privacyPolicyAccepted&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-2",children:C.privacyPolicyAccepted})]})]}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(f.A,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-1",children:"Onay S\xfcreci"}),(0,a.jsx)("p",{className:"text-sm text-blue-700",children:"Başvurunuz g\xf6nderildikten sonra y\xf6netim ekibimiz tarafından incelenecektir. Onay s\xfcreci genellikle 3-5 iş g\xfcn\xfc s\xfcrmektedir. Hesabınızdan başvuru durumunuzu takip edebilirsiniz."})]})]})})]}),6===A&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(j.A,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Başvuru \xd6nizlemesi"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl p-6 mb-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-green-600 mr-2"}),"Başvuru Bilgileriniz"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Aşağıdaki bilgileri kontrol edin. Gerekirse geri d\xf6n\xfcp d\xfczenleyebilirsiniz."})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-purple-600 mr-2"}),"\xdcr\xfcn Bilgileri"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"Ana \xdcr\xfcn Kategorisi:"}),(0,a.jsx)("p",{className:"text-gray-900 mt-1",children:c.mainProductCategory||"-"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"Tahmini \xdcr\xfcn Sayısı:"}),(0,a.jsx)("p",{className:"text-gray-900 mt-1",children:c.estimatedProductCount||"-"})]}),c.sampleProductListUrl&&(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"\xd6rnek \xdcr\xfcn Listesi:"}),(0,a.jsx)("p",{className:"text-blue-600 mt-1 break-all",children:(0,a.jsx)("a",{href:c.sampleProductListUrl,target:"_blank",rel:"noopener noreferrer",className:"hover:underline",children:c.sampleProductListUrl})})]})]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-purple-600 mr-2"}),"Şirket Bilgileri"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"Şirket Adı:"}),(0,a.jsx)("p",{className:"text-gray-900 mt-1",children:c.companyName||"-"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"Vergi Kimlik No:"}),(0,a.jsx)("p",{className:"text-gray-900 mt-1",children:c.taxNumber||"-"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"Vergi Dairesi:"}),(0,a.jsx)("p",{className:"text-gray-900 mt-1",children:c.taxOffice||"-"})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"Şirket Adresi:"}),(0,a.jsx)("p",{className:"text-gray-900 mt-1",children:c.companyAddress||"-"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 text-purple-600 mr-2"}),"Y\xfcklenen Belgeler"]}),(0,a.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(N.A,{className:`h-4 w-4 mr-2 ${c.taxCertificate?"text-green-600":"text-gray-300"}`}),(0,a.jsx)("span",{className:"text-gray-900",children:"Vergi Levhası"}),(0,a.jsx)("span",{className:`ml-2 text-xs px-2 py-1 rounded-full ${c.taxCertificate?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:c.taxCertificate?`${c.taxCertificate.name} (${(c.taxCertificate.size/1024/1024).toFixed(2)} MB)`:"Y\xfcklenmedi"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(N.A,{className:`h-4 w-4 mr-2 ${c.tradeRegistryGazette?"text-green-600":"text-gray-300"}`}),(0,a.jsx)("span",{className:"text-gray-900",children:"Ticaret Sicil Gazetesi"}),(0,a.jsx)("span",{className:`ml-2 text-xs px-2 py-1 rounded-full ${c.tradeRegistryGazette?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:c.tradeRegistryGazette?`${c.tradeRegistryGazette.name} (${(c.tradeRegistryGazette.size/1024/1024).toFixed(2)} MB)`:"Y\xfcklenmedi"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(N.A,{className:`h-4 w-4 mr-2 ${c.signatureCircular?"text-green-600":"text-gray-300"}`}),(0,a.jsx)("span",{className:"text-gray-900",children:"İmza Sirk\xfcleri"}),(0,a.jsx)("span",{className:`ml-2 text-xs px-2 py-1 rounded-full ${c.signatureCircular?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:c.signatureCircular?`${c.signatureCircular.name} (${(c.signatureCircular.size/1024/1024).toFixed(2)} MB)`:"Y\xfcklenmedi"})]}),c.additionalDocuments&&c.additionalDocuments.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium block mb-2",children:"Ek Belgeler:"}),c.additionalDocuments.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center ml-4 mb-1",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2 text-green-600"}),(0,a.jsx)("span",{className:"text-gray-900",children:e.name}),(0,a.jsxs)("span",{className:"ml-2 text-xs px-2 py-1 rounded-full bg-green-100 text-green-800",children:["(",(e.size/1024/1024).toFixed(2)," MB)"]})]},t))]})]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 text-purple-600 mr-2"}),"Yetkili Kişi Bilgileri"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"Yetkili Kişi:"}),(0,a.jsx)("p",{className:"text-gray-900 mt-1",children:c.authorizedPersonName||"-"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"T.C. Kimlik No:"}),(0,a.jsx)("p",{className:"text-gray-900 mt-1",children:c.authorizedPersonTcId||"-"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"E-posta:"}),(0,a.jsx)("p",{className:"text-gray-900 mt-1",children:e.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"Telefon:"}),(0,a.jsx)("p",{className:"text-gray-900 mt-1",children:e.phone})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"Alternatif Telefon:"}),(0,a.jsx)("p",{className:"text-gray-900 mt-1",children:c.alternativeContactNumber||"-"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 text-purple-600 mr-2"}),"S\xf6zleşme Onayları"]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:`h-4 w-4 mr-2 ${c.userAgreementAccepted?"text-green-600":"text-gray-300"}`}),(0,a.jsx)("span",{className:"text-gray-900",children:"Kullanıcı S\xf6zleşmesi"}),(0,a.jsx)("span",{className:`ml-2 text-xs px-2 py-1 rounded-full ${c.userAgreementAccepted?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:c.userAgreementAccepted?"Kabul Edildi":"Kabul Edilmedi"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:`h-4 w-4 mr-2 ${c.dealershipAgreementAccepted?"text-green-600":"text-gray-300"}`}),(0,a.jsx)("span",{className:"text-gray-900",children:"Satıcı S\xf6zleşmesi"}),(0,a.jsx)("span",{className:`ml-2 text-xs px-2 py-1 rounded-full ${c.dealershipAgreementAccepted?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:c.dealershipAgreementAccepted?"Kabul Edildi":"Kabul Edilmedi"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:`h-4 w-4 mr-2 ${c.privacyPolicyAccepted?"text-green-600":"text-gray-300"}`}),(0,a.jsx)("span",{className:"text-gray-900",children:"Gizlilik Politikası"}),(0,a.jsx)("span",{className:`ml-2 text-xs px-2 py-1 rounded-full ${c.privacyPolicyAccepted?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:c.privacyPolicyAccepted?"Kabul Edildi":"Kabul Edilmedi"})]})]})]}),(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(o.A,{className:"h-6 w-6 text-green-600 mr-3 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-green-900 mb-2",children:"Başvuru Hazır"}),(0,a.jsx)("p",{className:"text-sm text-green-700 mb-3",children:'Bilgileriniz kontrol edildi ve başvurunuz g\xf6nderilmeye hazır. "Başvuruyu G\xf6nder" butonuna tıklayarak başvurunuzu tamamlayabilirsiniz.'}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-green-600",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),(0,a.jsx)("span",{children:"Başvuru g\xf6nderildikten sonra 1-3 iş g\xfcn\xfc i\xe7inde değerlendirilecektir."})]})]})]})})]})]},A),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("button",{onClick:()=>w(e=>e-1),disabled:1===A,className:`flex items-center px-6 py-3 rounded-lg font-medium transition-colors ${1===A?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:"Geri"}),A<6?(0,a.jsxs)("button",{onClick:()=>{T(A)&&w(e=>e+1)},className:"flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors",children:["İleri",(0,a.jsx)(u.A,{className:"ml-2 h-4 w-4"})]}):(0,a.jsx)("button",{onClick:q,disabled:z,className:"flex items-center px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:z?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"G\xf6nderiliyor..."]}):(0,a.jsxs)(a.Fragment,{children:["Başvuruyu G\xf6nder",(0,a.jsx)(o.A,{className:"ml-2 h-4 w-4"})]})})]})]}),(0,a.jsx)(p,{isOpen:B,onClose:()=>{S(!1),s.push("/account")},onGoToAccount:()=>{S(!1),s.push("/account")}})]})}},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,181,658,85],()=>s(1965));module.exports=a})();