(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{25793:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>x});var t=r(95155),i=r(6874),a=r.n(i),l=r(76408),n=r(12115),o=r(87220),d=r(35695),c=r(66681),m=r(83717);m.F.None,m.F.Bronz,m.F.None,m.F.Gumus;let u={admin:{email:"<EMAIL>",password:"admin123"},dealership:{email:"<EMAIL>",password:"dealer123"},distributor:{email:"<EMAIL>",password:"dist123"},customer:{email:"<EMAIL>",password:"cust123"}};function x(){let[e,s]=(0,n.useState)(""),[r,i]=(0,n.useState)(""),[m,x]=(0,n.useState)(""),[h,p]=(0,n.useState)(!1),[g,b]=(0,n.useState)(!1),[j,v]=(0,n.useState)(!1),{error:y}=(0,o.A)(),f=(0,d.useRouter)(),w=(0,c._L)();(0,n.useEffect)(()=>{let e=localStorage.getItem("rememberedEmail");e&&(s(e),v(!0))},[]);let N=async s=>{s.preventDefault(),x(""),j?localStorage.setItem("rememberedEmail",e):localStorage.removeItem("rememberedEmail"),console.log("\uD83D\uDE80 Login form submit başlıyor..."),w.mutate({email:e,password:r},{onSuccess:()=>{console.log("✅ Login başarılı, y\xf6nlendirme yapılacak..."),f.push("/")},onError:e=>{x("E-posta veya şifre hatalı!")}})},k=async e=>{x("");let r=u[e];s(r.email),i(r.password),w.mutate({email:r.email,password:r.password},{onSuccess:()=>{f.push("/")},onError:e=>{x("Test girişi başarısız!")}})};return(0,t.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,t.jsx)("div",{className:"max-w-md mx-auto",children:(0,t.jsx)(l.P.div,{className:"bg-white rounded-2xl shadow-lg overflow-hidden",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,t.jsxs)("div",{className:"p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-2 text-gray-800",children:"Giriş Yap"}),(0,t.jsx)("p",{className:"text-gray-700",children:"Hesabınıza giriş yaparak alışverişe devam edin"})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("button",{onClick:()=>p(!h),className:"w-full text-center text-sm text-purple-600 hover:text-purple-800 transition",children:["\uD83E\uDDEA Test Hesapları ",h?"⬆️":"⬇️"]}),h&&(0,t.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},className:"mt-3 p-3 bg-gray-50 rounded-lg space-y-2",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-2",children:[(0,t.jsx)("button",{onClick:()=>k("admin"),className:"text-xs p-2 bg-red-100 text-red-700 rounded hover:bg-red-200 transition",disabled:w.isPending,children:"\uD83D\uDC51 Admin Girişi"}),(0,t.jsx)("button",{onClick:()=>k("dealership"),className:"text-xs p-2 bg-green-100 text-green-700 rounded hover:bg-green-200 transition",disabled:w.isPending,children:"\uD83C\uDFEA Satıcı Girişi"}),(0,t.jsx)("button",{onClick:()=>k("distributor"),className:"text-xs p-2 bg-purple-100 text-purple-700 rounded hover:bg-purple-200 transition",disabled:w.isPending,children:"\uD83D\uDCBC Distrib\xfct\xf6r Girişi"}),(0,t.jsx)("button",{onClick:()=>k("customer"),className:"text-xs p-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition",disabled:w.isPending,children:"\uD83D\uDC64 M\xfcşteri Girişi"})]})})]}),m&&(0,t.jsx)(l.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg",children:m}),(0,t.jsx)("form",{onSubmit:N,children:(0,t.jsxs)("div",{className:"space-y-5",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"E-posta Adresi"}),(0,t.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>s(e.target.value),required:!0,disabled:w.isPending,autoComplete:"email",className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100",placeholder:"<EMAIL>"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Şifre"}),(0,t.jsx)(a(),{href:"/forgot-password",className:"text-sm text-purple-600 hover:text-purple-800 transition",children:"Şifremi Unuttum"})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{id:"password",type:g?"text":"password",value:r,onChange:e=>i(e.target.value),required:!0,disabled:w.isPending,autoComplete:"current-password",className:"w-full px-4 py-3 pr-12 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100",placeholder:"********"}),(0,t.jsx)("button",{type:"button",onClick:()=>b(!g),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none disabled:cursor-not-allowed",disabled:w.isPending,children:g?(0,t.jsxs)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.543 7-1.275 4.057-5.065 7-9.543 7-4.477 0-8.268-2.943-9.542-7z"}),(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 3l18 18"})]}):(0,t.jsxs)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.543 7-1.275 4.057-5.065 7-9.543 7-4.477 0-8.268-2.943-9.542-7z"})]})})]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{id:"remember_me",type:"checkbox",checked:j,onChange:e=>v(e.target.checked),className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded",disabled:w.isPending}),(0,t.jsx)("label",{htmlFor:"remember_me",className:"ml-2 block text-sm text-gray-700",children:"Beni Hatırla"})]}),(0,t.jsx)(l.P.button,{whileHover:{scale:w.isPending?1:1.02},whileTap:{scale:w.isPending?1:.98},type:"submit",disabled:w.isPending,className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center",children:w.isPending?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Giriş Yapılıyor..."]}):"Giriş Yap"})]})}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)("p",{className:"text-gray-700",children:["Hen\xfcz hesabınız yok mu?"," ",(0,t.jsx)(a(),{href:"/register",className:"text-purple-600 font-medium hover:text-purple-800 transition",children:"Kayıt Ol"})]})}),(0,t.jsx)("div",{className:"mt-8",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("div",{className:"w-full border-t border-gray-200"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,t.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"veya"})})]})}),(0,t.jsxs)("div",{className:"mt-8 grid grid-cols-2 gap-3",children:[(0,t.jsxs)(l.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition",disabled:w.isPending,children:[(0,t.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[(0,t.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,t.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,t.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,t.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),(0,t.jsx)("span",{className:"ml-2",children:"Google"})]}),(0,t.jsxs)(l.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition",disabled:w.isPending,children:[(0,t.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),(0,t.jsx)("span",{className:"ml-2",children:"Facebook"})]})]})]})})})})}},35489:(e,s,r)=>{Promise.resolve().then(r.bind(r,25793))},83717:(e,s,r)=>{"use strict";r.d(s,{F:()=>i,Sz:()=>l,vn:()=>t,w7:()=>a});var t=function(e){return e[e.Default=0]="Default",e[e.PriceAsc=1]="PriceAsc",e[e.PriceDesc=2]="PriceDesc",e[e.RatingDesc=3]="RatingDesc",e}({}),i=function(e){return e[e.None=0]="None",e[e.Baslangic=1]="Baslangic",e[e.Girisimci=2]="Girisimci",e[e.Bronz=3]="Bronz",e[e.Gumus=4]="Gumus",e[e.Altin=5]="Altin",e[e.Platin=6]="Platin",e[e.PlatinMax=7]="PlatinMax",e}({}),a=function(e){return e[e.Unspecified=0]="Unspecified",e[e.Male=1]="Male",e[e.Female=2]="Female",e[e.Other=3]="Other",e}({}),l=function(e){return e[e.Pending=0]="Pending",e[e.Accepted=1]="Accepted",e[e.Rejected=2]="Rejected",e}({})}},e=>{var s=s=>e(e.s=s);e.O(0,[6408,6874,7323,6681,8441,1684,7358],()=>s(35489)),_N_E=e.O()}]);