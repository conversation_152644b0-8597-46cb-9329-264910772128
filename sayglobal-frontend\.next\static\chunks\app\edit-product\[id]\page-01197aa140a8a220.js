(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2197],{1243:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},33991:(e,a,t)=>{Promise.resolve().then(t.bind(t,47843))},37348:(e,a,t)=>{"use strict";t.d(a,{K_:()=>o});var r=t(65453),i=t(46786);let s={formData:{name:"",description:"",brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{},price:0,stock:0,extraDiscount:0,ratios:{pvRatio:0,cvRatio:0,spRatio:0},points:{pv:0,cv:0,sp:0},hasVariants:!1,variants:[],images:[],isActive:!0},selectedNames:{brandName:"",categoryName:"",subCategoryName:""},selectedFeatureDetails:[],variants:[],availableFeatures:[],error:null,originalProductId:null},n=(e,a)=>({pv:Math.round(e*(a.pvRatio/100)),cv:Math.round(e*(a.cvRatio/100)),sp:Math.round(e*(a.spRatio/100))}),l=e=>{let a={},t=[];return e.forEach(e=>{e.featureDetails.forEach(e=>{a[e.featureDefinitionId]||(a[e.featureDefinitionId]=[]),a[e.featureDefinitionId].includes(e.featureValueId)||a[e.featureDefinitionId].push(e.featureValueId),t.some(a=>a.featureName===e.featureName&&a.featureValue===e.featureValue)||t.push({featureName:e.featureName,featureValue:e.featureValue})})}),{newSelectedFeatures:a,newSelectedFeatureDetails:t}},o=(0,r.v)()((0,i.lt)((e,a)=>({...s,setFormData:a=>e(e=>({formData:{...e.formData,...a}})),handleInputChange:(t,r)=>{let i={...a().formData,[t]:r};if(("price"===t||"ratios"===t)&&!i.hasVariants){let e=n(i.price,i.ratios);i={...i,points:e}}e({formData:i})},handleRatioChange:(t,r)=>{let i=a().formData,s={...i.ratios,[t]:r},l={...i,ratios:s};if(!l.hasVariants){let e=n(l.price,s);l={...l,points:e}}e({formData:l})},setCategorySelection:t=>{e({formData:{...a().formData,brandId:t.brandId,categoryId:t.categoryId,subCategoryId:t.subCategoryId,selectedFeatures:t.selectedFeatures,hasVariants:!1},selectedNames:{brandName:t.brandName,categoryName:t.categoryName,subCategoryName:t.subCategoryName},selectedFeatureDetails:t.selectedFeatureDetails,error:null})},clearAllSelections:()=>{e({formData:{...a().formData,brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{},hasVariants:!1},selectedNames:{brandName:"",categoryName:"",subCategoryName:""},selectedFeatureDetails:[],availableFeatures:[],variants:[],error:null})},setAvailableFeatures:a=>e({availableFeatures:a}),setVariants:a=>e({variants:a}),saveVariant:(t,r)=>{let i,{variants:s}=a(),{newSelectedFeatures:n,newSelectedFeatureDetails:o}=l(i=r?s.map(e=>e.id===r?t:e):[...s,{...t,id:Date.now()}]);e(e=>({variants:i,formData:{...e.formData,selectedFeatures:n,hasVariants:i.length>1},selectedFeatureDetails:o}))},deleteVariant:t=>{let{variants:r}=a(),i=r.filter(e=>e.id!==t),{newSelectedFeatures:s,newSelectedFeatureDetails:n}=l(i);e(e=>({variants:i,formData:{...e.formData,selectedFeatures:s,hasVariants:i.length>1},selectedFeatureDetails:n}))},generateVariants:a=>{let{newSelectedFeatures:t,newSelectedFeatureDetails:r}=l(a);e(e=>({formData:{...e.formData,hasVariants:!0,selectedFeatures:t},selectedFeatureDetails:r,variants:a,error:null}))},handleImageUpload:t=>{let{formData:r}=a(),i=Array.from(t).map((e,a)=>({url:URL.createObjectURL(e),isMain:0===r.images.length&&0===a,sortOrder:r.images.length+a,file:e}));e({formData:{...r,images:[...r.images,...i]}})},removeImage:t=>{let{formData:r}=a(),i=r.images.filter((e,a)=>a!==t);r.images[t].isMain&&i.length>0&&(i[0].isMain=!0),e({formData:{...r,images:i}})},setMainImage:t=>{let{formData:r}=a(),i=r.images.map((e,a)=>({...e,isMain:a===t}));e({formData:{...r,images:i}})},setError:a=>e({error:a}),reset:()=>e({...s}),initializeWithProduct:t=>{var r,i,s,l,o,d,c;console.log("\uD83D\uDD04 Initializing product data:",t),console.log("\uD83D\uDDBC️ Variant images from API:",null==(r=t.variants)?void 0:r.map(e=>{var a;return{variantId:e.id,hasImages:!!e.images,imagesCount:(null==(a=e.images)?void 0:a.length)||0,images:e.images}}));let u=(null==(i=t.variants)?void 0:i.map((e,a)=>{var t,r;let i=(null==(t=e.features)?void 0:t.map(e=>({featureDefinitionId:e.featureDefinitionId,featureValueId:e.featureValueId,featureName:e.featureName||"",featureValue:e.featureValue||""})))||[],s={};i.forEach(e=>{s[e.featureDefinitionId]||(s[e.featureDefinitionId]=[]),s[e.featureDefinitionId].includes(e.featureValueId)||s[e.featureDefinitionId].push(e.featureValueId)});let l=i.length>0?i.map(e=>e.featureValue).join(" - "):"Varyant ".concat(a+1),o=e.price||0,d={pvRatio:e.pv||0,cvRatio:e.cv||0,spRatio:e.sp||0};return{id:e.id||Date.now()+a,name:l,pricing:{price:o,stock:e.stock||0,extraDiscount:e.extraDiscount||0,ratios:d,points:n(o,d)},selectedFeatures:s,features:i,featureDetails:i,images:(null==(r=e.images)?void 0:r.map(e=>({id:e.id,url:e.url,isMain:e.isMain,sortOrder:e.sortOrder,file:null})))||[],isActive:void 0===e.isActive||e.isActive}}))||[],m=[],g={};u.forEach(e=>{var a;null==(a=e.featureDetails)||a.forEach(e=>{g[e.featureDefinitionId]||(g[e.featureDefinitionId]=[]),g[e.featureDefinitionId].includes(e.featureValueId)||g[e.featureDefinitionId].push(e.featureValueId),m.some(a=>a.featureName===e.featureName&&a.featureValue===e.featureValue)||m.push({featureName:e.featureName,featureValue:e.featureValue})})}),console.log("\uD83D\uDD04 Converted variants:",u),console.log("\uD83D\uDD04 All feature details:",m),console.log("\uD83D\uDD04 Global selected features:",g),e({formData:{name:t.name||"",description:t.description||"",brandId:t.brandId||1,categoryId:t.categoryId||1,subCategoryId:t.subCategoryId||1,selectedFeatures:g,price:(null==(s=u[0])?void 0:s.pricing.price)||0,stock:(null==(l=u[0])?void 0:l.pricing.stock)||0,extraDiscount:(null==(o=u[0])?void 0:o.pricing.extraDiscount)||0,ratios:(null==(d=u[0])?void 0:d.pricing.ratios)||{pvRatio:0,cvRatio:0,spRatio:0},points:(null==(c=u[0])?void 0:c.pricing.points)||{pv:0,cv:0,sp:0},hasVariants:u.length>0,variants:[],images:[],isActive:void 0===t.isActive||t.isActive},selectedNames:{brandName:t.brandName||"",categoryName:t.categoryName||"",subCategoryName:t.subCategoryName||""},selectedFeatureDetails:m,variants:u,error:null}),console.log("✅ Product initialized successfully"),console.log("\uD83D\uDCCA Form data:",a().formData),console.log("\uD83C\uDFF7️ Selected names:",a().selectedNames),console.log("\uD83C\uDFAF Variants:",a().variants),console.log("\uD83D\uDD0D Has variants:",a().formData.hasVariants),console.log("\uD83D\uDD0D Variants length:",a().variants.length)},setOriginalProductId:a=>e({originalProductId:a})}),{name:"edit-product-store",enabled:!1}))},40646:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47843:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>w});var r=t(95155),i=t(12115),s=t(87220),n=t(35695),l=t(76408),o=t(1243),d=t(40646),c=t(35169),u=t(75525),m=t(81284),g=t(43332),x=t(62525),p=t(71539),f=t(27213),h=t(4229),b=t(6874),y=t.n(b),v=t(80722),N=t(22934),j=t(81087),I=t(37348),D=t(45106),k=t(26715),V=t(56407);let w=()=>{let{user:e,isLoading:a}=(0,s.A)(),t=(0,n.useRouter)(),b=(0,n.useParams)();(0,k.jE)();let[w,F]=(0,i.useState)(!1),[C,S]=(0,i.useState)(null),[A,P]=(0,i.useState)(!1),{formData:E,selectedNames:z,variants:M,error:R,availableFeatures:O,originalProductId:K}=(0,I.K_)(),{handleInputChange:_,setCategorySelection:T,clearAllSelections:Y,deleteVariant:q,setError:B,reset:G,setVariants:L,saveVariant:H,initializeWithProduct:U,setOriginalProductId:W}=(0,I.K_)(e=>e),{openProductCategorySelector:Q,closeProductCategorySelector:X,openProductVariant:J,closeProductVariant:Z}=(0,D.QR)(),$=(0,D.fW)(),ee=(0,D._f)(),ea=(0,D.HX)(),et=(0,D.qA)(),er=e=>{let a={};return e.forEach(e=>{e.featureDetails.forEach(e=>{a[e.featureDefinitionId]||(a[e.featureDefinitionId]=[]),a[e.featureDefinitionId].includes(e.featureValueId)||a[e.featureDefinitionId].push(e.featureValueId)})}),a},{mutateAsync:ei,isPending:es}=(0,V.mn)();(0,i.useEffect)(()=>{(async()=>{let e=(await b).id;S(e),W(parseInt(e))})()},[b,W]),(0,i.useEffect)(()=>{(async()=>{if(C)try{let e=await v.jU.getDealershipProductDetail(parseInt(C));e.success&&e.data?U(e.data):B("\xdcr\xfcn bulunamadı veya erişim yetkiniz bulunmuyor.")}catch(e){console.error("\xdcr\xfcn y\xfckleme hatası:",e),B("\xdcr\xfcn y\xfcklenirken bir hata oluştu.")}})()},[C,U,B]),(0,i.useEffect)(()=>()=>{G()},[G]),(0,i.useEffect)(()=>{e&&"dealership"!==e.role&&"admin"!==e.role&&t.push("/"),a||e||t.push("/login")},[e,a,t]);let en=e=>{J({editingVariant:e,availableFeatures:O,existingVariants:M})},el=async e=>{e.preventDefault(),B(null),console.log("\uD83D\uDE80 Edit Product Submit başladı"),console.log("\uD83D\uDCCB Form Data:",{name:E.name,description:E.description,brandId:E.brandId,subCategoryId:E.subCategoryId,originalProductId:K}),console.log("\uD83D\uDD27 Variants:",M);try{if(!E.name.trim())throw Error("\xdcr\xfcn adı gereklidir");if(!E.description.trim())throw Error("\xdcr\xfcn a\xe7ıklaması gereklidir");if(E.brandId<=0)throw Error("Marka ve kategori se\xe7imi gereklidir");if(0===M.length)throw Error("En az bir varyant oluşturmalısınız.");for(let e of M){if(e.pricing.price<=0)throw Error("".concat(e.name," varyantı i\xe7in fiyat 0'dan b\xfcy\xfck olmalıdır"));if(e.pricing.stock<0)throw Error("".concat(e.name," varyantı i\xe7in stok miktarı negatif olamaz"))}console.log("✅ Validation ge\xe7ti, FormData oluşturuluyor...");let e=new FormData;for(let[a,t]of(e.append("Product.Id",K.toString()),e.append("Product.Name",E.name),e.append("Product.Description",E.description),e.append("Product.BrandId",E.brandId.toString()),e.append("Product.SubCategoryId",E.subCategoryId.toString()),console.log("\uD83D\uDCE6 Product bilgileri FormData'ya eklendi:",{"Product.Id":K.toString(),"Product.Name":E.name,"Product.Description":E.description,"Product.BrandId":E.brandId.toString(),"Product.SubCategoryId":E.subCategoryId.toString()}),M.forEach((a,t)=>{if(console.log("\uD83D\uDD27 Variant ".concat(t," işleniyor:"),{id:a.id,name:a.name,pricing:a.pricing,selectedFeatures:a.selectedFeatures,imagesCount:a.images.length}),!a.id)throw console.error("❌ Variant ID eksik:",a),Error("Variant ".concat(t+1," i\xe7in ID eksik"));e.append("Variant[".concat(t,"].Id"),a.id.toString()),e.append("Variant[".concat(t,"].Stock"),a.pricing.stock.toString()),e.append("Variant[".concat(t,"].Price"),a.pricing.price.toString()),console.log("✅ Variant[".concat(t,"] pricing:"),{Id:a.id.toString(),Stock:a.pricing.stock.toString(),Price:a.pricing.price.toString()});let r=[],i=a.featureDetails.filter(e=>e.featureValueId&&e.featureValueId>0).map(e=>e.featureValueId),s=Object.values(a.selectedFeatures).flat(),n=[...i,...s];r=[...new Set(n)],console.log("\uD83D\uDD0D Variant ".concat(t," - featureDetails IDs:"),i),console.log("\uD83D\uDD0D Variant ".concat(t," - selectedFeatures IDs:"),s),console.log("✅ Variant ".concat(t," - birleştirilmiş featureValueIds:"),r),console.log("\uD83D\uDD0D Variant ".concat(t," final featureValueIds:"),r),console.log("\uD83D\uDD0D Variant ".concat(t," featureDetails:"),a.featureDetails),console.log("\uD83D\uDD0D Variant ".concat(t," selectedFeatures:"),a.selectedFeatures),r.forEach((a,r)=>{e.append("Variant[".concat(t,"].FeatureValueIds[").concat(r,"]"),a.toString()),console.log("✅ Variant[".concat(t,"].FeatureValueIds[").concat(r,"] = ").concat(a))});let l=0;a.images.forEach(a=>{a.file?(e.append("Variant[".concat(t,"].Images[").concat(l,"].File"),a.file),e.append("Variant[".concat(t,"].Images[").concat(l,"].IsMain"),a.isMain.toString()),e.append("Variant[".concat(t,"].Images[").concat(l,"].SortOrder"),l.toString()),console.log("\uD83D\uDCE4 Yeni fotoğraf g\xf6nderiliyor:",a.file.name,"Index:",l),l++):a.id&&a.url&&!a.url.startsWith("blob:")&&(e.append("Variant[".concat(t,"].Images[").concat(l,"].Id"),a.id.toString()),e.append("Variant[".concat(t,"].Images[").concat(l,"].IsMain"),a.isMain.toString()),e.append("Variant[".concat(t,"].Images[").concat(l,"].SortOrder"),l.toString()),console.log("\uD83D\uDD04 Mevcut fotoğraf korunuyor:",a.url,"ID:",a.id,"Index:",l),l++)})}),console.log("\uD83D\uDCCB Final FormData contents:"),e.entries()))t instanceof File?console.log("".concat(a,": File(").concat(t.name,", ").concat(t.size," bytes)")):console.log("".concat(a,": ").concat(t));console.log("\uD83D\uDE80 API \xe7ağrısı başlatılıyor..."),await ei(e),console.log("✅ \xdcr\xfcn başarıyla g\xfcncellendi!"),F(!0),setTimeout(()=>{t.push("/pending-products"),G()},3e3)}catch(e){var a,r;console.error("❌ \xdcr\xfcn g\xfcncelleme hatası:",e),console.error("❌ Error details:",{message:e.message,response:null==(a=e.response)?void 0:a.data,status:null==(r=e.response)?void 0:r.status}),B(e.message||"\xdcr\xfcn g\xfcncellenirken bir hata oluştu")}};return a?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):e&&("dealership"===e.role||"admin"===e.role)?K&&(!K||E.name)?R&&R.includes("bulunamadı")?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-8 text-center max-w-md mx-auto",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3},children:[(0,r.jsx)(o.A,{className:"h-16 w-16 text-red-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"\xdcr\xfcn Bulunamadı"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"D\xfczenlemek istediğiniz \xfcr\xfcn bulunamadı veya erişim yetkiniz bulunmuyor."}),(0,r.jsx)(y(),{href:"/pending-products",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:"\xdcr\xfcn Listesine D\xf6n"})]})}):w?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-8 text-center max-w-md mx-auto",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3},children:[(0,r.jsx)(d.A,{className:"h-16 w-16 text-green-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"\xdcr\xfcn Başarıyla G\xfcncellendi!"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"\xdcr\xfcn\xfcn\xfcz başarıyla g\xfcncellendi ve tekrar admin onayına g\xf6nderildi."}),(0,r.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-green-600 mx-auto"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Y\xf6nlendiriliyorsunuz..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:[(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(y(),{href:"/pending-products",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 mr-2"}),"\xdcr\xfcn Listesi"]}),(0,r.jsx)("span",{className:"text-gray-300",children:"/"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"\xdcr\xfcn D\xfczenle"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 bg-blue-100 px-4 py-2 rounded-lg",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-blue-600"}),(0,r.jsx)("span",{className:"text-blue-800 font-medium",children:"Satıcı Erişimi"})]})]})}),R&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsx)("p",{className:"text-red-800",children:R}),(0,r.jsx)("button",{onClick:()=>B(null),className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Kapat"})]}),(0,r.jsxs)("form",{onSubmit:el,className:"space-y-8",children:[(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(m.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Temel Bilgiler"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xdcr\xfcn Adı *"}),(0,r.jsx)("input",{type:"text",value:E.name,onChange:e=>_("name",e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black",placeholder:"\xdcr\xfcn adını girin...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"A\xe7ıklama *"}),(0,r.jsx)("textarea",{value:E.description,onChange:e=>_("description",e.target.value),rows:4,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black",placeholder:"\xdcr\xfcn a\xe7ıklamasını girin...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Marka, Kategori ve Detaylar"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{type:"button",onClick:async()=>{if(E&&E.subCategoryId>0)try{P(!0);let e=M.map(e=>{let a=e.features||[];console.log("\uD83D\uDD0D Variant features:",a);let t=[];return a.forEach(e=>{console.log("\uD83D\uDD0D Processing feature:",e),void 0!==e.featureDefinitionId&&void 0!==e.featureValueId?(console.log("✅ Feature added to details"),t.push({featureDefinitionId:e.featureDefinitionId,featureValueId:e.featureValueId,featureName:e.featureName,featureValue:e.featureValue})):console.log("❌ Feature skipped - missing IDs:",{featureDefinitionId:e.featureDefinitionId,featureValueId:e.featureValueId})}),console.log("\uD83D\uDD0D Final featureDetails for variant:",t),{...e,featureDetails:t}}),a=er(e);console.log("\uD83C\uDFAF Derived selectedFeatures:",a),L(e),Q({initialData:{brandId:E.brandId,categoryId:E.categoryId,subCategoryId:E.subCategoryId,selectedFeatures:a}})}catch(e){console.error("\xd6zellik se\xe7imleri y\xfcklenirken hata:",e),Q({initialData:{brandId:E.brandId,categoryId:E.categoryId,subCategoryId:E.subCategoryId,selectedFeatures:{}}})}finally{P(!1)}else Q({initialData:{brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{}}})},disabled:A,className:"flex-grow px-4 py-3 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 transition-colors flex items-center justify-center text-gray-600 hover:text-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:A?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"}),"Y\xfckleniyor..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"h-5 w-5 mr-2"}),E.brandId>0?"Kategori Se\xe7imini D\xfczenle":"Marka, Kategori ve Detaylar Se\xe7in"]})}),E.brandId>0&&(0,r.jsx)("button",{type:"button",onClick:Y,title:"T\xfcm se\xe7imleri temizle",className:"flex-shrink-0 p-3 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors",children:(0,r.jsx)(x.A,{className:"h-5 w-5"})})]}),E.brandId>0&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-gray-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"Se\xe7ili:"})," Marka: ",z.brandName,E.categoryId>0&&", Kategori: ".concat(z.categoryName),E.subCategoryId>0&&", Alt Kategori: ".concat(z.subCategoryName)]})})]})]})]}),(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Varyant Y\xf6netimi"})]})}),E.subCategoryId<=0&&(0,r.jsx)("div",{className:"p-4 text-center bg-gray-50 rounded-lg",children:(0,r.jsx)("p",{className:"text-gray-500 font-medium",children:"Tekli \xfcr\xfcn eklemek veya varyant oluşturmak i\xe7in l\xfctfen \xf6nce marka, kategori ve alt kategori se\xe7in."})}),E.subCategoryId>0&&0===M.length&&(0,r.jsxs)("div",{className:"p-8 text-center bg-gray-50 rounded-lg",children:[(0,r.jsx)(p.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-3"}),(0,r.jsx)("p",{className:"text-gray-500 font-medium",children:"Hen\xfcz varyant oluşturulmadı."}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1 mb-4",children:'Yukarıdaki "Marka, Kategori ve Detaylar Se\xe7in" kısmı ile d\xfczenleyebilirsiniz.'})]}),M.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 text-blue-600 mr-2 mt-0.5"}),(0,r.jsxs)("p",{className:"text-sm text-blue-800",children:["Oluşturulan her bir varyant kombinasyonu i\xe7in Fiyat ve Stok bilgilerini girin. Tek bir \xfcr\xfcn ekliyorsanız bile, bu \xfcr\xfcn bir varyant olarak kabul edilir.",(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Not:"})," PV, CV, SP ve indirim oranları admin tarafından belirlenecektir."]})]})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Varyant"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fiyat (₺)"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stok"}),(0,r.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,r.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:M.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.featureDetails.map(e=>e.featureValue).join(", ")})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.price})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.stock})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,r.jsx)("button",{type:"button",onClick:()=>en(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:"Detay"}),(0,r.jsx)("button",{type:"button",onClick:()=>q("number"==typeof e.id?e.id:0),className:"text-gray-500 hover:text-gray-800",children:"Sil"})]})]},e.id))})]})})]})]}),(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(f.A,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"\xdcr\xfcn Fotoğrafları"})]}),(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(m.A,{className:"h-5 w-5 text-blue-400"})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-blue-700",children:'Her bir varyantın fotoğraflarını, varyant tablosundaki "Detay" butonuna tıklayarak a\xe7ılan pencereden y\xf6netebilirsiniz.'})})]})}),M.some(e=>e.images.length>0)&&(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"T\xfcm Varyant G\xf6rselleri (\xd6nizleme)"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:M.flatMap(e=>e.images.map(a=>({...a,variantName:e.name}))).map((e,a)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg overflow-hidden",children:(0,r.jsx)("img",{src:e.url,alt:"".concat(e.variantName," g\xf6rseli"),className:"w-full h-full object-cover"})}),(0,r.jsx)("div",{className:"absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded",children:e.variantName}),e.isMain&&(0,r.jsx)("div",{className:"absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded",children:"Ana"})]},a))})]})]}),(0,r.jsxs)(l.P.div,{className:"flex justify-end space-x-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,r.jsx)(y(),{href:"/pending-products",className:"px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"İptal"}),(0,r.jsx)("button",{type:"submit",disabled:es,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:es?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"G\xfcncelleniyor..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"\xdcr\xfcn\xfc G\xfcncelle"]})})]})]})]}),(0,r.jsx)(N.A,{isOpen:$,onClose:X,onSelect:e=>{T({brandId:e.brandId,categoryId:e.categoryId,subCategoryId:e.subCategoryId,brandName:e.brandName,categoryName:e.categoryName,subCategoryName:e.subCategoryName,selectedFeatures:e.selectedFeatures,selectedFeatureDetails:e.selectedFeatureDetails}),L(e.generatedVariants),X(),B(null)},initialData:(null==ee?void 0:ee.initialData)&&"object"==typeof ee.initialData&&"selectedFeatures"in ee.initialData?ee.initialData:{brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{}},colorScheme:"blue"}),(0,r.jsx)(j.A,{isOpen:ea,onClose:Z,onSave:e=>{H(e,"number"==typeof e.id?e.id:void 0),Z()},editingVariant:null==et?void 0:et.editingVariant,availableFeatures:O,existingVariants:M,hidePvCvSp:!0,colorScheme:"blue"})]}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"\xdcr\xfcn bilgileri y\xfckleniyor..."})]})}):null}}},e=>{var a=a=>e(e.s=a);e.O(0,[6408,6874,7323,1531,6681,3651,6407,8441,1684,7358],()=>a(33991)),_N_E=e.O()}]);