[{"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\account\\edit\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\account\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\add-product\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\category-management\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\dealership-applications\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\orders\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\pending-products\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\products\\add\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\products\\edit\\[id]\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\products\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\reports\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\users\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\announcements\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\become-dealer\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\cart\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\checkout\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\earnings\\page.tsx": "18", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\edit-product\\[id]\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\forgot-password\\page.tsx": "20", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx": "21", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\login\\page.tsx": "22", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\my-team\\page.tsx": "23", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\order-details\\[orderId]\\page.tsx": "24", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\page.tsx": "25", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\panel\\page.tsx": "26", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\pending-products\\page.tsx": "27", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\product\\[id]\\page.tsx": "28", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\products\\page.tsx": "29", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\register\\page.tsx": "30", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\reset-password\\page.tsx": "31", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\team-tree\\page.tsx": "32", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AddAddressModal.tsx": "33", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AddCardModal.tsx": "34", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AddressList.tsx": "35", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AddToCartModal.tsx": "36", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AdminDealerNotificationModal.tsx": "37", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AdminProductDetailModal.tsx": "38", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AdminProductDetailModalNew.tsx": "39", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AnnouncementModal.tsx": "40", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\auth\\AuthContext.tsx": "41", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\BankingModal.tsx": "42", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\CategoryNavigation.tsx": "43", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\CustomCheckbox.tsx": "44", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\DealerApplicationSuccessModal.tsx": "45", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\DealershipApplicationDetailModal.tsx": "46", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\DealershipProductDetailModal.tsx": "47", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\EditPersonalInfoModal.tsx": "48", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\FavoriteModal.tsx": "49", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ImageModal.tsx": "50", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\NetworkStatusHandler.tsx": "51", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\PendingPlacementModal.tsx": "52", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\PlacementSuccessModal.tsx": "53", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProductApprovalModal.tsx": "54", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProductCard.tsx": "55", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProductCategorySelector.tsx": "56", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProductDeleteConfirmationModal.tsx": "57", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProductDetailModal.tsx": "58", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProductVariantModal.tsx": "59", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProductVariantSetupModal.tsx": "60", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProfilePictureMenu.tsx": "61", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ReferenceRegistrationModal.tsx": "62", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\RegisterSuccessModal.tsx": "63", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\SetDefaultCardModal.tsx": "64", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\SetDefaultConfirmationModal.tsx": "65", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\SuccessModal.tsx": "66", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\SuccessNotificationModal.tsx": "67", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\TeamMemberDetailModal.tsx": "68", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ui\\Footer.tsx": "69", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ui\\Header.tsx": "70", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\constants\\apiEndpoints.ts": "71", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\contexts\\CartContext.tsx": "72", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\contexts\\FavoritesContext.tsx": "73", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\account.ts": "74", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\admin.ts": "75", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\auth.ts": "76", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\categories.ts": "77", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\dealership.ts": "78", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\distributor.ts": "79", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\products.ts": "80", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\users.ts": "81", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useAddresses.ts": "82", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useAuth.ts": "83", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useCart.ts": "84", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useCatalogProducts.ts": "85", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useCategoryManagement.ts": "86", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useDebounce.ts": "87", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useDiscountRate.ts": "88", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useProducts.ts": "89", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useProductsPage.ts": "90", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useUsers.ts": "91", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\services\\api.ts": "92", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\services\\authService.ts": "93", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\services\\imageService.ts": "94", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\addProductStore.ts": "95", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\authStore.ts": "96", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\catalogProductDetailStore.ts": "97", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\categoryManagementStore.ts": "98", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\customerPriceStore.ts": "99", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\editProductStore.ts": "100", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\modalStore.ts": "101", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\myProductFilterStore.ts": "102", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\networkStore.ts": "103", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\productDetailStore.ts": "104", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\productFilterStore.ts": "105", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\productsPageStore.ts": "106", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\useAddressStore.ts": "107", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\userFilterStore.ts": "108", "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\types\\index.ts": "109"}, {"size": 32875, "mtime": 1753562954530, "results": "110", "hashOfConfig": "111"}, {"size": 121311, "mtime": 1753563421650, "results": "112", "hashOfConfig": "111"}, {"size": 27424, "mtime": 1752692999901, "results": "113", "hashOfConfig": "111"}, {"size": 73422, "mtime": 1752248934274, "results": "114", "hashOfConfig": "111"}, {"size": 21019, "mtime": 1750866165381, "results": "115", "hashOfConfig": "111"}, {"size": 19793, "mtime": 1748616673988, "results": "116", "hashOfConfig": "111"}, {"size": 22004, "mtime": 1752075938515, "results": "117", "hashOfConfig": "111"}, {"size": 28269, "mtime": 1753289389816, "results": "118", "hashOfConfig": "111"}, {"size": 29978, "mtime": 1752521792204, "results": "119", "hashOfConfig": "111"}, {"size": 41679, "mtime": 1753563469260, "results": "120", "hashOfConfig": "111"}, {"size": 28026, "mtime": 1753289249053, "results": "121", "hashOfConfig": "111"}, {"size": 19830, "mtime": 1748616801064, "results": "122", "hashOfConfig": "111"}, {"size": 24795, "mtime": 1752607924585, "results": "123", "hashOfConfig": "111"}, {"size": 13312, "mtime": 1748606170554, "results": "124", "hashOfConfig": "111"}, {"size": 70173, "mtime": 1751054976414, "results": "125", "hashOfConfig": "111"}, {"size": 30383, "mtime": 1753547708217, "results": "126", "hashOfConfig": "111"}, {"size": 44856, "mtime": 1748789399554, "results": "127", "hashOfConfig": "111"}, {"size": 21355, "mtime": 1749485503537, "results": "128", "hashOfConfig": "111"}, {"size": 43268, "mtime": 1752785286653, "results": "129", "hashOfConfig": "111"}, {"size": 8279, "mtime": 1748530896156, "results": "130", "hashOfConfig": "111"}, {"size": 2815, "mtime": 1751984761852, "results": "131", "hashOfConfig": "111"}, {"size": 21211, "mtime": 1751639888114, "results": "132", "hashOfConfig": "111"}, {"size": 26989, "mtime": 1750695160694, "results": "133", "hashOfConfig": "111"}, {"size": 19366, "mtime": 1748868331425, "results": "134", "hashOfConfig": "111"}, {"size": 43091, "mtime": 1751640965556, "results": "135", "hashOfConfig": "111"}, {"size": 38072, "mtime": 1752599318897, "results": "136", "hashOfConfig": "111"}, {"size": 22099, "mtime": 1752756425686, "results": "137", "hashOfConfig": "111"}, {"size": 47500, "mtime": 1753549964212, "results": "138", "hashOfConfig": "111"}, {"size": 25258, "mtime": 1753563270535, "results": "139", "hashOfConfig": "111"}, {"size": 19221, "mtime": 1751736272322, "results": "140", "hashOfConfig": "111"}, {"size": 15548, "mtime": 1753563335026, "results": "141", "hashOfConfig": "111"}, {"size": 42813, "mtime": 1750523082015, "results": "142", "hashOfConfig": "111"}, {"size": 17029, "mtime": 1751554226041, "results": "143", "hashOfConfig": "111"}, {"size": 19716, "mtime": 1751487221383, "results": "144", "hashOfConfig": "111"}, {"size": 15154, "mtime": 1751489095682, "results": "145", "hashOfConfig": "111"}, {"size": 16409, "mtime": 1753551299179, "results": "146", "hashOfConfig": "111"}, {"size": 7292, "mtime": 1750865212503, "results": "147", "hashOfConfig": "111"}, {"size": 17208, "mtime": 1750803669430, "results": "148", "hashOfConfig": "111"}, {"size": 38131, "mtime": 1752694473822, "results": "149", "hashOfConfig": "111"}, {"size": 5776, "mtime": 1751136335382, "results": "150", "hashOfConfig": "111"}, {"size": 3895, "mtime": 1751737235568, "results": "151", "hashOfConfig": "111"}, {"size": 15480, "mtime": 1751486182903, "results": "152", "hashOfConfig": "111"}, {"size": 25586, "mtime": 1752862614920, "results": "153", "hashOfConfig": "111"}, {"size": 3812, "mtime": 1753107814768, "results": "154", "hashOfConfig": "111"}, {"size": 7369, "mtime": 1750801151145, "results": "155", "hashOfConfig": "111"}, {"size": 14924, "mtime": 1750865379540, "results": "156", "hashOfConfig": "111"}, {"size": 33554, "mtime": 1752694133437, "results": "157", "hashOfConfig": "111"}, {"size": 12471, "mtime": 1751755248906, "results": "158", "hashOfConfig": "111"}, {"size": 9218, "mtime": 1750697084885, "results": "159", "hashOfConfig": "111"}, {"size": 9935, "mtime": 1753298015298, "results": "160", "hashOfConfig": "111"}, {"size": 9685, "mtime": 1751732875574, "results": "161", "hashOfConfig": "111"}, {"size": 9386, "mtime": 1750507575304, "results": "162", "hashOfConfig": "111"}, {"size": 4740, "mtime": 1750507757285, "results": "163", "hashOfConfig": "111"}, {"size": 4945, "mtime": 1752519742185, "results": "164", "hashOfConfig": "111"}, {"size": 10355, "mtime": 1753461630487, "results": "165", "hashOfConfig": "111"}, {"size": 27472, "mtime": 1752783607670, "results": "166", "hashOfConfig": "111"}, {"size": 13042, "mtime": 1751985982680, "results": "167", "hashOfConfig": "111"}, {"size": 17315, "mtime": 1750803649886, "results": "168", "hashOfConfig": "111"}, {"size": 20781, "mtime": 1752692816978, "results": "169", "hashOfConfig": "111"}, {"size": 18697, "mtime": 1752343276460, "results": "170", "hashOfConfig": "111"}, {"size": 8688, "mtime": 1751820583224, "results": "171", "hashOfConfig": "111"}, {"size": 10560, "mtime": 1751749901557, "results": "172", "hashOfConfig": "111"}, {"size": 9680, "mtime": 1751737862452, "results": "173", "hashOfConfig": "111"}, {"size": 6453, "mtime": 1751487511183, "results": "174", "hashOfConfig": "111"}, {"size": 11940, "mtime": 1751470354308, "results": "175", "hashOfConfig": "111"}, {"size": 7848, "mtime": 1750683265558, "results": "176", "hashOfConfig": "111"}, {"size": 11650, "mtime": 1752254292792, "results": "177", "hashOfConfig": "111"}, {"size": 28272, "mtime": 1752598993726, "results": "178", "hashOfConfig": "111"}, {"size": 18464, "mtime": 1748536533993, "results": "179", "hashOfConfig": "111"}, {"size": 39591, "mtime": 1753548491676, "results": "180", "hashOfConfig": "111"}, {"size": 3943, "mtime": 1753548346533, "results": "181", "hashOfConfig": "111"}, {"size": 4726, "mtime": 1748800688809, "results": "182", "hashOfConfig": "111"}, {"size": 3617, "mtime": 1750696686056, "results": "183", "hashOfConfig": "111"}, {"size": 14121, "mtime": 1750804928808, "results": "184", "hashOfConfig": "111"}, {"size": 8319, "mtime": 1750799292069, "results": "185", "hashOfConfig": "111"}, {"size": 2130, "mtime": 1750797516879, "results": "186", "hashOfConfig": "111"}, {"size": 1341, "mtime": 1748524799182, "results": "187", "hashOfConfig": "111"}, {"size": 2983, "mtime": 1750866299775, "results": "188", "hashOfConfig": "111"}, {"size": 7974, "mtime": 1749389048589, "results": "189", "hashOfConfig": "111"}, {"size": 13319, "mtime": 1750798166533, "results": "190", "hashOfConfig": "111"}, {"size": 1354, "mtime": 1748524812975, "results": "191", "hashOfConfig": "111"}, {"size": 23479, "mtime": 1753103098529, "results": "192", "hashOfConfig": "111"}, {"size": 14656, "mtime": 1753559907098, "results": "193", "hashOfConfig": "111"}, {"size": 7307, "mtime": 1753559954424, "results": "194", "hashOfConfig": "111"}, {"size": 5250, "mtime": 1753214788962, "results": "195", "hashOfConfig": "111"}, {"size": 9749, "mtime": 1752185710255, "results": "196", "hashOfConfig": "111"}, {"size": 422, "mtime": 1752681258162, "results": "197", "hashOfConfig": "111"}, {"size": 1634, "mtime": 1753458048549, "results": "198", "hashOfConfig": "111"}, {"size": 16435, "mtime": 1752761104833, "results": "199", "hashOfConfig": "111"}, {"size": 5076, "mtime": 1753104958722, "results": "200", "hashOfConfig": "111"}, {"size": 3210, "mtime": 1752607518039, "results": "201", "hashOfConfig": "111"}, {"size": 50096, "mtime": 1753548364510, "results": "202", "hashOfConfig": "111"}, {"size": 15188, "mtime": 1753103083044, "results": "203", "hashOfConfig": "111"}, {"size": 7180, "mtime": 1751752546178, "results": "204", "hashOfConfig": "111"}, {"size": 12351, "mtime": 1753562864373, "results": "205", "hashOfConfig": "111"}, {"size": 1233, "mtime": 1753562977285, "results": "206", "hashOfConfig": "111"}, {"size": 11962, "mtime": 1753562726153, "results": "207", "hashOfConfig": "111"}, {"size": 13756, "mtime": 1753562456552, "results": "208", "hashOfConfig": "111"}, {"size": 901, "mtime": 1753460063292, "results": "209", "hashOfConfig": "111"}, {"size": 21207, "mtime": 1753562629489, "results": "210", "hashOfConfig": "111"}, {"size": 23008, "mtime": 1753562154854, "results": "211", "hashOfConfig": "111"}, {"size": 649, "mtime": 1752680661880, "results": "212", "hashOfConfig": "111"}, {"size": 452, "mtime": 1751732885845, "results": "213", "hashOfConfig": "111"}, {"size": 11260, "mtime": 1752436013335, "results": "214", "hashOfConfig": "111"}, {"size": 776, "mtime": 1751981943700, "results": "215", "hashOfConfig": "111"}, {"size": 11573, "mtime": 1753104617510, "results": "216", "hashOfConfig": "111"}, {"size": 6948, "mtime": 1753562172086, "results": "217", "hashOfConfig": "111"}, {"size": 849, "mtime": 1752607503356, "results": "218", "hashOfConfig": "111"}, {"size": 22586, "mtime": 1753562184678, "results": "219", "hashOfConfig": "111"}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "qcb4zj", {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 24, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 27, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 2, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 2, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 16, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 98, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 4, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 18, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\account\\edit\\page.tsx", ["547"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\account\\page.tsx", ["548", "549", "550", "551", "552"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\add-product\\page.tsx", ["553", "554", "555", "556", "557", "558", "559", "560"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\category-management\\page.tsx", ["561"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\dealership-applications\\page.tsx", ["562", "563"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\orders\\page.tsx", ["564", "565"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\page.tsx", ["566"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\pending-products\\page.tsx", ["567", "568", "569", "570", "571", "572"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\products\\add\\page.tsx", ["573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\products\\edit\\[id]\\page.tsx", ["598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\products\\page.tsx", ["626", "627"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\reports\\page.tsx", ["628", "629"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\users\\page.tsx", ["630", "631", "632", "633"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\announcements\\page.tsx", ["634"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\become-dealer\\page.tsx", ["635", "636", "637", "638", "639", "640", "641"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\cart\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\checkout\\page.tsx", ["642"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\earnings\\page.tsx", ["643", "644", "645"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\edit-product\\[id]\\page.tsx", ["646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\forgot-password\\page.tsx", ["661"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx", ["662"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\login\\page.tsx", ["663", "664", "665", "666", "667"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\my-team\\page.tsx", ["668", "669", "670", "671", "672", "673", "674", "675"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\order-details\\[orderId]\\page.tsx", ["676"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\page.tsx", ["677"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\panel\\page.tsx", ["678", "679"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\pending-products\\page.tsx", ["680", "681", "682", "683"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\product\\[id]\\page.tsx", ["684", "685", "686"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\products\\page.tsx", ["687", "688", "689", "690"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\register\\page.tsx", ["691"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\reset-password\\page.tsx", ["692"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\team-tree\\page.tsx", ["693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AddAddressModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AddCardModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AddressList.tsx", ["705", "706"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AddToCartModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AdminDealerNotificationModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AdminProductDetailModal.tsx", ["707", "708"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AdminProductDetailModalNew.tsx", ["709", "710", "711"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\AnnouncementModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\auth\\AuthContext.tsx", ["712", "713", "714", "715"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\BankingModal.tsx", ["716"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\CategoryNavigation.tsx", ["717"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\CustomCheckbox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\DealerApplicationSuccessModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\DealershipApplicationDetailModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\DealershipProductDetailModal.tsx", ["718", "719", "720", "721", "722", "723"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\EditPersonalInfoModal.tsx", ["724", "725", "726", "727"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\FavoriteModal.tsx", ["728"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ImageModal.tsx", ["729"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\NetworkStatusHandler.tsx", ["730", "731", "732"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\PendingPlacementModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\PlacementSuccessModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProductApprovalModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProductCard.tsx", ["733"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProductCategorySelector.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProductDeleteConfirmationModal.tsx", ["734", "735", "736", "737"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProductDetailModal.tsx", ["738", "739"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProductVariantModal.tsx", ["740", "741", "742", "743", "744", "745", "746"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProductVariantSetupModal.tsx", ["747", "748", "749", "750", "751", "752", "753"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ProfilePictureMenu.tsx", ["754", "755"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ReferenceRegistrationModal.tsx", ["756"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\RegisterSuccessModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\SetDefaultCardModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\SetDefaultConfirmationModal.tsx", ["757", "758", "759", "760", "761", "762"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\SuccessModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\SuccessNotificationModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\TeamMemberDetailModal.tsx", ["763"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ui\\Footer.tsx", ["764"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\components\\ui\\Header.tsx", ["765", "766"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\constants\\apiEndpoints.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\contexts\\CartContext.tsx", ["767", "768"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\contexts\\FavoritesContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\account.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\admin.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\categories.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\dealership.ts", ["769", "770"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\distributor.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\products.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\data\\mocks\\users.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useAddresses.ts", ["771", "772", "773", "774", "775", "776", "777"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useAuth.ts", ["778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useCart.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useCatalogProducts.ts", ["794", "795", "796", "797"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useCategoryManagement.ts", ["798", "799"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useDebounce.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useDiscountRate.ts", ["800"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useProducts.ts", ["801", "802", "803", "804", "805", "806"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useProductsPage.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\hooks\\useUsers.ts", ["807"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\services\\api.ts", ["808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\services\\authService.ts", ["906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923"], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\services\\imageService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\addProductStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\authStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\catalogProductDetailStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\categoryManagementStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\customerPriceStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\editProductStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\modalStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\myProductFilterStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\networkStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\productDetailStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\productFilterStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\productsPageStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\useAddressStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\stores\\userFilterStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\types\\index.ts", [], [], {"ruleId": "924", "severity": 1, "message": "925", "line": 420, "column": 45, "nodeType": "926", "endLine": 429, "endColumn": 47}, {"ruleId": "924", "severity": 1, "message": "925", "line": 317, "column": 41, "nodeType": "926", "endLine": 326, "endColumn": 43}, {"ruleId": "927", "severity": 2, "message": "928", "line": 626, "column": 122, "nodeType": "929", "messageId": "930", "suggestions": "931"}, {"ruleId": "927", "severity": 2, "message": "928", "line": 675, "column": 109, "nodeType": "929", "messageId": "930", "suggestions": "932"}, {"ruleId": "924", "severity": 1, "message": "925", "line": 878, "column": 65, "nodeType": "926", "endLine": 882, "endColumn": 67}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1099, "column": 80, "nodeType": "935", "messageId": "936", "endLine": 1099, "endColumn": 83, "suggestions": "937"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 93, "column": 24, "nodeType": "935", "messageId": "936", "endLine": 93, "endColumn": 27, "suggestions": "938"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 194, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 194, "endColumn": 28, "suggestions": "939"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 361, "column": 48, "nodeType": "929", "messageId": "930", "suggestions": "941"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 361, "column": 82, "nodeType": "929", "messageId": "930", "suggestions": "942"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 434, "column": 96, "nodeType": "929", "messageId": "930", "suggestions": "943"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 434, "column": 102, "nodeType": "929", "messageId": "930", "suggestions": "944"}, {"ruleId": "924", "severity": 1, "message": "925", "line": 451, "column": 49, "nodeType": "926", "endLine": 455, "endColumn": 51}, {"ruleId": "933", "severity": 2, "message": "934", "line": 508, "column": 54, "nodeType": "935", "messageId": "936", "endLine": 508, "endColumn": 57, "suggestions": "945"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "946", "line": 456, "column": 111}, {"ruleId": "947", "severity": 2, "message": "948", "line": 6, "column": 10, "nodeType": null, "messageId": "949", "endLine": 6, "endColumn": 16}, {"ruleId": "947", "severity": 2, "message": "950", "line": 135, "column": 18, "nodeType": null, "messageId": "949", "endLine": 135, "endColumn": 23}, {"ruleId": "947", "severity": 2, "message": "951", "line": 11, "column": 5, "nodeType": null, "messageId": "949", "endLine": 11, "endColumn": 11}, {"ruleId": "947", "severity": 2, "message": "952", "line": 27, "column": 20, "nodeType": null, "messageId": "949", "endLine": 27, "endColumn": 29}, {"ruleId": "947", "severity": 2, "message": "953", "line": 15, "column": 5, "nodeType": null, "messageId": "949", "endLine": 15, "endColumn": 13}, {"ruleId": "947", "severity": 2, "message": "948", "line": 8, "column": 10, "nodeType": null, "messageId": "949", "endLine": 8, "endColumn": 16}, {"ruleId": "947", "severity": 2, "message": "953", "line": 18, "column": 5, "nodeType": null, "messageId": "949", "endLine": 18, "endColumn": 13}, {"ruleId": "947", "severity": 2, "message": "954", "line": 19, "column": 5, "nodeType": null, "messageId": "949", "endLine": 19, "endColumn": 18}, {"ruleId": "947", "severity": 2, "message": "951", "line": 20, "column": 5, "nodeType": null, "messageId": "949", "endLine": 20, "endColumn": 11}, {"ruleId": "933", "severity": 2, "message": "934", "line": 126, "column": 30, "nodeType": "935", "messageId": "936", "endLine": 126, "endColumn": 33, "suggestions": "955"}, {"ruleId": "924", "severity": 1, "message": "925", "line": 373, "column": 49, "nodeType": "926", "endLine": 377, "endColumn": 51}, {"ruleId": "947", "severity": 2, "message": "956", "line": 12, "column": 5, "nodeType": null, "messageId": "949", "endLine": 12, "endColumn": 15}, {"ruleId": "947", "severity": 2, "message": "957", "line": 13, "column": 5, "nodeType": null, "messageId": "949", "endLine": 13, "endColumn": 12}, {"ruleId": "947", "severity": 2, "message": "958", "line": 14, "column": 5, "nodeType": null, "messageId": "949", "endLine": 14, "endColumn": 9}, {"ruleId": "947", "severity": 2, "message": "959", "line": 16, "column": 5, "nodeType": null, "messageId": "949", "endLine": 16, "endColumn": 11}, {"ruleId": "947", "severity": 2, "message": "960", "line": 18, "column": 5, "nodeType": null, "messageId": "949", "endLine": 18, "endColumn": 6}, {"ruleId": "947", "severity": 2, "message": "961", "line": 24, "column": 5, "nodeType": null, "messageId": "949", "endLine": 24, "endColumn": 20}, {"ruleId": "947", "severity": 2, "message": "962", "line": 26, "column": 5, "nodeType": null, "messageId": "949", "endLine": 26, "endColumn": 18}, {"ruleId": "947", "severity": 2, "message": "963", "line": 27, "column": 5, "nodeType": null, "messageId": "949", "endLine": 27, "endColumn": 10}, {"ruleId": "947", "severity": 2, "message": "964", "line": 28, "column": 5, "nodeType": null, "messageId": "949", "endLine": 28, "endColumn": 13}, {"ruleId": "947", "severity": 2, "message": "965", "line": 29, "column": 5, "nodeType": null, "messageId": "949", "endLine": 29, "endColumn": 16}, {"ruleId": "947", "severity": 2, "message": "966", "line": 30, "column": 5, "nodeType": null, "messageId": "949", "endLine": 30, "endColumn": 23}, {"ruleId": "947", "severity": 2, "message": "967", "line": 31, "column": 5, "nodeType": null, "messageId": "949", "endLine": 31, "endColumn": 17}, {"ruleId": "947", "severity": 2, "message": "968", "line": 32, "column": 5, "nodeType": null, "messageId": "949", "endLine": 32, "endColumn": 17}, {"ruleId": "947", "severity": 2, "message": "969", "line": 67, "column": 9, "nodeType": null, "messageId": "949", "endLine": 67, "endColumn": 20}, {"ruleId": "947", "severity": 2, "message": "970", "line": 68, "column": 9, "nodeType": null, "messageId": "949", "endLine": 68, "endColumn": 21}, {"ruleId": "933", "severity": 2, "message": "934", "line": 111, "column": 24, "nodeType": "935", "messageId": "936", "endLine": 111, "endColumn": 27, "suggestions": "971"}, {"ruleId": "947", "severity": 2, "message": "972", "line": 170, "column": 11, "nodeType": null, "messageId": "949", "endLine": 170, "endColumn": 37}, {"ruleId": "947", "severity": 2, "message": "973", "line": 178, "column": 11, "nodeType": null, "messageId": "949", "endLine": 178, "endColumn": 35}, {"ruleId": "933", "severity": 2, "message": "934", "line": 232, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 232, "endColumn": 28, "suggestions": "974"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 399, "column": 48, "nodeType": "929", "messageId": "930", "suggestions": "975"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 399, "column": 82, "nodeType": "929", "messageId": "930", "suggestions": "976"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 482, "column": 96, "nodeType": "929", "messageId": "930", "suggestions": "977"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 482, "column": 102, "nodeType": "929", "messageId": "930", "suggestions": "978"}, {"ruleId": "924", "severity": 1, "message": "925", "line": 499, "column": 49, "nodeType": "926", "endLine": 503, "endColumn": 51}, {"ruleId": "933", "severity": 2, "message": "934", "line": 556, "column": 54, "nodeType": "935", "messageId": "936", "endLine": 556, "endColumn": 57, "suggestions": "979"}, {"ruleId": "947", "severity": 2, "message": "959", "line": 13, "column": 5, "nodeType": null, "messageId": "949", "endLine": 13, "endColumn": 11}, {"ruleId": "947", "severity": 2, "message": "960", "line": 15, "column": 5, "nodeType": null, "messageId": "949", "endLine": 15, "endColumn": 6}, {"ruleId": "947", "severity": 2, "message": "961", "line": 21, "column": 5, "nodeType": null, "messageId": "949", "endLine": 21, "endColumn": 20}, {"ruleId": "947", "severity": 2, "message": "962", "line": 23, "column": 5, "nodeType": null, "messageId": "949", "endLine": 23, "endColumn": 18}, {"ruleId": "947", "severity": 2, "message": "963", "line": 24, "column": 5, "nodeType": null, "messageId": "949", "endLine": 24, "endColumn": 10}, {"ruleId": "947", "severity": 2, "message": "964", "line": 25, "column": 5, "nodeType": null, "messageId": "949", "endLine": 25, "endColumn": 13}, {"ruleId": "947", "severity": 2, "message": "965", "line": 26, "column": 5, "nodeType": null, "messageId": "949", "endLine": 26, "endColumn": 16}, {"ruleId": "947", "severity": 2, "message": "966", "line": 27, "column": 5, "nodeType": null, "messageId": "949", "endLine": 27, "endColumn": 23}, {"ruleId": "947", "severity": 2, "message": "967", "line": 28, "column": 5, "nodeType": null, "messageId": "949", "endLine": 28, "endColumn": 17}, {"ruleId": "947", "severity": 2, "message": "968", "line": 29, "column": 5, "nodeType": null, "messageId": "949", "endLine": 29, "endColumn": 17}, {"ruleId": "947", "severity": 2, "message": "980", "line": 89, "column": 9, "nodeType": null, "messageId": "949", "endLine": 89, "endColumn": 26}, {"ruleId": "947", "severity": 2, "message": "969", "line": 97, "column": 9, "nodeType": null, "messageId": "949", "endLine": 97, "endColumn": 20}, {"ruleId": "947", "severity": 2, "message": "970", "line": 98, "column": 9, "nodeType": null, "messageId": "949", "endLine": 98, "endColumn": 21}, {"ruleId": "933", "severity": 2, "message": "934", "line": 183, "column": 24, "nodeType": "935", "messageId": "936", "endLine": 183, "endColumn": 27, "suggestions": "981"}, {"ruleId": "947", "severity": 2, "message": "972", "line": 242, "column": 11, "nodeType": null, "messageId": "949", "endLine": 242, "endColumn": 37}, {"ruleId": "947", "severity": 2, "message": "973", "line": 250, "column": 11, "nodeType": null, "messageId": "949", "endLine": 250, "endColumn": 35}, {"ruleId": "982", "severity": 2, "message": "983", "line": 351, "column": 23, "nodeType": "984", "messageId": "985", "endLine": 351, "endColumn": 26, "fix": "986"}, {"ruleId": "982", "severity": 2, "message": "987", "line": 351, "column": 28, "nodeType": "984", "messageId": "985", "endLine": 351, "endColumn": 33, "fix": "988"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 358, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 358, "endColumn": 28, "suggestions": "989"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 471, "column": 86, "nodeType": "935", "messageId": "936", "endLine": 471, "endColumn": 89, "suggestions": "990"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 472, "column": 79, "nodeType": "935", "messageId": "936", "endLine": 472, "endColumn": 82, "suggestions": "991"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 474, "column": 84, "nodeType": "935", "messageId": "936", "endLine": 474, "endColumn": 87, "suggestions": "992"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 596, "column": 48, "nodeType": "929", "messageId": "930", "suggestions": "993"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 596, "column": 82, "nodeType": "929", "messageId": "930", "suggestions": "994"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 679, "column": 96, "nodeType": "929", "messageId": "930", "suggestions": "995"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 679, "column": 102, "nodeType": "929", "messageId": "930", "suggestions": "996"}, {"ruleId": "924", "severity": 1, "message": "925", "line": 696, "column": 49, "nodeType": "926", "endLine": 700, "endColumn": 51}, {"ruleId": "933", "severity": 2, "message": "934", "line": 753, "column": 54, "nodeType": "935", "messageId": "936", "endLine": 753, "endColumn": 57, "suggestions": "997"}, {"ruleId": "947", "severity": 2, "message": "951", "line": 17, "column": 5, "nodeType": null, "messageId": "949", "endLine": 17, "endColumn": 11}, {"ruleId": "924", "severity": 1, "message": "925", "line": 394, "column": 61, "nodeType": "926", "endLine": 394, "endColumn": 156}, {"ruleId": "947", "severity": 2, "message": "998", "line": 9, "column": 5, "nodeType": null, "messageId": "949", "endLine": 9, "endColumn": 14}, {"ruleId": "947", "severity": 2, "message": "951", "line": 16, "column": 5, "nodeType": null, "messageId": "949", "endLine": 16, "endColumn": 11}, {"ruleId": "947", "severity": 2, "message": "999", "line": 10, "column": 21, "nodeType": null, "messageId": "949", "endLine": 10, "endColumn": 39}, {"ruleId": "947", "severity": 2, "message": "951", "line": 14, "column": 5, "nodeType": null, "messageId": "949", "endLine": 14, "endColumn": 11}, {"ruleId": "947", "severity": 2, "message": "1000", "line": 15, "column": 5, "nodeType": null, "messageId": "949", "endLine": 15, "endColumn": 17}, {"ruleId": "947", "severity": 2, "message": "1001", "line": 22, "column": 5, "nodeType": null, "messageId": "949", "endLine": 22, "endColumn": 10}, {"ruleId": "927", "severity": 2, "message": "928", "line": 215, "column": 31, "nodeType": "929", "messageId": "930", "suggestions": "1002"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 80, "column": 79, "nodeType": "935", "messageId": "936", "endLine": 80, "endColumn": 82, "suggestions": "1003"}, {"ruleId": "947", "severity": 2, "message": "950", "line": 261, "column": 18, "nodeType": null, "messageId": "949", "endLine": 261, "endColumn": 23}, {"ruleId": "927", "severity": 2, "message": "928", "line": 840, "column": 129, "nodeType": "929", "messageId": "930", "suggestions": "1004"}, {"ruleId": "927", "severity": 2, "message": "928", "line": 863, "column": 126, "nodeType": "929", "messageId": "930", "suggestions": "1005"}, {"ruleId": "927", "severity": 2, "message": "928", "line": 886, "column": 128, "nodeType": "929", "messageId": "930", "suggestions": "1006"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 1096, "column": 107, "nodeType": "929", "messageId": "930", "suggestions": "1007"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 1096, "column": 124, "nodeType": "929", "messageId": "930", "suggestions": "1008"}, {"ruleId": "947", "severity": 2, "message": "1009", "line": 13, "column": 11, "nodeType": null, "messageId": "949", "endLine": 13, "endColumn": 17}, {"ruleId": "947", "severity": 2, "message": "951", "line": 8, "column": 5, "nodeType": null, "messageId": "949", "endLine": 8, "endColumn": 11}, {"ruleId": "933", "severity": 2, "message": "934", "line": 32, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 32, "endColumn": 28, "suggestions": "1010"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 32, "column": 38, "nodeType": "935", "messageId": "936", "endLine": 32, "endColumn": 41, "suggestions": "1011"}, {"ruleId": "947", "severity": 2, "message": "1012", "line": 34, "column": 10, "nodeType": null, "messageId": "949", "endLine": 34, "endColumn": 21}, {"ruleId": "947", "severity": 2, "message": "1013", "line": 41, "column": 11, "nodeType": null, "messageId": "949", "endLine": 41, "endColumn": 22}, {"ruleId": "933", "severity": 2, "message": "934", "line": 120, "column": 29, "nodeType": "935", "messageId": "936", "endLine": 120, "endColumn": 32, "suggestions": "1014"}, {"ruleId": "982", "severity": 2, "message": "983", "line": 305, "column": 23, "nodeType": "984", "messageId": "985", "endLine": 305, "endColumn": 26, "fix": "1015"}, {"ruleId": "982", "severity": 2, "message": "987", "line": 305, "column": 28, "nodeType": "984", "messageId": "985", "endLine": 305, "endColumn": 33, "fix": "1016"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 326, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 326, "endColumn": 28, "suggestions": "1017"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 504, "column": 86, "nodeType": "935", "messageId": "936", "endLine": 504, "endColumn": 89, "suggestions": "1018"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 506, "column": 79, "nodeType": "935", "messageId": "936", "endLine": 506, "endColumn": 82, "suggestions": "1019"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 508, "column": 84, "nodeType": "935", "messageId": "936", "endLine": 508, "endColumn": 87, "suggestions": "1020"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 639, "column": 48, "nodeType": "929", "messageId": "930", "suggestions": "1021"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 639, "column": 82, "nodeType": "929", "messageId": "930", "suggestions": "1022"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 712, "column": 96, "nodeType": "929", "messageId": "930", "suggestions": "1023"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 712, "column": 102, "nodeType": "929", "messageId": "930", "suggestions": "1024"}, {"ruleId": "924", "severity": 1, "message": "925", "line": 729, "column": 49, "nodeType": "926", "endLine": 733, "endColumn": 51}, {"ruleId": "933", "severity": 2, "message": "934", "line": 786, "column": 54, "nodeType": "935", "messageId": "936", "endLine": 786, "endColumn": 57, "suggestions": "1025"}, {"ruleId": "947", "severity": 2, "message": "1026", "line": 34, "column": 18, "nodeType": null, "messageId": "949", "endLine": 34, "endColumn": 21}, {"ruleId": "947", "severity": 2, "message": "1027", "line": 3, "column": 15, "nodeType": null, "messageId": "949", "endLine": 3, "endColumn": 23}, {"ruleId": "947", "severity": 2, "message": "1028", "line": 21, "column": 20, "nodeType": null, "messageId": "949", "endLine": 21, "endColumn": 29}, {"ruleId": "947", "severity": 2, "message": "950", "line": 53, "column": 23, "nodeType": null, "messageId": "949", "endLine": 53, "endColumn": 28}, {"ruleId": "933", "severity": 2, "message": "934", "line": 53, "column": 30, "nodeType": "935", "messageId": "936", "endLine": 53, "endColumn": 33, "suggestions": "1029"}, {"ruleId": "947", "severity": 2, "message": "950", "line": 69, "column": 23, "nodeType": null, "messageId": "949", "endLine": 69, "endColumn": 28}, {"ruleId": "933", "severity": 2, "message": "934", "line": 69, "column": 30, "nodeType": "935", "messageId": "936", "endLine": 69, "endColumn": 33, "suggestions": "1030"}, {"ruleId": "947", "severity": 2, "message": "1031", "line": 4, "column": 23, "nodeType": null, "messageId": "949", "endLine": 4, "endColumn": 43}, {"ruleId": "947", "severity": 2, "message": "951", "line": 10, "column": 5, "nodeType": null, "messageId": "949", "endLine": 10, "endColumn": 11}, {"ruleId": "947", "severity": 2, "message": "1032", "line": 12, "column": 5, "nodeType": null, "messageId": "949", "endLine": 12, "endColumn": 10}, {"ruleId": "947", "severity": 2, "message": "1033", "line": 17, "column": 5, "nodeType": null, "messageId": "949", "endLine": 17, "endColumn": 11}, {"ruleId": "947", "severity": 2, "message": "1034", "line": 20, "column": 5, "nodeType": null, "messageId": "949", "endLine": 20, "endColumn": 10}, {"ruleId": "933", "severity": 2, "message": "934", "line": 77, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 77, "endColumn": 28, "suggestions": "1035"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 77, "column": 38, "nodeType": "935", "messageId": "936", "endLine": 77, "endColumn": 41, "suggestions": "1036"}, {"ruleId": "1037", "severity": 1, "message": "1038", "line": 113, "column": 8, "nodeType": "1039", "endLine": 113, "endColumn": 47, "suggestions": "1040"}, {"ruleId": "947", "severity": 2, "message": "1041", "line": 166, "column": 81, "nodeType": null, "messageId": "949", "endLine": 166, "endColumn": 86}, {"ruleId": "947", "severity": 2, "message": "1041", "line": 495, "column": 40, "nodeType": null, "messageId": "949", "endLine": 495, "endColumn": 45}, {"ruleId": "947", "severity": 2, "message": "1042", "line": 6, "column": 10, "nodeType": null, "messageId": "949", "endLine": 6, "endColumn": 28}, {"ruleId": "947", "severity": 2, "message": "953", "line": 16, "column": 5, "nodeType": null, "messageId": "949", "endLine": 16, "endColumn": 13}, {"ruleId": "947", "severity": 2, "message": "951", "line": 19, "column": 5, "nodeType": null, "messageId": "949", "endLine": 19, "endColumn": 11}, {"ruleId": "947", "severity": 2, "message": "1043", "line": 23, "column": 5, "nodeType": null, "messageId": "949", "endLine": 23, "endColumn": 8}, {"ruleId": "947", "severity": 2, "message": "1044", "line": 27, "column": 5, "nodeType": null, "messageId": "949", "endLine": 27, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "925", "line": 336, "column": 41, "nodeType": "926", "endLine": 340, "endColumn": 43}, {"ruleId": "933", "severity": 2, "message": "934", "line": 32, "column": 62, "nodeType": "935", "messageId": "936", "endLine": 32, "endColumn": 65, "suggestions": "1045"}, {"ruleId": "947", "severity": 2, "message": "1046", "line": 40, "column": 48, "nodeType": null, "messageId": "949", "endLine": 40, "endColumn": 67}, {"ruleId": "1037", "severity": 1, "message": "1047", "line": 83, "column": 8, "nodeType": "1039", "endLine": 83, "endColumn": 10, "suggestions": "1048"}, {"ruleId": "947", "severity": 2, "message": "1049", "line": 3, "column": 10, "nodeType": null, "messageId": "949", "endLine": 3, "endColumn": 18}, {"ruleId": "947", "severity": 2, "message": "1050", "line": 23, "column": 13, "nodeType": null, "messageId": "949", "endLine": 23, "endColumn": 17}, {"ruleId": "947", "severity": 2, "message": "1051", "line": 23, "column": 19, "nodeType": null, "messageId": "949", "endLine": 23, "endColumn": 34}, {"ruleId": "947", "severity": 2, "message": "1052", "line": 27, "column": 50, "nodeType": null, "messageId": "949", "endLine": 27, "endColumn": 68}, {"ruleId": "947", "severity": 2, "message": "1009", "line": 27, "column": 11, "nodeType": null, "messageId": "949", "endLine": 27, "endColumn": 17}, {"ruleId": "947", "severity": 2, "message": "1026", "line": 63, "column": 18, "nodeType": null, "messageId": "949", "endLine": 63, "endColumn": 21}, {"ruleId": "1037", "severity": 1, "message": "1053", "line": 392, "column": 8, "nodeType": "1039", "endLine": 392, "endColumn": 79, "suggestions": "1054"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 516, "column": 29, "nodeType": "935", "messageId": "936", "endLine": 516, "endColumn": 32, "suggestions": "1055"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 516, "column": 35, "nodeType": "935", "messageId": "936", "endLine": 516, "endColumn": 38, "suggestions": "1056"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 521, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 521, "endColumn": 28, "suggestions": "1057"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 795, "column": 39, "nodeType": "929", "messageId": "930", "suggestions": "1058"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 795, "column": 49, "nodeType": "929", "messageId": "930", "suggestions": "1059"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 796, "column": 39, "nodeType": "929", "messageId": "930", "suggestions": "1060"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 796, "column": 49, "nodeType": "929", "messageId": "930", "suggestions": "1061"}, {"ruleId": "947", "severity": 2, "message": "1062", "line": 889, "column": 58, "nodeType": null, "messageId": "949", "endLine": 889, "endColumn": 59}, {"ruleId": "933", "severity": 2, "message": "934", "line": 892, "column": 68, "nodeType": "935", "messageId": "936", "endLine": 892, "endColumn": 71, "suggestions": "1063"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 893, "column": 73, "nodeType": "935", "messageId": "936", "endLine": 893, "endColumn": 76, "suggestions": "1064"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 905, "column": 63, "nodeType": "935", "messageId": "936", "endLine": 905, "endColumn": 66, "suggestions": "1065"}, {"ruleId": "947", "severity": 2, "message": "1049", "line": 4, "column": 10, "nodeType": null, "messageId": "949", "endLine": 4, "endColumn": 18}, {"ruleId": "947", "severity": 2, "message": "1066", "line": 69, "column": 15, "nodeType": null, "messageId": "949", "endLine": 69, "endColumn": 30}, {"ruleId": "924", "severity": 1, "message": "925", "line": 140, "column": 37, "nodeType": "926", "endLine": 144, "endColumn": 39}, {"ruleId": "924", "severity": 1, "message": "925", "line": 182, "column": 49, "nodeType": "926", "endLine": 186, "endColumn": 51}, {"ruleId": "947", "severity": 2, "message": "1067", "line": 25, "column": 25, "nodeType": null, "messageId": "949", "endLine": 25, "endColumn": 39}, {"ruleId": "924", "severity": 1, "message": "925", "line": 305, "column": 45, "nodeType": "926", "endLine": 309, "endColumn": 47}, {"ruleId": "924", "severity": 1, "message": "925", "line": 349, "column": 49, "nodeType": "926", "endLine": 353, "endColumn": 51}, {"ruleId": "947", "severity": 2, "message": "1068", "line": 3, "column": 55, "nodeType": null, "messageId": "949", "endLine": 3, "endColumn": 66}, {"ruleId": "947", "severity": 2, "message": "1049", "line": 3, "column": 68, "nodeType": null, "messageId": "949", "endLine": 3, "endColumn": 76}, {"ruleId": "947", "severity": 2, "message": "1069", "line": 4, "column": 10, "nodeType": null, "messageId": "949", "endLine": 4, "endColumn": 18}, {"ruleId": "947", "severity": 2, "message": "1070", "line": 15, "column": 20, "nodeType": null, "messageId": "949", "endLine": 15, "endColumn": 30}, {"ruleId": "1037", "severity": 1, "message": "1071", "line": 31, "column": 8, "nodeType": "1039", "endLine": 37, "endColumn": 6, "suggestions": "1072"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 10, "column": 39, "nodeType": "935", "messageId": "936", "endLine": 10, "endColumn": 42, "suggestions": "1073"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 175, "column": 62, "nodeType": "935", "messageId": "936", "endLine": 175, "endColumn": 65, "suggestions": "1074"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 259, "column": 83, "nodeType": "935", "messageId": "936", "endLine": 259, "endColumn": 86, "suggestions": "1075"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 271, "column": 88, "nodeType": "935", "messageId": "936", "endLine": 271, "endColumn": 91, "suggestions": "1076"}, {"ruleId": "924", "severity": 1, "message": "925", "line": 284, "column": 45, "nodeType": "926", "endLine": 288, "endColumn": 47}, {"ruleId": "924", "severity": 1, "message": "925", "line": 328, "column": 49, "nodeType": "926", "endLine": 332, "endColumn": 51}, {"ruleId": "933", "severity": 2, "message": "934", "line": 407, "column": 93, "nodeType": "935", "messageId": "936", "endLine": 407, "endColumn": 96, "suggestions": "1077"}, {"ruleId": "947", "severity": 2, "message": "1078", "line": 5, "column": 10, "nodeType": null, "messageId": "949", "endLine": 5, "endColumn": 21}, {"ruleId": "947", "severity": 2, "message": "1079", "line": 9, "column": 10, "nodeType": null, "messageId": "949", "endLine": 9, "endColumn": 22}, {"ruleId": "933", "severity": 2, "message": "934", "line": 89, "column": 31, "nodeType": "935", "messageId": "936", "endLine": 89, "endColumn": 34, "suggestions": "1080"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 138, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 138, "endColumn": 28, "suggestions": "1081"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 10, "column": 14, "nodeType": "935", "messageId": "936", "endLine": 10, "endColumn": 17, "suggestions": "1082"}, {"ruleId": "1037", "severity": 1, "message": "1083", "line": 58, "column": 8, "nodeType": "1039", "endLine": 58, "endColumn": 50, "suggestions": "1084"}, {"ruleId": "947", "severity": 2, "message": "1085", "line": 118, "column": 12, "nodeType": null, "messageId": "949", "endLine": 118, "endColumn": 23}, {"ruleId": "1037", "severity": 1, "message": "1086", "line": 142, "column": 8, "nodeType": "1039", "endLine": 142, "endColumn": 10, "suggestions": "1087"}, {"ruleId": "1037", "severity": 1, "message": "1088", "line": 287, "column": 8, "nodeType": "1039", "endLine": 287, "endColumn": 16, "suggestions": "1089"}, {"ruleId": "947", "severity": 2, "message": "1046", "line": 15, "column": 48, "nodeType": null, "messageId": "949", "endLine": 15, "endColumn": 67}, {"ruleId": "1037", "severity": 1, "message": "1090", "line": 86, "column": 8, "nodeType": "1039", "endLine": 86, "endColumn": 19, "suggestions": "1091"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 162, "column": 78, "nodeType": "929", "messageId": "930", "suggestions": "1092"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 162, "column": 92, "nodeType": "929", "messageId": "930", "suggestions": "1093"}, {"ruleId": "924", "severity": 1, "message": "925", "line": 199, "column": 49, "nodeType": "926", "endLine": 203, "endColumn": 51}, {"ruleId": "924", "severity": 1, "message": "925", "line": 140, "column": 37, "nodeType": "926", "endLine": 144, "endColumn": 39}, {"ruleId": "924", "severity": 1, "message": "925", "line": 182, "column": 49, "nodeType": "926", "endLine": 186, "endColumn": 51}, {"ruleId": "947", "severity": 2, "message": "956", "line": 5, "column": 19, "nodeType": null, "messageId": "949", "endLine": 5, "endColumn": 29}, {"ruleId": "947", "severity": 2, "message": "957", "line": 5, "column": 31, "nodeType": null, "messageId": "949", "endLine": 5, "endColumn": 38}, {"ruleId": "947", "severity": 2, "message": "1094", "line": 5, "column": 46, "nodeType": null, "messageId": "949", "endLine": 5, "endColumn": 50}, {"ruleId": "947", "severity": 2, "message": "1095", "line": 5, "column": 69, "nodeType": null, "messageId": "949", "endLine": 5, "endColumn": 78}, {"ruleId": "947", "severity": 2, "message": "962", "line": 10, "column": 5, "nodeType": null, "messageId": "949", "endLine": 10, "endColumn": 18}, {"ruleId": "1037", "severity": 1, "message": "1096", "line": 74, "column": 8, "nodeType": "1039", "endLine": 74, "endColumn": 135, "suggestions": "1097"}, {"ruleId": "924", "severity": 1, "message": "925", "line": 318, "column": 53, "nodeType": "926", "endLine": 318, "endColumn": 163}, {"ruleId": "947", "severity": 2, "message": "1098", "line": 11, "column": 5, "nodeType": null, "messageId": "949", "endLine": 11, "endColumn": 19}, {"ruleId": "1037", "severity": 1, "message": "1099", "line": 38, "column": 8, "nodeType": "1039", "endLine": 38, "endColumn": 35, "suggestions": "1100"}, {"ruleId": "947", "severity": 2, "message": "1101", "line": 119, "column": 80, "nodeType": null, "messageId": "949", "endLine": 119, "endColumn": 81}, {"ruleId": "927", "severity": 2, "message": "940", "line": 279, "column": 61, "nodeType": "929", "messageId": "930", "suggestions": "1102"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 279, "column": 66, "nodeType": "929", "messageId": "930", "suggestions": "1103"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 279, "column": 90, "nodeType": "929", "messageId": "930", "suggestions": "1104"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 279, "column": 96, "nodeType": "929", "messageId": "930", "suggestions": "1105"}, {"ruleId": "947", "severity": 2, "message": "1106", "line": 6, "column": 10, "nodeType": null, "messageId": "949", "endLine": 6, "endColumn": 22}, {"ruleId": "933", "severity": 2, "message": "934", "line": 114, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 114, "endColumn": 28, "suggestions": "1107"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 17, "column": 32, "nodeType": "935", "messageId": "936", "endLine": 17, "endColumn": 35, "suggestions": "1108"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 151, "column": 86, "nodeType": "929", "messageId": "930", "suggestions": "1109"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 151, "column": 117, "nodeType": "929", "messageId": "930", "suggestions": "1110"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 155, "column": 77, "nodeType": "929", "messageId": "930", "suggestions": "1111"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 155, "column": 99, "nodeType": "929", "messageId": "930", "suggestions": "1112"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 162, "column": 85, "nodeType": "929", "messageId": "930", "suggestions": "1113"}, {"ruleId": "927", "severity": 2, "message": "940", "line": 162, "column": 107, "nodeType": "929", "messageId": "930", "suggestions": "1114"}, {"ruleId": "947", "severity": 2, "message": "1115", "line": 16, "column": 5, "nodeType": null, "messageId": "949", "endLine": 16, "endColumn": 10}, {"ruleId": "927", "severity": 2, "message": "928", "line": 85, "column": 81, "nodeType": "929", "messageId": "930", "suggestions": "1116"}, {"ruleId": "947", "severity": 2, "message": "1042", "line": 9, "column": 10, "nodeType": null, "messageId": "949", "endLine": 9, "endColumn": 28}, {"ruleId": "947", "severity": 2, "message": "1069", "line": 9, "column": 30, "nodeType": null, "messageId": "949", "endLine": 9, "endColumn": 38}, {"ruleId": "933", "severity": 2, "message": "934", "line": 19, "column": 64, "nodeType": "935", "messageId": "936", "endLine": 19, "endColumn": 67, "suggestions": "1117"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 38, "column": 33, "nodeType": "935", "messageId": "936", "endLine": 38, "endColumn": 36, "suggestions": "1118"}, {"ruleId": "947", "severity": 2, "message": "1119", "line": 1, "column": 33, "nodeType": null, "messageId": "949", "endLine": 1, "endColumn": 60}, {"ruleId": "982", "severity": 2, "message": "1120", "line": 4, "column": 12, "nodeType": "984", "messageId": "985", "endLine": 4, "endColumn": 63, "fix": "1121"}, {"ruleId": "947", "severity": 2, "message": "1122", "line": 2, "column": 10, "nodeType": null, "messageId": "949", "endLine": 2, "endColumn": 19}, {"ruleId": "947", "severity": 2, "message": "1123", "line": 5, "column": 10, "nodeType": null, "messageId": "949", "endLine": 5, "endColumn": 17}, {"ruleId": "947", "severity": 2, "message": "1124", "line": 10, "column": 10, "nodeType": null, "messageId": "949", "endLine": 10, "endColumn": 25}, {"ruleId": "947", "severity": 2, "message": "1013", "line": 56, "column": 11, "nodeType": null, "messageId": "949", "endLine": 56, "endColumn": 22}, {"ruleId": "947", "severity": 2, "message": "1125", "line": 232, "column": 33, "nodeType": null, "messageId": "949", "endLine": 232, "endColumn": 42}, {"ruleId": "947", "severity": 2, "message": "1126", "line": 232, "column": 44, "nodeType": null, "messageId": "949", "endLine": 232, "endColumn": 51}, {"ruleId": "947", "severity": 2, "message": "1126", "line": 454, "column": 48, "nodeType": null, "messageId": "949", "endLine": 454, "endColumn": 55}, {"ruleId": "947", "severity": 2, "message": "1009", "line": 21, "column": 11, "nodeType": null, "messageId": "949", "endLine": 21, "endColumn": 17}, {"ruleId": "933", "severity": 2, "message": "934", "line": 99, "column": 38, "nodeType": "935", "messageId": "936", "endLine": 99, "endColumn": 41, "suggestions": "1127"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 108, "column": 31, "nodeType": "935", "messageId": "936", "endLine": 108, "endColumn": 34, "suggestions": "1128"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 131, "column": 33, "nodeType": "935", "messageId": "936", "endLine": 131, "endColumn": 36, "suggestions": "1129"}, {"ruleId": "947", "severity": 2, "message": "1009", "line": 146, "column": 11, "nodeType": null, "messageId": "949", "endLine": 146, "endColumn": 17}, {"ruleId": "947", "severity": 2, "message": "1050", "line": 147, "column": 13, "nodeType": null, "messageId": "949", "endLine": 147, "endColumn": 17}, {"ruleId": "947", "severity": 2, "message": "1130", "line": 147, "column": 19, "nodeType": null, "messageId": "949", "endLine": 147, "endColumn": 26}, {"ruleId": "947", "severity": 2, "message": "1131", "line": 147, "column": 28, "nodeType": null, "messageId": "949", "endLine": 147, "endColumn": 37}, {"ruleId": "947", "severity": 2, "message": "1132", "line": 147, "column": 39, "nodeType": null, "messageId": "949", "endLine": 147, "endColumn": 49}, {"ruleId": "933", "severity": 2, "message": "934", "line": 206, "column": 42, "nodeType": "935", "messageId": "936", "endLine": 206, "endColumn": 45, "suggestions": "1133"}, {"ruleId": "947", "severity": 2, "message": "950", "line": 210, "column": 22, "nodeType": null, "messageId": "949", "endLine": 210, "endColumn": 27}, {"ruleId": "933", "severity": 2, "message": "934", "line": 219, "column": 26, "nodeType": "935", "messageId": "936", "endLine": 219, "endColumn": 29, "suggestions": "1134"}, {"ruleId": "947", "severity": 2, "message": "950", "line": 250, "column": 19, "nodeType": null, "messageId": "949", "endLine": 250, "endColumn": 24}, {"ruleId": "933", "severity": 2, "message": "934", "line": 290, "column": 26, "nodeType": "935", "messageId": "936", "endLine": 290, "endColumn": 29, "suggestions": "1135"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 358, "column": 38, "nodeType": "935", "messageId": "936", "endLine": 358, "endColumn": 41, "suggestions": "1136"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 366, "column": 31, "nodeType": "935", "messageId": "936", "endLine": 366, "endColumn": 34, "suggestions": "1137"}, {"ruleId": "1037", "severity": 1, "message": "1138", "line": 14, "column": 9, "nodeType": "1039", "endLine": 14, "endColumn": 44, "suggestions": "1139"}, {"ruleId": "1037", "severity": 1, "message": "1138", "line": 20, "column": 9, "nodeType": "1039", "endLine": 20, "endColumn": 40, "suggestions": "1140"}, {"ruleId": "1037", "severity": 1, "message": "1141", "line": 80, "column": 8, "nodeType": "1039", "endLine": 80, "endColumn": 33, "suggestions": "1142"}, {"ruleId": "1037", "severity": 1, "message": "1141", "line": 85, "column": 8, "nodeType": "1039", "endLine": 85, "endColumn": 25, "suggestions": "1143"}, {"ruleId": "947", "severity": 2, "message": "1144", "line": 23, "column": 11, "nodeType": null, "messageId": "949", "endLine": 23, "endColumn": 28}, {"ruleId": "947", "severity": 2, "message": "1145", "line": 37, "column": 11, "nodeType": null, "messageId": "949", "endLine": 37, "endColumn": 24}, {"ruleId": "933", "severity": 2, "message": "934", "line": 33, "column": 38, "nodeType": "935", "messageId": "936", "endLine": 33, "endColumn": 41, "suggestions": "1146"}, {"ruleId": "947", "severity": 2, "message": "1147", "line": 1, "column": 49, "nodeType": null, "messageId": "949", "endLine": 1, "endColumn": 59}, {"ruleId": "947", "severity": 2, "message": "1148", "line": 3, "column": 24, "nodeType": null, "messageId": "949", "endLine": 3, "endColumn": 33}, {"ruleId": "947", "severity": 2, "message": "1149", "line": 3, "column": 35, "nodeType": null, "messageId": "949", "endLine": 3, "endColumn": 58}, {"ruleId": "933", "severity": 2, "message": "934", "line": 60, "column": 26, "nodeType": "935", "messageId": "936", "endLine": 60, "endColumn": 29, "suggestions": "1150"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 102, "column": 27, "nodeType": "935", "messageId": "936", "endLine": 102, "endColumn": 30, "suggestions": "1151"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 233, "column": 29, "nodeType": "935", "messageId": "936", "endLine": 233, "endColumn": 32, "suggestions": "1152"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 29, "column": 34, "nodeType": "935", "messageId": "936", "endLine": 29, "endColumn": 37, "suggestions": "1153"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 57, "column": 23, "nodeType": "935", "messageId": "936", "endLine": 57, "endColumn": 26, "suggestions": "1154"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 58, "column": 22, "nodeType": "935", "messageId": "936", "endLine": 58, "endColumn": 25, "suggestions": "1155"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 61, "column": 30, "nodeType": "935", "messageId": "936", "endLine": 61, "endColumn": 33, "suggestions": "1156"}, {"ruleId": "947", "severity": 2, "message": "1157", "line": 74, "column": 7, "nodeType": null, "messageId": "949", "endLine": 74, "endColumn": 22}, {"ruleId": "933", "severity": 2, "message": "934", "line": 157, "column": 68, "nodeType": "935", "messageId": "936", "endLine": 157, "endColumn": 71, "suggestions": "1158"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 161, "column": 40, "nodeType": "935", "messageId": "936", "endLine": 161, "endColumn": 43, "suggestions": "1159"}, {"ruleId": "947", "severity": 2, "message": "1160", "line": 186, "column": 23, "nodeType": null, "messageId": "949", "endLine": 186, "endColumn": 38}, {"ruleId": "933", "severity": 2, "message": "934", "line": 195, "column": 36, "nodeType": "935", "messageId": "936", "endLine": 195, "endColumn": 39, "suggestions": "1161"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 251, "column": 35, "nodeType": "935", "messageId": "936", "endLine": 251, "endColumn": 38, "suggestions": "1162"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 319, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 319, "endColumn": 28, "suggestions": "1163"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 333, "column": 38, "nodeType": "935", "messageId": "936", "endLine": 333, "endColumn": 41, "suggestions": "1164"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 333, "column": 52, "nodeType": "935", "messageId": "936", "endLine": 333, "endColumn": 55, "suggestions": "1165"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 342, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 342, "endColumn": 28, "suggestions": "1166"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 352, "column": 69, "nodeType": "935", "messageId": "936", "endLine": 352, "endColumn": 72, "suggestions": "1167"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 361, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 361, "endColumn": 28, "suggestions": "1168"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 371, "column": 57, "nodeType": "935", "messageId": "936", "endLine": 371, "endColumn": 60, "suggestions": "1169"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 380, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 380, "endColumn": 28, "suggestions": "1170"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 403, "column": 17, "nodeType": "935", "messageId": "936", "endLine": 403, "endColumn": 20, "suggestions": "1171"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 412, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 412, "endColumn": 28, "suggestions": "1172"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 428, "column": 17, "nodeType": "935", "messageId": "936", "endLine": 428, "endColumn": 20, "suggestions": "1173"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 431, "column": 32, "nodeType": "935", "messageId": "936", "endLine": 431, "endColumn": 35, "suggestions": "1174"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 453, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 453, "endColumn": 28, "suggestions": "1175"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 464, "column": 40, "nodeType": "935", "messageId": "936", "endLine": 464, "endColumn": 43, "suggestions": "1176"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 471, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 471, "endColumn": 28, "suggestions": "1177"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 482, "column": 38, "nodeType": "935", "messageId": "936", "endLine": 482, "endColumn": 41, "suggestions": "1178"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 491, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 491, "endColumn": 28, "suggestions": "1179"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 502, "column": 37, "nodeType": "935", "messageId": "936", "endLine": 502, "endColumn": 40, "suggestions": "1180"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 511, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 511, "endColumn": 28, "suggestions": "1181"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 528, "column": 32, "nodeType": "935", "messageId": "936", "endLine": 528, "endColumn": 35, "suggestions": "1182"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 537, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 537, "endColumn": 28, "suggestions": "1183"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 548, "column": 58, "nodeType": "935", "messageId": "936", "endLine": 548, "endColumn": 61, "suggestions": "1184"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 557, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 557, "endColumn": 28, "suggestions": "1185"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 568, "column": 57, "nodeType": "935", "messageId": "936", "endLine": 568, "endColumn": 60, "suggestions": "1186"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 577, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 577, "endColumn": 28, "suggestions": "1187"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 588, "column": 66, "nodeType": "935", "messageId": "936", "endLine": 588, "endColumn": 69, "suggestions": "1188"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 597, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 597, "endColumn": 28, "suggestions": "1189"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 608, "column": 59, "nodeType": "935", "messageId": "936", "endLine": 608, "endColumn": 62, "suggestions": "1190"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 617, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 617, "endColumn": 28, "suggestions": "1191"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 628, "column": 47, "nodeType": "935", "messageId": "936", "endLine": 628, "endColumn": 50, "suggestions": "1192"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 637, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 637, "endColumn": 28, "suggestions": "1193"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 648, "column": 42, "nodeType": "935", "messageId": "936", "endLine": 648, "endColumn": 45, "suggestions": "1194"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 648, "column": 56, "nodeType": "935", "messageId": "936", "endLine": 648, "endColumn": 59, "suggestions": "1195"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 661, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 661, "endColumn": 28, "suggestions": "1196"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 671, "column": 48, "nodeType": "935", "messageId": "936", "endLine": 671, "endColumn": 51, "suggestions": "1197"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 671, "column": 62, "nodeType": "935", "messageId": "936", "endLine": 671, "endColumn": 65, "suggestions": "1198"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 684, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 684, "endColumn": 28, "suggestions": "1199"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 694, "column": 38, "nodeType": "935", "messageId": "936", "endLine": 694, "endColumn": 41, "suggestions": "1200"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 694, "column": 52, "nodeType": "935", "messageId": "936", "endLine": 694, "endColumn": 55, "suggestions": "1201"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 707, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 707, "endColumn": 28, "suggestions": "1202"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 717, "column": 56, "nodeType": "935", "messageId": "936", "endLine": 717, "endColumn": 59, "suggestions": "1203"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 726, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 726, "endColumn": 28, "suggestions": "1204"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 736, "column": 77, "nodeType": "935", "messageId": "936", "endLine": 736, "endColumn": 80, "suggestions": "1205"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 752, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 752, "endColumn": 28, "suggestions": "1206"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 773, "column": 110, "nodeType": "935", "messageId": "936", "endLine": 773, "endColumn": 113, "suggestions": "1207"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 782, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 782, "endColumn": 28, "suggestions": "1208"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 793, "column": 66, "nodeType": "935", "messageId": "936", "endLine": 793, "endColumn": 69, "suggestions": "1209"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 802, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 802, "endColumn": 28, "suggestions": "1210"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 813, "column": 53, "nodeType": "935", "messageId": "936", "endLine": 813, "endColumn": 56, "suggestions": "1211"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 825, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 825, "endColumn": 28, "suggestions": "1212"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 835, "column": 42, "nodeType": "935", "messageId": "936", "endLine": 835, "endColumn": 45, "suggestions": "1213"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 835, "column": 56, "nodeType": "935", "messageId": "936", "endLine": 835, "endColumn": 59, "suggestions": "1214"}, {"ruleId": "982", "severity": 2, "message": "983", "line": 841, "column": 23, "nodeType": "984", "messageId": "985", "endLine": 841, "endColumn": 26, "fix": "1215"}, {"ruleId": "982", "severity": 2, "message": "987", "line": 841, "column": 28, "nodeType": "984", "messageId": "985", "endLine": 841, "endColumn": 33, "fix": "1216"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 855, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 855, "endColumn": 28, "suggestions": "1217"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 871, "column": 48, "nodeType": "935", "messageId": "936", "endLine": 871, "endColumn": 51, "suggestions": "1218"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 880, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 880, "endColumn": 28, "suggestions": "1219"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 891, "column": 45, "nodeType": "935", "messageId": "936", "endLine": 891, "endColumn": 48, "suggestions": "1220"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 900, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 900, "endColumn": 28, "suggestions": "1221"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 911, "column": 67, "nodeType": "935", "messageId": "936", "endLine": 911, "endColumn": 70, "suggestions": "1222"}, {"ruleId": "982", "severity": 2, "message": "983", "line": 917, "column": 19, "nodeType": "984", "messageId": "985", "endLine": 917, "endColumn": 22, "fix": "1223"}, {"ruleId": "982", "severity": 2, "message": "987", "line": 917, "column": 24, "nodeType": "984", "messageId": "985", "endLine": 917, "endColumn": 29, "fix": "1224"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 939, "column": 56, "nodeType": "935", "messageId": "936", "endLine": 939, "endColumn": 59, "suggestions": "1225"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 948, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 948, "endColumn": 28, "suggestions": "1226"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 959, "column": 63, "nodeType": "935", "messageId": "936", "endLine": 959, "endColumn": 66, "suggestions": "1227"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 968, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 968, "endColumn": 28, "suggestions": "1228"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 979, "column": 97, "nodeType": "935", "messageId": "936", "endLine": 979, "endColumn": 100, "suggestions": "1229"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 992, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 992, "endColumn": 28, "suggestions": "1230"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1002, "column": 57, "nodeType": "935", "messageId": "936", "endLine": 1002, "endColumn": 60, "suggestions": "1231"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1011, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 1011, "endColumn": 28, "suggestions": "1232"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1032, "column": 36, "nodeType": "935", "messageId": "936", "endLine": 1032, "endColumn": 39, "suggestions": "1233"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1041, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 1041, "endColumn": 28, "suggestions": "1234"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1052, "column": 67, "nodeType": "935", "messageId": "936", "endLine": 1052, "endColumn": 70, "suggestions": "1235"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1061, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 1061, "endColumn": 28, "suggestions": "1236"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1072, "column": 41, "nodeType": "935", "messageId": "936", "endLine": 1072, "endColumn": 44, "suggestions": "1237"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1072, "column": 55, "nodeType": "935", "messageId": "936", "endLine": 1072, "endColumn": 58, "suggestions": "1238"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1081, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 1081, "endColumn": 28, "suggestions": "1239"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1092, "column": 39, "nodeType": "935", "messageId": "936", "endLine": 1092, "endColumn": 42, "suggestions": "1240"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1101, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 1101, "endColumn": 28, "suggestions": "1241"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1114, "column": 100, "nodeType": "935", "messageId": "936", "endLine": 1114, "endColumn": 103, "suggestions": "1242"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1127, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 1127, "endColumn": 28, "suggestions": "1243"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1137, "column": 35, "nodeType": "935", "messageId": "936", "endLine": 1137, "endColumn": 38, "suggestions": "1244"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1146, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 1146, "endColumn": 28, "suggestions": "1245"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1157, "column": 35, "nodeType": "935", "messageId": "936", "endLine": 1157, "endColumn": 38, "suggestions": "1246"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1166, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 1166, "endColumn": 28, "suggestions": "1247"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1177, "column": 61, "nodeType": "935", "messageId": "936", "endLine": 1177, "endColumn": 64, "suggestions": "1248"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1189, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 1189, "endColumn": 28, "suggestions": "1249"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1202, "column": 83, "nodeType": "935", "messageId": "936", "endLine": 1202, "endColumn": 86, "suggestions": "1250"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 1214, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 1214, "endColumn": 28, "suggestions": "1251"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 27, "column": 16, "nodeType": "935", "messageId": "936", "endLine": 27, "endColumn": 19, "suggestions": "1252"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 34, "column": 12, "nodeType": "935", "messageId": "936", "endLine": 34, "endColumn": 15, "suggestions": "1253"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 40, "column": 12, "nodeType": "935", "messageId": "936", "endLine": 40, "endColumn": 15, "suggestions": "1254"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 213, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 213, "endColumn": 28, "suggestions": "1255"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 239, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 239, "endColumn": 28, "suggestions": "1256"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 246, "column": 35, "nodeType": "935", "messageId": "936", "endLine": 246, "endColumn": 38, "suggestions": "1257"}, {"ruleId": "982", "severity": 2, "message": "1258", "line": 268, "column": 26, "nodeType": "984", "messageId": "985", "endLine": 268, "endColumn": 32, "fix": "1259"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 288, "column": 34, "nodeType": "935", "messageId": "936", "endLine": 288, "endColumn": 37, "suggestions": "1260"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 297, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 297, "endColumn": 28, "suggestions": "1261"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 334, "column": 27, "nodeType": "935", "messageId": "936", "endLine": 334, "endColumn": 30, "suggestions": "1262"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 340, "column": 34, "nodeType": "935", "messageId": "936", "endLine": 340, "endColumn": 37, "suggestions": "1263"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 360, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 360, "endColumn": 28, "suggestions": "1264"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 367, "column": 69, "nodeType": "935", "messageId": "936", "endLine": 367, "endColumn": 72, "suggestions": "1265"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 371, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 371, "endColumn": 28, "suggestions": "1266"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 378, "column": 58, "nodeType": "935", "messageId": "936", "endLine": 378, "endColumn": 61, "suggestions": "1267"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 392, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 392, "endColumn": 28, "suggestions": "1268"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 399, "column": 43, "nodeType": "935", "messageId": "936", "endLine": 399, "endColumn": 46, "suggestions": "1269"}, {"ruleId": "933", "severity": 2, "message": "934", "line": 406, "column": 25, "nodeType": "935", "messageId": "936", "endLine": 406, "endColumn": 28, "suggestions": "1270"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["1271", "1272", "1273", "1274"], ["1275", "1276", "1277", "1278"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1279", "1280"], ["1281", "1282"], ["1283", "1284"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["1285", "1286", "1287", "1288"], ["1289", "1290", "1291", "1292"], ["1293", "1294", "1295", "1296"], ["1297", "1298", "1299", "1300"], ["1301", "1302"], "Parsing error: Unexpected token. Did you mean `{'>'}` or `&gt;`?", "@typescript-eslint/no-unused-vars", "'motion' is defined but never used.", "unusedVar", "'error' is defined but never used.", "'Filter' is defined but never used.", "'setOrders' is assigned a value but never used.", "'Calendar' is defined but never used.", "'MessageCircle' is defined but never used.", ["1303", "1304"], "'DollarSign' is defined but never used.", "'Archive' is defined but never used.", "'Plus' is defined but never used.", "'Upload' is defined but never used.", "'X' is defined but never used.", "'ProductFormData' is defined but never used.", "'ProductPoints' is defined but never used.", "'Brand' is defined but never used.", "'Category' is defined but never used.", "'SubCategory' is defined but never used.", "'SubCategoryFeature' is defined but never used.", "'FeatureValue' is defined but never used.", "'ProductImage' is defined but never used.", "'removeImage' is assigned a value but never used.", "'setMainImage' is assigned a value but never used.", ["1305", "1306"], "'handleVariantPricingChange' is assigned a value but never used.", "'handleVariantRatioChange' is assigned a value but never used.", ["1307", "1308"], ["1309", "1310", "1311", "1312"], ["1313", "1314", "1315", "1316"], ["1317", "1318", "1319", "1320"], ["1321", "1322", "1323", "1324"], ["1325", "1326"], "'originalProductId' is assigned a value but never used.", ["1327", "1328"], "prefer-const", "'key' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "1329", "text": "1330"}, "'value' is never reassigned. Use 'const' instead.", {"range": "1331", "text": "1330"}, ["1332", "1333"], ["1334", "1335"], ["1336", "1337"], ["1338", "1339"], ["1340", "1341", "1342", "1343"], ["1344", "1345", "1346", "1347"], ["1348", "1349", "1350", "1351"], ["1352", "1353", "1354", "1355"], ["1356", "1357"], "'BarChart3' is defined but never used.", "'UserRoleStatistics' is defined but never used.", "'MoreVertical' is defined but never used.", "'Crown' is defined but never used.", ["1358", "1359", "1360", "1361"], ["1362", "1363"], ["1364", "1365", "1366", "1367"], ["1368", "1369", "1370", "1371"], ["1372", "1373", "1374", "1375"], ["1376", "1377", "1378", "1379"], ["1380", "1381", "1382", "1383"], "'router' is assigned a value but never used.", ["1384", "1385"], ["1386", "1387"], "'useMutation' is defined but never used.", "'queryClient' is assigned a value but never used.", ["1388", "1389"], {"range": "1390", "text": "1330"}, {"range": "1391", "text": "1330"}, ["1392", "1393"], ["1394", "1395"], ["1396", "1397"], ["1398", "1399"], ["1400", "1401", "1402", "1403"], ["1404", "1405", "1406", "1407"], ["1408", "1409", "1410", "1411"], ["1412", "1413", "1414", "1415"], ["1416", "1417"], "'err' is defined but never used.", "'Metadata' is defined but never used.", "'authError' is assigned a value but never used.", ["1418", "1419"], ["1420", "1421"], "'distributorDashboard' is defined but never used.", "'UserX' is defined but never used.", "'Target' is defined but never used.", "'Phone' is defined but never used.", ["1422", "1423"], ["1424", "1425"], "react-hooks/exhaustive-deps", "React Hook React.useEffect has a missing dependency: 'handleFilter'. Either include it or remove the dependency array.", "ArrayExpression", ["1426"], "'index' is defined but never used.", "'MembershipLevelIds' is defined but never used.", "'Tag' is defined but never used.", "'RefreshCw' is defined but never used.", ["1427", "1428"], "'discountRateLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'resetState'. Either include it or remove the dependency array.", ["1429"], "'useState' is defined but never used.", "'user' is assigned a value but never used.", "'isAuthenticated' is assigned a value but never used.", "'resetCustomerPrice' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'allNodes'. Either include it or remove the dependency array.", ["1430"], ["1431", "1432"], ["1433", "1434"], ["1435", "1436"], ["1437", "1438", "1439", "1440"], ["1441", "1442", "1443", "1444"], ["1445", "1446", "1447", "1448"], ["1449", "1450", "1451", "1452"], "'i' is defined but never used.", ["1453", "1454"], ["1455", "1456"], ["1457", "1458"], "'addressToDelete' is assigned a value but never used.", "'ProductMessage' is defined but never used.", "'useCallback' is defined but never used.", "'AuthUser' is defined but never used.", "'clearError' is assigned a value but never used.", "React Hook useMemo has a missing dependency: 'bankingInfo'. Either include it or remove the dependency array.", ["1459"], ["1460", "1461"], ["1462", "1463"], ["1464", "1465"], ["1466", "1467"], ["1468", "1469"], "'userService' is defined but never used.", "'useAuthStore' is defined but never used.", ["1470", "1471"], ["1472", "1473"], ["1474", "1475"], "React Hook useEffect has missing dependencies: 'goToNextImage', 'goToPreviousImage', and 'onClose'. Either include them or remove the dependency array. If 'onClose' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1476"], "'nextRetryIn' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setStatus' and 'status'. Either include them or remove the dependency array.", ["1477"], "React Hook useEffect has a missing dependency: 'attemptReconnect'. Either include it or remove the dependency array.", ["1478"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["1479"], ["1480", "1481", "1482", "1483"], ["1484", "1485", "1486", "1487"], "'Lock' is defined but never used.", "'ImageIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'formData'. Either include it or remove the dependency array.", ["1488"], "'VariantPricing' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAllFeatureValues'. Either include it or remove the dependency array.", ["1489"], "'_' is defined but never used.", ["1490", "1491", "1492", "1493"], ["1494", "1495", "1496", "1497"], ["1498", "1499", "1500", "1501"], ["1502", "1503", "1504", "1505"], "'imageService' is defined but never used.", ["1506", "1507"], ["1508", "1509"], ["1510", "1511", "1512", "1513"], ["1514", "1515", "1516", "1517"], ["1518", "1519", "1520", "1521"], ["1522", "1523", "1524", "1525"], ["1526", "1527", "1528", "1529"], ["1530", "1531", "1532", "1533"], "'Clock' is defined but never used.", ["1534", "1535", "1536", "1537"], ["1538", "1539"], ["1540", "1541"], "'DealershipApplicationStatus' is defined but never used.", "'mockDealershipApplications' is never reassigned. Use 'const' instead.", {"range": "1542", "text": "1543"}, "'useEffect' is defined but never used.", "'useAuth' is defined but never used.", "'useModalActions' is defined but never used.", "'variables' is defined but never used.", "'context' is defined but never used.", ["1544", "1545"], ["1546", "1547"], ["1548", "1549"], "'setUser' is assigned a value but never used.", "'clearAuth' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", ["1550", "1551"], ["1552", "1553"], ["1554", "1555"], ["1556", "1557"], ["1558", "1559"], "React Hook useMemo has a missing dependency: 'store'. Either include it or remove the dependency array.", ["1560"], ["1561"], "React Hook useEffect has a missing dependency: 'store'. Either include it or remove the dependency array.", ["1562"], ["1563"], "'FeatureDefinition' is defined but never used.", "'BrandCategory' is defined but never used.", ["1564", "1565"], "'useQueries' is defined but never used.", "'MyProduct' is defined but never used.", "'DealershipProductDetail' is defined but never used.", ["1566", "1567"], ["1568", "1569"], ["1570", "1571"], ["1572", "1573"], ["1574", "1575"], ["1576", "1577"], ["1578", "1579"], "'publicEndpoints' is assigned a value but never used.", ["1580", "1581"], ["1582", "1583"], "'refreshResponse' is assigned a value but never used.", ["1584", "1585"], ["1586", "1587"], ["1588", "1589"], ["1590", "1591"], ["1592", "1593"], ["1594", "1595"], ["1596", "1597"], ["1598", "1599"], ["1600", "1601"], ["1602", "1603"], ["1604", "1605"], ["1606", "1607"], ["1608", "1609"], ["1610", "1611"], ["1612", "1613"], ["1614", "1615"], ["1616", "1617"], ["1618", "1619"], ["1620", "1621"], ["1622", "1623"], ["1624", "1625"], ["1626", "1627"], ["1628", "1629"], ["1630", "1631"], ["1632", "1633"], ["1634", "1635"], ["1636", "1637"], ["1638", "1639"], ["1640", "1641"], ["1642", "1643"], ["1644", "1645"], ["1646", "1647"], ["1648", "1649"], ["1650", "1651"], ["1652", "1653"], ["1654", "1655"], ["1656", "1657"], ["1658", "1659"], ["1660", "1661"], ["1662", "1663"], ["1664", "1665"], ["1666", "1667"], ["1668", "1669"], ["1670", "1671"], ["1672", "1673"], ["1674", "1675"], ["1676", "1677"], ["1678", "1679"], ["1680", "1681"], ["1682", "1683"], ["1684", "1685"], ["1686", "1687"], ["1688", "1689"], ["1690", "1691"], {"range": "1692", "text": "1330"}, {"range": "1693", "text": "1330"}, ["1694", "1695"], ["1696", "1697"], ["1698", "1699"], ["1700", "1701"], ["1702", "1703"], ["1704", "1705"], {"range": "1706", "text": "1330"}, {"range": "1707", "text": "1330"}, ["1708", "1709"], ["1710", "1711"], ["1712", "1713"], ["1714", "1715"], ["1716", "1717"], ["1718", "1719"], ["1720", "1721"], ["1722", "1723"], ["1724", "1725"], ["1726", "1727"], ["1728", "1729"], ["1730", "1731"], ["1732", "1733"], ["1734", "1735"], ["1736", "1737"], ["1738", "1739"], ["1740", "1741"], ["1742", "1743"], ["1744", "1745"], ["1746", "1747"], ["1748", "1749"], ["1750", "1751"], ["1752", "1753"], ["1754", "1755"], ["1756", "1757"], ["1758", "1759"], ["1760", "1761"], ["1762", "1763"], ["1764", "1765"], ["1766", "1767"], ["1768", "1769"], ["1770", "1771"], ["1772", "1773"], "'cookie' is never reassigned. Use 'const' instead.", {"range": "1774", "text": "1775"}, ["1776", "1777"], ["1778", "1779"], ["1780", "1781"], ["1782", "1783"], ["1784", "1785"], ["1786", "1787"], ["1788", "1789"], ["1790", "1791"], ["1792", "1793"], ["1794", "1795"], ["1796", "1797"], {"messageId": "1798", "data": "1799", "fix": "1800", "desc": "1801"}, {"messageId": "1798", "data": "1802", "fix": "1803", "desc": "1804"}, {"messageId": "1798", "data": "1805", "fix": "1806", "desc": "1807"}, {"messageId": "1798", "data": "1808", "fix": "1809", "desc": "1810"}, {"messageId": "1798", "data": "1811", "fix": "1812", "desc": "1801"}, {"messageId": "1798", "data": "1813", "fix": "1814", "desc": "1804"}, {"messageId": "1798", "data": "1815", "fix": "1816", "desc": "1807"}, {"messageId": "1798", "data": "1817", "fix": "1818", "desc": "1810"}, {"messageId": "1819", "fix": "1820", "desc": "1821"}, {"messageId": "1822", "fix": "1823", "desc": "1824"}, {"messageId": "1819", "fix": "1825", "desc": "1821"}, {"messageId": "1822", "fix": "1826", "desc": "1824"}, {"messageId": "1819", "fix": "1827", "desc": "1821"}, {"messageId": "1822", "fix": "1828", "desc": "1824"}, {"messageId": "1798", "data": "1829", "fix": "1830", "desc": "1831"}, {"messageId": "1798", "data": "1832", "fix": "1833", "desc": "1834"}, {"messageId": "1798", "data": "1835", "fix": "1836", "desc": "1837"}, {"messageId": "1798", "data": "1838", "fix": "1839", "desc": "1840"}, {"messageId": "1798", "data": "1841", "fix": "1842", "desc": "1831"}, {"messageId": "1798", "data": "1843", "fix": "1844", "desc": "1834"}, {"messageId": "1798", "data": "1845", "fix": "1846", "desc": "1837"}, {"messageId": "1798", "data": "1847", "fix": "1848", "desc": "1840"}, {"messageId": "1798", "data": "1849", "fix": "1850", "desc": "1831"}, {"messageId": "1798", "data": "1851", "fix": "1852", "desc": "1834"}, {"messageId": "1798", "data": "1853", "fix": "1854", "desc": "1837"}, {"messageId": "1798", "data": "1855", "fix": "1856", "desc": "1840"}, {"messageId": "1798", "data": "1857", "fix": "1858", "desc": "1831"}, {"messageId": "1798", "data": "1859", "fix": "1860", "desc": "1834"}, {"messageId": "1798", "data": "1861", "fix": "1862", "desc": "1837"}, {"messageId": "1798", "data": "1863", "fix": "1864", "desc": "1840"}, {"messageId": "1819", "fix": "1865", "desc": "1821"}, {"messageId": "1822", "fix": "1866", "desc": "1824"}, {"messageId": "1819", "fix": "1867", "desc": "1821"}, {"messageId": "1822", "fix": "1868", "desc": "1824"}, {"messageId": "1819", "fix": "1869", "desc": "1821"}, {"messageId": "1822", "fix": "1870", "desc": "1824"}, {"messageId": "1819", "fix": "1871", "desc": "1821"}, {"messageId": "1822", "fix": "1872", "desc": "1824"}, {"messageId": "1798", "data": "1873", "fix": "1874", "desc": "1831"}, {"messageId": "1798", "data": "1875", "fix": "1876", "desc": "1834"}, {"messageId": "1798", "data": "1877", "fix": "1878", "desc": "1837"}, {"messageId": "1798", "data": "1879", "fix": "1880", "desc": "1840"}, {"messageId": "1798", "data": "1881", "fix": "1882", "desc": "1831"}, {"messageId": "1798", "data": "1883", "fix": "1884", "desc": "1834"}, {"messageId": "1798", "data": "1885", "fix": "1886", "desc": "1837"}, {"messageId": "1798", "data": "1887", "fix": "1888", "desc": "1840"}, {"messageId": "1798", "data": "1889", "fix": "1890", "desc": "1831"}, {"messageId": "1798", "data": "1891", "fix": "1892", "desc": "1834"}, {"messageId": "1798", "data": "1893", "fix": "1894", "desc": "1837"}, {"messageId": "1798", "data": "1895", "fix": "1896", "desc": "1840"}, {"messageId": "1798", "data": "1897", "fix": "1898", "desc": "1831"}, {"messageId": "1798", "data": "1899", "fix": "1900", "desc": "1834"}, {"messageId": "1798", "data": "1901", "fix": "1902", "desc": "1837"}, {"messageId": "1798", "data": "1903", "fix": "1904", "desc": "1840"}, {"messageId": "1819", "fix": "1905", "desc": "1821"}, {"messageId": "1822", "fix": "1906", "desc": "1824"}, {"messageId": "1819", "fix": "1907", "desc": "1821"}, {"messageId": "1822", "fix": "1908", "desc": "1824"}, [14801, 14817], "const [key, value]", [14801, 14817], {"messageId": "1819", "fix": "1909", "desc": "1821"}, {"messageId": "1822", "fix": "1910", "desc": "1824"}, {"messageId": "1819", "fix": "1911", "desc": "1821"}, {"messageId": "1822", "fix": "1912", "desc": "1824"}, {"messageId": "1819", "fix": "1913", "desc": "1821"}, {"messageId": "1822", "fix": "1914", "desc": "1824"}, {"messageId": "1819", "fix": "1915", "desc": "1821"}, {"messageId": "1822", "fix": "1916", "desc": "1824"}, {"messageId": "1798", "data": "1917", "fix": "1918", "desc": "1831"}, {"messageId": "1798", "data": "1919", "fix": "1920", "desc": "1834"}, {"messageId": "1798", "data": "1921", "fix": "1922", "desc": "1837"}, {"messageId": "1798", "data": "1923", "fix": "1924", "desc": "1840"}, {"messageId": "1798", "data": "1925", "fix": "1926", "desc": "1831"}, {"messageId": "1798", "data": "1927", "fix": "1928", "desc": "1834"}, {"messageId": "1798", "data": "1929", "fix": "1930", "desc": "1837"}, {"messageId": "1798", "data": "1931", "fix": "1932", "desc": "1840"}, {"messageId": "1798", "data": "1933", "fix": "1934", "desc": "1831"}, {"messageId": "1798", "data": "1935", "fix": "1936", "desc": "1834"}, {"messageId": "1798", "data": "1937", "fix": "1938", "desc": "1837"}, {"messageId": "1798", "data": "1939", "fix": "1940", "desc": "1840"}, {"messageId": "1798", "data": "1941", "fix": "1942", "desc": "1831"}, {"messageId": "1798", "data": "1943", "fix": "1944", "desc": "1834"}, {"messageId": "1798", "data": "1945", "fix": "1946", "desc": "1837"}, {"messageId": "1798", "data": "1947", "fix": "1948", "desc": "1840"}, {"messageId": "1819", "fix": "1949", "desc": "1821"}, {"messageId": "1822", "fix": "1950", "desc": "1824"}, {"messageId": "1798", "data": "1951", "fix": "1952", "desc": "1801"}, {"messageId": "1798", "data": "1953", "fix": "1954", "desc": "1804"}, {"messageId": "1798", "data": "1955", "fix": "1956", "desc": "1807"}, {"messageId": "1798", "data": "1957", "fix": "1958", "desc": "1810"}, {"messageId": "1819", "fix": "1959", "desc": "1821"}, {"messageId": "1822", "fix": "1960", "desc": "1824"}, {"messageId": "1798", "data": "1961", "fix": "1962", "desc": "1801"}, {"messageId": "1798", "data": "1963", "fix": "1964", "desc": "1804"}, {"messageId": "1798", "data": "1965", "fix": "1966", "desc": "1807"}, {"messageId": "1798", "data": "1967", "fix": "1968", "desc": "1810"}, {"messageId": "1798", "data": "1969", "fix": "1970", "desc": "1801"}, {"messageId": "1798", "data": "1971", "fix": "1972", "desc": "1804"}, {"messageId": "1798", "data": "1973", "fix": "1974", "desc": "1807"}, {"messageId": "1798", "data": "1975", "fix": "1976", "desc": "1810"}, {"messageId": "1798", "data": "1977", "fix": "1978", "desc": "1801"}, {"messageId": "1798", "data": "1979", "fix": "1980", "desc": "1804"}, {"messageId": "1798", "data": "1981", "fix": "1982", "desc": "1807"}, {"messageId": "1798", "data": "1983", "fix": "1984", "desc": "1810"}, {"messageId": "1798", "data": "1985", "fix": "1986", "desc": "1831"}, {"messageId": "1798", "data": "1987", "fix": "1988", "desc": "1834"}, {"messageId": "1798", "data": "1989", "fix": "1990", "desc": "1837"}, {"messageId": "1798", "data": "1991", "fix": "1992", "desc": "1840"}, {"messageId": "1798", "data": "1993", "fix": "1994", "desc": "1831"}, {"messageId": "1798", "data": "1995", "fix": "1996", "desc": "1834"}, {"messageId": "1798", "data": "1997", "fix": "1998", "desc": "1837"}, {"messageId": "1798", "data": "1999", "fix": "2000", "desc": "1840"}, {"messageId": "1819", "fix": "2001", "desc": "1821"}, {"messageId": "1822", "fix": "2002", "desc": "1824"}, {"messageId": "1819", "fix": "2003", "desc": "1821"}, {"messageId": "1822", "fix": "2004", "desc": "1824"}, {"messageId": "1819", "fix": "2005", "desc": "1821"}, {"messageId": "1822", "fix": "2006", "desc": "1824"}, [12917, 12933], [12917, 12933], {"messageId": "1819", "fix": "2007", "desc": "1821"}, {"messageId": "1822", "fix": "2008", "desc": "1824"}, {"messageId": "1819", "fix": "2009", "desc": "1821"}, {"messageId": "1822", "fix": "2010", "desc": "1824"}, {"messageId": "1819", "fix": "2011", "desc": "1821"}, {"messageId": "1822", "fix": "2012", "desc": "1824"}, {"messageId": "1819", "fix": "2013", "desc": "1821"}, {"messageId": "1822", "fix": "2014", "desc": "1824"}, {"messageId": "1798", "data": "2015", "fix": "2016", "desc": "1831"}, {"messageId": "1798", "data": "2017", "fix": "2018", "desc": "1834"}, {"messageId": "1798", "data": "2019", "fix": "2020", "desc": "1837"}, {"messageId": "1798", "data": "2021", "fix": "2022", "desc": "1840"}, {"messageId": "1798", "data": "2023", "fix": "2024", "desc": "1831"}, {"messageId": "1798", "data": "2025", "fix": "2026", "desc": "1834"}, {"messageId": "1798", "data": "2027", "fix": "2028", "desc": "1837"}, {"messageId": "1798", "data": "2029", "fix": "2030", "desc": "1840"}, {"messageId": "1798", "data": "2031", "fix": "2032", "desc": "1831"}, {"messageId": "1798", "data": "2033", "fix": "2034", "desc": "1834"}, {"messageId": "1798", "data": "2035", "fix": "2036", "desc": "1837"}, {"messageId": "1798", "data": "2037", "fix": "2038", "desc": "1840"}, {"messageId": "1798", "data": "2039", "fix": "2040", "desc": "1831"}, {"messageId": "1798", "data": "2041", "fix": "2042", "desc": "1834"}, {"messageId": "1798", "data": "2043", "fix": "2044", "desc": "1837"}, {"messageId": "1798", "data": "2045", "fix": "2046", "desc": "1840"}, {"messageId": "1819", "fix": "2047", "desc": "1821"}, {"messageId": "1822", "fix": "2048", "desc": "1824"}, {"messageId": "1819", "fix": "2049", "desc": "1821"}, {"messageId": "1822", "fix": "2050", "desc": "1824"}, {"messageId": "1819", "fix": "2051", "desc": "1821"}, {"messageId": "1822", "fix": "2052", "desc": "1824"}, {"messageId": "1819", "fix": "2053", "desc": "1821"}, {"messageId": "1822", "fix": "2054", "desc": "1824"}, {"messageId": "1819", "fix": "2055", "desc": "1821"}, {"messageId": "1822", "fix": "2056", "desc": "1824"}, {"desc": "2057", "fix": "2058"}, {"messageId": "1819", "fix": "2059", "desc": "1821"}, {"messageId": "1822", "fix": "2060", "desc": "1824"}, {"desc": "2061", "fix": "2062"}, {"desc": "2063", "fix": "2064"}, {"messageId": "1819", "fix": "2065", "desc": "1821"}, {"messageId": "1822", "fix": "2066", "desc": "1824"}, {"messageId": "1819", "fix": "2067", "desc": "1821"}, {"messageId": "1822", "fix": "2068", "desc": "1824"}, {"messageId": "1819", "fix": "2069", "desc": "1821"}, {"messageId": "1822", "fix": "2070", "desc": "1824"}, {"messageId": "1798", "data": "2071", "fix": "2072", "desc": "1831"}, {"messageId": "1798", "data": "2073", "fix": "2074", "desc": "1834"}, {"messageId": "1798", "data": "2075", "fix": "2076", "desc": "1837"}, {"messageId": "1798", "data": "2077", "fix": "2078", "desc": "1840"}, {"messageId": "1798", "data": "2079", "fix": "2080", "desc": "1831"}, {"messageId": "1798", "data": "2081", "fix": "2082", "desc": "1834"}, {"messageId": "1798", "data": "2083", "fix": "2084", "desc": "1837"}, {"messageId": "1798", "data": "2085", "fix": "2086", "desc": "1840"}, {"messageId": "1798", "data": "2087", "fix": "2088", "desc": "1831"}, {"messageId": "1798", "data": "2089", "fix": "2090", "desc": "1834"}, {"messageId": "1798", "data": "2091", "fix": "2092", "desc": "1837"}, {"messageId": "1798", "data": "2093", "fix": "2094", "desc": "1840"}, {"messageId": "1798", "data": "2095", "fix": "2096", "desc": "1831"}, {"messageId": "1798", "data": "2097", "fix": "2098", "desc": "1834"}, {"messageId": "1798", "data": "2099", "fix": "2100", "desc": "1837"}, {"messageId": "1798", "data": "2101", "fix": "2102", "desc": "1840"}, {"messageId": "1819", "fix": "2103", "desc": "1821"}, {"messageId": "1822", "fix": "2104", "desc": "1824"}, {"messageId": "1819", "fix": "2105", "desc": "1821"}, {"messageId": "1822", "fix": "2106", "desc": "1824"}, {"messageId": "1819", "fix": "2107", "desc": "1821"}, {"messageId": "1822", "fix": "2108", "desc": "1824"}, {"desc": "2109", "fix": "2110"}, {"messageId": "1819", "fix": "2111", "desc": "1821"}, {"messageId": "1822", "fix": "2112", "desc": "1824"}, {"messageId": "1819", "fix": "2113", "desc": "1821"}, {"messageId": "1822", "fix": "2114", "desc": "1824"}, {"messageId": "1819", "fix": "2115", "desc": "1821"}, {"messageId": "1822", "fix": "2116", "desc": "1824"}, {"messageId": "1819", "fix": "2117", "desc": "1821"}, {"messageId": "1822", "fix": "2118", "desc": "1824"}, {"messageId": "1819", "fix": "2119", "desc": "1821"}, {"messageId": "1822", "fix": "2120", "desc": "1824"}, {"messageId": "1819", "fix": "2121", "desc": "1821"}, {"messageId": "1822", "fix": "2122", "desc": "1824"}, {"messageId": "1819", "fix": "2123", "desc": "1821"}, {"messageId": "1822", "fix": "2124", "desc": "1824"}, {"messageId": "1819", "fix": "2125", "desc": "1821"}, {"messageId": "1822", "fix": "2126", "desc": "1824"}, {"desc": "2127", "fix": "2128"}, {"desc": "2129", "fix": "2130"}, {"desc": "2131", "fix": "2132"}, {"desc": "2133", "fix": "2134"}, {"messageId": "1798", "data": "2135", "fix": "2136", "desc": "1831"}, {"messageId": "1798", "data": "2137", "fix": "2138", "desc": "1834"}, {"messageId": "1798", "data": "2139", "fix": "2140", "desc": "1837"}, {"messageId": "1798", "data": "2141", "fix": "2142", "desc": "1840"}, {"messageId": "1798", "data": "2143", "fix": "2144", "desc": "1831"}, {"messageId": "1798", "data": "2145", "fix": "2146", "desc": "1834"}, {"messageId": "1798", "data": "2147", "fix": "2148", "desc": "1837"}, {"messageId": "1798", "data": "2149", "fix": "2150", "desc": "1840"}, {"desc": "2151", "fix": "2152"}, {"desc": "2153", "fix": "2154"}, {"messageId": "1798", "data": "2155", "fix": "2156", "desc": "1831"}, {"messageId": "1798", "data": "2157", "fix": "2158", "desc": "1834"}, {"messageId": "1798", "data": "2159", "fix": "2160", "desc": "1837"}, {"messageId": "1798", "data": "2161", "fix": "2162", "desc": "1840"}, {"messageId": "1798", "data": "2163", "fix": "2164", "desc": "1831"}, {"messageId": "1798", "data": "2165", "fix": "2166", "desc": "1834"}, {"messageId": "1798", "data": "2167", "fix": "2168", "desc": "1837"}, {"messageId": "1798", "data": "2169", "fix": "2170", "desc": "1840"}, {"messageId": "1798", "data": "2171", "fix": "2172", "desc": "1831"}, {"messageId": "1798", "data": "2173", "fix": "2174", "desc": "1834"}, {"messageId": "1798", "data": "2175", "fix": "2176", "desc": "1837"}, {"messageId": "1798", "data": "2177", "fix": "2178", "desc": "1840"}, {"messageId": "1798", "data": "2179", "fix": "2180", "desc": "1831"}, {"messageId": "1798", "data": "2181", "fix": "2182", "desc": "1834"}, {"messageId": "1798", "data": "2183", "fix": "2184", "desc": "1837"}, {"messageId": "1798", "data": "2185", "fix": "2186", "desc": "1840"}, {"messageId": "1819", "fix": "2187", "desc": "1821"}, {"messageId": "1822", "fix": "2188", "desc": "1824"}, {"messageId": "1819", "fix": "2189", "desc": "1821"}, {"messageId": "1822", "fix": "2190", "desc": "1824"}, {"messageId": "1798", "data": "2191", "fix": "2192", "desc": "1831"}, {"messageId": "1798", "data": "2193", "fix": "2194", "desc": "1834"}, {"messageId": "1798", "data": "2195", "fix": "2196", "desc": "1837"}, {"messageId": "1798", "data": "2197", "fix": "2198", "desc": "1840"}, {"messageId": "1798", "data": "2199", "fix": "2200", "desc": "1831"}, {"messageId": "1798", "data": "2201", "fix": "2202", "desc": "1834"}, {"messageId": "1798", "data": "2203", "fix": "2204", "desc": "1837"}, {"messageId": "1798", "data": "2205", "fix": "2206", "desc": "1840"}, {"messageId": "1798", "data": "2207", "fix": "2208", "desc": "1831"}, {"messageId": "1798", "data": "2209", "fix": "2210", "desc": "1834"}, {"messageId": "1798", "data": "2211", "fix": "2212", "desc": "1837"}, {"messageId": "1798", "data": "2213", "fix": "2214", "desc": "1840"}, {"messageId": "1798", "data": "2215", "fix": "2216", "desc": "1831"}, {"messageId": "1798", "data": "2217", "fix": "2218", "desc": "1834"}, {"messageId": "1798", "data": "2219", "fix": "2220", "desc": "1837"}, {"messageId": "1798", "data": "2221", "fix": "2222", "desc": "1840"}, {"messageId": "1798", "data": "2223", "fix": "2224", "desc": "1831"}, {"messageId": "1798", "data": "2225", "fix": "2226", "desc": "1834"}, {"messageId": "1798", "data": "2227", "fix": "2228", "desc": "1837"}, {"messageId": "1798", "data": "2229", "fix": "2230", "desc": "1840"}, {"messageId": "1798", "data": "2231", "fix": "2232", "desc": "1831"}, {"messageId": "1798", "data": "2233", "fix": "2234", "desc": "1834"}, {"messageId": "1798", "data": "2235", "fix": "2236", "desc": "1837"}, {"messageId": "1798", "data": "2237", "fix": "2238", "desc": "1840"}, {"messageId": "1798", "data": "2239", "fix": "2240", "desc": "1801"}, {"messageId": "1798", "data": "2241", "fix": "2242", "desc": "1804"}, {"messageId": "1798", "data": "2243", "fix": "2244", "desc": "1807"}, {"messageId": "1798", "data": "2245", "fix": "2246", "desc": "1810"}, {"messageId": "1819", "fix": "2247", "desc": "1821"}, {"messageId": "1822", "fix": "2248", "desc": "1824"}, {"messageId": "1819", "fix": "2249", "desc": "1821"}, {"messageId": "1822", "fix": "2250", "desc": "1824"}, [116, 2461], "const mockDealershipApplications: DealershipApplication[] = [\r\n    {\r\n        id: 1,\r\n        userId: 6,\r\n        userName: '<PERSON><PERSON><PERSON>',\r\n        userEmail: '<EMAIL>',\r\n        applicationData: {\r\n            firstName: 'Mehmet',\r\n            lastName: '<PERSON>ı<PERSON><PERSON>',\r\n            email: '<EMAIL>',\r\n            phone: '+90 ************',\r\n            mainProductCategory: 'Elektronik',\r\n            subProductCategories: ['Telefon', 'Bilgisayar', 'Aksesuar'],\r\n            estimatedProductCount: '50-100 adet',\r\n            sampleProductListUrl: 'https://example.com/products',\r\n            companyName: 'Yılmaz Elektronik Ltd. Şti.',\r\n            taxNumber: '1234567890',\r\n            taxOffice: 'İstanbul Vergi Dairesi',\r\n            companyAddress: 'Atatürk Cad. No:123 Kadıköy/İstanbul',\r\n            authorizedPersonName: 'Mehmet <PERSON>',\r\n            authorizedPersonTcId: '12345678901',\r\n            alternativeContactNumber: '+90 ************',\r\n            userAgreementAccepted: true,\r\n            dealershipAgreementAccepted: true,\r\n            privacyPolicyAccepted: true\r\n        },\r\n        status: 'pending',\r\n        submittedAt: '2024-01-15T10:30:00Z'\r\n    },\r\n    {\r\n        id: 2,\r\n        userId: 7,\r\n        userName: 'Ayşe Kaya',\r\n        userEmail: '<EMAIL>',\r\n        applicationData: {\r\n            firstName: 'Ayşe',\r\n            lastName: 'Kaya',\r\n            email: '<EMAIL>',\r\n            phone: '+90 ************',\r\n            mainProductCategory: 'Ev & Yaşam',\r\n            subProductCategories: ['Mutfak', 'Dekorasyon'],\r\n            estimatedProductCount: '20-50 adet',\r\n            companyName: 'Kaya Ev Tekstili',\r\n            taxNumber: '0987654321',\r\n            taxOffice: 'Ankara Vergi Dairesi',\r\n            companyAddress: 'Kızılay Cad. No:456 Çankaya/Ankara',\r\n            authorizedPersonName: 'Ayşe Kaya',\r\n            authorizedPersonTcId: '10987654321',\r\n            alternativeContactNumber: '+90 ************',\r\n            userAgreementAccepted: true,\r\n            dealershipAgreementAccepted: true,\r\n            privacyPolicyAccepted: true\r\n        },\r\n        status: 'approved',\r\n        submittedAt: '2024-01-10T14:20:00Z',\r\n        reviewedAt: '2024-01-12T09:15:00Z',\r\n        reviewedBy: 1,\r\n        adminNotes: 'Başvuru uygun bulunmuştur.'\r\n    }\r\n];", {"messageId": "1819", "fix": "2251", "desc": "1821"}, {"messageId": "1822", "fix": "2252", "desc": "1824"}, {"messageId": "1819", "fix": "2253", "desc": "1821"}, {"messageId": "1822", "fix": "2254", "desc": "1824"}, {"messageId": "1819", "fix": "2255", "desc": "1821"}, {"messageId": "1822", "fix": "2256", "desc": "1824"}, {"messageId": "1819", "fix": "2257", "desc": "1821"}, {"messageId": "1822", "fix": "2258", "desc": "1824"}, {"messageId": "1819", "fix": "2259", "desc": "1821"}, {"messageId": "1822", "fix": "2260", "desc": "1824"}, {"messageId": "1819", "fix": "2261", "desc": "1821"}, {"messageId": "1822", "fix": "2262", "desc": "1824"}, {"messageId": "1819", "fix": "2263", "desc": "1821"}, {"messageId": "1822", "fix": "2264", "desc": "1824"}, {"messageId": "1819", "fix": "2265", "desc": "1821"}, {"messageId": "1822", "fix": "2266", "desc": "1824"}, {"desc": "2267", "fix": "2268"}, {"desc": "2267", "fix": "2269"}, {"desc": "2270", "fix": "2271"}, {"desc": "2272", "fix": "2273"}, {"messageId": "1819", "fix": "2274", "desc": "1821"}, {"messageId": "1822", "fix": "2275", "desc": "1824"}, {"messageId": "1819", "fix": "2276", "desc": "1821"}, {"messageId": "1822", "fix": "2277", "desc": "1824"}, {"messageId": "1819", "fix": "2278", "desc": "1821"}, {"messageId": "1822", "fix": "2279", "desc": "1824"}, {"messageId": "1819", "fix": "2280", "desc": "1821"}, {"messageId": "1822", "fix": "2281", "desc": "1824"}, {"messageId": "1819", "fix": "2282", "desc": "1821"}, {"messageId": "1822", "fix": "2283", "desc": "1824"}, {"messageId": "1819", "fix": "2284", "desc": "1821"}, {"messageId": "1822", "fix": "2285", "desc": "1824"}, {"messageId": "1819", "fix": "2286", "desc": "1821"}, {"messageId": "1822", "fix": "2287", "desc": "1824"}, {"messageId": "1819", "fix": "2288", "desc": "1821"}, {"messageId": "1822", "fix": "2289", "desc": "1824"}, {"messageId": "1819", "fix": "2290", "desc": "1821"}, {"messageId": "1822", "fix": "2291", "desc": "1824"}, {"messageId": "1819", "fix": "2292", "desc": "1821"}, {"messageId": "1822", "fix": "2293", "desc": "1824"}, {"messageId": "1819", "fix": "2294", "desc": "1821"}, {"messageId": "1822", "fix": "2295", "desc": "1824"}, {"messageId": "1819", "fix": "2296", "desc": "1821"}, {"messageId": "1822", "fix": "2297", "desc": "1824"}, {"messageId": "1819", "fix": "2298", "desc": "1821"}, {"messageId": "1822", "fix": "2299", "desc": "1824"}, {"messageId": "1819", "fix": "2300", "desc": "1821"}, {"messageId": "1822", "fix": "2301", "desc": "1824"}, {"messageId": "1819", "fix": "2302", "desc": "1821"}, {"messageId": "1822", "fix": "2303", "desc": "1824"}, {"messageId": "1819", "fix": "2304", "desc": "1821"}, {"messageId": "1822", "fix": "2305", "desc": "1824"}, {"messageId": "1819", "fix": "2306", "desc": "1821"}, {"messageId": "1822", "fix": "2307", "desc": "1824"}, {"messageId": "1819", "fix": "2308", "desc": "1821"}, {"messageId": "1822", "fix": "2309", "desc": "1824"}, {"messageId": "1819", "fix": "2310", "desc": "1821"}, {"messageId": "1822", "fix": "2311", "desc": "1824"}, {"messageId": "1819", "fix": "2312", "desc": "1821"}, {"messageId": "1822", "fix": "2313", "desc": "1824"}, {"messageId": "1819", "fix": "2314", "desc": "1821"}, {"messageId": "1822", "fix": "2315", "desc": "1824"}, {"messageId": "1819", "fix": "2316", "desc": "1821"}, {"messageId": "1822", "fix": "2317", "desc": "1824"}, {"messageId": "1819", "fix": "2318", "desc": "1821"}, {"messageId": "1822", "fix": "2319", "desc": "1824"}, {"messageId": "1819", "fix": "2320", "desc": "1821"}, {"messageId": "1822", "fix": "2321", "desc": "1824"}, {"messageId": "1819", "fix": "2322", "desc": "1821"}, {"messageId": "1822", "fix": "2323", "desc": "1824"}, {"messageId": "1819", "fix": "2324", "desc": "1821"}, {"messageId": "1822", "fix": "2325", "desc": "1824"}, {"messageId": "1819", "fix": "2326", "desc": "1821"}, {"messageId": "1822", "fix": "2327", "desc": "1824"}, {"messageId": "1819", "fix": "2328", "desc": "1821"}, {"messageId": "1822", "fix": "2329", "desc": "1824"}, {"messageId": "1819", "fix": "2330", "desc": "1821"}, {"messageId": "1822", "fix": "2331", "desc": "1824"}, {"messageId": "1819", "fix": "2332", "desc": "1821"}, {"messageId": "1822", "fix": "2333", "desc": "1824"}, {"messageId": "1819", "fix": "2334", "desc": "1821"}, {"messageId": "1822", "fix": "2335", "desc": "1824"}, {"messageId": "1819", "fix": "2336", "desc": "1821"}, {"messageId": "1822", "fix": "2337", "desc": "1824"}, {"messageId": "1819", "fix": "2338", "desc": "1821"}, {"messageId": "1822", "fix": "2339", "desc": "1824"}, {"messageId": "1819", "fix": "2340", "desc": "1821"}, {"messageId": "1822", "fix": "2341", "desc": "1824"}, {"messageId": "1819", "fix": "2342", "desc": "1821"}, {"messageId": "1822", "fix": "2343", "desc": "1824"}, {"messageId": "1819", "fix": "2344", "desc": "1821"}, {"messageId": "1822", "fix": "2345", "desc": "1824"}, {"messageId": "1819", "fix": "2346", "desc": "1821"}, {"messageId": "1822", "fix": "2347", "desc": "1824"}, {"messageId": "1819", "fix": "2348", "desc": "1821"}, {"messageId": "1822", "fix": "2349", "desc": "1824"}, {"messageId": "1819", "fix": "2350", "desc": "1821"}, {"messageId": "1822", "fix": "2351", "desc": "1824"}, {"messageId": "1819", "fix": "2352", "desc": "1821"}, {"messageId": "1822", "fix": "2353", "desc": "1824"}, {"messageId": "1819", "fix": "2354", "desc": "1821"}, {"messageId": "1822", "fix": "2355", "desc": "1824"}, {"messageId": "1819", "fix": "2356", "desc": "1821"}, {"messageId": "1822", "fix": "2357", "desc": "1824"}, {"messageId": "1819", "fix": "2358", "desc": "1821"}, {"messageId": "1822", "fix": "2359", "desc": "1824"}, {"messageId": "1819", "fix": "2360", "desc": "1821"}, {"messageId": "1822", "fix": "2361", "desc": "1824"}, {"messageId": "1819", "fix": "2362", "desc": "1821"}, {"messageId": "1822", "fix": "2363", "desc": "1824"}, {"messageId": "1819", "fix": "2364", "desc": "1821"}, {"messageId": "1822", "fix": "2365", "desc": "1824"}, {"messageId": "1819", "fix": "2366", "desc": "1821"}, {"messageId": "1822", "fix": "2367", "desc": "1824"}, {"messageId": "1819", "fix": "2368", "desc": "1821"}, {"messageId": "1822", "fix": "2369", "desc": "1824"}, {"messageId": "1819", "fix": "2370", "desc": "1821"}, {"messageId": "1822", "fix": "2371", "desc": "1824"}, {"messageId": "1819", "fix": "2372", "desc": "1821"}, {"messageId": "1822", "fix": "2373", "desc": "1824"}, {"messageId": "1819", "fix": "2374", "desc": "1821"}, {"messageId": "1822", "fix": "2375", "desc": "1824"}, {"messageId": "1819", "fix": "2376", "desc": "1821"}, {"messageId": "1822", "fix": "2377", "desc": "1824"}, {"messageId": "1819", "fix": "2378", "desc": "1821"}, {"messageId": "1822", "fix": "2379", "desc": "1824"}, {"messageId": "1819", "fix": "2380", "desc": "1821"}, {"messageId": "1822", "fix": "2381", "desc": "1824"}, {"messageId": "1819", "fix": "2382", "desc": "1821"}, {"messageId": "1822", "fix": "2383", "desc": "1824"}, {"messageId": "1819", "fix": "2384", "desc": "1821"}, {"messageId": "1822", "fix": "2385", "desc": "1824"}, {"messageId": "1819", "fix": "2386", "desc": "1821"}, {"messageId": "1822", "fix": "2387", "desc": "1824"}, {"messageId": "1819", "fix": "2388", "desc": "1821"}, {"messageId": "1822", "fix": "2389", "desc": "1824"}, {"messageId": "1819", "fix": "2390", "desc": "1821"}, {"messageId": "1822", "fix": "2391", "desc": "1824"}, {"messageId": "1819", "fix": "2392", "desc": "1821"}, {"messageId": "1822", "fix": "2393", "desc": "1824"}, {"messageId": "1819", "fix": "2394", "desc": "1821"}, {"messageId": "1822", "fix": "2395", "desc": "1824"}, {"messageId": "1819", "fix": "2396", "desc": "1821"}, {"messageId": "1822", "fix": "2397", "desc": "1824"}, {"messageId": "1819", "fix": "2398", "desc": "1821"}, {"messageId": "1822", "fix": "2399", "desc": "1824"}, {"messageId": "1819", "fix": "2400", "desc": "1821"}, {"messageId": "1822", "fix": "2401", "desc": "1824"}, [33891, 33907], [33891, 33907], {"messageId": "1819", "fix": "2402", "desc": "1821"}, {"messageId": "1822", "fix": "2403", "desc": "1824"}, {"messageId": "1819", "fix": "2404", "desc": "1821"}, {"messageId": "1822", "fix": "2405", "desc": "1824"}, {"messageId": "1819", "fix": "2406", "desc": "1821"}, {"messageId": "1822", "fix": "2407", "desc": "1824"}, {"messageId": "1819", "fix": "2408", "desc": "1821"}, {"messageId": "1822", "fix": "2409", "desc": "1824"}, {"messageId": "1819", "fix": "2410", "desc": "1821"}, {"messageId": "1822", "fix": "2411", "desc": "1824"}, {"messageId": "1819", "fix": "2412", "desc": "1821"}, {"messageId": "1822", "fix": "2413", "desc": "1824"}, [36957, 36973], [36957, 36973], {"messageId": "1819", "fix": "2414", "desc": "1821"}, {"messageId": "1822", "fix": "2415", "desc": "1824"}, {"messageId": "1819", "fix": "2416", "desc": "1821"}, {"messageId": "1822", "fix": "2417", "desc": "1824"}, {"messageId": "1819", "fix": "2418", "desc": "1821"}, {"messageId": "1822", "fix": "2419", "desc": "1824"}, {"messageId": "1819", "fix": "2420", "desc": "1821"}, {"messageId": "1822", "fix": "2421", "desc": "1824"}, {"messageId": "1819", "fix": "2422", "desc": "1821"}, {"messageId": "1822", "fix": "2423", "desc": "1824"}, {"messageId": "1819", "fix": "2424", "desc": "1821"}, {"messageId": "1822", "fix": "2425", "desc": "1824"}, {"messageId": "1819", "fix": "2426", "desc": "1821"}, {"messageId": "1822", "fix": "2427", "desc": "1824"}, {"messageId": "1819", "fix": "2428", "desc": "1821"}, {"messageId": "1822", "fix": "2429", "desc": "1824"}, {"messageId": "1819", "fix": "2430", "desc": "1821"}, {"messageId": "1822", "fix": "2431", "desc": "1824"}, {"messageId": "1819", "fix": "2432", "desc": "1821"}, {"messageId": "1822", "fix": "2433", "desc": "1824"}, {"messageId": "1819", "fix": "2434", "desc": "1821"}, {"messageId": "1822", "fix": "2435", "desc": "1824"}, {"messageId": "1819", "fix": "2436", "desc": "1821"}, {"messageId": "1822", "fix": "2437", "desc": "1824"}, {"messageId": "1819", "fix": "2438", "desc": "1821"}, {"messageId": "1822", "fix": "2439", "desc": "1824"}, {"messageId": "1819", "fix": "2440", "desc": "1821"}, {"messageId": "1822", "fix": "2441", "desc": "1824"}, {"messageId": "1819", "fix": "2442", "desc": "1821"}, {"messageId": "1822", "fix": "2443", "desc": "1824"}, {"messageId": "1819", "fix": "2444", "desc": "1821"}, {"messageId": "1822", "fix": "2445", "desc": "1824"}, {"messageId": "1819", "fix": "2446", "desc": "1821"}, {"messageId": "1822", "fix": "2447", "desc": "1824"}, {"messageId": "1819", "fix": "2448", "desc": "1821"}, {"messageId": "1822", "fix": "2449", "desc": "1824"}, {"messageId": "1819", "fix": "2450", "desc": "1821"}, {"messageId": "1822", "fix": "2451", "desc": "1824"}, {"messageId": "1819", "fix": "2452", "desc": "1821"}, {"messageId": "1822", "fix": "2453", "desc": "1824"}, {"messageId": "1819", "fix": "2454", "desc": "1821"}, {"messageId": "1822", "fix": "2455", "desc": "1824"}, {"messageId": "1819", "fix": "2456", "desc": "1821"}, {"messageId": "1822", "fix": "2457", "desc": "1824"}, {"messageId": "1819", "fix": "2458", "desc": "1821"}, {"messageId": "1822", "fix": "2459", "desc": "1824"}, {"messageId": "1819", "fix": "2460", "desc": "1821"}, {"messageId": "1822", "fix": "2461", "desc": "1824"}, {"messageId": "1819", "fix": "2462", "desc": "1821"}, {"messageId": "1822", "fix": "2463", "desc": "1824"}, {"messageId": "1819", "fix": "2464", "desc": "1821"}, {"messageId": "1822", "fix": "2465", "desc": "1824"}, {"messageId": "1819", "fix": "2466", "desc": "1821"}, {"messageId": "1822", "fix": "2467", "desc": "1824"}, {"messageId": "1819", "fix": "2468", "desc": "1821"}, {"messageId": "1822", "fix": "2469", "desc": "1824"}, {"messageId": "1819", "fix": "2470", "desc": "1821"}, {"messageId": "1822", "fix": "2471", "desc": "1824"}, {"messageId": "1819", "fix": "2472", "desc": "1821"}, {"messageId": "1822", "fix": "2473", "desc": "1824"}, {"messageId": "1819", "fix": "2474", "desc": "1821"}, {"messageId": "1822", "fix": "2475", "desc": "1824"}, {"messageId": "1819", "fix": "2476", "desc": "1821"}, {"messageId": "1822", "fix": "2477", "desc": "1824"}, {"messageId": "1819", "fix": "2478", "desc": "1821"}, {"messageId": "1822", "fix": "2479", "desc": "1824"}, [9336, 9346], "const cookie", {"messageId": "1819", "fix": "2480", "desc": "1821"}, {"messageId": "1822", "fix": "2481", "desc": "1824"}, {"messageId": "1819", "fix": "2482", "desc": "1821"}, {"messageId": "1822", "fix": "2483", "desc": "1824"}, {"messageId": "1819", "fix": "2484", "desc": "1821"}, {"messageId": "1822", "fix": "2485", "desc": "1824"}, {"messageId": "1819", "fix": "2486", "desc": "1821"}, {"messageId": "1822", "fix": "2487", "desc": "1824"}, {"messageId": "1819", "fix": "2488", "desc": "1821"}, {"messageId": "1822", "fix": "2489", "desc": "1824"}, {"messageId": "1819", "fix": "2490", "desc": "1821"}, {"messageId": "1822", "fix": "2491", "desc": "1824"}, {"messageId": "1819", "fix": "2492", "desc": "1821"}, {"messageId": "1822", "fix": "2493", "desc": "1824"}, {"messageId": "1819", "fix": "2494", "desc": "1821"}, {"messageId": "1822", "fix": "2495", "desc": "1824"}, {"messageId": "1819", "fix": "2496", "desc": "1821"}, {"messageId": "1822", "fix": "2497", "desc": "1824"}, {"messageId": "1819", "fix": "2498", "desc": "1821"}, {"messageId": "1822", "fix": "2499", "desc": "1824"}, {"messageId": "1819", "fix": "2500", "desc": "1821"}, {"messageId": "1822", "fix": "2501", "desc": "1824"}, "replaceWithAlt", {"alt": "2502"}, {"range": "2503", "text": "2504"}, "Replace with `&apos;`.", {"alt": "2505"}, {"range": "2506", "text": "2507"}, "Replace with `&lsquo;`.", {"alt": "2508"}, {"range": "2509", "text": "2510"}, "Replace with `&#39;`.", {"alt": "2511"}, {"range": "2512", "text": "2513"}, "Replace with `&rsquo;`.", {"alt": "2502"}, {"range": "2514", "text": "2515"}, {"alt": "2505"}, {"range": "2516", "text": "2517"}, {"alt": "2508"}, {"range": "2518", "text": "2519"}, {"alt": "2511"}, {"range": "2520", "text": "2521"}, "suggestUnknown", {"range": "2522", "text": "2523"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "2524", "text": "2525"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "2526", "text": "2523"}, {"range": "2527", "text": "2525"}, {"range": "2528", "text": "2523"}, {"range": "2529", "text": "2525"}, {"alt": "2530"}, {"range": "2531", "text": "2532"}, "Replace with `&quot;`.", {"alt": "2533"}, {"range": "2534", "text": "2535"}, "Replace with `&ldquo;`.", {"alt": "2536"}, {"range": "2537", "text": "2538"}, "Replace with `&#34;`.", {"alt": "2539"}, {"range": "2540", "text": "2541"}, "Replace with `&rdquo;`.", {"alt": "2530"}, {"range": "2542", "text": "2543"}, {"alt": "2533"}, {"range": "2544", "text": "2545"}, {"alt": "2536"}, {"range": "2546", "text": "2547"}, {"alt": "2539"}, {"range": "2548", "text": "2549"}, {"alt": "2530"}, {"range": "2550", "text": "2551"}, {"alt": "2533"}, {"range": "2552", "text": "2553"}, {"alt": "2536"}, {"range": "2554", "text": "2555"}, {"alt": "2539"}, {"range": "2556", "text": "2557"}, {"alt": "2530"}, {"range": "2558", "text": "2559"}, {"alt": "2533"}, {"range": "2560", "text": "2561"}, {"alt": "2536"}, {"range": "2562", "text": "2563"}, {"alt": "2539"}, {"range": "2564", "text": "2565"}, {"range": "2566", "text": "2523"}, {"range": "2567", "text": "2525"}, {"range": "2568", "text": "2523"}, {"range": "2569", "text": "2525"}, {"range": "2570", "text": "2523"}, {"range": "2571", "text": "2525"}, {"range": "2572", "text": "2523"}, {"range": "2573", "text": "2525"}, {"alt": "2530"}, {"range": "2574", "text": "2532"}, {"alt": "2533"}, {"range": "2575", "text": "2535"}, {"alt": "2536"}, {"range": "2576", "text": "2538"}, {"alt": "2539"}, {"range": "2577", "text": "2541"}, {"alt": "2530"}, {"range": "2578", "text": "2543"}, {"alt": "2533"}, {"range": "2579", "text": "2545"}, {"alt": "2536"}, {"range": "2580", "text": "2547"}, {"alt": "2539"}, {"range": "2581", "text": "2549"}, {"alt": "2530"}, {"range": "2582", "text": "2551"}, {"alt": "2533"}, {"range": "2583", "text": "2553"}, {"alt": "2536"}, {"range": "2584", "text": "2555"}, {"alt": "2539"}, {"range": "2585", "text": "2557"}, {"alt": "2530"}, {"range": "2586", "text": "2559"}, {"alt": "2533"}, {"range": "2587", "text": "2561"}, {"alt": "2536"}, {"range": "2588", "text": "2563"}, {"alt": "2539"}, {"range": "2589", "text": "2565"}, {"range": "2590", "text": "2523"}, {"range": "2591", "text": "2525"}, {"range": "2592", "text": "2523"}, {"range": "2593", "text": "2525"}, {"range": "2594", "text": "2523"}, {"range": "2595", "text": "2525"}, {"range": "2596", "text": "2523"}, {"range": "2597", "text": "2525"}, {"range": "2598", "text": "2523"}, {"range": "2599", "text": "2525"}, {"range": "2600", "text": "2523"}, {"range": "2601", "text": "2525"}, {"alt": "2530"}, {"range": "2602", "text": "2532"}, {"alt": "2533"}, {"range": "2603", "text": "2535"}, {"alt": "2536"}, {"range": "2604", "text": "2538"}, {"alt": "2539"}, {"range": "2605", "text": "2541"}, {"alt": "2530"}, {"range": "2606", "text": "2543"}, {"alt": "2533"}, {"range": "2607", "text": "2545"}, {"alt": "2536"}, {"range": "2608", "text": "2547"}, {"alt": "2539"}, {"range": "2609", "text": "2549"}, {"alt": "2530"}, {"range": "2610", "text": "2551"}, {"alt": "2533"}, {"range": "2611", "text": "2553"}, {"alt": "2536"}, {"range": "2612", "text": "2555"}, {"alt": "2539"}, {"range": "2613", "text": "2557"}, {"alt": "2530"}, {"range": "2614", "text": "2559"}, {"alt": "2533"}, {"range": "2615", "text": "2561"}, {"alt": "2536"}, {"range": "2616", "text": "2563"}, {"alt": "2539"}, {"range": "2617", "text": "2565"}, {"range": "2618", "text": "2523"}, {"range": "2619", "text": "2525"}, {"alt": "2502"}, {"range": "2620", "text": "2621"}, {"alt": "2505"}, {"range": "2622", "text": "2623"}, {"alt": "2508"}, {"range": "2624", "text": "2625"}, {"alt": "2511"}, {"range": "2626", "text": "2627"}, {"range": "2628", "text": "2523"}, {"range": "2629", "text": "2525"}, {"alt": "2502"}, {"range": "2630", "text": "2631"}, {"alt": "2505"}, {"range": "2632", "text": "2633"}, {"alt": "2508"}, {"range": "2634", "text": "2635"}, {"alt": "2511"}, {"range": "2636", "text": "2637"}, {"alt": "2502"}, {"range": "2638", "text": "2631"}, {"alt": "2505"}, {"range": "2639", "text": "2633"}, {"alt": "2508"}, {"range": "2640", "text": "2635"}, {"alt": "2511"}, {"range": "2641", "text": "2637"}, {"alt": "2502"}, {"range": "2642", "text": "2643"}, {"alt": "2505"}, {"range": "2644", "text": "2645"}, {"alt": "2508"}, {"range": "2646", "text": "2647"}, {"alt": "2511"}, {"range": "2648", "text": "2649"}, {"alt": "2530"}, {"range": "2650", "text": "2651"}, {"alt": "2533"}, {"range": "2652", "text": "2653"}, {"alt": "2536"}, {"range": "2654", "text": "2655"}, {"alt": "2539"}, {"range": "2656", "text": "2657"}, {"alt": "2530"}, {"range": "2658", "text": "2659"}, {"alt": "2533"}, {"range": "2660", "text": "2661"}, {"alt": "2536"}, {"range": "2662", "text": "2663"}, {"alt": "2539"}, {"range": "2664", "text": "2665"}, {"range": "2666", "text": "2523"}, {"range": "2667", "text": "2525"}, {"range": "2668", "text": "2523"}, {"range": "2669", "text": "2525"}, {"range": "2670", "text": "2523"}, {"range": "2671", "text": "2525"}, {"range": "2672", "text": "2523"}, {"range": "2673", "text": "2525"}, {"range": "2674", "text": "2523"}, {"range": "2675", "text": "2525"}, {"range": "2676", "text": "2523"}, {"range": "2677", "text": "2525"}, {"range": "2678", "text": "2523"}, {"range": "2679", "text": "2525"}, {"alt": "2530"}, {"range": "2680", "text": "2532"}, {"alt": "2533"}, {"range": "2681", "text": "2535"}, {"alt": "2536"}, {"range": "2682", "text": "2538"}, {"alt": "2539"}, {"range": "2683", "text": "2541"}, {"alt": "2530"}, {"range": "2684", "text": "2543"}, {"alt": "2533"}, {"range": "2685", "text": "2545"}, {"alt": "2536"}, {"range": "2686", "text": "2547"}, {"alt": "2539"}, {"range": "2687", "text": "2549"}, {"alt": "2530"}, {"range": "2688", "text": "2551"}, {"alt": "2533"}, {"range": "2689", "text": "2553"}, {"alt": "2536"}, {"range": "2690", "text": "2555"}, {"alt": "2539"}, {"range": "2691", "text": "2557"}, {"alt": "2530"}, {"range": "2692", "text": "2559"}, {"alt": "2533"}, {"range": "2693", "text": "2561"}, {"alt": "2536"}, {"range": "2694", "text": "2563"}, {"alt": "2539"}, {"range": "2695", "text": "2565"}, {"range": "2696", "text": "2523"}, {"range": "2697", "text": "2525"}, {"range": "2698", "text": "2523"}, {"range": "2699", "text": "2525"}, {"range": "2700", "text": "2523"}, {"range": "2701", "text": "2525"}, {"range": "2702", "text": "2523"}, {"range": "2703", "text": "2525"}, {"range": "2704", "text": "2523"}, {"range": "2705", "text": "2525"}, "Update the dependencies array to be: [searchTerm, levelFilter, statusFilter, handleFilter]", {"range": "2706", "text": "2707"}, {"range": "2708", "text": "2523"}, {"range": "2709", "text": "2525"}, "Update the dependencies array to be: [resetState]", {"range": "2710", "text": "2711"}, "Update the dependencies array to be: [isInitialLoad, allNodes.length, width, height, offsetX, offsetY, zoom, allNodes]", {"range": "2712", "text": "2713"}, {"range": "2714", "text": "2523"}, {"range": "2715", "text": "2525"}, {"range": "2716", "text": "2523"}, {"range": "2717", "text": "2525"}, {"range": "2718", "text": "2523"}, {"range": "2719", "text": "2525"}, {"alt": "2530"}, {"range": "2720", "text": "2721"}, {"alt": "2533"}, {"range": "2722", "text": "2723"}, {"alt": "2536"}, {"range": "2724", "text": "2725"}, {"alt": "2539"}, {"range": "2726", "text": "2727"}, {"alt": "2530"}, {"range": "2728", "text": "2729"}, {"alt": "2533"}, {"range": "2730", "text": "2731"}, {"alt": "2536"}, {"range": "2732", "text": "2733"}, {"alt": "2539"}, {"range": "2734", "text": "2735"}, {"alt": "2530"}, {"range": "2736", "text": "2737"}, {"alt": "2533"}, {"range": "2738", "text": "2739"}, {"alt": "2536"}, {"range": "2740", "text": "2741"}, {"alt": "2539"}, {"range": "2742", "text": "2743"}, {"alt": "2530"}, {"range": "2744", "text": "2745"}, {"alt": "2533"}, {"range": "2746", "text": "2747"}, {"alt": "2536"}, {"range": "2748", "text": "2749"}, {"alt": "2539"}, {"range": "2750", "text": "2751"}, {"range": "2752", "text": "2523"}, {"range": "2753", "text": "2525"}, {"range": "2754", "text": "2523"}, {"range": "2755", "text": "2525"}, {"range": "2756", "text": "2523"}, {"range": "2757", "text": "2525"}, "Update the dependencies array to be: [bankingInfo]", {"range": "2758", "text": "2759"}, {"range": "2760", "text": "2523"}, {"range": "2761", "text": "2525"}, {"range": "2762", "text": "2523"}, {"range": "2763", "text": "2525"}, {"range": "2764", "text": "2523"}, {"range": "2765", "text": "2525"}, {"range": "2766", "text": "2523"}, {"range": "2767", "text": "2525"}, {"range": "2768", "text": "2523"}, {"range": "2769", "text": "2525"}, {"range": "2770", "text": "2523"}, {"range": "2771", "text": "2525"}, {"range": "2772", "text": "2523"}, {"range": "2773", "text": "2525"}, {"range": "2774", "text": "2523"}, {"range": "2775", "text": "2525"}, "Update the dependencies array to be: [isOpen, currentImageIndex, images.length, onClose, goToPreviousImage, goToNextImage]", {"range": "2776", "text": "2777"}, "Update the dependencies array to be: [setStatus, status]", {"range": "2778", "text": "2779"}, "Update the dependencies array to be: [attemptReconnect, status]", {"range": "2780", "text": "2781"}, "Update the dependencies array to be: [handleClose, modalOpen]", {"range": "2782", "text": "2783"}, {"alt": "2530"}, {"range": "2784", "text": "2530"}, {"alt": "2533"}, {"range": "2785", "text": "2533"}, {"alt": "2536"}, {"range": "2786", "text": "2536"}, {"alt": "2539"}, {"range": "2787", "text": "2539"}, {"alt": "2530"}, {"range": "2788", "text": "2530"}, {"alt": "2533"}, {"range": "2789", "text": "2533"}, {"alt": "2536"}, {"range": "2790", "text": "2536"}, {"alt": "2539"}, {"range": "2791", "text": "2539"}, "Update the dependencies array to be: [formData.pricing.price, formData.pricing.ratios.pvRatio, formData.pricing.ratios.cvRatio, formData.pricing.ratios.spRatio, formData]", {"range": "2792", "text": "2793"}, "Update the dependencies array to be: [isOpen, availableFeatures, loadAllFeatureValues]", {"range": "2794", "text": "2795"}, {"alt": "2530"}, {"range": "2796", "text": "2797"}, {"alt": "2533"}, {"range": "2798", "text": "2799"}, {"alt": "2536"}, {"range": "2800", "text": "2801"}, {"alt": "2539"}, {"range": "2802", "text": "2803"}, {"alt": "2530"}, {"range": "2804", "text": "2805"}, {"alt": "2533"}, {"range": "2806", "text": "2807"}, {"alt": "2536"}, {"range": "2808", "text": "2809"}, {"alt": "2539"}, {"range": "2810", "text": "2811"}, {"alt": "2530"}, {"range": "2812", "text": "2813"}, {"alt": "2533"}, {"range": "2814", "text": "2815"}, {"alt": "2536"}, {"range": "2816", "text": "2817"}, {"alt": "2539"}, {"range": "2818", "text": "2819"}, {"alt": "2530"}, {"range": "2820", "text": "2821"}, {"alt": "2533"}, {"range": "2822", "text": "2823"}, {"alt": "2536"}, {"range": "2824", "text": "2825"}, {"alt": "2539"}, {"range": "2826", "text": "2827"}, {"range": "2828", "text": "2523"}, {"range": "2829", "text": "2525"}, {"range": "2830", "text": "2523"}, {"range": "2831", "text": "2525"}, {"alt": "2530"}, {"range": "2832", "text": "2530"}, {"alt": "2533"}, {"range": "2833", "text": "2533"}, {"alt": "2536"}, {"range": "2834", "text": "2536"}, {"alt": "2539"}, {"range": "2835", "text": "2539"}, {"alt": "2530"}, {"range": "2836", "text": "2530"}, {"alt": "2533"}, {"range": "2837", "text": "2533"}, {"alt": "2536"}, {"range": "2838", "text": "2536"}, {"alt": "2539"}, {"range": "2839", "text": "2539"}, {"alt": "2530"}, {"range": "2840", "text": "2530"}, {"alt": "2533"}, {"range": "2841", "text": "2533"}, {"alt": "2536"}, {"range": "2842", "text": "2536"}, {"alt": "2539"}, {"range": "2843", "text": "2539"}, {"alt": "2530"}, {"range": "2844", "text": "2530"}, {"alt": "2533"}, {"range": "2845", "text": "2533"}, {"alt": "2536"}, {"range": "2846", "text": "2536"}, {"alt": "2539"}, {"range": "2847", "text": "2539"}, {"alt": "2530"}, {"range": "2848", "text": "2530"}, {"alt": "2533"}, {"range": "2849", "text": "2533"}, {"alt": "2536"}, {"range": "2850", "text": "2536"}, {"alt": "2539"}, {"range": "2851", "text": "2539"}, {"alt": "2530"}, {"range": "2852", "text": "2530"}, {"alt": "2533"}, {"range": "2853", "text": "2533"}, {"alt": "2536"}, {"range": "2854", "text": "2536"}, {"alt": "2539"}, {"range": "2855", "text": "2539"}, {"alt": "2502"}, {"range": "2856", "text": "2857"}, {"alt": "2505"}, {"range": "2858", "text": "2859"}, {"alt": "2508"}, {"range": "2860", "text": "2861"}, {"alt": "2511"}, {"range": "2862", "text": "2863"}, {"range": "2864", "text": "2523"}, {"range": "2865", "text": "2525"}, {"range": "2866", "text": "2523"}, {"range": "2867", "text": "2525"}, {"range": "2868", "text": "2523"}, {"range": "2869", "text": "2525"}, {"range": "2870", "text": "2523"}, {"range": "2871", "text": "2525"}, {"range": "2872", "text": "2523"}, {"range": "2873", "text": "2525"}, {"range": "2874", "text": "2523"}, {"range": "2875", "text": "2525"}, {"range": "2876", "text": "2523"}, {"range": "2877", "text": "2525"}, {"range": "2878", "text": "2523"}, {"range": "2879", "text": "2525"}, {"range": "2880", "text": "2523"}, {"range": "2881", "text": "2525"}, {"range": "2882", "text": "2523"}, {"range": "2883", "text": "2525"}, "Update the dependencies array to be: [productId, store]", {"range": "2884", "text": "2885"}, {"range": "2886", "text": "2885"}, "Update the dependencies array to be: [query.data, query.error, store]", {"range": "2887", "text": "2888"}, "Update the dependencies array to be: [query.isLoading, store]", {"range": "2889", "text": "2890"}, {"range": "2891", "text": "2523"}, {"range": "2892", "text": "2525"}, {"range": "2893", "text": "2523"}, {"range": "2894", "text": "2525"}, {"range": "2895", "text": "2523"}, {"range": "2896", "text": "2525"}, {"range": "2897", "text": "2523"}, {"range": "2898", "text": "2525"}, {"range": "2899", "text": "2523"}, {"range": "2900", "text": "2525"}, {"range": "2901", "text": "2523"}, {"range": "2902", "text": "2525"}, {"range": "2903", "text": "2523"}, {"range": "2904", "text": "2525"}, {"range": "2905", "text": "2523"}, {"range": "2906", "text": "2525"}, {"range": "2907", "text": "2523"}, {"range": "2908", "text": "2525"}, {"range": "2909", "text": "2523"}, {"range": "2910", "text": "2525"}, {"range": "2911", "text": "2523"}, {"range": "2912", "text": "2525"}, {"range": "2913", "text": "2523"}, {"range": "2914", "text": "2525"}, {"range": "2915", "text": "2523"}, {"range": "2916", "text": "2525"}, {"range": "2917", "text": "2523"}, {"range": "2918", "text": "2525"}, {"range": "2919", "text": "2523"}, {"range": "2920", "text": "2525"}, {"range": "2921", "text": "2523"}, {"range": "2922", "text": "2525"}, {"range": "2923", "text": "2523"}, {"range": "2924", "text": "2525"}, {"range": "2925", "text": "2523"}, {"range": "2926", "text": "2525"}, {"range": "2927", "text": "2523"}, {"range": "2928", "text": "2525"}, {"range": "2929", "text": "2523"}, {"range": "2930", "text": "2525"}, {"range": "2931", "text": "2523"}, {"range": "2932", "text": "2525"}, {"range": "2933", "text": "2523"}, {"range": "2934", "text": "2525"}, {"range": "2935", "text": "2523"}, {"range": "2936", "text": "2525"}, {"range": "2937", "text": "2523"}, {"range": "2938", "text": "2525"}, {"range": "2939", "text": "2523"}, {"range": "2940", "text": "2525"}, {"range": "2941", "text": "2523"}, {"range": "2942", "text": "2525"}, {"range": "2943", "text": "2523"}, {"range": "2944", "text": "2525"}, {"range": "2945", "text": "2523"}, {"range": "2946", "text": "2525"}, {"range": "2947", "text": "2523"}, {"range": "2948", "text": "2525"}, {"range": "2949", "text": "2523"}, {"range": "2950", "text": "2525"}, {"range": "2951", "text": "2523"}, {"range": "2952", "text": "2525"}, {"range": "2953", "text": "2523"}, {"range": "2954", "text": "2525"}, {"range": "2955", "text": "2523"}, {"range": "2956", "text": "2525"}, {"range": "2957", "text": "2523"}, {"range": "2958", "text": "2525"}, {"range": "2959", "text": "2523"}, {"range": "2960", "text": "2525"}, {"range": "2961", "text": "2523"}, {"range": "2962", "text": "2525"}, {"range": "2963", "text": "2523"}, {"range": "2964", "text": "2525"}, {"range": "2965", "text": "2523"}, {"range": "2966", "text": "2525"}, {"range": "2967", "text": "2523"}, {"range": "2968", "text": "2525"}, {"range": "2969", "text": "2523"}, {"range": "2970", "text": "2525"}, {"range": "2971", "text": "2523"}, {"range": "2972", "text": "2525"}, {"range": "2973", "text": "2523"}, {"range": "2974", "text": "2525"}, {"range": "2975", "text": "2523"}, {"range": "2976", "text": "2525"}, {"range": "2977", "text": "2523"}, {"range": "2978", "text": "2525"}, {"range": "2979", "text": "2523"}, {"range": "2980", "text": "2525"}, {"range": "2981", "text": "2523"}, {"range": "2982", "text": "2525"}, {"range": "2983", "text": "2523"}, {"range": "2984", "text": "2525"}, {"range": "2985", "text": "2523"}, {"range": "2986", "text": "2525"}, {"range": "2987", "text": "2523"}, {"range": "2988", "text": "2525"}, {"range": "2989", "text": "2523"}, {"range": "2990", "text": "2525"}, {"range": "2991", "text": "2523"}, {"range": "2992", "text": "2525"}, {"range": "2993", "text": "2523"}, {"range": "2994", "text": "2525"}, {"range": "2995", "text": "2523"}, {"range": "2996", "text": "2525"}, {"range": "2997", "text": "2523"}, {"range": "2998", "text": "2525"}, {"range": "2999", "text": "2523"}, {"range": "3000", "text": "2525"}, {"range": "3001", "text": "2523"}, {"range": "3002", "text": "2525"}, {"range": "3003", "text": "2523"}, {"range": "3004", "text": "2525"}, {"range": "3005", "text": "2523"}, {"range": "3006", "text": "2525"}, {"range": "3007", "text": "2523"}, {"range": "3008", "text": "2525"}, {"range": "3009", "text": "2523"}, {"range": "3010", "text": "2525"}, {"range": "3011", "text": "2523"}, {"range": "3012", "text": "2525"}, {"range": "3013", "text": "2523"}, {"range": "3014", "text": "2525"}, {"range": "3015", "text": "2523"}, {"range": "3016", "text": "2525"}, {"range": "3017", "text": "2523"}, {"range": "3018", "text": "2525"}, {"range": "3019", "text": "2523"}, {"range": "3020", "text": "2525"}, {"range": "3021", "text": "2523"}, {"range": "3022", "text": "2525"}, {"range": "3023", "text": "2523"}, {"range": "3024", "text": "2525"}, {"range": "3025", "text": "2523"}, {"range": "3026", "text": "2525"}, {"range": "3027", "text": "2523"}, {"range": "3028", "text": "2525"}, {"range": "3029", "text": "2523"}, {"range": "3030", "text": "2525"}, {"range": "3031", "text": "2523"}, {"range": "3032", "text": "2525"}, {"range": "3033", "text": "2523"}, {"range": "3034", "text": "2525"}, {"range": "3035", "text": "2523"}, {"range": "3036", "text": "2525"}, {"range": "3037", "text": "2523"}, {"range": "3038", "text": "2525"}, {"range": "3039", "text": "2523"}, {"range": "3040", "text": "2525"}, {"range": "3041", "text": "2523"}, {"range": "3042", "text": "2525"}, {"range": "3043", "text": "2523"}, {"range": "3044", "text": "2525"}, {"range": "3045", "text": "2523"}, {"range": "3046", "text": "2525"}, {"range": "3047", "text": "2523"}, {"range": "3048", "text": "2525"}, {"range": "3049", "text": "2523"}, {"range": "3050", "text": "2525"}, {"range": "3051", "text": "2523"}, {"range": "3052", "text": "2525"}, {"range": "3053", "text": "2523"}, {"range": "3054", "text": "2525"}, {"range": "3055", "text": "2523"}, {"range": "3056", "text": "2525"}, {"range": "3057", "text": "2523"}, {"range": "3058", "text": "2525"}, {"range": "3059", "text": "2523"}, {"range": "3060", "text": "2525"}, {"range": "3061", "text": "2523"}, {"range": "3062", "text": "2525"}, {"range": "3063", "text": "2523"}, {"range": "3064", "text": "2525"}, {"range": "3065", "text": "2523"}, {"range": "3066", "text": "2525"}, {"range": "3067", "text": "2523"}, {"range": "3068", "text": "2525"}, {"range": "3069", "text": "2523"}, {"range": "3070", "text": "2525"}, {"range": "3071", "text": "2523"}, {"range": "3072", "text": "2525"}, {"range": "3073", "text": "2523"}, {"range": "3074", "text": "2525"}, {"range": "3075", "text": "2523"}, {"range": "3076", "text": "2525"}, {"range": "3077", "text": "2523"}, {"range": "3078", "text": "2525"}, {"range": "3079", "text": "2523"}, {"range": "3080", "text": "2525"}, {"range": "3081", "text": "2523"}, {"range": "3082", "text": "2525"}, {"range": "3083", "text": "2523"}, {"range": "3084", "text": "2525"}, {"range": "3085", "text": "2523"}, {"range": "3086", "text": "2525"}, {"range": "3087", "text": "2523"}, {"range": "3088", "text": "2525"}, {"range": "3089", "text": "2523"}, {"range": "3090", "text": "2525"}, {"range": "3091", "text": "2523"}, {"range": "3092", "text": "2525"}, {"range": "3093", "text": "2523"}, {"range": "3094", "text": "2525"}, {"range": "3095", "text": "2523"}, {"range": "3096", "text": "2525"}, {"range": "3097", "text": "2523"}, {"range": "3098", "text": "2525"}, {"range": "3099", "text": "2523"}, {"range": "3100", "text": "2525"}, {"range": "3101", "text": "2523"}, {"range": "3102", "text": "2525"}, {"range": "3103", "text": "2523"}, {"range": "3104", "text": "2525"}, {"range": "3105", "text": "2523"}, {"range": "3106", "text": "2525"}, {"range": "3107", "text": "2523"}, {"range": "3108", "text": "2525"}, {"range": "3109", "text": "2523"}, {"range": "3110", "text": "2525"}, {"range": "3111", "text": "2523"}, {"range": "3112", "text": "2525"}, {"range": "3113", "text": "2523"}, {"range": "3114", "text": "2525"}, {"range": "3115", "text": "2523"}, {"range": "3116", "text": "2525"}, {"range": "3117", "text": "2523"}, {"range": "3118", "text": "2525"}, "&apos;", [39286, 39351], "Tebrikler! Artık Say Global&apos;de satıcı olarak ürün satabilirsiniz.", "&lsquo;", [39286, 39351], "Tebrikler! Artık Say Global&lsquo;de satıcı olarak ürün satabilirsiniz.", "&#39;", [39286, 39351], "Tebrikler! Artık Say Global&#39;de satıcı olarak ürün satabilirsiniz.", "&rsquo;", [39286, 39351], "Tebrikler! Artık Say Global&rsquo;de satıcı olarak ürün satabilirsiniz.", [45042, 45092], "Say Global&apos;de satıcı olarak ürün satmaya başlayın.", [45042, 45092], "Say Global&lsquo;de satıcı olarak ürün satmaya başlayın.", [45042, 45092], "Say Global&#39;de satıcı olarak ürün satmaya başlayın.", [45042, 45092], "Say Global&rsquo;de satıcı olarak ürün satmaya başlayın.", [80871, 80874], "unknown", [80871, 80874], "never", [3074, 3077], [3074, 3077], [7398, 7401], [7398, 7401], "&quot;", [16839, 16986], "\n                                    <PERSON><PERSON><PERSON><PERSON><PERSON> &quot;<PERSON><PERSON>, <PERSON><PERSON>i ve Detaylar Seçin\" kı<PERSON>ı ile düzenleyebilirsiniz.\n                                ", "&ldquo;", [16839, 16986], "\n                                    Yukarıdaki &ldquo;<PERSON><PERSON>, <PERSON>gori ve Detaylar Seçin\" kısmı ile düzenleyebilirsiniz.\n                                ", "&#34;", [16839, 16986], "\n                                    Yu<PERSON><PERSON><PERSON>i &#34;<PERSON><PERSON>, <PERSON><PERSON><PERSON> ve <PERSON>\" kı<PERSON><PERSON> ile düzenleyebilirsiniz.\n                                ", "&rdquo;", [16839, 16986], "\n                                    Yukarıdaki &rdquo;<PERSON><PERSON>, <PERSON><PERSON>i ve Detaylar Seçin\" kısmı ile düzenleyebilirsiniz.\n                                ", [16839, 16986], "\n                                    <PERSON><PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON>, <PERSON><PERSON>i ve Detaylar Seçin&quot; kı<PERSON><PERSON> ile düzenleyebilirsiniz.\n                                ", [16839, 16986], "\n                                    <PERSON><PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON>, <PERSON><PERSON>i ve Detaylar Seçin&ldquo; kı<PERSON><PERSON> ile düzenleyebilirsiniz.\n                                ", [16839, 16986], "\n                                    <PERSON><PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON>, <PERSON><PERSON><PERSON> ve Detaylar Seçin&#34; kı<PERSON><PERSON> ile düzenleyebilirsiniz.\n                                ", [16839, 16986], "\n                                    <PERSON><PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON>, <PERSON><PERSON>i ve Detaylar Seçin&rdquo; kı<PERSON><PERSON> ile düzenleyebilirsiniz.\n                                ", [22271, 22467], "\n                                        Her bir varyantın fotoğraflarını, varyant tablosundaki &quot;Detay\" butonuna tıklayarak açılan pencereden yönetebilirsiniz.\n                                    ", [22271, 22467], "\n                                        Her bir varyantın fotoğraflarını, varyant tablosundaki &ldquo;Detay\" butonuna tıklayarak açılan pencereden yönetebilirsiniz.\n                                    ", [22271, 22467], "\n                                        Her bir varyantın fotoğraflarını, varyant tablosundaki &#34;Detay\" butonuna tıklayarak açılan pencereden yönetebilirsiniz.\n                                    ", [22271, 22467], "\n                                        Her bir varyantın fotoğraflarını, varyant tablosundaki &rdquo;Detay\" butonuna tıklayarak açılan pencereden yönetebilirsiniz.\n                                    ", [22271, 22467], "\n                                        Her bir varyantın fotoğraflarını, varyant tablosundaki \"Detay&quot; butonuna tıklayarak açılan pencereden yönetebilirsiniz.\n                                    ", [22271, 22467], "\n                                        Her bir varyantın fotoğraflarını, varyant tablosundaki \"Detay&ldquo; butonuna tıklayarak açılan pencereden yönetebilirsiniz.\n                                    ", [22271, 22467], "\n                                        Her bir varyantın fotoğraflarını, varyant tablosundaki \"Detay&#34; but<PERSON>una tıklayarak açılan pencereden yönetebilirsiniz.\n                                    ", [22271, 22467], "\n                                        Her bir varyantın fotoğraflarını, varyant tablosundaki \"Detay&rdquo; butonuna tıklayarak açılan pencereden yönetebilirsiniz.\n                                    ", [26459, 26462], [26459, 26462], [4895, 4898], [4895, 4898], [3458, 3461], [3458, 3461], [8929, 8932], [8929, 8932], [18327, 18474], [18327, 18474], [18327, 18474], [18327, 18474], [18327, 18474], [18327, 18474], [18327, 18474], [18327, 18474], [24937, 25133], [24937, 25133], [24937, 25133], [24937, 25133], [24937, 25133], [24937, 25133], [24937, 25133], [24937, 25133], [29121, 29124], [29121, 29124], [6180, 6183], [6180, 6183], [15051, 15054], [15051, 15054], [21022, 21025], [21022, 21025], [21121, 21124], [21121, 21124], [21217, 21220], [21217, 21220], [29660, 29807], [29660, 29807], [29660, 29807], [29660, 29807], [29660, 29807], [29660, 29807], [29660, 29807], [29660, 29807], [36306, 36502], [36306, 36502], [36306, 36502], [36306, 36502], [36306, 36502], [36306, 36502], [36306, 36502], [36306, 36502], [40493, 40496], [40493, 40496], [8304, 8390], "\r\n                    Say Global&apos;den en güncel haberler ve duyurular\r\n                ", [8304, 8390], "\r\n                    Say Global&lsquo;den en güncel haberler ve duyurular\r\n                ", [8304, 8390], "\r\n                    Say Global&#39;den en güncel haberler ve duyurular\r\n                ", [8304, 8390], "\r\n                    Say Global&rsquo;den en güncel haberler ve duyurular\r\n                ", [2519, 2522], [2519, 2522], [47183, 47257], "&apos;ni okudum ve kabul ediyorum.\n                                            ", [47183, 47257], "&lsquo;ni okudum ve kabul ediyorum.\n                                            ", [47183, 47257], "&#39;ni okudum ve kabul ediyorum.\n                                            ", [47183, 47257], "&rsquo;ni okudum ve kabul ediyorum.\n                                            ", [48893, 48967], [48893, 48967], [48893, 48967], [48893, 48967], [50601, 50675], "&apos;nı okudum ve kabul ediyorum.\n                                            ", [50601, 50675], "&lsquo;nı okudum ve kabul ediyorum.\n                                            ", [50601, 50675], "&#39;nı okudum ve kabul ediyorum.\n                                            ", [50601, 50675], "&rsquo;nı okudum ve kabul ediyorum.\n                                            ", [66591, 66854], "\n                                            Bilgileriniz kontrol edildi ve başvurunuz gönderilmeye hazır. &quot;Başvuruyu Gönder\" butonuna tıklayarak\n                                            başvurunuzu tamamlayabilirsiniz.\n                                        ", [66591, 66854], "\n                                            Bilgileriniz kontrol edildi ve başvurunuz gönderilmeye hazır. &ldquo;Başvuruyu Gönder\" butonuna tıklayarak\n                                            başvurunuzu tamamlayabilirsiniz.\n                                        ", [66591, 66854], "\n                                            Bilgileriniz kontrol edildi ve başvurunuz gönderilmeye hazır. &#34;Başvuruyu Gönder\" butonuna tıklayarak\n                                            başvurunuzu tamamlayabilirsiniz.\n                                        ", [66591, 66854], "\n                                            Bilgileriniz kontrol edildi ve başvurunuz gönderilmeye hazır. &rdquo;Başvuruyu Gönder\" butonuna tıklayarak\n                                            başvurunuzu tamamlayabilirsiniz.\n                                        ", [66591, 66854], "\n                                            Bilgileriniz kontrol edildi ve başvurunuz gönderilmeye hazır. \"Başvuruyu Gönder&quot; butonuna tıklayarak\n                                            başvurunuzu tamamlayabilirsiniz.\n                                        ", [66591, 66854], "\n                                            Bilgileriniz kontrol edildi ve başvurunuz gönderilmeye hazır. \"Başvuruyu Gönder&ldquo; butonuna tıklayarak\n                                            başvurunuzu tamamlayabilirsiniz.\n                                        ", [66591, 66854], "\n                                            Bilgileriniz kontrol edildi ve başvurunuz gönderilmeye hazır. \"Başvuruyu Gönder&#34; butonuna tıklayarak\n                                            başvurunuzu tamamlayabilirsiniz.\n                                        ", [66591, 66854], "\n                                            Bilgileriniz kontrol edildi ve başvurunuz gönderilmeye hazır. \"Başvuruyu Gönder&rdquo; butonuna tıklayarak\n                                            başvurunuzu tamamlayabilirsiniz.\n                                        ", [1047, 1050], [1047, 1050], [1060, 1063], [1060, 1063], [3995, 3998], [3995, 3998], [13673, 13676], [13673, 13676], [22813, 22816], [22813, 22816], [23015, 23018], [23015, 23018], [23111, 23114], [23111, 23114], [32489, 32636], [32489, 32636], [32489, 32636], [32489, 32636], [32489, 32636], [32489, 32636], [32489, 32636], [32489, 32636], [37921, 38117], [37921, 38117], [37921, 38117], [37921, 38117], [37921, 38117], [37921, 38117], [37921, 38117], [37921, 38117], [42112, 42115], [42112, 42115], [1831, 1834], [1831, 1834], [2386, 2389], [2386, 2389], [2388, 2391], [2388, 2391], [2401, 2404], [2401, 2404], [3427, 3466], "[searchTerm, levelFilter, statusFilter, handleFilter]", [1349, 1352], [1349, 1352], [2990, 2992], "[resetState]", [14890, 14961], "[isInitialLoad, allNodes.length, width, height, offsetX, offsetY, zoom, allNodes]", [19320, 19323], [19320, 19323], [19326, 19329], [19326, 19329], [19603, 19606], [19603, 19606], [31484, 31582], "\n                                    • &quot;<PERSON><PERSON>\" üyeyi ağacın en sol boş pozisyonuna yerleştirir", [31484, 31582], "\n                                    • &ldquo;<PERSON><PERSON>\" üyeyi ağacın en sol boş pozisyonuna yerleştirir", [31484, 31582], "\n                                    • &#34;<PERSON><PERSON>\" üyeyi ağacın en sol boş pozisyonuna yerleştirir", [31484, 31582], "\n                                    • &rdquo;<PERSON><PERSON>\" üyeyi ağacın en sol boş pozisyonuna yerleştirir", [31484, 31582], "\n                                    • \"Sola Ekle&quot; üyeyi ağacın en sol boş pozisyonuna yerleştirir", [31484, 31582], "\n                                    • \"Sola Ekle&ldquo; üyeyi ağacın en sol boş pozisyonuna yerleştirir", [31484, 31582], "\n                                    • \"Sola Ekle&#34; <PERSON><PERSON><PERSON> ağacın en sol boş pozisyonuna yerleştirir", [31484, 31582], "\n                                    • \"Sola Ekle&rdquo; üyeyi ağacın en sol boş pozisyonuna yerleştirir", [31588, 31686], "\n                                    • &quot;<PERSON><PERSON><PERSON>\" üyeyi ağacın en sağ boş pozisyonuna yerleştirir", [31588, 31686], "\n                                    • &ldquo;<PERSON><PERSON><PERSON>\" üyeyi ağacın en sağ boş pozisyonuna yerleştirir", [31588, 31686], "\n                                    • &#34;<PERSON><PERSON><PERSON>\" üyeyi ağacın en sağ boş pozisyonuna yerleştirir", [31588, 31686], "\n                                    • &rdquo;<PERSON><PERSON><PERSON>\" üyeyi ağacın en sağ boş pozisyonuna yerleştirir", [31588, 31686], "\n                                    • \"Sağa <PERSON>&quot; üyeyi ağacın en sağ boş pozisyonuna yerleştirir", [31588, 31686], "\n                                    • \"Sağa Ekle&ldquo; üyeyi ağacın en sağ boş pozisyonuna yerleştirir", [31588, 31686], "\n                                    • \"<PERSON>ğa <PERSON>&#34; <PERSON><PERSON>yi ağacın en sağ boş pozisyonuna yerleştirir", [31588, 31686], "\n                                    • \"Sağa Ekle&rdquo; üyeyi ağacın en sağ boş pozisyonuna yerleştirir", [37187, 37190], [37187, 37190], [37264, 37267], [37264, 37267], [37951, 37954], [37951, 37954], [1117, 1293], "[bankingInfo]", [466, 469], [466, 469], [5958, 5961], [5958, 5961], [10388, 10391], [10388, 10391], [11438, 11441], [11438, 11441], [20870, 20873], [20870, 20873], [3141, 3144], [3141, 3144], [5083, 5086], [5083, 5086], [234, 237], [234, 237], [1575, 1617], "[isOpen, currentImageIndex, images.length, onClose, goToPreviousImage, goToNextImage]", [4547, 4549], "[setStatus, status]", [9388, 9396], "[attemptReconnect, status]", [2544, 2555], "[handleClose, modalOpen]", [6827, 6828], [6827, 6828], [6827, 6828], [6827, 6828], [6841, 6842], [6841, 6842], [6841, 6842], [6841, 6842], [2731, 2858], "[formData.pricing.price, formData.pricing.ratios.pvRatio, formData.pricing.ratios.cvRatio, formData.pricing.ratios.spRatio, formData]", [1216, 1243], "[isOpen, availableFeatures, loadAllFeatureValues]", [10911, 11188], "\n                                                    Örne<PERSON><PERSON> &quot;<PERSON><PERSON>\" <PERSON><PERSON><PERSON> ve <PERSON>, \"<PERSON>en\" i<PERSON><PERSON> S ve M <PERSON>çerseniz,\n                                                    4 varyant oluşur: Kırmızı-S, K<PERSON>rm<PERSON>zı-M, Mavi-S, Mavi-M\n                                                ", [10911, 11188], "\n                                                    Örneğin &ldquo;<PERSON><PERSON>\" <PERSON><PERSON><PERSON> ve <PERSON>, \"Beden\" i<PERSON>in S ve M <PERSON>çerseniz,\n                                                    4 varyant oluşur: Kırmızı-S, K<PERSON>rmızı-M, Mavi-S, Mavi-M\n                                                ", [10911, 11188], "\n                                                    Örneğin &#34;<PERSON><PERSON>\" <PERSON><PERSON><PERSON>, \"<PERSON><PERSON>\" <PERSON><PERSON><PERSON> S ve <PERSON>z,\n                                                    4 varyant oluşur: Kırmızı-S, <PERSON><PERSON>rm<PERSON>z<PERSON><PERSON><PERSON>, Mavi-S, Mavi-<PERSON>\n                                                ", [10911, 11188], "\n                                                    Örneğin &rdquo;<PERSON><PERSON>\" <PERSON><PERSON><PERSON> ve <PERSON>, \"Beden\" i<PERSON>in S ve M <PERSON>çerseniz,\n                                                    4 varyant oluşur: Kırmızı-S, K<PERSON>rmızı-M, Mavi-S, Mavi-M\n                                                ", [10911, 11188], "\n                                                    <PERSON><PERSON><PERSON><PERSON> \"Ren<PERSON>&quot; <PERSON><PERSON><PERSON> ve <PERSON>, \"<PERSON>en\" i<PERSON><PERSON> S ve M <PERSON>çerseniz,\n                                                    4 varyant oluşur: Kırmızı-S, <PERSON><PERSON>rm<PERSON>z<PERSON><PERSON>M, Mavi-S, Mavi-M\n                                                ", [10911, 11188], "\n                                                    <PERSON><PERSON><PERSON><PERSON> \"Renk&ldquo; <PERSON><PERSON><PERSON> ve <PERSON>, \"Beden\" i<PERSON><PERSON> S ve M <PERSON>çerseniz,\n                                                    4 varyant oluşur: Kırmızı-S, K<PERSON>rmızı-M, Mavi-S, Mavi-M\n                                                ", [10911, 11188], "\n                                                    <PERSON><PERSON><PERSON><PERSON> \"Renk&#34; <PERSON><PERSON><PERSON>, \"<PERSON><PERSON>\" <PERSON><PERSON><PERSON> <PERSON> ve <PERSON>,\n                                                    4 varyant oluşur: Kırmızı-S, <PERSON><PERSON><PERSON><PERSON>z<PERSON><PERSON><PERSON>, Mavi-S, Mavi-<PERSON>\n                                                ", [10911, 11188], "\n                                                    <PERSON><PERSON><PERSON><PERSON> \"Renk&rdquo; <PERSON><PERSON><PERSON> ve <PERSON>, \"Beden\" i<PERSON><PERSON> S ve <PERSON>çerseniz,\n                                                    4 varyant oluşur: Kırmızı-S, K<PERSON>rmızı-M, Mavi-S, Mavi-M\n                                                ", [10911, 11188], "\n                                                    <PERSON><PERSON><PERSON><PERSON> \"Ren<PERSON>\" i<PERSON><PERSON> ve Mavi, &quot;<PERSON><PERSON>\" i<PERSON><PERSON> S ve M <PERSON>çerseniz,\n                                                    4 varyant oluşur: Kırmızı-S, K<PERSON>rmızı-M, Mavi-S, Mavi-M\n                                                ", [10911, 11188], "\n                                                    <PERSON><PERSON><PERSON><PERSON> \"Renk\" i<PERSON><PERSON> ve Mavi, &ldquo;<PERSON>en\" i<PERSON>in S ve M <PERSON>çerseniz,\n                                                    4 varyant oluşur: Kırmızı-S, K<PERSON>rmızı-M, Mavi-S, Mavi-M\n                                                ", [10911, 11188], "\n                                                    <PERSON><PERSON><PERSON><PERSON> \"Ren<PERSON>\" i<PERSON><PERSON> ve Mavi, &#34;<PERSON><PERSON>\" i<PERSON><PERSON> S ve <PERSON>,\n                                                    4 varyant oluşur: Kırmızı-S, <PERSON><PERSON>rm<PERSON>z<PERSON><PERSON><PERSON>, Mavi-S, Mavi-<PERSON>\n                                                ", [10911, 11188], "\n                                                    <PERSON><PERSON><PERSON><PERSON> \"Renk\" i<PERSON><PERSON> ve Mavi, &rdquo;<PERSON>en\" i<PERSON>in S ve M <PERSON>çerseniz,\n                                                    4 varyant oluşur: Kırmızı-S, K<PERSON>rmızı-M, Mavi-S, Mavi-M\n                                                ", [10911, 11188], "\n                                                    <PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" i<PERSON><PERSON>, \"<PERSON><PERSON>&quot; i<PERSON><PERSON> <PERSON> ve M <PERSON>çerseniz,\n                                                    4 varyant oluşur: Kırmızı-S, <PERSON><PERSON>rm<PERSON>zı<PERSON>M, Mavi-S, Mavi-M\n                                                ", [10911, 11188], "\n                                                    <PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" i<PERSON><PERSON>, \"Beden&ldquo; i<PERSON><PERSON> S ve M <PERSON>çerseniz,\n                                                    4 varyant oluşur: Kırmızı-S, K<PERSON>rmızı-M, Mavi-S, Mavi-M\n                                                ", [10911, 11188], "\n                                                    <PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" i<PERSON><PERSON>, \"Beden&#34; <PERSON><PERSON><PERSON> <PERSON> ve <PERSON>,\n                                                    4 varyant oluşur: Kırmızı-S, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>vi-S, <PERSON>vi-<PERSON>\n                                                ", [10911, 11188], "\n                                                    <PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" i<PERSON><PERSON>, \"Beden&rdquo; i<PERSON><PERSON> <PERSON> ve <PERSON>çerseniz,\n                                                    4 varyant oluşur: Kırmızı-S, K<PERSON>rmızı-M, Mavi-S, Mavi-M\n                                                ", [3458, 3461], [3458, 3461], [476, 479], [476, 479], [6308, 6309], [6308, 6309], [6308, 6309], [6308, 6309], [6339, 6340], [6339, 6340], [6339, 6340], [6339, 6340], [6672, 6673], [6672, 6673], [6672, 6673], [6672, 6673], [6694, 6695], [6694, 6695], [6694, 6695], [6694, 6695], [7150, 7151], [7150, 7151], [7150, 7151], [7150, 7151], [7172, 7173], [7172, 7173], [7172, 7173], [7172, 7173], [4891, 5069], "\r\n                            Sağlıklı yaşam ürünleri ve kozmetik alanında Türkiye&apos;nin lider markası. Doğal içerikli ürünlerle sağlığınıza sağlık katın.\r\n                        ", [4891, 5069], "\r\n                            Sağlıklı yaşam ürünleri ve kozmetik alanında Türkiye&lsquo;nin lider markası. Doğal içerikli ürünlerle sağlığınıza sağlık katın.\r\n                        ", [4891, 5069], "\r\n                            Sağlıklı yaşam ürünleri ve kozmetik alanında Türkiye&#39;nin lider markası. Doğal içerikli ürünlerle sağlığınıza sağlık katın.\r\n                        ", [4891, 5069], "\r\n                            Sağlıklı yaşam ürünleri ve kozmetik alanında Türkiye&rsquo;nin lider markası. Doğal içerikli ürünlerle sağlığınıza sağlık katın.\r\n                        ", [807, 810], [807, 810], [1408, 1411], [1408, 1411], [4609, 4612], [4609, 4612], [4962, 4965], [4962, 4965], [5787, 5790], [5787, 5790], [9155, 9158], [9155, 9158], [9606, 9609], [9606, 9609], [11975, 11978], [11975, 11978], [14079, 14082], [14079, 14082], [14379, 14382], [14379, 14382], [632, 667], "[productId, store]", [813, 844], [3094, 3119], "[query.data, query.error, store]", [3268, 3285], "[query.isLoading, store]", [1302, 1305], [1302, 1305], [2891, 2894], [2891, 2894], [4776, 4779], [4776, 4779], [9661, 9664], [9661, 9664], [1414, 1417], [1414, 1417], [2183, 2186], [2183, 2186], [2218, 2221], [2218, 2221], [2271, 2274], [2271, 2274], [6170, 6173], [6170, 6173], [6379, 6382], [6379, 6382], [8067, 8070], [8067, 8070], [10673, 10676], [10673, 10676], [14092, 14095], [14092, 14095], [14611, 14614], [14611, 14614], [14625, 14628], [14625, 14628], [15007, 15010], [15007, 15010], [15338, 15341], [15338, 15341], [15729, 15732], [15729, 15732], [16062, 16065], [16062, 16065], [16480, 16483], [16480, 16483], [17153, 17156], [17153, 17156], [17534, 17537], [17534, 17537], [18026, 18029], [18026, 18029], [18114, 18117], [18114, 18117], [18880, 18883], [18880, 18883], [19258, 19261], [19258, 19261], [19492, 19495], [19492, 19495], [19891, 19894], [19891, 19894], [20276, 20279], [20276, 20279], [20690, 20693], [20690, 20693], [21059, 21062], [21059, 21062], [21571, 21574], [21571, 21574], [21920, 21923], [21920, 21923], [22292, 22295], [22292, 22295], [22691, 22694], [22691, 22694], [23075, 23078], [23075, 23078], [23499, 23502], [23499, 23502], [23894, 23897], [23894, 23897], [24338, 24341], [24338, 24341], [24737, 24740], [24737, 24740], [25159, 25162], [25159, 25162], [25536, 25539], [25536, 25539], [25927, 25930], [25927, 25930], [26327, 26330], [26327, 26330], [26341, 26344], [26341, 26344], [26849, 26852], [26849, 26852], [27217, 27220], [27217, 27220], [27231, 27234], [27231, 27234], [27753, 27756], [27753, 27756], [28075, 28078], [28075, 28078], [28089, 28092], [28089, 28092], [28594, 28597], [28594, 28597], [28935, 28938], [28935, 28938], [29336, 29339], [29336, 29339], [29703, 29706], [29703, 29706], [30338, 30341], [30338, 30341], [31230, 31233], [31230, 31233], [31628, 31631], [31628, 31631], [32023, 32026], [32023, 32026], [32456, 32459], [32456, 32459], [32820, 32823], [32820, 32823], [33331, 33334], [33331, 33334], [33673, 33676], [33673, 33676], [33687, 33690], [33687, 33690], [34419, 34422], [34419, 34422], [35035, 35038], [35035, 35038], [35435, 35438], [35435, 35438], [35824, 35827], [35824, 35827], [36282, 36285], [36282, 36285], [36699, 36702], [36699, 36702], [37761, 37764], [37761, 37764], [38161, 38164], [38161, 38164], [38551, 38554], [38551, 38554], [38975, 38978], [38975, 38978], [39404, 39407], [39404, 39407], [39910, 39913], [39910, 39913], [40263, 40266], [40263, 40266], [40672, 40675], [40672, 40675], [41310, 41313], [41310, 41313], [41668, 41671], [41668, 41671], [42071, 42074], [42071, 42074], [42526, 42529], [42526, 42529], [42890, 42893], [42890, 42893], [42904, 42907], [42904, 42907], [43294, 43297], [43294, 43297], [43655, 43658], [43655, 43658], [44023, 44026], [44023, 44026], [44468, 44471], [44468, 44471], [44982, 44985], [44982, 44985], [45306, 45309], [45306, 45309], [45674, 45677], [45674, 45677], [46039, 46042], [46039, 46042], [46409, 46412], [46409, 46412], [46789, 46792], [46789, 46792], [47377, 47380], [47377, 47380], [47962, 47965], [47962, 47965], [48457, 48460], [48457, 48460], [601, 604], [601, 604], [703, 706], [703, 706], [799, 802], [799, 802], [7352, 7355], [7352, 7355], [8319, 8322], [8319, 8322], [8479, 8482], [8479, 8482], [10335, 10338], [10335, 10338], [10751, 10754], [10751, 10754], [12146, 12149], [12146, 12149], [12315, 12318], [12315, 12318], [13209, 13212], [13209, 13212], [13426, 13429], [13426, 13429], [13599, 13602], [13599, 13602], [13797, 13800], [13797, 13800], [14320, 14323], [14320, 14323], [14511, 14514], [14511, 14514], [14819, 14822], [14819, 14822]]