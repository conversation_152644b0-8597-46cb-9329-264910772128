"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6681],{35159:(e,a,r)=>{r.d(a,{n:()=>o});var t=r(65453),s=r(46786);let o=(0,t.v)()((0,s.lt)(e=>({user:null,isLoading:!1,isAuthenticated:!1,error:null,clearError:()=>e({error:null}),setLoading:a=>e({isLoading:a})}),{name:"auth-store"}))},46893:(e,a,r)=>{r.d(a,{S:()=>t});let t={LOGIN:"/api/Account/login",LOGOUT:"/api/Account/logout",REGISTER:"/api/Account/register",REFRESH_TOKEN:"/api/Account/refresh",USER_INFO:"/api/User/me",PROFILE_INFO:"/api/User/getprofileinfo",UPDATE_PROFILE:"/api/User/update-profile",PROFILE_PICTURE:"/api/User/profile-picture",DELETE_PROFILE_PICTURE:"/api/User/delete-profile-picture",USER_ADDRESSES:"/api/User/addresses",DELETE_ADDRESS:"/api/User/delete-address",SET_DEFAULT_ADDRESS:"/api/User/set-default-address",CREATE_ADDRESS:"/api/User/add-address",ADD_REFERENCE:"/api/User/Account/add-reference",MAKE_ADMIN:"/api/User/make-admin",TEST_AUTH:"/api/Account/test",DEBUG_CLAIMS:"/api/Account/debug-claims",GET_BRANDS:"/api/Products/brands",GET_SUBCATEGORIES:"/api/Products",CREATE_FULL_PRODUCT:"/api/Products/create-full-product",CREATE_DEALERSHIP_PRODUCT:"/api/Products/create-dealership-product",ADD_PRODUCT_IMAGE:"/api/Products/addimage",DELETE_PRODUCT_IMAGE:"/api/Products/image",REPLACE_PRODUCT_IMAGE:"/api/Products/image/replace",GET_ADMIN_PRODUCTS:"/api/Products/products-admin",DELETE_PRODUCT:"/api/Products/deleteproduct",GET_PRODUCTS:"/api/Products/getproducts",GET_PRODUCT_DETAIL:"/api/Products/productdetail",GET_PRODUCT_VARIANTS:"/api/Products/{productId}/variants",GET_ADMIN_PRODUCT_STATISTICS:"/api/Products/admin/product-statistics",GET_CATEGORIES_BY_BRAND:"/api/Products/categories-by-brand",GET_FEATURE_VALUES:"/api/Products/feature-values",GET_PRODUCT_FEATURES:"/api/Products/product-features",CREATE_BRAND:"/api/Products/createbrand",CREATE_CATEGORY:"/api/Products/createcategory",CREATE_SUBCATEGORY:"/api/Products/createsubcategory",CREATE_FEATURE_DEFINITION:"/api/Products/createdefinition",CREATE_FEATURE_VALUE:"/api/Products/createvalue",CREATE_SUBCATEGORY_FEATURE:"/api/Products/createsubfeature",CREATE_BRAND_CATEGORY:"/api/Products/brand-category",GET_CATEGORIES:"/api/Products/categories",GET_SUBCATEGORY_FEATURES:"/api/Products/subcategoryfeatures",GET_SUBCATEGORY_FEATURES_BY_ID:"/api/Products/subcategoryfeatures/{subCategoryId}",GET_FEATURE_VALUES_BY_DEFINITION_ID:"/api/Products/feature-values/{definitionId}",GET_PRODUCT_FEATURES_BY_PRODUCT_ID:"/api/Products/product-features/{productId}",GET_CATEGORIES_BY_BRAND_ID:"/api/Products/categories-by-brand/{brandId}",GET_SUBCATEGORIES_BY_CATEGORY:"/api/Products/{categoryId}/subcategories",GET_ALL_FEATURE_DEFINITIONS:"/api/Products/features",UPDATE_FULL_PRODUCT:"/api/Products/update-full-product",UPDATE_PRODUCT_STATUS:"/api/Products/updateproductstatus",GET_PRODUCT_MESSAGE:"/api/Products/productmessage",GET_USERS:"/api/User/getusers",GET_USER_ROLE_COUNTS:"/api/User/user-role-counts",GET_DISCOUNT_RATE:"/api/User/discount-rate",UPDATE_CART_TYPE:"/api/User/update-cart-type",GET_MY_PRODUCTS:"/api/Products/my-products",GET_MY_PRODUCT_STATISTICS:"/api/Products/myproductstats",UPDATE_SIMPLE_PRODUCT:"/api/Products/update-simple",GET_DEALERSHIP_PRODUCT_DETAIL:"/api/Products/dealership-product-detail",FILTER_PRODUCTS:"/api/catalog/products/filter",GET_REFERENCE_DATA:"/api/catalog/reference-data",GET_CATALOG_PRODUCT_DETAIL:"/api/catalog/product-detail",ADD_TO_CART:"/api/User/cart/add",GET_CART_ITEMS:"/api/User/cart/items",GET_CART_COUNT:"/api/User/cart/count",REMOVE_FROM_CART:"/api/User/cart/remove",UPDATE_CART_QUANTITY:"/api/User/cart/update-quantity"}},53234:(e,a,r)=>{r.d(a,{M:()=>l,P:()=>n});var t=r(32960),s=r(80722),o=r(87220);let l={all:["discountRate"],user:e=>[...l.all,"user",e]},n=()=>{let{user:e,isAuthenticated:a}=(0,o.A)();return(0,t.I)({queryKey:l.user((null==e?void 0:e.id)||null),queryFn:async()=>{let e=await s.Dv.getDiscountRate();if(!e.success)throw Error(e.error||"İndirim oranı alınamadı");return e.data},enabled:a&&!!(null==e?void 0:e.id),staleTime:3e5,refetchOnWindowFocus:!1,refetchOnMount:!0,retry:(e,a)=>{var r;return(null==a||null==(r=a.response)?void 0:r.status)!==401&&e<2},retryDelay:e=>Math.min(1e3*2**e,3e4)})}},57038:(e,a,r)=>{r.d(a,{w:()=>o});var t=r(65453),s=r(46786);let o=(0,t.v)()((0,s.Zr)(e=>({isCustomerPrice:!1,setIsCustomerPrice:a=>e({isCustomerPrice:a}),resetCustomerPrice:()=>e({isCustomerPrice:!1}),_hasHydrated:!1,setHasHydrated:a=>e({_hasHydrated:a})}),{name:"customer-price-store",onRehydrateStorage:()=>e=>{null==e||e.setHasHydrated(!0)}}))},59959:(e,a,r)=>{r.d(a,{y:()=>o});var t=r(80722),s=r(46893);let o={clearAuthCookies(){console.log("\uD83E\uDDF9 Auth cookieleri temizleniyor..."),["AccessToken","RefreshToken","AuthToken","Token"].forEach(e=>{document.cookie=e+"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/",document.cookie=e+"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain="+window.location.hostname,document.cookie=e+"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=."+window.location.hostname}),console.log("\uD83E\uDDF9 Auth cookieleri temizlendi")},async login(e,a){console.log("\uD83D\uDEAA AuthService login başlıyor...",{email:e});let r=localStorage.getItem("hasLoggedOut");console.log("\uD83D\uDD0D Login başlangıcında logout flag kontrol\xfc:",{hasLoggedOut:r}),r&&(console.log("\uD83E\uDDF9 Eski logout flag temizleniyor (yeni login)"),localStorage.removeItem("hasLoggedOut"));let o={email:e,password:a};console.log("\uD83D\uDCE4 G\xf6nderilecek veri:",o),console.log("\uD83D\uDCE1 API URL:",t.Ay.defaults.baseURL),console.log("\uD83D\uDD27 API Config:",{baseURL:t.Ay.defaults.baseURL,withCredentials:t.Ay.defaults.withCredentials,headers:t.Ay.defaults.headers});try{var l,n,i,d,c,u,g,y,p,E,m,h,A,T,f,S,v,k,D,R;console.log("\uD83D\uDCE1 POST ".concat(s.S.LOGIN," \xe7ağrısı yapılıyor..."));let e=await t.Ay.post(s.S.LOGIN,o);console.log("\uD83D\uDCE1 Login response:",e.data),console.log("\uD83D\uDCCA Response status:",e.status),console.log("\uD83C\uDF6A Response headers:",e.headers),console.log("\uD83C\uDF6A Backend Set-Cookie ile token g\xf6nderdi"),console.log("\uD83D\uDD0D Response structure debug:",{data:e.data,status:e.status,message:null==(l=e.data)?void 0:l.message,hasData:!!e.data,statusType:null==(n=e.data)?void 0:n.statusType,dataKeys:e.data?Object.keys(e.data):[]});let a=200===e.status,r=e.headers["set-cookie"]||e.headers["Set-Cookie"]||e.headers["SET-COOKIE"],_=(null==(i=e.data)?void 0:i.message)==="Giriş başarılı."||(null==(d=e.data)?void 0:d.message)==="Login successful"||(null==(u=e.data)||null==(c=u.message)?void 0:c.includes("başarılı"))||(null==(y=e.data)||null==(g=y.message)?void 0:g.includes("successful")),P=(null==(p=e.data)?void 0:p.status)===0||(null==(E=e.data)?void 0:E.status)===200||(null==(m=e.data)?void 0:m.success)===!0,I=!(null==(A=e.data)||null==(h=A.message)?void 0:h.includes("hatalı"))&&!(null==(f=e.data)||null==(T=f.message)?void 0:T.includes("error"))&&!(null==(v=e.data)||null==(S=v.message)?void 0:S.includes("failed"))&&!(null==(D=e.data)||null==(k=D.message)?void 0:k.includes("invalid"))&&!(null==(R=e.data)?void 0:R.error),C=a&&(_||P||I);if(console.log("\uD83D\uDD0D Success detection:",{httpSuccess:a,hasSetCookie:!!r,messageSuccess:_,statusSuccess:P,noErrorMessage:I,finalSuccess:C}),C)return console.log("✅ Login başarılı!"),console.log("\uD83C\uDF6A Backend Set-Cookie header ile token g\xf6nderdi"),console.log("\uD83D\uDD04 Cookie browser tarafından otomatik set edilecek"),!0;return console.log("❌ Login başarısız - Response criteria not met"),this.clearAuthCookies(),!1}catch(e){throw e}},async register(e){console.log("\uD83D\uDCE4 Register request:",e);try{let a=await t.Ay.post(s.S.REGISTER,e);console.log("✅ Register response:",a.data);let r=a.data;return{success:0===r.status,message:r.message,user:r.data?{id:0,firstName:e.firstName,lastName:e.lastName,email:e.email,phoneNumber:e.phoneNumber||""}:void 0}}catch(e){throw console.error("❌ Register error:",e),e}},refreshToken:async()=>(await t.Ay.get(s.S.REFRESH_TOKEN)).data,async logout(){try{await t.Ay.get(s.S.LOGOUT),console.log("✅ Backend logout başarılı")}catch(e){console.error("❌ Backend logout hatası:",e)}finally{for(let e of(localStorage.clear(),console.log("\uD83E\uDDF9 localStorage temizlendi"),document.cookie.split(";"))){let a=e.indexOf("="),r=a>-1?e.substr(0,a).trim():e.trim();r&&(document.cookie="".concat(r,"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"),document.cookie="".concat(r,"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=").concat(window.location.hostname),document.cookie="".concat(r,"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.").concat(window.location.hostname))}console.log("\uD83C\uDF6A Cookieler temizlendi"),sessionStorage.clear(),console.log("\uD83E\uDDF9 sessionStorage temizlendi")}},async getUserInfo(){console.log("\uD83D\uDD0E AuthService getUserInfo başlıyor..."),console.log("\uD83C\uDF6A withCredentials ile cookie otomatik g\xf6nderilecek");try{let e=await t.Ay.get(s.S.USER_INFO);return console.log("✅ getUserInfo başarılı:",e.data),e.data}catch(a){var e;if((null==(e=a.response)?void 0:e.status)===401)return console.log("ℹ️ Kullanıcı giriş yapmamış. (Bu beklenen bir durumdur)"),null;throw console.error("❌ getUserInfo sırasında beklenmedik bir hata oluştu:",a),a}},async addReference(e){try{await t.Ay.post(s.S.ADD_REFERENCE,{referansCode:e})}catch(e){throw console.error("❌ Referans eklenirken hata:",e),e}},async makeAdmin(e){try{await t.Ay.post(s.S.MAKE_ADMIN,{userIdOrEmail:e})}catch(e){throw console.error("❌ Admin yapma hatası:",e),e}},test:async()=>(await t.Ay.get(s.S.TEST_AUTH)).data,debugClaims:async()=>(await t.Ay.get(s.S.DEBUG_CLAIMS)).data,async getProfileInfo(){console.log("\uD83D\uDD0E AuthService getProfileInfo başlıyor...");try{let e=await t.Ay.get(s.S.PROFILE_INFO);if(console.log("✅ getProfileInfo raw response:",e.data),0===e.data.status&&e.data.data)return console.log("✅ getProfileInfo başarılı:",e.data.data),e.data.data;throw Error(e.data.message||"Profil bilgileri alınamadı")}catch(e){throw console.error("❌ getProfileInfo sırasında hata oluştu:",e),e}},async updateProfile(e){try{return(await t.Ay.post(s.S.UPDATE_PROFILE,e)).data}catch(e){throw console.error("❌ Update profile error:",e),e}},async updateProfilePicture(e){console.log("\uD83D\uDCE4 Update profile picture request");try{let a=new FormData;a.append("file",e);let r=await t.Ay.put(s.S.PROFILE_PICTURE,a,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ Update profile picture response:",r.data),r.data}catch(e){throw console.error("❌ Update profile picture error:",e),e}},async deleteProfilePicture(){console.log("\uD83D\uDDD1️ Delete profile picture request");try{let e=await t.Ay.delete(s.S.DELETE_PROFILE_PICTURE);return console.log("✅ Delete profile picture response:",e.data),e.data}catch(e){throw console.error("❌ Delete profile picture error:",e),e}}}},66681:(e,a,r)=>{r.d(a,{Ng:()=>m,Py:()=>p,ZF:()=>y,_L:()=>E,dS:()=>A,ge:()=>h});var t=r(32960),s=r(26715),o=r(5041),l=r(12115),n=r(59959),i=r(35159),d=r(82073),c=r(53234),u=r(35695),g=r(57038);let y={all:["auth"],user:()=>[...y.all,"user"],profile:()=>[...y.all,"profile"],profileInfo:()=>[...y.all,"profileInfo"]},p=()=>{(0,u.useRouter)();let e=(0,t.I)({queryKey:y.user(),queryFn:async()=>{try{let e=await n.y.getUserInfo();if(!e)return null;let a=e.user||e;return a.userId||a.id||a.email||(a={userId:1,email:"<EMAIL>",firstName:"User",lastName:"User",phoneNumber:"",isActive:!0,registeredAt:new Date().toISOString(),membershipLevelId:1,careerRankId:1,referenceId:0,roles:["Customer"]}),{id:void 0!==a.userId?a.userId:void 0!==a.id?a.id:1,firstName:a.firstName||"User",lastName:a.lastName||"User",email:a.email||"<EMAIL>",phoneNumber:a.phoneNumber||"",isActive:void 0===a.isActive||a.isActive,registeredAt:a.registeredAt||new Date().toISOString(),membershipLevelId:void 0!==a.membershipLevelId?a.membershipLevelId:1,careerRankId:void 0!==a.careerRankId?a.careerRankId:1,referenceId:void 0!==a.referenceId?a.referenceId:void 0!==a.referanceId?a.referanceId:0,roles:a.roles||(a.role?[a.role]:["Customer"]),role:a.role?a.role.toLowerCase():a.roles&&a.roles.includes("Admin")?"admin":a.roles&&a.roles.includes("Dealership")?"dealership":"customer",membershipLevel:void 0!==a.membershipLevelId?a.membershipLevelId:0,joinDate:a.registeredAt?new Date(a.registeredAt).toISOString().split("T")[0]:"",isDealershipApproved:a.roles&&a.roles.includes("Dealership")}}catch(e){throw e}},enabled:!0,staleTime:9e5,gcTime:18e5,refetchOnWindowFocus:!1,refetchOnMount:"always",refetchOnReconnect:!0,refetchInterval:!1,refetchIntervalInBackground:!1,retry:(e,a)=>{var r,t;return(null==a||null==(r=a.response)?void 0:r.status)!==401&&(null==a||null==(t=a.response)?void 0:t.status)!==403&&e<2},retryDelay:e=>Math.min(1e3*2**e,3e4),throwOnError:e=>{var a;return(null==(a=e.response)?void 0:a.status)!==401}});return(0,l.useEffect)(()=>{e.isSuccess&&e.data&&i.n.setState({user:e.data,isAuthenticated:!0,error:null})},[e.isSuccess,e.data]),(0,l.useEffect)(()=>{if(e.isError&&e.error){var a,r;(null==(r=e.error)||null==(a=r.response)?void 0:a.status)===401&&i.n.setState({user:null,isAuthenticated:!1})}},[e.isError,e.error]),e},E=()=>{let e=(0,s.jE)();(0,u.useRouter)();let{user:a,setUser:r,clearAuth:t,setLoading:l}=(0,i.n)();return(0,o.n)({mutationFn:async e=>{let a=await n.y.login(e.email,e.password);if(!a)throw Error("Login failed: Invalid credentials from service");return a},onSuccess:async()=>{try{await e.invalidateQueries({queryKey:y.user()});let a=await e.fetchQuery({queryKey:y.user()});if(!a||!a.id)throw Error("Fetched user data is invalid or missing ID.");i.n.setState({isAuthenticated:!0,error:null,isLoading:!1,user:a}),e.invalidateQueries({queryKey:d.N$.all}),a.id&&(e.invalidateQueries({queryKey:d.N$.list(a.id)}),e.removeQueries({queryKey:d.N$.list(a.id)})),e.invalidateQueries({queryKey:c.M.all}),a.id&&(e.invalidateQueries({queryKey:c.M.user(a.id)}),e.removeQueries({queryKey:c.M.user(a.id)})),g.w.getState().resetCustomerPrice(),e.invalidateQueries({queryKey:["cartCount"]}),e.invalidateQueries({queryKey:["cartItems"]});try{console.log("✅ Background checkAuth başarılı - user bilgisi g\xfcncellendi")}catch(e){console.log("⚠️ Background checkAuth başarısız - mevcut user bilgisi korunuyor:",e.message)}}catch(e){i.n.setState({error:"Giriş başarılı fakat kullanıcı verileri alınamadı.",isAuthenticated:!1,user:null})}},onError:e=>{var a,r;i.n.setState({error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Giriş başarısız",isAuthenticated:!1,user:null})}})},m=()=>{let e=(0,s.jE)();return(0,o.n)({mutationFn:async()=>{await n.y.logout()},onSuccess:()=>{e.clear(),i.n.setState({user:null,isAuthenticated:!1,error:null}),g.w.getState().resetCustomerPrice()},onError:a=>{e.clear(),i.n.setState({user:null,isAuthenticated:!1}),g.w.getState().resetCustomerPrice()}})},h=()=>(0,o.n)({mutationFn:async e=>{if(e.password!==e.confirmPassword)throw Error("Şifreler eşleşmiyor");return(await n.y.register({firstName:e.firstName,lastName:e.lastName,email:e.email,password:e.password,phoneNumber:e.phoneNumber||"",referansCode:e.referansCode})).success},onSuccess:()=>{i.n.setState({error:null})},onError:e=>{i.n.setState({error:e.message||"Kayıt başarısız"})}}),A=()=>(0,t.I)({queryKey:y.profileInfo(),queryFn:async()=>{try{let e=await n.y.getProfileInfo();return console.log("\uD83D\uDCCB Profile Info Data:",e),e}catch(e){throw console.error("❌ Profile Info Error:",e),e}},enabled:!0,staleTime:3e5,gcTime:9e5,refetchOnWindowFocus:!1,refetchOnMount:"always",refetchOnReconnect:!0,refetchInterval:!1,refetchIntervalInBackground:!1,retry:(e,a)=>{var r,t;return(null==a||null==(r=a.response)?void 0:r.status)!==401&&(null==a||null==(t=a.response)?void 0:t.status)!==403&&e<2},retryDelay:e=>Math.min(1e3*2**e,3e4),throwOnError:e=>{var a;return(null==(a=e.response)?void 0:a.status)!==401}})},79023:(e,a,r)=>{r.d(a,{V:()=>t});let t=(0,r(65453).v)(e=>({status:navigator.onLine?"online":"offline",setStatus:a=>e({status:a})}))},80722:(e,a,r)=>{r.d(a,{Ay:()=>g,CV:()=>m,Dv:()=>p,jU:()=>E,qd:()=>y});var t=r(23464),s=r(32784),o=r(46893),l=r(79023);let n=e=>{if("undefined"==typeof document)return null;let a="; ".concat(document.cookie).split("; ".concat(e,"="));if(2===a.length){var r;return(null==(r=a.pop())?void 0:r.split(";").shift())||null}return null},i=t.A.create({baseURL:"https://api.sayglobalweb.com",headers:{"Content-Type":"application/json",Accept:"application/json"},timeout:3e4,withCredentials:!0});(0,s.Ay)(i,{retries:3,retryCondition:e=>{var a;return(null==(a=e.response)||!a.status||!(e.response.status>=400)||!(e.response.status<500))&&(s.Ay.isNetworkError(e)||s.Ay.isIdempotentRequestError(e))},retryDelay:(e,a)=>(console.warn("[axios-retry] Request failed: ".concat(a.message,". Retry attempt #").concat(e,"...")),1e3*Math.pow(2,e-1))});let d=!1,c=[],u=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;c.forEach(r=>{let{resolve:t,reject:s}=r;e?s(e):t(a)}),c=[]};o.S.LOGIN,o.S.REGISTER,o.S.REFRESH_TOKEN,o.S.LOGOUT,i.interceptors.request.use(e=>{var a;console.log("\uD83D\uDCE1 API Request başlıyor:",null==(a=e.method)?void 0:a.toUpperCase(),e.url),console.log("\uD83C\uDF6A withCredentials:",e.withCredentials);let r=n("AccessToken");return r?(e.headers.Authorization="Bearer ".concat(r),console.log("\uD83D\uDD11 Authorization header eklendi (JS readable cookie)")):console.log("\uD83D\uDD11 Cookie HttpOnly olabilir - browser otomatik g\xf6nderecek"),e},e=>Promise.reject(e)),i.interceptors.response.use(e=>("online"!==l.V.getState().status&&(console.log("✅ API başarılı - Network status online'a \xe7ekiliyor"),l.V.getState().setStatus("online")),e),async e=>{var a,r,s,n,g;let y=e.config;if(((null==(a=y.url)?void 0:a.includes(o.S.LOGIN))||(null==(r=y.url)?void 0:r.includes(o.S.REFRESH_TOKEN)))&&(null==(s=e.response)?void 0:s.status)===401)return Promise.reject(e);if((null==(n=e.response)?void 0:n.status)===401&&!y._retry){if(y._retry=!0,d)return new Promise((e,a)=>{c.push({resolve:e,reject:a})}).then(()=>i(y)).catch(e=>Promise.reject(e));d=!0;let a=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;try{return await i.get(o.S.REFRESH_TOKEN)}catch(l){var r;let s=t.A.isAxiosError(l)&&!l.response,o=t.A.isAxiosError(l)&&(null==(r=l.response)?void 0:r.status)===401;if((s||o)&&e<2){console.log("\uD83D\uDD04 Refresh token denemesi ".concat(e+1,"/3 başarısız (").concat(s?"Ağ hatası":"401 - Timeout olabilir","). ").concat(e<1?"Tekrar deneniyor...":"Son deneme yapılıyor..."));let r=1e3*Math.pow(2,e);return await new Promise(e=>setTimeout(e,r)),a(e+1)}throw l}};try{return await a(),u(null),d=!1,i(y)}catch(a){if(d=!1,t.A.isAxiosError(a)&&(null==(g=a.response)?void 0:g.status)===401)return console.log("\uD83D\uDEAA 3 deneme sonrasında da 401 hatası. Kullanıcı ger\xe7ekten giriş yapmamış."),u(e,null),Promise.reject(e);return u(a,null),t.A.isAxiosError(a)&&!a.response?(console.log("\uD83D\uDD0C 3 deneme sonrasında refresh token yenilenemedi. Ağ bağlantısı sorunlu."),l.V.getState().setStatus("reconnecting")):(console.log("\uD83D\uDCA5 Beklenmedik hata sonrası auth:force-logout event g\xf6nderiliyor"),window.dispatchEvent(new CustomEvent("auth:force-logout"))),Promise.reject(a)}}return t.A.isAxiosError(e)&&!e.response?(console.log("\uD83D\uDD0C Genel ağ hatası algılandı. Yeniden bağlanma moduna ge\xe7iliyor."),l.V.getState().setStatus("reconnecting"),new Promise(()=>{})):Promise.reject(e)});let g=i,y={async getAddresses(){try{console.log("\uD83D\uDCCD Adresler alınıyor...");let e=await i.get(o.S.USER_ADDRESSES);console.log("\uD83D\uDCCD API Response:",e),console.log("\uD83D\uDCCD Response data:",e.data),console.log("\uD83D\uDCCD Response status:",e.status),console.log("\uD83D\uDCCD Response data type:",typeof e.data),console.log("\uD83D\uDCCD Response data is array:",Array.isArray(e.data)),"object"==typeof e.data&&null!==e.data&&(console.log("\uD83D\uDCCD Response data keys:",Object.keys(e.data)),console.log("\uD83D\uDCCD Response data values:",Object.values(e.data)),e.data.data&&(console.log("\uD83D\uDCCD Nested data found:",e.data.data),console.log("\uD83D\uDCCD Nested data is array:",Array.isArray(e.data.data))),e.data.addresses&&(console.log("\uD83D\uDCCD Addresses property found:",e.data.addresses),console.log("\uD83D\uDCCD Addresses is array:",Array.isArray(e.data.addresses))),e.data.result&&(console.log("\uD83D\uDCCD Result property found:",e.data.result),console.log("\uD83D\uDCCD Result is array:",Array.isArray(e.data.result))));let a=e.data;return e.data.data&&Array.isArray(e.data.data)?(a=e.data.data,console.log("\uD83D\uDCCD Using nested data array")):e.data.addresses&&Array.isArray(e.data.addresses)?(a=e.data.addresses,console.log("\uD83D\uDCCD Using addresses property")):e.data.result&&Array.isArray(e.data.result)?(a=e.data.result,console.log("\uD83D\uDCCD Using result property")):Array.isArray(e.data)?(a=e.data,console.log("\uD83D\uDCCD Using direct response data")):(console.warn("\uD83D\uDCCD No valid array found in response, using empty array"),a=[]),console.log("\uD83D\uDCCD Final address data:",a),console.log("\uD83D\uDCCD Final address data type:",typeof a),console.log("\uD83D\uDCCD Final address data is array:",Array.isArray(a)),console.log("\uD83D\uDCCD Final address count:",a.length),{success:!0,data:a}}catch(t){var e,a,r;return console.error("❌ Adresler alınırken hata:",t),console.error("❌ Error response:",t.response),console.error("❌ Error data:",null==(e=t.response)?void 0:e.data),{success:!1,error:(null==(r=t.response)||null==(a=r.data)?void 0:a.message)||t.message||"Adresler alınırken bir hata oluştu",data:[]}}},async createAddress(e){try{console.log("➕ Yeni adres oluşturuluyor:",e);let a=await i.post(o.S.CREATE_ADDRESS,e);return console.log("✅ Adres oluşturma başarılı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ Adres eklenirken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Adres eklenirken bir hata oluştu"}}},async deleteAddress(e,a){try{console.log("\uD83D\uDDD1️ Adres siliniyor:",{addressId:e,userId:a});let r=await i.post(o.S.DELETE_ADDRESS,{addressId:e,userId:a});return console.log("✅ Adres silme başarılı:",r.data),{success:!0,data:r.data}}catch(e){var r,t;return console.error("❌ Adres silinirken hata:",e),{success:!1,error:(null==(t=e.response)||null==(r=t.data)?void 0:r.message)||"Adres silinirken bir hata oluştu"}}},async setDefaultAddress(e){try{console.log("⭐ Varsayılan adres ayarlanıyor:",{addressId:e});let a=await i.post("".concat(o.S.SET_DEFAULT_ADDRESS,"?addressId=").concat(e));return console.log("✅ Varsayılan adres ayarlama başarılı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ Varsayılan adres ayarlanırken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Varsayılan adres ayarlanırken bir hata oluştu"}}}},p={async updateProfile(e){try{console.log("\uD83D\uDC64 Profil g\xfcncelleniyor:",e);let a=await i.post(o.S.UPDATE_PROFILE,e);return console.log("✅ Profil g\xfcncelleme başarılı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ Profil g\xfcncellenirken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Profil g\xfcncellenirken bir hata oluştu"}}},async getUsers(e){try{let a={page:e.page||1,pageSize:e.pageSize||10,search:e.search||""};e.roleId&&e.roleId>0&&(a.roleId=e.roleId),void 0!==e.isActive&&(a.isActive=e.isActive);let r=await i.post(o.S.GET_USERS,a);return{success:!0,data:r.data}}catch(e){var a,r;return console.error("❌ Kullanıcılar alınırken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||e.message||"Kullanıcılar alınırken bir hata oluştu",data:[]}}},async getUserRoleCounts(){try{let e=await i.get(o.S.GET_USER_ROLE_COUNTS);return{success:!0,data:e.data}}catch(r){var e,a;return console.error("❌ Kullanıcı rol sayıları alınırken hata:",r),{success:!1,error:(null==(a=r.response)||null==(e=a.data)?void 0:e.message)||r.message||"Kullanıcı rol sayıları alınırken bir hata oluştu",data:null}}},async getDiscountRate(){try{console.log("\uD83D\uDCB0 Kullanıcı indirim oranı alınıyor...");let e=await i.get(o.S.GET_DISCOUNT_RATE);return console.log("✅ Kullanıcı indirim oranı başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(r){var e,a;return console.error("❌ Kullanıcı indirim oranı alınırken hata:",r),{success:!1,error:(null==(a=r.response)||null==(e=a.data)?void 0:e.message)||r.message||"Kullanıcı indirim oranı alınırken bir hata oluştu",data:null}}},async updateCartType(){try{console.log("\uD83D\uDED2 Sepet tipi g\xfcncelleniyor...");let e=await i.post(o.S.UPDATE_CART_TYPE);return console.log("✅ Sepet tipi başarıyla g\xfcncellendi:",e.data),{success:!0,data:e.data}}catch(r){var e,a;return console.error("❌ Sepet tipi g\xfcncellenirken hata:",r),{success:!1,error:(null==(a=r.response)||null==(e=a.data)?void 0:e.message)||r.message||"Sepet tipi g\xfcncellenirken bir hata oluştu",data:null}}}},E={async getBrands(){try{console.log("\uD83C\uDFF7️ Markalar alınıyor...");let e=await i.get(o.S.GET_BRANDS);return console.log("✅ Markalar başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(r){var e,a;return console.error("❌ Markalar alınırken hata:",r),{success:!1,error:(null==(a=r.response)||null==(e=a.data)?void 0:e.message)||"Markalar alınırken bir hata oluştu",data:[]}}},async getCategoriesByBrand(e){try{console.log("\uD83D\uDCC2 Kategoriler alınıyor, brandId:",e);let a=await i.get("".concat(o.S.GET_CATEGORIES_BY_BRAND,"/").concat(e));return console.log("✅ Kategoriler başarıyla alındı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ Kategoriler alınırken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Kategoriler alınırken bir hata oluştu",data:[]}}},async getSubCategories(e){try{console.log("\uD83D\uDCC1 Alt kategoriler alınıyor, categoryId:",e);let a=await i.get("".concat(o.S.GET_SUBCATEGORIES,"/").concat(e,"/subcategories"));return console.log("✅ Alt kategoriler başarıyla alındı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ Alt kategoriler alınırken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Alt kategoriler alınırken bir hata oluştu",data:[]}}},async getSubCategoryFeatures(e){try{console.log("\uD83D\uDD27 Alt kategori \xf6zellikleri alınıyor, subCategoryId:",e);let a=await i.get("".concat(o.S.GET_SUBCATEGORY_FEATURES,"/").concat(e));return console.log("✅ Alt kategori \xf6zellikleri başarıyla alındı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ Alt kategori \xf6zellikleri alınırken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Alt kategori \xf6zellikleri alınırken bir hata oluştu",data:[]}}},async getFeatureValues(e){try{console.log("\uD83C\uDFF7️ \xd6zellik değerleri alınıyor, definitionId:",e);let a=await i.get("".concat(o.S.GET_FEATURE_VALUES,"/").concat(e));return console.log("✅ \xd6zellik değerleri başarıyla alındı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ \xd6zellik değerleri alınırken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"\xd6zellik değerleri alınırken bir hata oluştu",data:[]}}},async getAllFeatureDefinitions(){try{console.log("\uD83D\uDD0D T\xfcm \xf6zellik tanımları alınıyor...");let e=await i.get(o.S.GET_ALL_FEATURE_DEFINITIONS);return console.log("✅ T\xfcm \xf6zellik tanımları başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(r){var e,a;return console.error("❌ T\xfcm \xf6zellik tanımları alınırken hata:",r),{success:!1,error:(null==(a=r.response)||null==(e=a.data)?void 0:e.message)||"T\xfcm \xf6zellik tanımları alınırken bir hata oluştu",data:[]}}},async createFullProduct(e){try{console.log("➕ Tam \xfcr\xfcn oluşturuluyor:",e);let a=await i.post(o.S.CREATE_FULL_PRODUCT,e,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ Tam \xfcr\xfcn oluşturma başarılı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ Tam \xfcr\xfcn oluşturulurken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"\xdcr\xfcn oluşturulurken bir hata oluştu"}}},async createDealershipProduct(e){try{console.log("➕ Satıcı \xfcr\xfcn\xfc oluşturuluyor:",e);let a=await i.post(o.S.CREATE_DEALERSHIP_PRODUCT,e,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ Satıcı \xfcr\xfcn\xfc oluşturma başarılı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ Satıcı \xfcr\xfcn\xfc oluşturulurken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"\xdcr\xfcn oluşturulurken bir hata oluştu"}}},async addProductImage(e){try{console.log("\uD83D\uDDBC️ \xdcr\xfcn g\xf6rseli ekleniyor:",e);let a=await i.post(o.S.ADD_PRODUCT_IMAGE,e,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ \xdcr\xfcn g\xf6rseli ekleme başarılı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ \xdcr\xfcn g\xf6rseli eklenirken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"\xdcr\xfcn g\xf6rseli eklenirken bir hata oluştu"}}},async deleteProductImage(e){try{console.log("\uD83D\uDDD1️ \xdcr\xfcn g\xf6rseli siliniyor, imageId:",e);let a=await i.delete("".concat(o.S.DELETE_PRODUCT_IMAGE,"/").concat(e));return console.log("✅ \xdcr\xfcn g\xf6rseli silme başarılı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ \xdcr\xfcn g\xf6rseli silinirken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"\xdcr\xfcn g\xf6rseli silinirken bir hata oluştu"}}},async replaceProductImage(e,a){try{console.log("\uD83D\uDD04 \xdcr\xfcn g\xf6rseli değiştiriliyor, imageId:",e);let r=new FormData;r.append("file",a);let t=await i.put("".concat(o.S.REPLACE_PRODUCT_IMAGE,"/").concat(e),r,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ \xdcr\xfcn g\xf6rseli değiştirme başarılı:",t.data),{success:!0,data:t.data}}catch(e){var r,t;return console.error("❌ \xdcr\xfcn g\xf6rseli değiştirilirken hata:",e),{success:!1,error:(null==(t=e.response)||null==(r=t.data)?void 0:r.message)||"\xdcr\xfcn g\xf6rseli değiştirilirken bir hata oluştu"}}},async getAdminProducts(e){try{let a=await i.get(o.S.GET_ADMIN_PRODUCTS,{params:e});return Array.isArray(a.data)?a.data:[]}catch(e){return console.error("❌ Admin \xfcr\xfcnleri alınırken hata:",e),[]}},async getMyProducts(e){try{console.log("\uD83D\uDCE6 Kullanıcıya ait \xfcr\xfcnler alınıyor:",e);let a=await i.post(o.S.GET_MY_PRODUCTS,e);return console.log("✅ Kullanıcıya ait \xfcr\xfcnler başarıyla alındı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ Kullanıcıya ait \xfcr\xfcnler alınırken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"\xdcr\xfcnler alınırken bir hata oluştu",data:null}}},async getDealershipProductDetail(e){try{console.log("\uD83D\uDCE6 Dealership \xfcr\xfcn detayı alınıyor, productId:",e);let a=await i.get("".concat(o.S.GET_DEALERSHIP_PRODUCT_DETAIL,"/").concat(e));return console.log("✅ Dealership \xfcr\xfcn detayı başarıyla alındı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ Dealership \xfcr\xfcn detayı alınırken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"\xdcr\xfcn detayı alınırken bir hata oluştu",data:null}}},async deleteProduct(e){try{console.log("\uD83D\uDDD1️ \xdcr\xfcn siliniyor, productId:",e);let a=await i.get(o.S.DELETE_PRODUCT,{params:{productId:e}});return console.log("✅ \xdcr\xfcn silme başarılı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ \xdcr\xfcn silinirken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"\xdcr\xfcn silinirken bir hata oluştu"}}},async updateFullProduct(e){try{for(let[a,r]of(console.log("\uD83D\uDD04 Tam \xfcr\xfcn g\xfcncelleniyor:",e),console.log("\uD83D\uDCCB FormData i\xe7eriği:"),e.entries()))console.log("".concat(a,":"),r);let a=await i.post(o.S.UPDATE_FULL_PRODUCT,e,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ Tam \xfcr\xfcn g\xfcncelleme başarılı:",a.data),{success:!0,data:a.data}}catch(e){var a,r,t,s,l,n;throw console.error("❌ Tam \xfcr\xfcn g\xfcncellenirken hata:",e),console.error("❌ Hata detayları:",null==(a=e.response)?void 0:a.data),console.error("❌ HTTP Status:",null==(r=e.response)?void 0:r.status),Error((null==(s=e.response)||null==(t=s.data)?void 0:t.message)||(null==(n=e.response)||null==(l=n.data)?void 0:l.title)||e.message||"\xdcr\xfcn g\xfcncellenirken bir hata oluştu")}},async getAdminProductStatistics(){try{console.log("\uD83D\uDCCA Admin \xfcr\xfcn istatistikleri alınıyor...");let e=await i.get(o.S.GET_ADMIN_PRODUCT_STATISTICS);return console.log("✅ Admin \xfcr\xfcn istatistikleri başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(r){var e,a;return console.error("❌ Admin \xfcr\xfcn istatistikleri alınırken hata:",r),{success:!1,error:(null==(a=r.response)||null==(e=a.data)?void 0:e.message)||"İstatistikler alınırken bir hata oluştu",data:null}}},async getMyProductStatistics(){try{console.log("\uD83D\uDCCA Kullanıcı \xfcr\xfcn istatistikleri alınıyor...");let e=await i.get(o.S.GET_MY_PRODUCT_STATISTICS);return console.log("✅ Kullanıcı \xfcr\xfcn istatistikleri başarıyla alındı:",e.data),{success:!0,data:e.data.data}}catch(r){var e,a;return console.error("❌ Kullanıcı \xfcr\xfcn istatistikleri alınırken hata:",r),{success:!1,error:(null==(a=r.response)||null==(e=a.data)?void 0:e.message)||"İstatistikler alınırken bir hata oluştu",data:null}}},async updateSimpleProduct(e){for(let[a,r]of(console.log("\uD83D\uDD04 API Service: Basit \xfcr\xfcn g\xfcncelleniyor..."),console.log("\uD83D\uDD17 Endpoint:",o.S.UPDATE_SIMPLE_PRODUCT),console.log("\uD83D\uDCCB API Service: FormData contents:"),e.entries()))r instanceof File?console.log("  ".concat(a,": File(").concat(r.name,", ").concat(r.size," bytes, ").concat(r.type,")")):console.log("  ".concat(a,": ").concat(r));let a=await i.post(o.S.UPDATE_SIMPLE_PRODUCT,e,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ API Service: Basit \xfcr\xfcn başarıyla g\xfcncellendi"),console.log("\uD83D\uDCC4 Response status:",a.status),console.log("\uD83D\uDCC4 Response data:",a.data),a.data},async getProductDetail(e){try{console.log("\uD83D\uDCE6 \xdcr\xfcn detayı alınıyor, productId:",e);let a=await i.get("".concat(o.S.GET_PRODUCT_DETAIL,"/").concat(e));return console.log("✅ \xdcr\xfcn detayı başarıyla alındı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ \xdcr\xfcn detayı alınırken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"\xdcr\xfcn detayı alınırken bir hata oluştu",data:null}}},async getCatalogProductDetail(e){try{console.log("\uD83D\uDCE6 Catalog \xfcr\xfcn detayı alınıyor, productId:",e);let a=await i.get("".concat(o.S.GET_CATALOG_PRODUCT_DETAIL,"/").concat(e));return console.log("✅ Catalog \xfcr\xfcn detayı başarıyla alındı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ Catalog \xfcr\xfcn detayı alınırken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"\xdcr\xfcn detayı alınırken bir hata oluştu",data:null}}},async updateProductStatus(e,a,r){try{console.log("\uD83D\uDD04 \xdcr\xfcn durumu g\xfcncelleniyor:",{productId:e,isApproved:a,message:r});let t=await i.post(o.S.UPDATE_PRODUCT_STATUS,{productId:e,isApproved:a,message:r});return console.log("✅ \xdcr\xfcn durumu başarıyla g\xfcncellendi:",t.data),{success:!0,data:t.data}}catch(e){var t,s;return console.error("❌ \xdcr\xfcn durumu g\xfcncellenirken hata:",e),{success:!1,error:(null==(s=e.response)||null==(t=s.data)?void 0:t.message)||"\xdcr\xfcn durumu g\xfcncellenirken bir hata oluştu"}}},async getProductMessage(e){try{console.log("\uD83D\uDCDD \xdcr\xfcn admin notu alınıyor, productId:",e);let a=await i.get("".concat(o.S.GET_PRODUCT_MESSAGE,"/").concat(e));return console.log("✅ \xdcr\xfcn admin notu başarıyla alındı:",a.data),{success:!0,data:a.data}}catch(e){var a,r,t;if(console.error("❌ \xdcr\xfcn admin notu alınırken hata:",e),(null==(a=e.response)?void 0:a.status)===404)return{success:!0,data:null};return{success:!1,error:(null==(t=e.response)||null==(r=t.data)?void 0:r.message)||"\xdcr\xfcn admin notu alınırken bir hata oluştu",data:null}}},async getCategories(){try{console.log("\uD83D\uDCC2 Kategoriler alınıyor...");let e=await i.get(o.S.GET_CATEGORIES);return console.log("✅ Kategoriler başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(r){var e,a;return console.error("❌ Kategoriler alınırken hata:",r),{success:!1,error:(null==(a=r.response)||null==(e=a.data)?void 0:e.message)||"Kategoriler alınırken bir hata oluştu",data:[]}}},async getSubCategoriesByCategory(e){try{console.log("\uD83D\uDCC1 Alt kategoriler alınıyor, categoryId:",e);let a=await i.get("".concat(o.S.GET_SUBCATEGORIES_BY_CATEGORY.replace("{categoryId}",e.toString())));return console.log("✅ Alt kategoriler başarıyla alındı:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ Alt kategoriler alınırken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Alt kategoriler alınırken bir hata oluştu",data:[]}}},async filterProducts(e){try{console.log("\uD83D\uDD0D \xdcr\xfcnler filtreleniyor:",e);let a=await i.post(o.S.FILTER_PRODUCTS,e);return console.log("✅ \xdcr\xfcnler başarıyla filtrelendi:",a.data),{success:!0,data:a.data}}catch(e){var a,r;return console.error("❌ \xdcr\xfcnler filtrelenirken hata:",e),{success:!1,error:(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"\xdcr\xfcnler filtrelenirken bir hata oluştu",data:null}}},async getReferenceData(){try{console.log("\uD83D\uDCCB Reference data alınıyor...");let e=await i.get(o.S.GET_REFERENCE_DATA);return console.log("✅ Reference data başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(r){var e,a;return console.error("❌ Reference data alınırken hata:",r),{success:!1,error:(null==(a=r.response)||null==(e=a.data)?void 0:e.message)||"Reference data alınırken bir hata oluştu",data:null}}}},m={async addToCart(e,a,r){try{console.log("\uD83D\uDED2 Sepete \xfcr\xfcn ekleniyor:",{productVariantId:e,quantity:a,isCustomerPrice:r});let t=await i.post(o.S.ADD_TO_CART,{productVariantId:e,quantity:a,isCustomerPrice:r});return console.log("✅ \xdcr\xfcn sepete başarıyla eklendi:",t.data),{success:!0,data:t.data}}catch(e){var t,s;return console.error("❌ Sepete \xfcr\xfcn eklenirken hata:",e),{success:!1,error:(null==(s=e.response)||null==(t=s.data)?void 0:t.message)||"\xdcr\xfcn sepete eklenirken bir hata oluştu"}}},async getCartItems(){try{console.log("\uD83D\uDED2 Sepet i\xe7erikleri alınıyor...");let e=await i.get(o.S.GET_CART_ITEMS);return console.log("✅ Sepet i\xe7erikleri başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(r){var e,a;return console.error("❌ Sepet i\xe7erikleri alınırken hata:",r),{success:!1,error:(null==(a=r.response)||null==(e=a.data)?void 0:e.message)||"Sepet i\xe7erikleri alınırken bir hata oluştu",data:null}}},async getCartCount(){try{console.log("\uD83D\uDED2 Sepet \xfcr\xfcn sayısı alınıyor...");let e=await i.get(o.S.GET_CART_COUNT);return console.log("✅ Sepet \xfcr\xfcn sayısı başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(r){var e,a;return console.error("❌ Sepet \xfcr\xfcn sayısı alınırken hata:",r),{success:!1,error:(null==(a=r.response)||null==(e=a.data)?void 0:e.message)||"Sepet \xfcr\xfcn sayısı alınırken bir hata oluştu",data:0}}},async removeFromCart(e){try{console.log("\uD83D\uDDD1️ Sepetten \xfcr\xfcn \xe7ıkarılıyor:",{productVariantId:e});let a="".concat(o.S.REMOVE_FROM_CART,"/").concat(e);console.log("\uD83D\uDD0D API URL:",a),console.log("\uD83D\uDD0D API_ENDPOINTS.REMOVE_FROM_CART:",o.S.REMOVE_FROM_CART);let r=await i.delete(a);return console.log("✅ \xdcr\xfcn sepetten başarıyla \xe7ıkarıldı:",r.data),{success:!0,data:r.data}}catch(e){var a,r,t,s;return console.error("❌ Sepetten \xfcr\xfcn \xe7ıkarılırken hata:",e),console.error("❌ Error response:",e.response),console.error("❌ Error status:",null==(a=e.response)?void 0:a.status),console.error("❌ Error data:",null==(r=e.response)?void 0:r.data),{success:!1,error:(null==(s=e.response)||null==(t=s.data)?void 0:t.message)||"\xdcr\xfcn sepetten \xe7ıkarılırken bir hata oluştu"}}},async updateCartQuantity(e,a){try{console.log("\uD83D\uDD04 Sepet \xfcr\xfcn miktarı g\xfcncelleniyor:",{productVariantId:e,quantity:a});let r=await i.post(o.S.UPDATE_CART_QUANTITY,{productVariantId:e,quantity:a});return console.log("✅ Sepet \xfcr\xfcn miktarı başarıyla g\xfcncellendi:",r.data),{success:!0,data:r.data}}catch(e){var r,t;return console.error("❌ Sepet \xfcr\xfcn miktarı g\xfcncellenirken hata:",e),{success:!1,error:(null==(t=e.response)||null==(r=t.data)?void 0:r.message)||"\xdcr\xfcn miktarı g\xfcncellenirken bir hata oluştu"}}}}},82073:(e,a,r)=>{r.d(a,{N$:()=>d,Xk:()=>p,_c:()=>u,aO:()=>g,fB:()=>y});var t=r(26715),s=r(32960),o=r(5041),l=r(80722),n=r(66681),i=r(35159);let d={all:["addresses"],lists:()=>[...d.all,"list"],list:e=>[...d.lists(),{userId:e}],detail:e=>[...d.all,"detail",e],favorites:()=>[...d.all,"favorites"],search:e=>[...d.all,"search",{query:e}]},c=()=>{let e=(0,i.n)(),{data:a,isLoading:r}=(0,n.Py)(),t=e.user||a;return{isAuthReady:e.isAuthenticated&&!e.isLoading&&!!(null==t?void 0:t.id),user:t}},u=()=>{let{isAuthReady:e,user:a}=c();return(0,t.jE)(),(0,s.I)({queryKey:(null==a?void 0:a.id)?d.list(a.id):["addresses","no-user"],queryFn:async()=>{if(!(null==a?void 0:a.id))return console.log("⚠️ useAddresses: No user ID available, returning empty array"),[];console.log("\uD83D\uDCCD Fetching addresses for user:",a.id);let e=await l.qd.getAddresses();if(!e.success){let a=Error(e.error||"Adresler y\xfcklenemedi");throw a.name="AddressLoadError",a}let r=(e.data||[]).sort((e,a)=>e.isDefault&&!a.isDefault?-1:!e.isDefault&&a.isDefault?1:e.id||a.id?e.id?a.id?e.id-a.id:-1:1:0);return console.log("\uD83D\uDCCD Addresses sorted (default first, then by ID):",r.map(e=>({id:e.id,title:e.title,isDefault:e.isDefault}))),r},enabled:e,staleTime:3e5,gcTime:6e5,refetchOnMount:!0,refetchOnWindowFocus:!1,refetchOnReconnect:!0,retry:(e,a)=>"AddressLoadError"===a.name?e<2:e<3,retryDelay:e=>Math.min(1e3*2**e,3e4)})},g=()=>{let e=(0,t.jE)(),a=(0,i.n)(),{data:r}=(0,n.Py)(),s=a.user||r;return(0,o.n)({mutationFn:async r=>{if(console.log("\uD83D\uDD04 TanStack Query: Creating address...",r),!a.isAuthenticated||!(null==s?void 0:s.id)){console.error("❌ Authentication Error:",{isAuthenticated:a.isAuthenticated,hasUser:!!s,userId:null==s?void 0:s.id});let e=Error("Kullanıcı oturum a\xe7mamış veya kullanıcı bilgileri y\xfcklenmemiş");throw e.name="AuthenticationError",e}let t=0===(e.getQueryData(d.list(s.id))||[]).length,o={...r,isDefault:!!t||r.isDefault};t&&console.log("\uD83C\uDFE0 İlk adres ekleniyor - otomatik varsayılan olarak ayarlandı");let n=await l.qd.createAddress(o);if(!n.success){let e=Error(n.error||"Adres eklenemedi");throw e.name="AddressCreateError",e}return console.log("✅ TanStack Query: Address created successfully"),n.data},onMutate:async a=>{let r=d.list((null==s?void 0:s.id)||0);await e.cancelQueries({queryKey:r});let t=e.getQueryData(r);if(t){let s=0===t.length,o={id:Date.now(),title:a.title,fullAddress:a.fullAddress,city:a.city,district:a.district,postalCode:a.postalCode,isDefault:!!s||a.isDefault};s&&console.log("\uD83C\uDFE0 Optimistic: İlk adres - otomatik varsayılan olarak ayarlandı");let l=[...t,o].sort((e,a)=>e.isDefault&&!a.isDefault?-1:!e.isDefault&&a.isDefault?1:e.id||a.id?e.id?a.id?e.id-a.id:-1:1:0);e.setQueryData(r,l),console.log("\uD83D\uDE80 Optimistic update: Address added to cache with smart sorting (default first)")}return{previousAddresses:t}},onSuccess:(a,r,t)=>{console.log("✅ TanStack Query: Address creation confirmed by server");let o=d.list((null==s?void 0:s.id)||0);e.setQueryData(o,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=[...e.filter(e=>!("number"==typeof e.id&&e.id>Date.now()-1e4)),a].sort((e,a)=>e.isDefault&&!a.isDefault?-1:!e.isDefault&&a.isDefault?1:e.id||a.id?e.id?a.id?e.id-a.id:-1:1:0);return console.log("\uD83D\uDCCD Cache updated with smart sorted addresses:",r.map(e=>({id:e.id,title:e.title,isDefault:e.isDefault}))),r})},onError:(a,r,t)=>{if(console.error("❌ TanStack Query: Address creation failed:",a),null==t?void 0:t.previousAddresses){let a=d.list((null==s?void 0:s.id)||0);e.setQueryData(a,t.previousAddresses),console.log("\uD83D\uDD04 Rollback: Optimistic update reverted")}},onSettled:()=>{let a=d.list((null==s?void 0:s.id)||0);e.invalidateQueries({queryKey:a})}})},y=e=>{let a=(0,t.jE)(),{data:r}=(0,n.Py)();return(0,o.n)({mutationFn:async e=>{console.log("\uD83D\uDD04 TanStack Query: Deleting address:",e);let a=await l.qd.deleteAddress(e,(null==r?void 0:r.id)||0);if(!a.success){let e=Error(a.error||"Adres silinemedi");throw e.name="AddressDeleteError",e}return console.log("✅ TanStack Query: Address deleted successfully"),e},onMutate:async e=>{let t=d.list((null==r?void 0:r.id)||0);await a.cancelQueries({queryKey:t});let s=a.getQueryData(t),o=null==s?void 0:s.find(a=>a.id===e),l=null==o?void 0:o.isDefault,n=(null==s?void 0:s.filter(a=>a.id!==e))||[];if(s){let r=s.filter(a=>a.id!==e);if(l&&r.length>0){let e=r.sort((e,a)=>(e.id||0)-(a.id||0))[0];r=r.map(a=>({...a,isDefault:a.id===e.id})),console.log("\uD83C\uDFE0 Default address removed, setting next address as default:",e.title)}let o=r.sort((e,a)=>e.isDefault&&!a.isDefault?-1:!e.isDefault&&a.isDefault?1:(e.id||0)-(a.id||0));a.setQueryData(t,o),console.log("\uD83D\uDE80 Optimistic update: Address removed from cache with auto-default handling")}return{previousAddresses:s,removedAddress:o,wasDefault:l,remainingAddresses:n}},onSuccess:(a,r,t)=>{if(console.log("✅ TanStack Query: Address deletion confirmed by server"),(null==t?void 0:t.wasDefault)&&(null==t?void 0:t.remainingAddresses.length)>0){let a=t.remainingAddresses.sort((e,a)=>(e.id||0)-(a.id||0))[0];console.log("\uD83C\uDFE0 Default address was removed, next default should be:",a.title),e&&a&&e(a,t.removedAddress)}},onError:(e,t,s)=>{if(console.error("❌ TanStack Query: Address deletion failed:",e),null==s?void 0:s.previousAddresses){let e=d.list((null==r?void 0:r.id)||0);a.setQueryData(e,s.previousAddresses),console.log("\uD83D\uDD04 Rollback: Deleted address restored to cache")}},onSettled:()=>{let e=d.list((null==r?void 0:r.id)||0);a.invalidateQueries({queryKey:e})}})},p=()=>{let e=(0,t.jE)(),{data:a}=(0,n.Py)();return(0,o.n)({mutationFn:async e=>{console.log("\uD83D\uDD04 TanStack Query: Setting address as default:",e);let a=await l.qd.setDefaultAddress(e);if(!a.success){let e=Error(a.error||"Varsayılan adres ayarlanamadı");throw e.name="SetDefaultAddressError",e}return console.log("✅ TanStack Query: Address set as default successfully"),a.data},onMutate:async r=>{let t=d.list((null==a?void 0:a.id)||0);await e.cancelQueries({queryKey:t});let s=e.getQueryData(t);if(s){let a=s.map(e=>({...e,isDefault:e.id===r})).sort((e,a)=>e.isDefault&&!a.isDefault?-1:!e.isDefault&&a.isDefault?1:e.id||a.id?e.id?a.id?e.id-a.id:-1:1:0);e.setQueryData(t,a),console.log("\uD83D\uDE80 Optimistic update: Default address changed with smart sorting")}return{previousAddresses:s}},onSuccess:(r,t,s)=>{console.log("✅ TanStack Query: Set default address confirmed by server");let o=d.list((null==a?void 0:a.id)||0);e.setQueryData(o,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],a=e.map(e=>({...e,isDefault:e.id===t})).sort((e,a)=>e.isDefault&&!a.isDefault?-1:!e.isDefault&&a.isDefault?1:e.id||a.id?e.id?a.id?e.id-a.id:-1:1:0);return console.log("\uD83D\uDCCD Cache updated with new default address (smart sorted):",a.map(e=>({id:e.id,title:e.title,isDefault:e.isDefault}))),a})},onError:(r,t,s)=>{if(console.error("❌ TanStack Query: Set default address failed:",r),null==s?void 0:s.previousAddresses){let r=d.list((null==a?void 0:a.id)||0);e.setQueryData(r,s.previousAddresses),console.log("\uD83D\uDD04 Rollback: Default address change reverted")}},onSettled:()=>{let r=d.list((null==a?void 0:a.id)||0);e.invalidateQueries({queryKey:r})}})}},87220:(e,a,r)=>{r.d(a,{A:()=>d,O:()=>i});var t=r(95155),s=r(12115),o=r(35159),l=r(66681);let n=(0,s.createContext)(void 0),i=e=>{let{children:a}=e,{data:r,isLoading:i,isSuccess:d}=(0,l.Py)(),{error:c,clearError:u}=(0,o.n)(),g=(0,l._L)(),y=(0,l.Ng)(),p=(0,l.ge)();(0,s.useEffect)(()=>{let e=()=>{y.isPending||y.mutate()};return window.addEventListener("auth:force-logout",e),()=>{window.removeEventListener("auth:force-logout",e)}},[y]);let E={isAuthenticated:d&&!!r,user:r||null,login:async e=>{try{return await g.mutateAsync(e),!0}catch(e){return console.error("❌ AuthContext login error:",e),!1}},register:async e=>{try{return await p.mutateAsync(e),!0}catch(e){return console.error("❌ AuthContext register error:",e),!1}},logout:async()=>{await y.mutateAsync()},updateUserRole:async(e,a,r,t)=>{console.log("User role update requested:",{userId:e,newRole:a,isDealershipApproved:r,applicationStatus:t})},isLoading:g.isPending||p.isPending||y.isPending||i,error:c};return(0,t.jsx)(n.Provider,{value:E,children:a})},d=()=>{let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}}]);