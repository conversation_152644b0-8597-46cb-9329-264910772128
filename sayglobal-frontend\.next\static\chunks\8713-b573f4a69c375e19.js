"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8713],{31802:(e,r,n)=>{let i;function f(e,r){return new Promise(function(n,i){let f;return o(e).then(function(e){try{return f=e,n(new Blob([r.slice(0,2),f,r.slice(2)],{type:"image/jpeg"}))}catch(e){return i(e)}},i)})}n.d(r,{A:()=>P});let o=e=>new Promise((r,n)=>{let i=new FileReader;i.addEventListener("load",({target:{result:e}})=>{let i=new DataView(e),f=0;if(65496!==i.getUint16(f))return n("not a valid JPEG");for(f+=2;;){let o=i.getUint16(f);if(65498===o)break;let a=i.getUint16(f+2);if(65505===o&&0x45786966===i.getUint32(f+4)){let o,l=f+10;switch(i.getUint16(l)){case 18761:o=!0;break;case 19789:o=!1;break;default:return n("TIFF header contains invalid endian")}if(42!==i.getUint16(l+2,o))return n("TIFF header contains invalid version");let s=i.getUint32(l+4,o),u=l+s+2+12*i.getUint16(l+s,o);for(let e=l+s+2;e<u;e+=12)if(274==i.getUint16(e,o)){if(3!==i.getUint16(e+2,o))return n("Orientation data type is invalid");if(1!==i.getUint32(e+4,o))return n("Orientation data count is invalid");i.setUint16(e+8,1,o);break}return r(e.slice(f,f+2+a))}f+=2+a}return r(new Blob)}),i.readAsArrayBuffer(e)});var a={},l={get exports(){return a},set exports(t){a=t}};!function(e){var r,n,i={};l.exports=i,i.parse=function(e,r){for(var n=i.bin.readUshort,f=i.bin.readUint,o=0,a={},l=new Uint8Array(e),s=l.length-4;0x6054b50!=f(l,s);)s--;var u=n(l,o=s+4+4);n(l,o+=2);var c=f(l,o+=2),h=f(l,o+=4);o+=4,o=h;for(var d=0;d<u;d++){f(l,o),o+=4,o+=4,o+=4,f(l,o+=4),c=f(l,o+=4);var g=f(l,o+=4),p=n(l,o+=4),A=n(l,o+2),w=n(l,o+4);o+=6;var m=f(l,o+=8);o+=4,o+=p+A+w,i._readLocal(l,m,a,c,g,r)}return a},i._readLocal=function(e,r,n,f,o,a){var l=i.bin.readUshort,s=i.bin.readUint;s(e,r),l(e,r+=4),l(e,r+=2);var u=l(e,r+=2);s(e,r+=2),s(e,r+=4),r+=4;var c=l(e,r+=8),h=l(e,r+=2);r+=2;var d=i.bin.readUTF8(e,r,c);if(r+=c,r+=h,a)n[d]={size:o,csize:f};else{var g=new Uint8Array(e.buffer,r);if(0==u)n[d]=new Uint8Array(g.buffer.slice(r,r+f));else{if(8!=u)throw"unknown compression method: "+u;var p=new Uint8Array(o);i.inflateRaw(g,p),n[d]=p}}},i.inflateRaw=function(e,r){return i.F.inflate(e,r)},i.inflate=function(e,r){return e[0],e[1],i.inflateRaw(new Uint8Array(e.buffer,e.byteOffset+2,e.length-6),r)},i.deflate=function(e,r){null==r&&(r={level:6});var n=0,f=new Uint8Array(50+Math.floor(1.1*e.length));f[n]=120,f[n+1]=156,n+=2,n=i.F.deflateRaw(e,f,n,r.level);var o=i.adler(e,0,e.length);return f[n+0]=o>>>24&255,f[n+1]=o>>>16&255,f[n+2]=o>>>8&255,f[n+3]=o>>>0&255,new Uint8Array(f.buffer,0,n+4)},i.deflateRaw=function(e,r){null==r&&(r={level:6});var n=new Uint8Array(50+Math.floor(1.1*e.length)),f=i.F.deflateRaw(e,n,f,r.level);return new Uint8Array(n.buffer,0,f)},i.encode=function(e,r){null==r&&(r=!1);var n=0,f=i.bin.writeUint,o=i.bin.writeUshort,a={};for(var l in e){var s=!i._noNeed(l)&&!r,u=e[l],c=i.crc.crc(u,0,u.length);a[l]={cpr:s,usize:u.length,crc:c,file:s?i.deflateRaw(u):u}}for(var l in a)n+=a[l].file.length+30+46+2*i.bin.sizeUTF8(l);var h=new Uint8Array(n+=22),d=0,g=[];for(var l in a){var p=a[l];g.push(d),d=i._writeHeader(h,d,l,p,0)}var A=0,w=d;for(var l in a)p=a[l],g.push(d),d=i._writeHeader(h,d,l,p,1,g[A++]);var m=d-w;return f(h,d,0x6054b50),d+=4,o(h,d+=4,A),o(h,d+=2,A),f(h,d+=2,m),f(h,d+=4,w),d+=4,d+=2,h.buffer},i._noNeed=function(e){var r=e.split(".").pop().toLowerCase();return -1!="png,jpg,jpeg,zip".indexOf(r)},i._writeHeader=function(e,r,n,f,o,a){var l=i.bin.writeUint,s=i.bin.writeUshort,u=f.file;return l(e,r,0==o?0x4034b50:0x2014b50),r+=4,1==o&&(r+=2),s(e,r,20),s(e,r+=2,0),s(e,r+=2,8*!!f.cpr),l(e,r+=2,0),l(e,r+=4,f.crc),l(e,r+=4,u.length),l(e,r+=4,f.usize),s(e,r+=4,i.bin.sizeUTF8(n)),s(e,r+=2,0),r+=2,1==o&&(r+=2,r+=2,l(e,r+=6,a),r+=4),r+=i.bin.writeUTF8(e,r,n),0==o&&(e.set(u,r),r+=u.length),r},i.crc={table:function(){for(var e=new Uint32Array(256),r=0;r<256;r++){for(var n=r,i=0;i<8;i++)1&n?n=0xedb88320^n>>>1:n>>>=1;e[r]=n}return e}(),update:function(e,r,n,f){for(var o=0;o<f;o++)e=i.crc.table[255&(e^r[n+o])]^e>>>8;return e},crc:function(e,r,n){return 0xffffffff^i.crc.update(0xffffffff,e,r,n)}},i.adler=function(e,r,n){for(var i=1,f=0,o=r,a=r+n;o<a;){for(var l=Math.min(o+5552,a);o<l;)f+=i+=e[o++];i%=65521,f%=65521}return f<<16|i},i.bin={readUshort:function(e,r){return e[r]|e[r+1]<<8},writeUshort:function(e,r,n){e[r]=255&n,e[r+1]=n>>8&255},readUint:function(e,r){return 0x1000000*e[r+3]+(e[r+2]<<16|e[r+1]<<8|e[r])},writeUint:function(e,r,n){e[r]=255&n,e[r+1]=n>>8&255,e[r+2]=n>>16&255,e[r+3]=n>>24&255},readASCII:function(e,r,n){for(var i="",f=0;f<n;f++)i+=String.fromCharCode(e[r+f]);return i},writeASCII:function(e,r,n){for(var i=0;i<n.length;i++)e[r+i]=n.charCodeAt(i)},pad:function(e){return e.length<2?"0"+e:e},readUTF8:function(e,r,n){for(var f,o="",a=0;a<n;a++)o+="%"+i.bin.pad(e[r+a].toString(16));try{f=decodeURIComponent(o)}catch(f){return i.bin.readASCII(e,r,n)}return f},writeUTF8:function(e,r,n){for(var i=n.length,f=0,o=0;o<i;o++){var a=n.charCodeAt(o);if(0==(0xffffff80&a))e[r+f]=a,f++;else if(0==(0xfffff800&a))e[r+f]=192|a>>6,e[r+f+1]=128|(0|a)&63,f+=2;else if(0==(0xffff0000&a))e[r+f]=224|a>>12,e[r+f+1]=128|a>>6&63,e[r+f+2]=128|(0|a)&63,f+=3;else{if(0!=(0xffe00000&a))throw"e";e[r+f]=240|a>>18,e[r+f+1]=128|a>>12&63,e[r+f+2]=128|a>>6&63,e[r+f+3]=128|(0|a)&63,f+=4}}return f},sizeUTF8:function(e){for(var r=e.length,n=0,i=0;i<r;i++){var f=e.charCodeAt(i);if(0==(0xffffff80&f))n++;else if(0==(0xfffff800&f))n+=2;else if(0==(0xffff0000&f))n+=3;else{if(0!=(0xffe00000&f))throw"e";n+=4}}return n}},i.F={},i.F.deflateRaw=function(e,r,n,f){var o=[[0,0,0,0,0],[4,4,8,4,0],[4,5,16,8,0],[4,6,16,16,0],[4,10,16,32,0],[8,16,32,32,0],[8,16,128,128,0],[8,32,128,256,0],[32,128,258,1024,1],[32,258,258,4096,1]][f],a=i.F.U,l=i.F._goodIndex;i.F._hash;var s=i.F._putsE,u=0,c=n<<3,h=0,d=e.length;if(0==f){for(;u<d;)s(r,c,+(u+(E=Math.min(65535,d-u))==d)),c=i.F._copyExact(e,u,E,r,c+8),u+=E;return c>>>3}var g=a.lits,p=a.strt,A=a.prev,w=0,m=0,v=0,b=0,y=0,U=0;for(d>2&&(p[U=i.F._hash(e,0)]=0),u=0;u<d;u++){if(y=U,u+1<d-2){U=i.F._hash(e,u+1);var F=u+1&32767;A[F]=p[U],p[U]=F}if(h<=u){(w>14e3||m>26697)&&d-u>100&&(h<u&&(g[w]=u-h,w+=2,h=u),c=i.F._writeBlock(+(u==d-1||h==d),g,w,b,e,v,u-v,r,c),w=m=b=0,v=u);var x=0;u<d-2&&(x=i.F._bestMatch(e,u,A,y,Math.min(o[2],d-u),o[3]));var E=x>>>16,_=65535&x;if(0!=x){_=65535&x;var C=l(E=x>>>16,a.of0);a.lhst[257+C]++;var I=l(_,a.df0);a.dhst[I]++,b+=a.exb[C]+a.dxb[I],g[w]=E<<23|u-h,g[w+1]=_<<16|C<<8|I,w+=2,h=u+E}else a.lhst[e[u]]++;m++}}for(v==u&&0!=e.length||(h<u&&(g[w]=u-h,w+=2,h=u),c=i.F._writeBlock(1,g,w,b,e,v,u-v,r,c),w=0,m=0,w=m=b=0,v=u);0!=(7&c);)c++;return c>>>3},i.F._bestMatch=function(e,r,n,f,o,a){var l=32767&r,s=n[l],u=l-s+32768&32767;if(s==l||f!=i.F._hash(e,r-u))return 0;for(var c=0,h=0,d=Math.min(32767,r);u<=d&&0!=--a&&s!=l;){if(0==c||e[r+c]==e[r+c-u]){var g=i.F._howLong(e,r,u);if(g>c){if(h=u,(c=g)>=o)break;u+2<g&&(g=u+2);for(var p=0,A=0;A<g-2;A++){var w=r-u+A+32768&32767,m=w-n[w]+32768&32767;m>p&&(p=m,s=w)}}}u+=(l=s)-(s=n[l])+32768&32767}return c<<16|h},i.F._howLong=function(e,r,n){if(e[r]!=e[r-n]||e[r+1]!=e[r+1-n]||e[r+2]!=e[r+2-n])return 0;var i=r,f=Math.min(e.length,r+258);for(r+=3;r<f&&e[r]==e[r-n];)r++;return r-i},i.F._hash=function(e,r){return(e[r]<<8|e[r+1])+(e[r+2]<<4)&65535},i.saved=0,i.F._writeBlock=function(e,r,n,f,o,a,l,s,u){var c,h,d,g,p,A,w,m,v,b,y,U=i.F.U,F=i.F._putsF,x=i.F._putsE;U.lhst[256]++,g=(d=i.F.getTrees())[0],p=d[1],A=d[2],w=d[3],m=d[4],v=d[5],b=d[6],y=d[7];var E=32+(0==(u+3&7)?0:8-(u+3&7))+(l<<3),_=f+i.F.contSize(U.fltree,U.lhst)+i.F.contSize(U.fdtree,U.dhst),C=f+i.F.contSize(U.ltree,U.lhst)+i.F.contSize(U.dtree,U.dhst);C+=14+3*v+i.F.contSize(U.itree,U.ihst)+(2*U.ihst[16]+3*U.ihst[17]+7*U.ihst[18]);for(var I=0;I<286;I++)U.lhst[I]=0;for(I=0;I<30;I++)U.dhst[I]=0;for(I=0;I<19;I++)U.ihst[I]=0;var M=E<_&&E<C?0:_<C?1:2;if(F(s,u,e),F(s,u+1,M),u+=3,0==M){for(;0!=(7&u);)u++;u=i.F._copyExact(o,a,l,s,u)}else{if(1==M&&(c=U.fltree,h=U.fdtree),2==M){i.F.makeCodes(U.ltree,g),i.F.revCodes(U.ltree,g),i.F.makeCodes(U.dtree,p),i.F.revCodes(U.dtree,p),i.F.makeCodes(U.itree,A),i.F.revCodes(U.itree,A),c=U.ltree,h=U.dtree,x(s,u,w-257),x(s,u+=5,m-1),x(s,u+=5,v-4),u+=4;for(var B=0;B<v;B++)x(s,u+3*B,U.itree[1+(U.ordr[B]<<1)]);u+=3*v,u=i.F._codeTiny(b,U.itree,s,u),u=i.F._codeTiny(y,U.itree,s,u)}for(var R=a,S=0;S<n;S+=2){for(var T=r[S],Q=T>>>23,P=R+(8388607&T);R<P;)u=i.F._writeLit(o[R++],c,s,u);if(0!=Q){var k=r[S+1],H=k>>16,L=k>>8&255,O=255&k;x(s,u=i.F._writeLit(257+L,c,s,u),Q-U.of0[L]),u+=U.exb[L],F(s,u=i.F._writeLit(O,h,s,u),H-U.df0[O]),u+=U.dxb[O],R+=Q}}u=i.F._writeLit(256,c,s,u)}return u},i.F._copyExact=function(e,r,n,i,f){var o=f>>>3;return i[o]=n,i[o+1]=n>>>8,i[o+2]=255-i[o],i[o+3]=255-i[o+1],o+=4,i.set(new Uint8Array(e.buffer,r,n),o),f+(n+4<<3)},i.F.getTrees=function(){for(var e=i.F.U,r=i.F._hufTree(e.lhst,e.ltree,15),n=i.F._hufTree(e.dhst,e.dtree,15),f=[],o=i.F._lenCodes(e.ltree,f),a=[],l=i.F._lenCodes(e.dtree,a),s=0;s<f.length;s+=2)e.ihst[f[s]]++;for(s=0;s<a.length;s+=2)e.ihst[a[s]]++;for(var u=i.F._hufTree(e.ihst,e.itree,7),c=19;c>4&&0==e.itree[1+(e.ordr[c-1]<<1)];)c--;return[r,n,u,o,l,c,f,a]},i.F.getSecond=function(e){for(var r=[],n=0;n<e.length;n+=2)r.push(e[n+1]);return r},i.F.nonZero=function(e){for(var r="",n=0;n<e.length;n+=2)0!=e[n+1]&&(r+=(n>>1)+",");return r},i.F.contSize=function(e,r){for(var n=0,i=0;i<r.length;i++)n+=r[i]*e[1+(i<<1)];return n},i.F._codeTiny=function(e,r,n,f){for(var o=0;o<e.length;o+=2){var a=e[o],l=e[o+1];f=i.F._writeLit(a,r,n,f);var s=16==a?2:17==a?3:7;a>15&&(i.F._putsE(n,f,l,s),f+=s)}return f},i.F._lenCodes=function(e,r){for(var n=e.length;2!=n&&0==e[n-1];)n-=2;for(var i=0;i<n;i+=2){var f=e[i+1],o=i+3<n?e[i+3]:-1,a=i+5<n?e[i+5]:-1,l=0==i?-1:e[i-1];if(0==f&&o==f&&a==f){for(var s=i+5;s+2<n&&e[s+2]==f;)s+=2;(u=Math.min(s+1-i>>>1,138))<11?r.push(17,u-3):r.push(18,u-11),i+=2*u-2}else if(f==l&&o==f&&a==f){for(s=i+5;s+2<n&&e[s+2]==f;)s+=2;var u=Math.min(s+1-i>>>1,6);r.push(16,u-3),i+=2*u-2}else r.push(f,0)}return n>>>1},i.F._hufTree=function(e,r,n){var f=[],o=e.length,a=r.length,l=0;for(l=0;l<a;l+=2)r[l]=0,r[l+1]=0;for(l=0;l<o;l++)0!=e[l]&&f.push({lit:l,f:e[l]});var s=f.length,u=f.slice(0);if(0==s)return 0;if(1==s){var c=f[0].lit;return u=+(0==c),r[1+(c<<1)]=1,r[1+(u<<1)]=1,1}f.sort(function(e,r){return e.f-r.f});var h=f[0],d=f[1],g=0,p=1,A=2;for(f[0]={lit:-1,f:h.f+d.f,l:h,r:d,d:0};p!=s-1;)h=g!=p&&(A==s||f[g].f<f[A].f)?f[g++]:f[A++],d=g!=p&&(A==s||f[g].f<f[A].f)?f[g++]:f[A++],f[p++]={lit:-1,f:h.f+d.f,l:h,r:d};var w=i.F.setDepth(f[p-1],0);for(w>n&&(i.F.restrictDepth(u,n,w),w=n),l=0;l<s;l++)r[1+(u[l].lit<<1)]=u[l].d;return w},i.F.setDepth=function(e,r){return -1!=e.lit?(e.d=r,r):Math.max(i.F.setDepth(e.l,r+1),i.F.setDepth(e.r,r+1))},i.F.restrictDepth=function(e,r,n){var i=0,f=1<<n-r,o=0;for(e.sort(function(e,r){return r.d==e.d?e.f-r.f:r.d-e.d}),i=0;i<e.length&&e[i].d>r;i++){var a=e[i].d;e[i].d=r,o+=f-(1<<n-a)}for(o>>>=n-r;o>0;)(a=e[i].d)<r?(e[i].d++,o-=1<<r-a-1):i++;for(;i>=0;i--)e[i].d==r&&o<0&&(e[i].d--,o++);0!=o&&console.log("debt left")},i.F._goodIndex=function(e,r){var n=0;return r[16]<=e&&(n|=16),r[8|n]<=e&&(n|=8),r[4|n]<=e&&(n|=4),r[2|n]<=e&&(n|=2),r[1|n]<=e&&(n|=1),n},i.F._writeLit=function(e,r,n,f){return i.F._putsF(n,f,r[e<<1]),f+r[1+(e<<1)]},i.F.inflate=function(e,r){var n=Uint8Array;if(3==e[0]&&0==e[1])return r||new n(0);var f=i.F,o=f._bitsF,a=f._bitsE,l=f._decodeTiny,s=f.makeCodes,u=f.codes2map,c=f._get17,h=f.U,d=null==r;d&&(r=new n(e.length>>>2<<3));for(var g,p,A=0,w=0,m=0,v=0,b=0,y=0,U=0,F=0,x=0;0==A;)if(A=o(e,x,1),w=o(e,x+1,2),x+=3,0!=w){if(d&&(r=i.F._check(r,F+131072)),1==w&&(g=h.flmap,p=h.fdmap,y=511,U=31),2==w){m=a(e,x,5)+257,v=a(e,x+5,5)+1,b=a(e,x+10,4)+4,x+=14;for(var E=0;E<38;E+=2)h.itree[E]=0,h.itree[E+1]=0;var _=1;for(E=0;E<b;E++){var C=a(e,x+3*E,3);h.itree[1+(h.ordr[E]<<1)]=C,C>_&&(_=C)}x+=3*b,s(h.itree,_),u(h.itree,_,h.imap),g=h.lmap,p=h.dmap,x=l(h.imap,(1<<_)-1,m+v,e,x,h.ttree);var I=f._copyOut(h.ttree,0,m,h.ltree);y=(1<<I)-1;var M=f._copyOut(h.ttree,m,v,h.dtree);U=(1<<M)-1,s(h.ltree,I),u(h.ltree,I,g),s(h.dtree,M),u(h.dtree,M,p)}for(;;){var B=g[c(e,x)&y];x+=15&B;var R=B>>>4;if(R>>>8==0)r[F++]=R;else{if(256==R)break;var S=F+R-254;if(R>264){var T=h.ldef[R-257];S=F+(T>>>3)+a(e,x,7&T),x+=7&T}var Q=p[c(e,x)&U];x+=15&Q;var P=Q>>>4,k=h.ddef[P],H=(k>>>4)+o(e,x,15&k);for(x+=15&k,d&&(r=i.F._check(r,F+131072));F<S;)r[F]=r[F++-H],r[F]=r[F++-H],r[F]=r[F++-H],r[F]=r[F++-H];F=S}}}else{0!=(7&x)&&(x+=8-(7&x));var L=4+(x>>>3),O=e[L-4]|e[L-3]<<8;d&&(r=i.F._check(r,F+O)),r.set(new n(e.buffer,e.byteOffset+L,O),F),x=L+O<<3,F+=O}return r.length==F?r:r.slice(0,F)},i.F._check=function(e,r){var n=e.length;if(r<=n)return e;var i=new Uint8Array(Math.max(n<<1,r));return i.set(e,0),i},i.F._decodeTiny=function(e,r,n,f,o,a){for(var l=i.F._bitsE,s=i.F._get17,u=0;u<n;){var c=e[s(f,o)&r];o+=15&c;var h=c>>>4;if(h<=15)a[u]=h,u++;else{var d=0,g=0;16==h?(g=3+l(f,o,2),o+=2,d=a[u-1]):17==h?(g=3+l(f,o,3),o+=3):18==h&&(g=11+l(f,o,7),o+=7);for(var p=u+g;u<p;)a[u]=d,u++}}return o},i.F._copyOut=function(e,r,n,i){for(var f=0,o=0,a=i.length>>>1;o<n;){var l=e[o+r];i[o<<1]=0,i[1+(o<<1)]=l,l>f&&(f=l),o++}for(;o<a;)i[o<<1]=0,i[1+(o<<1)]=0,o++;return f},i.F.makeCodes=function(e,r){for(var n,f,o,a,l=i.F.U,s=e.length,u=l.bl_count,c=0;c<=r;c++)u[c]=0;for(c=1;c<s;c+=2)u[e[c]]++;var h=l.next_code;for(n=0,u[0]=0,f=1;f<=r;f++)n=n+u[f-1]<<1,h[f]=n;for(o=0;o<s;o+=2)0!=(a=e[o+1])&&(e[o]=h[a],h[a]++)},i.F.codes2map=function(e,r,n){for(var f=e.length,o=i.F.U.rev15,a=0;a<f;a+=2)if(0!=e[a+1])for(var l=a>>1,s=e[a+1],u=l<<4|s,c=r-s,h=e[a]<<c,d=h+(1<<c);h!=d;)n[o[h]>>>15-r]=u,h++},i.F.revCodes=function(e,r){for(var n=i.F.U.rev15,f=15-r,o=0;o<e.length;o+=2){var a=e[o]<<r-e[o+1];e[o]=n[a]>>>f}},i.F._putsE=function(e,r,n){n<<=7&r;var i=r>>>3;e[i]|=n,e[i+1]|=n>>>8},i.F._putsF=function(e,r,n){n<<=7&r;var i=r>>>3;e[i]|=n,e[i+1]|=n>>>8,e[i+2]|=n>>>16},i.F._bitsE=function(e,r,n){return(e[r>>>3]|e[1+(r>>>3)]<<8)>>>(7&r)&(1<<n)-1},i.F._bitsF=function(e,r,n){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16)>>>(7&r)&(1<<n)-1},i.F._get17=function(e,r){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16)>>>(7&r)},i.F._get25=function(e,r){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16|e[3+(r>>>3)]<<24)>>>(7&r)},i.F.U=(r=Uint16Array,n=Uint32Array,{next_code:new r(16),bl_count:new r(16),ordr:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],of0:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],exb:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],ldef:new r(32),df0:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],dxb:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],ddef:new n(32),flmap:new r(512),fltree:[],fdmap:new r(32),fdtree:[],lmap:new r(32768),ltree:[],ttree:[],dmap:new r(32768),dtree:[],imap:new r(512),itree:[],rev15:new r(32768),lhst:new n(286),dhst:new n(30),ihst:new n(19),lits:new n(15e3),strt:new r(65536),prev:new r(32768)}),function(){for(var e=i.F.U,r=0;r<32768;r++){var n=r;n=(0xff00ff00&(n=(0xf0f0f0f0&(n=(0xcccccccc&(n=(0xaaaaaaaa&n)>>>1|(0x55555555&n)<<1))>>>2|(0x33333333&n)<<2))>>>4|(0xf0f0f0f&n)<<4))>>>8|(0xff00ff&n)<<8,e.rev15[r]=(n>>>16|n<<16)>>>17}function f(e,r,n){for(;0!=r--;)e.push(0,n)}for(r=0;r<32;r++)e.ldef[r]=e.of0[r]<<3|e.exb[r],e.ddef[r]=e.df0[r]<<4|e.dxb[r];f(e.fltree,144,8),f(e.fltree,112,9),f(e.fltree,24,7),f(e.fltree,8,8),i.F.makeCodes(e.fltree,9),i.F.codes2map(e.fltree,9,e.flmap),i.F.revCodes(e.fltree,9),f(e.fdtree,32,5),i.F.makeCodes(e.fdtree,5),i.F.codes2map(e.fdtree,5,e.fdmap),i.F.revCodes(e.fdtree,5),f(e.itree,19,0),f(e.ltree,286,0),f(e.dtree,30,0),f(e.ttree,320,0)}()}();var s=function(e,r){return r.forEach(function(r){r&&"string"!=typeof r&&!Array.isArray(r)&&Object.keys(r).forEach(function(n){if("default"!==n&&!(n in e)){var i=Object.getOwnPropertyDescriptor(r,n);Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:function(){return r[n]}})}})}),Object.freeze(e)}({__proto__:null,default:a},[a]);let u=function(){var e={nextZero(e,r){for(;0!=e[r];)r++;return r},readUshort:(e,r)=>e[r]<<8|e[r+1],writeUshort(e,r,n){e[r]=n>>8&255,e[r+1]=255&n},readUint:(e,r)=>0x1000000*e[r]+(e[r+1]<<16|e[r+2]<<8|e[r+3]),writeUint(e,r,n){e[r]=n>>24&255,e[r+1]=n>>16&255,e[r+2]=n>>8&255,e[r+3]=255&n},readASCII(e,r,n){let i="";for(let f=0;f<n;f++)i+=String.fromCharCode(e[r+f]);return i},writeASCII(e,r,n){for(let i=0;i<n.length;i++)e[r+i]=n.charCodeAt(i)},readBytes(e,r,n){let i=[];for(let f=0;f<n;f++)i.push(e[r+f]);return i},pad:e=>e.length<2?`0${e}`:e,readUTF8(r,n,i){let f,o="";for(let f=0;f<i;f++)o+=`%${e.pad(r[n+f].toString(16))}`;try{f=decodeURIComponent(o)}catch(f){return e.readASCII(r,n,i)}return f}};function r(r,n,i,f){let a=n*i,l=Math.ceil(n*o(f)/8),s=new Uint8Array(4*a),u=new Uint32Array(s.buffer),{ctype:c}=f,{depth:h}=f,d=e.readUshort;if(6==c){let e=a<<2;if(8==h)for(var g=0;g<e;g+=4)s[g]=r[g],s[g+1]=r[g+1],s[g+2]=r[g+2],s[g+3]=r[g+3];if(16==h)for(g=0;g<e;g++)s[g]=r[g<<1]}else if(2==c){let e=f.tabs.tRNS;if(null==e){if(8==h)for(g=0;g<a;g++){var p=3*g;u[g]=-0x1000000|r[p+2]<<16|r[p+1]<<8|r[p]}if(16==h)for(g=0;g<a;g++)p=6*g,u[g]=-0x1000000|r[p+4]<<16|r[p+2]<<8|r[p]}else{var A=e[0];let n=e[1],i=e[2];if(8==h)for(g=0;g<a;g++){var w=g<<2;p=3*g,u[g]=-0x1000000|r[p+2]<<16|r[p+1]<<8|r[p],r[p]==A&&r[p+1]==n&&r[p+2]==i&&(s[w+3]=0)}if(16==h)for(g=0;g<a;g++)w=g<<2,p=6*g,u[g]=-0x1000000|r[p+4]<<16|r[p+2]<<8|r[p],d(r,p)==A&&d(r,p+2)==n&&d(r,p+4)==i&&(s[w+3]=0)}}else if(3==c){let e=f.tabs.PLTE,o=f.tabs.tRNS,u=o?o.length:0;if(1==h)for(var m,v=0;v<i;v++){var b=v*l,y=v*n;for(g=0;g<n;g++){w=y+g<<2;var U=3*(m=r[b+(g>>3)]>>7-(7&g)&1);s[w]=e[U],s[w+1]=e[U+1],s[w+2]=e[U+2],s[w+3]=m<u?o[m]:255}}if(2==h)for(v=0;v<i;v++)for(b=v*l,y=v*n,g=0;g<n;g++)w=y+g<<2,U=3*(m=r[b+(g>>2)]>>6-((3&g)<<1)&3),s[w]=e[U],s[w+1]=e[U+1],s[w+2]=e[U+2],s[w+3]=m<u?o[m]:255;if(4==h)for(v=0;v<i;v++)for(b=v*l,y=v*n,g=0;g<n;g++)w=y+g<<2,U=3*(m=r[b+(g>>1)]>>4-((1&g)<<2)&15),s[w]=e[U],s[w+1]=e[U+1],s[w+2]=e[U+2],s[w+3]=m<u?o[m]:255;if(8==h)for(g=0;g<a;g++)w=g<<2,U=3*(m=r[g]),s[w]=e[U],s[w+1]=e[U+1],s[w+2]=e[U+2],s[w+3]=m<u?o[m]:255}else if(4==c){if(8==h)for(g=0;g<a;g++){w=g<<2;var F,x=r[F=g<<1];s[w]=x,s[w+1]=x,s[w+2]=x,s[w+3]=r[F+1]}if(16==h)for(g=0;g<a;g++)w=g<<2,x=r[F=g<<2],s[w]=x,s[w+1]=x,s[w+2]=x,s[w+3]=r[F+2]}else if(0==c)for(A=f.tabs.tRNS?f.tabs.tRNS:-1,v=0;v<i;v++){let e=v*l,i=v*n;if(1==h)for(var E=0;E<n;E++){var _=255*((x=255*(r[e+(E>>>3)]>>>7-(7&E)&1))!=255*A);u[i+E]=_<<24|x<<16|x<<8|x}else if(2==h)for(E=0;E<n;E++)_=255*((x=85*(r[e+(E>>>2)]>>>6-((3&E)<<1)&3))!=85*A),u[i+E]=_<<24|x<<16|x<<8|x;else if(4==h)for(E=0;E<n;E++)_=255*((x=17*(r[e+(E>>>1)]>>>4-((1&E)<<2)&15))!=17*A),u[i+E]=_<<24|x<<16|x<<8|x;else if(8==h)for(E=0;E<n;E++)_=255*((x=r[e+E])!=A),u[i+E]=_<<24|x<<16|x<<8|x;else if(16==h)for(E=0;E<n;E++)x=r[e+(E<<1)],_=255*(d(r,e+(E<<1))!=A),u[i+E]=_<<24|x<<16|x<<8|x}return s}function n(e,r,n,l){let s=new Uint8Array((Math.ceil(n*o(e)/8)+1+e.interlace)*l);return r=e.tabs.CgBI?f(r,s):i(r,s),0==e.interlace?r=a(r,e,0,n,l):1==e.interlace&&(r=function(e,r){let n=r.width,i=r.height,f=o(r),l=f>>3,s=Math.ceil(n*f/8),u=new Uint8Array(i*s),c=0,h=[0,0,4,0,2,0,1],d=[0,4,0,2,0,1,0],g=[8,8,8,4,4,2,2],p=[8,8,4,4,2,2,1],A=0;for(;A<7;){let o=g[A],m=p[A],v=0,b=0,y=h[A];for(;y<i;)y+=o,b++;let U=d[A];for(;U<n;)U+=m,v++;let F=Math.ceil(v*f/8);a(e,r,c,v,b);let x=0,E=h[A];for(;E<i;){let r=d[A],i=c+x*F<<3;for(;r<n;){var w;if(1==f&&(w=(w=e[i>>3])>>7-(7&i)&1,u[E*s+(r>>3)]|=w<<7-(7&r)),2==f&&(w=(w=e[i>>3])>>6-(7&i)&3,u[E*s+(r>>2)]|=w<<6-((3&r)<<1)),4==f&&(w=(w=e[i>>3])>>4-(7&i)&15,u[E*s+(r>>1)]|=w<<4-((1&r)<<2)),f>=8){let n=E*s+r*l;for(let r=0;r<l;r++)u[n+r]=e[(i>>3)+r]}i+=f,r+=m}x++,E+=o}v*b!=0&&(c+=b*(1+F)),A+=1}return u}(r,e)),r}function i(e,r){return f(new Uint8Array(e.buffer,2,e.length-6),r)}var f=function(){let e={H:{}};return e.H.N=function(r,n){let i=Uint8Array,f,o,a=0,l=0,s=0,u=0,c=0,h=0,d=0,g=0,p=0;if(3==r[0]&&0==r[1])return n||new i(0);let A=e.H,w=A.b,m=A.e,v=A.R,b=A.n,y=A.A,U=A.Z,F=A.m,x=null==n;for(x&&(n=new i(r.length>>>2<<5));0==a;)if(a=w(r,p,1),l=w(r,p+1,2),p+=3,0!=l){if(x&&(n=e.H.W(n,g+131072)),1==l&&(f=F.J,o=F.h,h=511,d=31),2==l){s=m(r,p,5)+257,u=m(r,p+5,5)+1,c=m(r,p+10,4)+4,p+=14;let e=1;for(var E=0;E<38;E+=2)F.Q[E]=0,F.Q[E+1]=0;for(E=0;E<c;E++){let n=m(r,p+3*E,3);F.Q[1+(F.X[E]<<1)]=n,n>e&&(e=n)}p+=3*c,b(F.Q,e),y(F.Q,e,F.u),f=F.w,o=F.d,p=v(F.u,(1<<e)-1,s+u,r,p,F.v);let n=A.V(F.v,0,s,F.C);h=(1<<n)-1;let i=A.V(F.v,s,u,F.D);d=(1<<i)-1,b(F.C,n),y(F.C,n,f),b(F.D,i),y(F.D,i,o)}for(;;){let e=f[U(r,p)&h];p+=15&e;let i=e>>>4;if(i>>>8==0)n[g++]=i;else{if(256==i)break;{let e=g+i-254;if(i>264){let n=F.q[i-257];e=g+(n>>>3)+m(r,p,7&n),p+=7&n}let f=o[U(r,p)&d];p+=15&f;let a=f>>>4,l=F.c[a],s=(l>>>4)+w(r,p,15&l);for(p+=15&l;g<e;)n[g]=n[g++-s],n[g]=n[g++-s],n[g]=n[g++-s],n[g]=n[g++-s];g=e}}}}else{0!=(7&p)&&(p+=8-(7&p));let f=4+(p>>>3),o=r[f-4]|r[f-3]<<8;x&&(n=e.H.W(n,g+o)),n.set(new i(r.buffer,r.byteOffset+f,o),g),p=f+o<<3,g+=o}return n.length==g?n:n.slice(0,g)},e.H.W=function(e,r){let n=e.length;if(r<=n)return e;let i=new Uint8Array(n<<1);return i.set(e,0),i},e.H.R=function(r,n,i,f,o,a){let l=e.H.e,s=e.H.Z,u=0;for(;u<i;){let e=r[s(f,o)&n];o+=15&e;let i=e>>>4;if(i<=15)a[u]=i,u++;else{let e=0,r=0;16==i?(r=3+l(f,o,2),o+=2,e=a[u-1]):17==i?(r=3+l(f,o,3),o+=3):18==i&&(r=11+l(f,o,7),o+=7);let n=u+r;for(;u<n;)a[u]=e,u++}}return o},e.H.V=function(e,r,n,i){let f=0,o=0,a=i.length>>>1;for(;o<n;){let n=e[o+r];i[o<<1]=0,i[1+(o<<1)]=n,n>f&&(f=n),o++}for(;o<a;)i[o<<1]=0,i[1+(o<<1)]=0,o++;return f},e.H.n=function(r,n){let i,f,o,a,l=e.H.m,s=r.length,u=l.j;for(var c=0;c<=n;c++)u[c]=0;for(c=1;c<s;c+=2)u[r[c]]++;let h=l.K;for(i=0,u[0]=0,f=1;f<=n;f++)i=i+u[f-1]<<1,h[f]=i;for(o=0;o<s;o+=2)0!=(a=r[o+1])&&(r[o]=h[a],h[a]++)},e.H.A=function(r,n,i){let f=r.length,o=e.H.m.r;for(let e=0;e<f;e+=2)if(0!=r[e+1]){let f=e>>1,a=r[e+1],l=f<<4|a,s=n-a,u=r[e]<<s,c=u+(1<<s);for(;u!=c;)i[o[u]>>>15-n]=l,u++}},e.H.l=function(r,n){let i=e.H.m.r,f=15-n;for(let e=0;e<r.length;e+=2){let o=r[e]<<n-r[e+1];r[e]=i[o]>>>f}},e.H.M=function(e,r,n){n<<=7&r;let i=r>>>3;e[i]|=n,e[i+1]|=n>>>8},e.H.I=function(e,r,n){n<<=7&r;let i=r>>>3;e[i]|=n,e[i+1]|=n>>>8,e[i+2]|=n>>>16},e.H.e=function(e,r,n){return(e[r>>>3]|e[1+(r>>>3)]<<8)>>>(7&r)&(1<<n)-1},e.H.b=function(e,r,n){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16)>>>(7&r)&(1<<n)-1},e.H.Z=function(e,r){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16)>>>(7&r)},e.H.i=function(e,r){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16|e[3+(r>>>3)]<<24)>>>(7&r)},e.H.m=function(){let e=Uint16Array,r=Uint32Array;return{K:new e(16),j:new e(16),X:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],T:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],q:new e(32),p:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],z:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],c:new r(32),J:new e(512),_:[],h:new e(32),$:[],w:new e(32768),C:[],v:[],d:new e(32768),D:[],u:new e(512),Q:[],r:new e(32768),s:new r(286),Y:new r(30),a:new r(19),t:new r(15e3),k:new e(65536),g:new e(32768)}}(),function(){let r=e.H.m;for(var n=0;n<32768;n++){let e=n;e=(0xff00ff00&(e=(0xf0f0f0f0&(e=(0xcccccccc&(e=(0xaaaaaaaa&e)>>>1|(0x55555555&e)<<1))>>>2|(0x33333333&e)<<2))>>>4|(0xf0f0f0f&e)<<4))>>>8|(0xff00ff&e)<<8,r.r[n]=(e>>>16|e<<16)>>>17}function i(e,r,n){for(;0!=r--;)e.push(0,n)}for(n=0;n<32;n++)r.q[n]=r.S[n]<<3|r.T[n],r.c[n]=r.p[n]<<4|r.z[n];i(r._,144,8),i(r._,112,9),i(r._,24,7),i(r._,8,8),e.H.n(r._,9),e.H.A(r._,9,r.J),e.H.l(r._,9),i(r.$,32,5),e.H.n(r.$,5),e.H.A(r.$,5,r.h),e.H.l(r.$,5),i(r.Q,19,0),i(r.C,286,0),i(r.D,30,0),i(r.v,320,0)}(),e.H.N}();function o(e){return[1,null,3,1,2,null,4][e.ctype]*e.depth}function a(e,r,n,i,f){let a,s,u=o(r),c=Math.ceil(i*u/8);u=Math.ceil(u/8);let h=e[n],d=0;if(h>1&&(e[n]=[0,0,1][h-2]),3==h)for(d=u;d<c;d++)e[d+1]=e[d+1]+(e[d+1-u]>>>1)&255;for(let r=0;r<f;r++)if(h=e[(s=(a=n+r*c)+r+1)-1],d=0,0==h)for(;d<c;d++)e[a+d]=e[s+d];else if(1==h){for(;d<u;d++)e[a+d]=e[s+d];for(;d<c;d++)e[a+d]=e[s+d]+e[a+d-u]}else if(2==h)for(;d<c;d++)e[a+d]=e[s+d]+e[a+d-c];else if(3==h){for(;d<u;d++)e[a+d]=e[s+d]+(e[a+d-c]>>>1);for(;d<c;d++)e[a+d]=e[s+d]+(e[a+d-c]+e[a+d-u]>>>1)}else{for(;d<u;d++)e[a+d]=e[s+d]+l(0,e[a+d-c],0);for(;d<c;d++)e[a+d]=e[s+d]+l(e[a+d-u],e[a+d-c],e[a+d-u-c])}return e}function l(e,r,n){let i=e+r-n,f=i-e,o=i-r,a=i-n;return f*f<=o*o&&f*f<=a*a?e:o*o<=a*a?r:n}function s(e,r,n,i,f,o,a,l,s){let u=Math.min(r,f),c=Math.min(n,o),h=0,d=0;for(let n=0;n<c;n++)for(let o=0;o<u;o++)if(a>=0&&l>=0?(h=n*r+o<<2,d=(l+n)*f+a+o<<2):(h=(-l+n)*r-a+o<<2,d=n*f+o<<2),0==s)i[d]=e[h],i[d+1]=e[h+1],i[d+2]=e[h+2],i[d+3]=e[h+3];else if(1==s){var g=e[h+3]*(1/255),p=e[h]*g,A=e[h+1]*g,w=e[h+2]*g,m=i[d+3]*(1/255),v=i[d]*m,b=i[d+1]*m,y=i[d+2]*m;let r=1-g,n=g+m*r,f=0==n?0:1/n;i[d+3]=255*n,i[d+0]=(p+v*r)*f,i[d+1]=(A+b*r)*f,i[d+2]=(w+y*r)*f}else if(2==s)g=e[h+3],p=e[h],A=e[h+1],w=e[h+2],m=i[d+3],v=i[d],b=i[d+1],y=i[d+2],g==m&&p==v&&A==b&&w==y?(i[d]=0,i[d+1]=0,i[d+2]=0,i[d+3]=0):(i[d]=p,i[d+1]=A,i[d+2]=w,i[d+3]=g);else if(3==s){if(g=e[h+3],p=e[h],A=e[h+1],w=e[h+2],m=i[d+3],v=i[d],b=i[d+1],y=i[d+2],g==m&&p==v&&A==b&&w==y)continue;if(g<220&&m>20)return!1}return!0}return{decode:function(r){let o=new Uint8Array(r),a=8,l=e.readUshort,s=e.readUint,u={tabs:{},frames:[]},c=new Uint8Array(o.length),h,d=0,g=0,p=[137,80,78,71,13,10,26,10];for(var A,w=0;w<8;w++)if(o[w]!=p[w])throw"The input is not a PNG file!";for(;a<o.length;){let r=e.readUint(o,a);a+=4;let p=e.readASCII(o,a,4);if(a+=4,"IHDR"==p)m=a,u.width=e.readUint(o,m),m+=4,u.height=e.readUint(o,m),u.depth=o[m+=4],u.ctype=o[++m],u.compress=o[++m],u.filter=o[++m],u.interlace=o[++m],m++;else if("iCCP"==p){for(var m,v,b=a;0!=o[b];)b++;e.readASCII(o,a,b-a),o[b+1];let n=o.slice(b+2,a+r),l=null;try{l=i(n)}catch(e){l=f(n)}u.tabs[p]=l}else if("CgBI"==p)u.tabs[p]=o.slice(a,a+4);else if("IDAT"==p){for(w=0;w<r;w++)c[d+w]=o[a+w];d+=r}else if("acTL"==p)u.tabs[p]={num_frames:s(o,a),num_plays:s(o,a+4)},h=new Uint8Array(o.length);else if("fcTL"==p){0!=g&&((A=u.frames[u.frames.length-1]).data=n(u,h.slice(0,g),A.rect.width,A.rect.height),g=0);let e={x:s(o,a+12),y:s(o,a+16),width:s(o,a+4),height:s(o,a+8)},r=l(o,a+22),i={rect:e,delay:Math.round(1e3*(r=l(o,a+20)/(0==r?100:r))),dispose:o[a+24],blend:o[a+25]};u.frames.push(i)}else if("fdAT"==p){for(w=0;w<r-4;w++)h[g+w]=o[a+w+4];g+=r-4}else if("pHYs"==p)u.tabs[p]=[e.readUint(o,a),e.readUint(o,a+4),o[a+8]];else if("cHRM"==p)for(w=0,u.tabs[p]=[];w<8;w++)u.tabs[p].push(e.readUint(o,a+4*w));else if("tEXt"==p||"zTXt"==p){null==u.tabs[p]&&(u.tabs[p]={});var y=e.nextZero(o,a),U=e.readASCII(o,a,y-a),F=a+r-y-1;if("tEXt"==p)v=e.readASCII(o,y+1,F);else{var x=i(o.slice(y+2,y+2+F));v=e.readUTF8(x,0,x.length)}u.tabs[p][U]=v}else if("iTXt"==p){null==u.tabs[p]&&(u.tabs[p]={}),y=0,b=a,y=e.nextZero(o,b),U=e.readASCII(o,b,y-b);let n=o[b=y+1];o[b+1],b+=2,y=e.nextZero(o,b),e.readASCII(o,b,y-b),b=y+1,y=e.nextZero(o,b),e.readUTF8(o,b,y-b),F=r-((b=y+1)-a),0==n?v=e.readUTF8(o,b,F):(x=i(o.slice(b,b+F)),v=e.readUTF8(x,0,x.length)),u.tabs[p][U]=v}else if("PLTE"==p)u.tabs[p]=e.readBytes(o,a,r);else if("hIST"==p){let e=u.tabs.PLTE.length/3;for(w=0,u.tabs[p]=[];w<e;w++)u.tabs[p].push(l(o,a+2*w))}else if("tRNS"==p)3==u.ctype?u.tabs[p]=e.readBytes(o,a,r):0==u.ctype?u.tabs[p]=l(o,a):2==u.ctype&&(u.tabs[p]=[l(o,a),l(o,a+2),l(o,a+4)]);else if("gAMA"==p)u.tabs[p]=e.readUint(o,a)/1e5;else if("sRGB"==p)u.tabs[p]=o[a];else if("bKGD"==p)0==u.ctype||4==u.ctype?u.tabs[p]=[l(o,a)]:2==u.ctype||6==u.ctype?u.tabs[p]=[l(o,a),l(o,a+2),l(o,a+4)]:3==u.ctype&&(u.tabs[p]=o[a]);else if("IEND"==p)break;a+=r,e.readUint(o,a),a+=4}return 0!=g&&((A=u.frames[u.frames.length-1]).data=n(u,h.slice(0,g),A.rect.width,A.rect.height)),u.data=n(u,c,u.width,u.height),delete u.compress,delete u.interlace,delete u.filter,u},toRGBA8:function(e){let n=e.width,i=e.height;if(null==e.tabs.acTL)return[r(e.data,n,i,e).buffer];let f=[];null==e.frames[0].data&&(e.frames[0].data=e.data);let o=n*i*4,a=new Uint8Array(o),l=new Uint8Array(o),u=new Uint8Array(o);for(let h=0;h<e.frames.length;h++){let d=e.frames[h],g=d.rect.x,p=d.rect.y,A=d.rect.width,w=d.rect.height,m=r(d.data,A,w,e);if(0!=h)for(var c=0;c<o;c++)u[c]=a[c];if(0==d.blend?s(m,A,w,a,n,i,g,p,0):1==d.blend&&s(m,A,w,a,n,i,g,p,1),f.push(a.buffer.slice(0)),0==d.dispose);else if(1==d.dispose)s(l,A,w,a,n,i,g,p,0);else if(2==d.dispose)for(c=0;c<o;c++)a[c]=u[c]}return f},_paeth:l,_copyTile:s,_bin:e}}();!function(){let{_copyTile:e}=u,{_bin:r}=u,n=u._paeth;var i={table:function(){let e=new Uint32Array(256);for(let r=0;r<256;r++){let n=r;for(let e=0;e<8;e++)1&n?n=0xedb88320^n>>>1:n>>>=1;e[r]=n}return e}(),update(e,r,n,f){for(let o=0;o<f;o++)e=i.table[255&(e^r[n+o])]^e>>>8;return e},crc:(e,r,n)=>0xffffffff^i.update(0xffffffff,e,r,n)};function f(e,r,n,i){r[n]+=e[0]*i>>4,r[n+1]+=e[1]*i>>4,r[n+2]+=e[2]*i>>4,r[n+3]+=e[3]*i>>4}function o(e){return Math.max(0,Math.min(255,e))}function a(e,r){let n=e[0]-r[0],i=e[1]-r[1],f=e[2]-r[2],o=e[3]-r[3];return n*n+i*i+f*f+o*o}function l(e,r,n,i,l,s,u){null==u&&(u=1);let c=i.length,h=[];for(var d,g=0;g<c;g++){let e=i[g];h.push([e>>>0&255,e>>>8&255,e>>>16&255,e>>>24&255])}for(g=0;g<c;g++){let e=0xffffffff;for(var p=0,A=0;A<c;A++){var w=a(h[g],h[A]);A!=g&&w<e&&(e=w,p=A)}}let m=new Uint32Array(l.buffer),v=new Int16Array(r*n*4),b=[0,8,2,10,12,4,14,6,3,11,1,9,15,7,13,5];for(g=0;g<b.length;g++)b[g]=255*((b[g]+.5)/16-.5);for(let l=0;l<n;l++)for(let y=0;y<r;y++){g=4*(l*r+y),2!=u?d=[o(e[g]+v[g]),o(e[g+1]+v[g+1]),o(e[g+2]+v[g+2]),o(e[g+3]+v[g+3])]:(w=b[4*(3&l)+(3&y)],d=[o(e[g]+w),o(e[g+1]+w),o(e[g+2]+w),o(e[g+3]+w)]),p=0;let U=0xffffff;for(A=0;A<c;A++){let e=a(d,h[A]);e<U&&(U=e,p=A)}let F=h[p],x=[d[0]-F[0],d[1]-F[1],d[2]-F[2],d[3]-F[3]];1==u&&(y!=r-1&&f(x,v,g+4,7),l!=n-1&&(0!=y&&f(x,v,g+4*r-4,3),f(x,v,g+4*r,5),y!=r-1&&f(x,v,g+4*r+4,1))),s[g>>2]=p,m[g>>2]=i[p]}}function c(e,n,f,o,a){null==a&&(a={});let{crc:l}=i,s=r.writeUint,u=r.writeUshort,c=r.writeASCII,h=8,d=e.frames.length>1,g,p=!1,A=33+20*!!d;if(null!=a.sRGB&&(A+=13),null!=a.pHYs&&(A+=21),null!=a.iCCP&&(A+=21+(g=pako.deflate(a.iCCP)).length+4),3==e.ctype){for(var w=e.plte.length,m=0;m<w;m++)e.plte[m]>>>24!=255&&(p=!0);A+=8+3*w+4+(p?8+ +w+4:0)}for(var v=0;v<e.frames.length;v++)d&&(A+=38),A+=(F=e.frames[v]).cimg.length+12,0!=v&&(A+=4);let b=new Uint8Array(A+=12),y=[137,80,78,71,13,10,26,10];for(m=0;m<8;m++)b[m]=y[m];if(s(b,h,13),c(b,h+=4,"IHDR"),s(b,h+=4,n),s(b,h+=4,f),b[h+=4]=e.depth,b[++h]=e.ctype,b[++h]=0,b[++h]=0,b[++h]=0,s(b,++h,l(b,h-17,17)),h+=4,null!=a.sRGB&&(s(b,h,1),c(b,h+=4,"sRGB"),b[h+=4]=a.sRGB,s(b,++h,l(b,h-5,5)),h+=4),null!=a.iCCP){let e=13+g.length;s(b,h,e),c(b,h+=4,"iCCP"),c(b,h+=4,"ICC profile"),h+=11,h+=2,b.set(g,h),s(b,h+=g.length,l(b,h-(e+4),e+4)),h+=4}if(null!=a.pHYs&&(s(b,h,9),c(b,h+=4,"pHYs"),s(b,h+=4,a.pHYs[0]),s(b,h+=4,a.pHYs[1]),b[h+=4]=a.pHYs[2],s(b,++h,l(b,h-13,13)),h+=4),d&&(s(b,h,8),c(b,h+=4,"acTL"),s(b,h+=4,e.frames.length),s(b,h+=4,null!=a.loop?a.loop:0),s(b,h+=4,l(b,h-12,12)),h+=4),3==e.ctype){for(s(b,h,3*(w=e.plte.length)),c(b,h+=4,"PLTE"),h+=4,m=0;m<w;m++){let r=3*m,n=e.plte[m],i=255&n,f=n>>>8&255,o=n>>>16&255;b[h+r+0]=i,b[h+r+1]=f,b[h+r+2]=o}if(s(b,h+=3*w,l(b,h-3*w-4,3*w+4)),h+=4,p){for(s(b,h,w),c(b,h+=4,"tRNS"),h+=4,m=0;m<w;m++)b[h+m]=e.plte[m]>>>24&255;s(b,h+=w,l(b,h-w-4,w+4)),h+=4}}let U=0;for(v=0;v<e.frames.length;v++){var F=e.frames[v];d&&(s(b,h,26),c(b,h+=4,"fcTL"),s(b,h+=4,U++),s(b,h+=4,F.rect.width),s(b,h+=4,F.rect.height),s(b,h+=4,F.rect.x),s(b,h+=4,F.rect.y),u(b,h+=4,o[v]),u(b,h+=2,1e3),b[h+=2]=F.dispose,b[++h]=F.blend,s(b,++h,l(b,h-30,30)),h+=4);let r=F.cimg;s(b,h,(w=r.length)+4*(0!=v));let n=h+=4;c(b,h,0==v?"IDAT":"fdAT"),h+=4,0!=v&&(s(b,h,U++),h+=4),b.set(r,h),s(b,h+=w,l(b,n,h-n)),h+=4}return s(b,h,0),c(b,h+=4,"IEND"),s(b,h+=4,l(b,h-4,4)),h+=4,b.buffer}function h(e,r,i){for(let f=0;f<e.frames.length;f++){let o=e.frames[f];o.rect.width;let a=o.rect.height,l=new Uint8Array(a*o.bpl+a);o.cimg=function(e,r,i,f,o,a,l){let u=[],c,h=[0,1,2,3,4];-1!=a?h=[a]:(r*f>5e5||1==i)&&(h=[0]),l&&(c={level:0});for(var d=0;d<h.length;d++){for(let a=0;a<r;a++)!function(e,r,i,f,o,a){let l=i*f,s=l+i;if(e[s]=a,s++,0==a)if(f<500)for(var u=0;u<f;u++)e[s+u]=r[l+u];else e.set(new Uint8Array(r.buffer,l,f),s);else if(1==a){for(u=0;u<o;u++)e[s+u]=r[l+u];for(u=o;u<f;u++)e[s+u]=r[l+u]-r[l+u-o]+256&255}else if(0==i){for(u=0;u<o;u++)e[s+u]=r[l+u];if(2==a)for(u=o;u<f;u++)e[s+u]=r[l+u];if(3==a)for(u=o;u<f;u++)e[s+u]=r[l+u]-(r[l+u-o]>>1)+256&255;if(4==a)for(u=o;u<f;u++)e[s+u]=r[l+u]-n(r[l+u-o],0,0)+256&255}else{if(2==a)for(u=0;u<f;u++)e[s+u]=r[l+u]+256-r[l+u-f]&255;if(3==a){for(u=0;u<o;u++)e[s+u]=r[l+u]+256-(r[l+u-f]>>1)&255;for(u=o;u<f;u++)e[s+u]=r[l+u]+256-(r[l+u-f]+r[l+u-o]>>1)&255}if(4==a){for(u=0;u<o;u++)e[s+u]=r[l+u]+256-n(0,r[l+u-f],0)&255;for(u=o;u<f;u++)e[s+u]=r[l+u]+256-n(r[l+u-o],r[l+u-f],r[l+u-o-f])&255}}}(o,e,a,f,i,h[d]);u.push(s.deflate(o,c))}let g,p=1e9;for(d=0;d<u.length;d++)u[d].length<p&&(g=d,p=u[d].length);return u[g]}(o.img,a,o.bpp,o.bpl,l,r,i)}}function d(r,n,i,f,o){let a=o[0],s=o[1],u=o[2],c=o[3],h=o[4],d=o[5],w=6,m=8,v=255;for(var b=0;b<r.length;b++){let e=new Uint8Array(r[b]);for(var y=e.length,U=0;U<y;U+=4)v&=e[U+3]}let F=255!=v,x=function(r,n,i,f,o,a){let l=[];for(var s,u=0;u<r.length;u++){let s=new Uint8Array(r[u]),g=new Uint32Array(s.buffer),A=0,w=0,m=n,v=i,b=+!!f;if(0!=u){let y=a||f||1==u||0!=l[u-2].dispose?1:2,U=0,F=1e9;for(let e=0;e<y;e++){var c,h=new Uint8Array(r[u-1-e]);let f=new Uint32Array(r[u-1-e]),a=n,l=i,s=-1,p=-1;for(let e=0;e<i;e++)for(let r=0;r<n;r++)g[d=e*n+r]!=f[d]&&(r<a&&(a=r),r>s&&(s=r),e<l&&(l=e),e>p&&(p=e));-1==s&&(a=l=s=p=0),o&&(1==(1&a)&&a--,1==(1&l)&&l--);let b=(s-a+1)*(p-l+1);b<F&&(F=b,U=e,A=a,w=l,m=s-a+1,v=p-l+1)}h=new Uint8Array(r[u-1-U]),1==U&&(l[u-1].dispose=2),e(h,n,i,c=new Uint8Array(m*v*4),m,v,-A,-w,0),1==(b=+!!e(s,n,i,c,m,v,-A,-w,3))?p(s,n,i,c,{x:A,y:w,width:m,height:v}):e(s,n,i,c,m,v,-A,-w,0)}else c=s.slice(0);l.push({rect:{x:A,y:w,width:m,height:v},img:c,blend:b,dispose:0})}if(f)for(u=0;u<l.length;u++){if(1==(s=l[u]).blend)continue;let e=s.rect,f=l[u-1].rect,a=Math.min(e.x,f.x),c=Math.min(e.y,f.y),h={x:a,y:c,width:Math.max(e.x+e.width,f.x+f.width)-a,height:Math.max(e.y+e.height,f.y+f.height)-c};l[u-1].dispose=1,u-1!=0&&g(r,n,i,l,u-1,h,o),g(r,n,i,l,u,h,o)}if(1!=r.length)for(var d=0;d<l.length;d++)(s=l[d]).rect.width,s.rect.height;return l}(r,n,i,a,s,u),E={},_=[],C=[];if(0!=f){let e=[];for(U=0;U<x.length;U++)e.push(x[U].img.buffer);let r=A(function(e){let r=0;for(var n=0;n<e.length;n++)r+=e[n].byteLength;let i=new Uint8Array(r),f=0;for(n=0;n<e.length;n++){let r=new Uint8Array(e[n]),o=r.length;for(let e=0;e<o;e+=4){let n=r[e],o=r[e+1],a=r[e+2],l=r[e+3];0==l&&(n=o=a=0),i[f+e]=n,i[f+e+1]=o,i[f+e+2]=a,i[f+e+3]=l}f+=o}return i.buffer}(e),f);for(U=0;U<r.plte.length;U++)_.push(r.plte[U].est.rgba);let n=0;for(U=0;U<x.length;U++){let e=(M=x[U]).img.length;var I=new Uint8Array(r.inds.buffer,n>>2,e>>2);C.push(I);let i=new Uint8Array(r.abuf,n,e);d&&l(M.img,M.rect.width,M.rect.height,_,i,I),M.img.set(i),n+=e}}else for(b=0;b<x.length;b++){var M=x[b];let e=new Uint32Array(M.img.buffer);var B=M.rect.width;for(I=new Uint8Array(y=e.length),C.push(I),U=0;U<y;U++){let r=e[U];if(0!=U&&r==e[U-1])I[U]=I[U-1];else if(U>B&&r==e[U-B])I[U]=I[U-B];else{let e=E[r];if(null==e&&(E[r]=e=_.length,_.push(r),_.length>=300))break;I[U]=e}}}let R=_.length;for(R<=256&&0==h&&(m=Math.max(m=R<=2?1:R<=4?2:R<=16?4:8,c)),b=0;b<x.length;b++){(M=x[b]).rect.x,M.rect.y,B=M.rect.width;let e=M.rect.height,r=M.img;new Uint32Array(r.buffer);let n=4*B,i=4;if(R<=256&&0==h){var S=new Uint8Array((n=Math.ceil(m*B/8))*e);let f=C[b];for(let r=0;r<e;r++){U=r*n;let e=r*B;if(8==m)for(var T=0;T<B;T++)S[U+T]=f[e+T];else if(4==m)for(T=0;T<B;T++)S[U+(T>>1)]|=f[e+T]<<4-4*(1&T);else if(2==m)for(T=0;T<B;T++)S[U+(T>>2)]|=f[e+T]<<6-2*(3&T);else if(1==m)for(T=0;T<B;T++)S[U+(T>>3)]|=f[e+T]<<7-(7&T)}r=S,w=3,i=1}else if(0==F&&1==x.length){S=new Uint8Array(B*e*3);let f=B*e;for(U=0;U<f;U++){let e=3*U,n=4*U;S[e]=r[n],S[e+1]=r[n+1],S[e+2]=r[n+2]}r=S,w=2,i=3,n=3*B}M.img=r,M.bpl=n,M.bpp=i}return{ctype:w,depth:m,plte:_,frames:x}}function g(r,n,i,f,o,a,l){let s=Uint8Array,u=Uint32Array,c=new s(r[o-1]),h=new u(r[o-1]),d=o+1<r.length?new s(r[o+1]):null,g=new s(r[o]),A=new u(g.buffer),w=n,m=i,v=-1,b=-1;for(let e=0;e<a.height;e++)for(let r=0;r<a.width;r++){let i=a.x+r,l=a.y+e,s=l*n+i,u=A[s];0==u||0==f[o-1].dispose&&h[s]==u&&(null==d||0!=d[4*s+3])||(i<w&&(w=i),i>v&&(v=i),l<m&&(m=l),l>b&&(b=l))}-1==v&&(w=m=v=b=0),l&&(1==(1&w)&&w--,1==(1&m)&&m--),a={x:w,y:m,width:v-w+1,height:b-m+1};let y=f[o];y.rect=a,y.blend=1,y.img=new Uint8Array(a.width*a.height*4),0==f[o-1].dispose?(e(c,n,i,y.img,a.width,a.height,-a.x,-a.y,0),p(g,n,i,y.img,a)):e(g,n,i,y.img,a.width,a.height,-a.x,-a.y,0)}function p(r,n,i,f,o){e(r,n,i,f,o.width,o.height,-o.x,-o.y,2)}function A(e,r){let n,i=new Uint8Array(e),f=i.slice(0),o=new Uint32Array(f.buffer),a=w(f,r),l=a[0],s=a[1],u=i.length,c=new Uint8Array(u>>2);if(i.length<2e7)for(var h=0;h<u;h+=4)n=m(l,d=i[h]*(1/255),g=i[h+1]*(1/255),p=i[h+2]*(1/255),A=i[h+3]*(1/255)),c[h>>2]=n.ind,o[h>>2]=n.est.rgba;else for(h=0;h<u;h+=4){var d=i[h]*(1/255),g=i[h+1]*(1/255),p=i[h+2]*(1/255),A=i[h+3]*(1/255);for(n=l;n.left;)n=0>=v(n.est,d,g,p,A)?n.left:n.right;c[h>>2]=n.ind,o[h>>2]=n.est.rgba}return{abuf:f.buffer,inds:c,plte:s}}function w(e,r,n){null==n&&(n=1e-4);let i=new Uint32Array(e.buffer),f={i0:0,i1:e.length,bst:null,est:null,tdst:0,left:null,right:null};f.bst=y(e,f.i0,f.i1),f.est=U(f.bst);let o=[f];for(;o.length<r;){let r=0,f=0;for(var a=0;a<o.length;a++)o[a].est.L>r&&(r=o[a].est.L,f=a);if(r<n)break;let l=o[f],s=function(e,r,n,i,f,o){for(i-=4;n<i;){for(;b(e,n,f)<=o;)n+=4;for(;b(e,i,f)>o;)i-=4;if(n>=i)break;let a=r[n>>2];r[n>>2]=r[i>>2],r[i>>2]=a,n+=4,i-=4}for(;b(e,n,f)>o;)n-=4;return n+4}(e,i,l.i0,l.i1,l.est.e,l.est.eMq255);if(l.i0>=s||l.i1<=s){l.est.L=0;continue}let u={i0:l.i0,i1:s,bst:null,est:null,tdst:0,left:null,right:null};u.bst=y(e,u.i0,u.i1),u.est=U(u.bst);let c={i0:s,i1:l.i1,bst:null,est:null,tdst:0,left:null,right:null};for(a=0,c.bst={R:[],m:[],N:l.bst.N-u.bst.N};a<16;a++)c.bst.R[a]=l.bst.R[a]-u.bst.R[a];for(a=0;a<4;a++)c.bst.m[a]=l.bst.m[a]-u.bst.m[a];c.est=U(c.bst),l.left=u,l.right=c,o[f]=u,o.push(c)}for(o.sort((e,r)=>r.bst.N-e.bst.N),a=0;a<o.length;a++)o[a].ind=a;return[f,o]}function m(e,r,n,i,f){if(null==e.left)return e.tdst=function(e,r,n,i,f){let o=r-e[0],a=n-e[1],l=i-e[2],s=f-e[3];return o*o+a*a+l*l+s*s}(e.est.q,r,n,i,f),e;let o=v(e.est,r,n,i,f),a=e.left,l=e.right;o>0&&(a=e.right,l=e.left);let s=m(a,r,n,i,f);if(s.tdst<=o*o)return s;let u=m(l,r,n,i,f);return u.tdst<s.tdst?u:s}function v(e,r,n,i,f){let{e:o}=e;return o[0]*r+o[1]*n+o[2]*i+o[3]*f-e.eMq}function b(e,r,n){return e[r]*n[0]+e[r+1]*n[1]+e[r+2]*n[2]+e[r+3]*n[3]}function y(e,r,n){let i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],f=[0,0,0,0];for(let o=r;o<n;o+=4){let r=e[o]*(1/255),n=e[o+1]*(1/255),a=e[o+2]*(1/255),l=e[o+3]*(1/255);f[0]+=r,f[1]+=n,f[2]+=a,f[3]+=l,i[0]+=r*r,i[1]+=r*n,i[2]+=r*a,i[3]+=r*l,i[5]+=n*n,i[6]+=n*a,i[7]+=n*l,i[10]+=a*a,i[11]+=a*l,i[15]+=l*l}return i[4]=i[1],i[8]=i[2],i[9]=i[6],i[12]=i[3],i[13]=i[7],i[14]=i[11],{R:i,m:f,N:n-r>>2}}function U(e){let{R:r}=e,{m:n}=e,{N:i}=e,f=n[0],o=n[1],a=n[2],l=n[3],s=0==i?0:1/i,u=[r[0]-f*f*s,r[1]-f*o*s,r[2]-f*a*s,r[3]-f*l*s,r[4]-o*f*s,r[5]-o*o*s,r[6]-o*a*s,r[7]-o*l*s,r[8]-a*f*s,r[9]-a*o*s,r[10]-a*a*s,r[11]-a*l*s,r[12]-l*f*s,r[13]-l*o*s,r[14]-l*a*s,r[15]-l*l*s],c=[Math.random(),Math.random(),Math.random(),Math.random()],h=0,d=0;if(0!=i)for(let e=0;e<16&&(c=F.multVec(u,c),d=Math.sqrt(F.dot(c,c)),c=F.sml(1/d,c),!(0!=e&&1e-9>Math.abs(d-h)));e++)h=d;let g=[f*s,o*s,a*s,l*s];return{Cov:u,q:g,e:c,L:h,eMq255:F.dot(F.sml(255,g),c),eMq:F.dot(c,g),rgba:(Math.round(255*g[3])<<24|Math.round(255*g[2])<<16|Math.round(255*g[1])<<8|(0|Math.round(255*g[0])))>>>0}}var F={multVec:(e,r)=>[e[0]*r[0]+e[1]*r[1]+e[2]*r[2]+e[3]*r[3],e[4]*r[0]+e[5]*r[1]+e[6]*r[2]+e[7]*r[3],e[8]*r[0]+e[9]*r[1]+e[10]*r[2]+e[11]*r[3],e[12]*r[0]+e[13]*r[1]+e[14]*r[2]+e[15]*r[3]],dot:(e,r)=>e[0]*r[0]+e[1]*r[1]+e[2]*r[2]+e[3]*r[3],sml:(e,r)=>[e*r[0],e*r[1],e*r[2],e*r[3]]};u.encode=function(e,r,n,i,f,o,a){null==i&&(i=0),null==a&&(a=!1);let l=d(e,r,n,i,[!1,!1,!1,0,a,!1]);return h(l,-1),c(l,r,n,f,o)},u.encodeLL=function(e,r,n,i,f,o,a,l){let s={ctype:0+2*(1!=i)+4*(0!=f),depth:o,frames:[]},u=(i+f)*o,d=u*r;for(let i=0;i<e.length;i++)s.frames.push({rect:{x:0,y:0,width:r,height:n},img:new Uint8Array(e[i]),blend:0,dispose:1,bpp:Math.ceil(u/8),bpl:Math.ceil(d/8)});return h(s,0,!0),c(s,r,n,a,l)},u.encode.compress=d,u.encode.dither=l,u.quantize=A,u.quantize.getKDtree=w,u.quantize.getNearest=m}();let c={toArrayBuffer(e,r){let n=e.width,i=e.height,f=n<<2,o=new Uint32Array(e.getContext("2d").getImageData(0,0,n,i).data.buffer),a=(32*n+31)/32<<2,l=a*i,s=122+l,u=new ArrayBuffer(s),h=new DataView(u),d,g,p,A,w=1048576,m=0,v=0,b=0;function y(e){h.setUint16(v,e,!0),v+=2}function U(e){h.setUint32(v,e,!0),v+=4}y(19778),U(s),v+=4,U(122),U(108),U(n),U(-i>>>0),y(1),y(32),U(3),U(l),U(2835),U(2835),v+=8,U(0xff0000),U(65280),U(255),U(0xff000000),U(0x57696e20),function e(){for(;m<i&&w>0;){for(A=122+m*a,d=0;d<f;)w--,p=(g=o[b++])>>>24,h.setUint32(A+d,g<<8|p),d+=4;m++}b<o.length?(w=1048576,setTimeout(e,c._dly)):r(u)}()},toBlob(e,r){this.toArrayBuffer(e,e=>{r(new Blob([e],{type:"image/bmp"}))})},_dly:9};var h={CHROME:"CHROME",FIREFOX:"FIREFOX",DESKTOP_SAFARI:"DESKTOP_SAFARI",IE:"IE",IOS:"IOS",ETC:"ETC"},d={[h.CHROME]:16384,[h.FIREFOX]:11180,[h.DESKTOP_SAFARI]:16384,[h.IE]:8192,[h.IOS]:4096,[h.ETC]:8192};let g="undefined"!=typeof window,p="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,A=g&&window.cordova&&window.cordova.require&&window.cordova.require("cordova/modulemapper"),w=(g||p)&&(A&&A.getOriginalSymbol(window,"File")||"undefined"!=typeof File&&File),m=(g||p)&&(A&&A.getOriginalSymbol(window,"FileReader")||"undefined"!=typeof FileReader&&FileReader);function v(e,r,n=Date.now()){return new Promise(i=>{let f=e.split(","),o=f[0].match(/:(.*?);/)[1],a=globalThis.atob(f[1]),l=a.length,s=new Uint8Array(l);for(;l--;)s[l]=a.charCodeAt(l);let u=new Blob([s],{type:o});u.name=r,u.lastModified=n,i(u)})}function b(e){return new Promise((r,n)=>{let i=new m;i.onload=()=>r(i.result),i.onerror=e=>n(e),i.readAsDataURL(e)})}function y(e){return new Promise((r,n)=>{let i=new Image;i.onload=()=>r(i),i.onerror=e=>n(e),i.src=e})}function U(){if(void 0!==U.cachedResult)return U.cachedResult;let e=h.ETC,{userAgent:r}=navigator;return/Chrom(e|ium)/i.test(r)?e=h.CHROME:/iP(ad|od|hone)/i.test(r)&&/WebKit/i.test(r)?e=h.IOS:/Safari/i.test(r)?e=h.DESKTOP_SAFARI:/Firefox/i.test(r)?e=h.FIREFOX:(/MSIE/i.test(r)||!0==!!document.documentMode)&&(e=h.IE),U.cachedResult=e,U.cachedResult}function F(e,r){let n=d[U()],i=e,f=r,o=i*f,a=i>f?f/i:i/f;for(;o>n*n;){let e=(n+i)/2,r=(n+f)/2;e<r?(f=r,i=r*a):(f=e*a,i=e),o=i*f}return{width:i,height:f}}function x(e,r){let n,i;try{if(i=(n=new OffscreenCanvas(e,r)).getContext("2d"),null===i)throw Error("getContext of OffscreenCanvas returns null")}catch(e){i=(n=document.createElement("canvas")).getContext("2d")}return n.width=e,n.height=r,[n,i]}function E(e,r){let{width:n,height:i}=F(e.width,e.height),[f,o]=x(n,i);return r&&/jpe?g/.test(r)&&(o.fillStyle="white",o.fillRect(0,0,f.width,f.height)),o.drawImage(e,0,0,f.width,f.height),f}function _(){return void 0!==_.cachedResult||(_.cachedResult=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"undefined"!=typeof document&&"ontouchend"in document),_.cachedResult}function C(e,r={}){return new Promise(function(n,i){let f,o;var a=function(){try{return o=E(f,r.fileType||e.type),n([f,o])}catch(e){return i(e)}},l=function(r){try{var n=function(e){try{throw e}catch(e){return i(e)}};try{let r;return b(e).then(function(e){try{return r=e,y(r).then(function(e){try{return f=e,function(){try{return a()}catch(e){return i(e)}}()}catch(e){return n(e)}},n)}catch(e){return n(e)}},n)}catch(e){n(e)}}catch(e){return i(e)}};try{if(_()||[h.DESKTOP_SAFARI,h.MOBILE_SAFARI].includes(U()))throw Error("Skip createImageBitmap on IOS and Safari");return createImageBitmap(e).then(function(e){try{return f=e,a()}catch(e){return l()}},l)}catch(e){l()}})}function I(e,r,n,i,f=1){return new Promise(function(o,a){let l;if("image/png"===r){let o,a;return o=e.getContext("2d"),{data:a}=o.getImageData(0,0,e.width,e.height),(l=new Blob([u.encode([a.buffer],e.width,e.height,4096*f)],{type:r})).name=n,l.lastModified=i,d.call(this)}if("image/bmp"===r)return new Promise(r=>c.toBlob(e,r)).then((function(e){try{return(l=e).name=n,l.lastModified=i,h.call(this)}catch(e){return a(e)}}).bind(this),a);if("function"==typeof OffscreenCanvas&&e instanceof OffscreenCanvas)return e.convertToBlob({type:r,quality:f}).then((function(e){try{return(l=e).name=n,l.lastModified=i,s.call(this)}catch(e){return a(e)}}).bind(this),a);return v(e.toDataURL(r,f),n,i).then((function(e){try{return l=e,s.call(this)}catch(e){return a(e)}}).bind(this),a);function s(){return h.call(this)}function h(){return d.call(this)}function d(){return o(l)}})}function M(e){e.width=0,e.height=0}function B(){return new Promise(function(e,r){let n,i,f,o;return void 0!==B.cachedResult?e(B.cachedResult):v("data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==","test.jpg",Date.now()).then(function(a){try{return n=a,C(n).then(function(a){try{return i=a[1],I(i,n.type,n.name,n.lastModified).then(function(n){try{return f=n,M(i),C(f).then(function(n){try{return o=n[0],B.cachedResult=1===o.width&&2===o.height,e(B.cachedResult)}catch(e){return r(e)}},r)}catch(e){return r(e)}},r)}catch(e){return r(e)}},r)}catch(e){return r(e)}},r)})}function R(e){return new Promise((r,n)=>{let i=new m;i.onload=e=>{let n=new DataView(e.target.result);if(65496!=n.getUint16(0,!1))return r(-2);let i=n.byteLength,f=2;for(;f<i&&!(8>=n.getUint16(f+2,!1));){let e=n.getUint16(f,!1);if(f+=2,65505==e){if(0x45786966!=n.getUint32(f+=2,!1))return r(-1);let e=18761==n.getUint16(f+=6,!1);f+=n.getUint32(f+4,e);let i=n.getUint16(f,e);f+=2;for(let o=0;o<i;o++)if(274==n.getUint16(f+12*o,e))return r(n.getUint16(f+12*o+8,e))}else{if(65280!=(65280&e))break;f+=n.getUint16(f,!1)}}return r(-1)},i.onerror=e=>n(e),i.readAsArrayBuffer(e)})}function S(e,r){let{width:n}=e,{height:i}=e,{maxWidthOrHeight:f}=r,o,a=e;return isFinite(f)&&(n>f||i>f)&&([a,o]=x(n,i),n>i?(a.width=f,a.height=i/n*f):(a.width=n/i*f,a.height=f),o.drawImage(e,0,0,a.width,a.height),M(e)),a}function T(e,r){let{width:n}=e,{height:i}=e,[f,o]=x(n,i);switch(r>4&&r<9?(f.width=i,f.height=n):(f.width=n,f.height=i),r){case 2:o.transform(-1,0,0,1,n,0);break;case 3:o.transform(-1,0,0,-1,n,i);break;case 4:o.transform(1,0,0,-1,0,i);break;case 5:o.transform(0,1,1,0,0,0);break;case 6:o.transform(0,1,-1,0,i,0);break;case 7:o.transform(0,-1,-1,0,i,n);break;case 8:o.transform(0,-1,1,0,0,n)}return o.drawImage(e,0,0,n,i),M(e),f}function Q(e,r,n=0){return new Promise(function(i,f){let o,a,l,s,u,c,h,d,g,p,A,w,m,v,b,y,U,F,E,_;function Q(e=5){if(r.signal&&r.signal.aborted)throw r.signal.reason;o+=e,r.onProgress(Math.min(o,100))}function P(e){if(r.signal&&r.signal.aborted)throw r.signal.reason;o=Math.min(Math.max(e,o),100),r.onProgress(o)}return o=n,a=r.maxIteration||10,l=1024*r.maxSizeMB*1024,Q(),C(e,r).then((function(n){try{return[,s]=n,Q(),u=S(s,r),Q(),new Promise(function(n,i){var f;if(!(f=r.exifOrientation))return R(e).then((function(e){try{return f=e,o.call(this)}catch(e){return i(e)}}).bind(this),i);function o(){return n(f)}return o.call(this)}).then((function(n){try{return c=n,Q(),B().then((function(n){try{return h=n?u:T(u,c),Q(),d=r.initialQuality||1,g=r.fileType||e.type,I(h,g,e.name,e.lastModified,d).then((function(n){try{var o;if(p=n,Q(),A=p.size>l,w=p.size>e.size,!A&&!w)return P(100),i(p);function c(){if(a--&&(b>l||b>m)){let r,n;return r=_?.95*E.width:E.width,n=_?.95*E.height:E.height,[U,F]=x(r,n),F.drawImage(E,0,0,r,n),d*="image/png"===g?.85:.95,I(U,g,e.name,e.lastModified,d).then(function(e){try{return y=e,M(E),E=U,b=y.size,P(Math.min(99,Math.floor((v-b)/(v-l)*100))),c}catch(e){return f(e)}},f)}return[1]}return m=e.size,b=v=p.size,E=h,_=!r.alwaysKeepResolution&&A,(o=(function(e){for(;e;){if(e.then)return void e.then(o,f);try{if(e.pop){if(e.length)return e.pop()?C.call(this):e;e=c}else e=e.call(this)}catch(e){return f(e)}}}).bind(this))(c);function C(){return M(E),M(U),M(u),M(h),M(s),P(100),i(y)}}catch(e){return f(e)}}).bind(this),f)}catch(e){return f(e)}}).bind(this),f)}catch(e){return f(e)}}).bind(this),f)}catch(e){return f(e)}}).bind(this),f)})}function P(e,r){return new Promise(function(n,o){let a,l,s,u,c,h;if(a={...r},s=0,{onProgress:u}=a,a.maxSizeMB=a.maxSizeMB||Number.POSITIVE_INFINITY,c="boolean"!=typeof a.useWebWorker||a.useWebWorker,delete a.useWebWorker,a.onProgress=e=>{s=e,"function"==typeof u&&u(s)},!(e instanceof Blob||e instanceof w))return o(Error("The file given is not an instance of Blob or File"));if(!/^image/.test(e.type))return o(Error("The file given is not an image"));if(h="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,!c||"function"!=typeof Worker||h)return Q(e,a).then((function(e){try{return l=e,p.call(this)}catch(e){return o(e)}}).bind(this),o);var d=(function(){try{return p.call(this)}catch(e){return o(e)}}).bind(this),g=function(r){try{return Q(e,a).then(function(e){try{return l=e,d()}catch(e){return o(e)}},o)}catch(e){return o(e)}};try{return a.libURL=a.libURL||"https://cdn.jsdelivr.net/npm/browser-image-compression@2.0.2/dist/browser-image-compression.js",new Promise((r,n)=>{i||(i=function(e){let r=[];return"function"==typeof e?r.push(`(${e})()`):r.push(e),URL.createObjectURL(new Blob(r))}("\nlet scriptImported = false\nself.addEventListener('message', async (e) => {\n  const { file, id, imageCompressionLibUrl, options } = e.data\n  options.onProgress = (progress) => self.postMessage({ progress, id })\n  try {\n    if (!scriptImported) {\n      // console.log('[worker] importScripts', imageCompressionLibUrl)\n      self.importScripts(imageCompressionLibUrl)\n      scriptImported = true\n    }\n    // console.log('[worker] self', self)\n    const compressedFile = await imageCompression(file, options)\n    self.postMessage({ file: compressedFile, id })\n  } catch (e) {\n    // console.error('[worker] error', e)\n    self.postMessage({ error: e.message + '\\n' + e.stack, id })\n  }\n})\n"));let f=new Worker(i);f.addEventListener("message",function(e){if(a.signal&&a.signal.aborted)f.terminate();else if(void 0===e.data.progress){if(e.data.error)return n(Error(e.data.error)),void f.terminate();r(e.data.file),f.terminate()}else a.onProgress(e.data.progress)}),f.addEventListener("error",n),a.signal&&a.signal.addEventListener("abort",()=>{n(a.signal.reason),f.terminate()}),f.postMessage({file:e,imageCompressionLibUrl:a.libURL,options:{...a,onProgress:void 0,signal:void 0}})}).then(function(e){try{return l=e,d()}catch(e){return g()}},g)}catch(e){g()}function p(){try{l.name=e.name,l.lastModified=e.lastModified}catch(e){}try{a.preserveExif&&"image/jpeg"===e.type&&(!a.fileType||a.fileType&&a.fileType===e.type)&&(l=f(e,l))}catch(e){}return n(l)}})}P.getDataUrlFromFile=b,P.getFilefromDataUrl=v,P.loadImage=y,P.drawImageInCanvas=E,P.drawFileInCanvas=C,P.canvasToFile=I,P.getExifOrientation=R,P.handleMaxWidthOrHeight=S,P.followExifOrientation=T,P.cleanupCanvasMemory=M,P.isAutoOrientationInBrowser=B,P.approximateBelowMaximumCanvasSizeOfBrowser=F,P.copyExifWithoutOrientation=f,P.getBrowserName=U,P.version="2.0.2"},60760:(e,r,n)=>{n.d(r,{N:()=>v});var i=n(95155),f=n(12115),o=n(90869),a=n(82885),l=n(97494),s=n(80845),u=n(27351),c=n(51508);class h extends f.Component{getSnapshotBeforeUpdate(e){let r=this.props.childRef.current;if(r&&e.isPresent&&!this.props.isPresent){let e=r.offsetParent,n=(0,u.s)(e)&&e.offsetWidth||0,i=this.props.sizeRef.current;i.height=r.offsetHeight||0,i.width=r.offsetWidth||0,i.top=r.offsetTop,i.left=r.offsetLeft,i.right=n-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(e){let{children:r,isPresent:n,anchorX:o}=e,a=(0,f.useId)(),l=(0,f.useRef)(null),s=(0,f.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,f.useContext)(c.Q);return(0,f.useInsertionEffect)(()=>{let{width:e,height:r,top:i,left:f,right:c}=s.current;if(n||!l.current||!e||!r)return;l.current.dataset.motionPopId=a;let h=document.createElement("style");return u&&(h.nonce=u),document.head.appendChild(h),h.sheet&&h.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(r,"px !important;\n            ").concat("left"===o?"left: ".concat(f):"right: ".concat(c),"px !important;\n            top: ").concat(i,"px !important;\n          }\n        ")),()=>{document.head.contains(h)&&document.head.removeChild(h)}},[n]),(0,i.jsx)(h,{isPresent:n,childRef:l,sizeRef:s,children:f.cloneElement(r,{ref:l})})}let g=e=>{let{children:r,initial:n,isPresent:o,onExitComplete:l,custom:u,presenceAffectsLayout:c,mode:h,anchorX:g}=e,A=(0,a.M)(p),w=(0,f.useId)(),m=!0,v=(0,f.useMemo)(()=>(m=!1,{id:w,initial:n,isPresent:o,custom:u,onExitComplete:e=>{for(let r of(A.set(e,!0),A.values()))if(!r)return;l&&l()},register:e=>(A.set(e,!1),()=>A.delete(e))}),[o,A,l]);return c&&m&&(v={...v}),(0,f.useMemo)(()=>{A.forEach((e,r)=>A.set(r,!1))},[o]),f.useEffect(()=>{o||A.size||!l||l()},[o]),"popLayout"===h&&(r=(0,i.jsx)(d,{isPresent:o,anchorX:g,children:r})),(0,i.jsx)(s.t.Provider,{value:v,children:r})};function p(){return new Map}var A=n(32082);let w=e=>e.key||"";function m(e){let r=[];return f.Children.forEach(e,e=>{(0,f.isValidElement)(e)&&r.push(e)}),r}let v=e=>{let{children:r,custom:n,initial:s=!0,onExitComplete:u,presenceAffectsLayout:c=!0,mode:h="sync",propagate:d=!1,anchorX:p="left"}=e,[v,b]=(0,A.xQ)(d),y=(0,f.useMemo)(()=>m(r),[r]),U=d&&!v?[]:y.map(w),F=(0,f.useRef)(!0),x=(0,f.useRef)(y),E=(0,a.M)(()=>new Map),[_,C]=(0,f.useState)(y),[I,M]=(0,f.useState)(y);(0,l.E)(()=>{F.current=!1,x.current=y;for(let e=0;e<I.length;e++){let r=w(I[e]);U.includes(r)?E.delete(r):!0!==E.get(r)&&E.set(r,!1)}},[I,U.length,U.join("-")]);let B=[];if(y!==_){let e=[...y];for(let r=0;r<I.length;r++){let n=I[r],i=w(n);U.includes(i)||(e.splice(r,0,n),B.push(n))}return"wait"===h&&B.length&&(e=B),M(m(e)),C(y),null}let{forceRender:R}=(0,f.useContext)(o.L);return(0,i.jsx)(i.Fragment,{children:I.map(e=>{let r=w(e),f=(!d||!!v)&&(y===I||U.includes(r));return(0,i.jsx)(g,{isPresent:f,initial:(!F.current||!!s)&&void 0,custom:n,presenceAffectsLayout:c,mode:h,onExitComplete:f?void 0:()=>{if(!E.has(r))return;E.set(r,!0);let e=!0;E.forEach(r=>{r||(e=!1)}),e&&(null==R||R(),M(x.current),d&&(null==b||b()),u&&u())},anchorX:p,children:e},r)})})}}}]);