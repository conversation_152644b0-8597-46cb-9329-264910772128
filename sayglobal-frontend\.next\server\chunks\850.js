"use strict";exports.id=850,exports.ids=[850],exports.modules={12850:(e,r,n)=>{let i;function f(e,r){return new Promise(function(n,i){let f;return a(e).then(function(e){try{return f=e,n(new Blob([r.slice(0,2),f,r.slice(2)],{type:"image/jpeg"}))}catch(e){return i(e)}},i)})}n.d(r,{A:()=>H});let a=e=>new Promise((r,n)=>{let i=new FileReader;i.addEventListener("load",({target:{result:e}})=>{let i=new DataView(e),f=0;if(65496!==i.getUint16(f))return n("not a valid JPEG");for(f+=2;;){let a=i.getUint16(f);if(65498===a)break;let o=i.getUint16(f+2);if(65505===a&&0x45786966===i.getUint32(f+4)){let a,l=f+10;switch(i.getUint16(l)){case 18761:a=!0;break;case 19789:a=!1;break;default:return n("TIFF header contains invalid endian")}if(42!==i.getUint16(l+2,a))return n("TIFF header contains invalid version");let s=i.getUint32(l+4,a),u=l+s+2+12*i.getUint16(l+s,a);for(let e=l+s+2;e<u;e+=12)if(274==i.getUint16(e,a)){if(3!==i.getUint16(e+2,a))return n("Orientation data type is invalid");if(1!==i.getUint32(e+4,a))return n("Orientation data count is invalid");i.setUint16(e+8,1,a);break}return r(e.slice(f,f+2+o))}f+=2+o}return r(new Blob)}),i.readAsArrayBuffer(e)});var o={},l={get exports(){return o},set exports(t){o=t}};!function(e){var r,n,i={};l.exports=i,i.parse=function(e,r){for(var n=i.bin.readUshort,f=i.bin.readUint,a=0,o={},l=new Uint8Array(e),s=l.length-4;0x6054b50!=f(l,s);)s--;var u=n(l,a=s+4+4);n(l,a+=2);var c=f(l,a+=2),h=f(l,a+=4);a+=4,a=h;for(var d=0;d<u;d++){f(l,a),a+=4,a+=4,a+=4,f(l,a+=4),c=f(l,a+=4);var g=f(l,a+=4),A=n(l,a+=4),w=n(l,a+2),p=n(l,a+4);a+=6;var v=f(l,a+=8);a+=4,a+=A+w+p,i._readLocal(l,v,o,c,g,r)}return o},i._readLocal=function(e,r,n,f,a,o){var l=i.bin.readUshort,s=i.bin.readUint;s(e,r),l(e,r+=4),l(e,r+=2);var u=l(e,r+=2);s(e,r+=2),s(e,r+=4),r+=4;var c=l(e,r+=8),h=l(e,r+=2);r+=2;var d=i.bin.readUTF8(e,r,c);if(r+=c,r+=h,o)n[d]={size:a,csize:f};else{var g=new Uint8Array(e.buffer,r);if(0==u)n[d]=new Uint8Array(g.buffer.slice(r,r+f));else{if(8!=u)throw"unknown compression method: "+u;var A=new Uint8Array(a);i.inflateRaw(g,A),n[d]=A}}},i.inflateRaw=function(e,r){return i.F.inflate(e,r)},i.inflate=function(e,r){return e[0],e[1],i.inflateRaw(new Uint8Array(e.buffer,e.byteOffset+2,e.length-6),r)},i.deflate=function(e,r){null==r&&(r={level:6});var n=0,f=new Uint8Array(50+Math.floor(1.1*e.length));f[n]=120,f[n+1]=156,n+=2,n=i.F.deflateRaw(e,f,n,r.level);var a=i.adler(e,0,e.length);return f[n+0]=a>>>24&255,f[n+1]=a>>>16&255,f[n+2]=a>>>8&255,f[n+3]=a>>>0&255,new Uint8Array(f.buffer,0,n+4)},i.deflateRaw=function(e,r){null==r&&(r={level:6});var n=new Uint8Array(50+Math.floor(1.1*e.length)),f=i.F.deflateRaw(e,n,f,r.level);return new Uint8Array(n.buffer,0,f)},i.encode=function(e,r){null==r&&(r=!1);var n=0,f=i.bin.writeUint,a=i.bin.writeUshort,o={};for(var l in e){var s=!i._noNeed(l)&&!r,u=e[l],c=i.crc.crc(u,0,u.length);o[l]={cpr:s,usize:u.length,crc:c,file:s?i.deflateRaw(u):u}}for(var l in o)n+=o[l].file.length+30+46+2*i.bin.sizeUTF8(l);var h=new Uint8Array(n+=22),d=0,g=[];for(var l in o){var A=o[l];g.push(d),d=i._writeHeader(h,d,l,A,0)}var w=0,p=d;for(var l in o)A=o[l],g.push(d),d=i._writeHeader(h,d,l,A,1,g[w++]);var v=d-p;return f(h,d,0x6054b50),d+=4,a(h,d+=4,w),a(h,d+=2,w),f(h,d+=2,v),f(h,d+=4,p),d+=4,d+=2,h.buffer},i._noNeed=function(e){var r=e.split(".").pop().toLowerCase();return -1!="png,jpg,jpeg,zip".indexOf(r)},i._writeHeader=function(e,r,n,f,a,o){var l=i.bin.writeUint,s=i.bin.writeUshort,u=f.file;return l(e,r,0==a?0x4034b50:0x2014b50),r+=4,1==a&&(r+=2),s(e,r,20),s(e,r+=2,0),s(e,r+=2,8*!!f.cpr),l(e,r+=2,0),l(e,r+=4,f.crc),l(e,r+=4,u.length),l(e,r+=4,f.usize),s(e,r+=4,i.bin.sizeUTF8(n)),s(e,r+=2,0),r+=2,1==a&&(r+=2,r+=2,l(e,r+=6,o),r+=4),r+=i.bin.writeUTF8(e,r,n),0==a&&(e.set(u,r),r+=u.length),r},i.crc={table:function(){for(var e=new Uint32Array(256),r=0;r<256;r++){for(var n=r,i=0;i<8;i++)1&n?n=0xedb88320^n>>>1:n>>>=1;e[r]=n}return e}(),update:function(e,r,n,f){for(var a=0;a<f;a++)e=i.crc.table[255&(e^r[n+a])]^e>>>8;return e},crc:function(e,r,n){return 0xffffffff^i.crc.update(0xffffffff,e,r,n)}},i.adler=function(e,r,n){for(var i=1,f=0,a=r,o=r+n;a<o;){for(var l=Math.min(a+5552,o);a<l;)f+=i+=e[a++];i%=65521,f%=65521}return f<<16|i},i.bin={readUshort:function(e,r){return e[r]|e[r+1]<<8},writeUshort:function(e,r,n){e[r]=255&n,e[r+1]=n>>8&255},readUint:function(e,r){return 0x1000000*e[r+3]+(e[r+2]<<16|e[r+1]<<8|e[r])},writeUint:function(e,r,n){e[r]=255&n,e[r+1]=n>>8&255,e[r+2]=n>>16&255,e[r+3]=n>>24&255},readASCII:function(e,r,n){for(var i="",f=0;f<n;f++)i+=String.fromCharCode(e[r+f]);return i},writeASCII:function(e,r,n){for(var i=0;i<n.length;i++)e[r+i]=n.charCodeAt(i)},pad:function(e){return e.length<2?"0"+e:e},readUTF8:function(e,r,n){for(var f,a="",o=0;o<n;o++)a+="%"+i.bin.pad(e[r+o].toString(16));try{f=decodeURIComponent(a)}catch(f){return i.bin.readASCII(e,r,n)}return f},writeUTF8:function(e,r,n){for(var i=n.length,f=0,a=0;a<i;a++){var o=n.charCodeAt(a);if(0==(0xffffff80&o))e[r+f]=o,f++;else if(0==(0xfffff800&o))e[r+f]=192|o>>6,e[r+f+1]=128|(0|o)&63,f+=2;else if(0==(0xffff0000&o))e[r+f]=224|o>>12,e[r+f+1]=128|o>>6&63,e[r+f+2]=128|(0|o)&63,f+=3;else{if(0!=(0xffe00000&o))throw"e";e[r+f]=240|o>>18,e[r+f+1]=128|o>>12&63,e[r+f+2]=128|o>>6&63,e[r+f+3]=128|(0|o)&63,f+=4}}return f},sizeUTF8:function(e){for(var r=e.length,n=0,i=0;i<r;i++){var f=e.charCodeAt(i);if(0==(0xffffff80&f))n++;else if(0==(0xfffff800&f))n+=2;else if(0==(0xffff0000&f))n+=3;else{if(0!=(0xffe00000&f))throw"e";n+=4}}return n}},i.F={},i.F.deflateRaw=function(e,r,n,f){var a=[[0,0,0,0,0],[4,4,8,4,0],[4,5,16,8,0],[4,6,16,16,0],[4,10,16,32,0],[8,16,32,32,0],[8,16,128,128,0],[8,32,128,256,0],[32,128,258,1024,1],[32,258,258,4096,1]][f],o=i.F.U,l=i.F._goodIndex;i.F._hash;var s=i.F._putsE,u=0,c=n<<3,h=0,d=e.length;if(0==f){for(;u<d;)s(r,c,+(u+(E=Math.min(65535,d-u))==d)),c=i.F._copyExact(e,u,E,r,c+8),u+=E;return c>>>3}var g=o.lits,A=o.strt,w=o.prev,p=0,v=0,m=0,b=0,y=0,U=0;for(d>2&&(A[U=i.F._hash(e,0)]=0),u=0;u<d;u++){if(y=U,u+1<d-2){U=i.F._hash(e,u+1);var F=u+1&32767;w[F]=A[U],A[U]=F}if(h<=u){(p>14e3||v>26697)&&d-u>100&&(h<u&&(g[p]=u-h,p+=2,h=u),c=i.F._writeBlock(+(u==d-1||h==d),g,p,b,e,m,u-m,r,c),p=v=b=0,m=u);var x=0;u<d-2&&(x=i.F._bestMatch(e,u,w,y,Math.min(a[2],d-u),a[3]));var E=x>>>16,_=65535&x;if(0!=x){_=65535&x;var C=l(E=x>>>16,o.of0);o.lhst[257+C]++;var I=l(_,o.df0);o.dhst[I]++,b+=o.exb[C]+o.dxb[I],g[p]=E<<23|u-h,g[p+1]=_<<16|C<<8|I,p+=2,h=u+E}else o.lhst[e[u]]++;v++}}for(m==u&&0!=e.length||(h<u&&(g[p]=u-h,p+=2,h=u),c=i.F._writeBlock(1,g,p,b,e,m,u-m,r,c),p=0,v=0,p=v=b=0,m=u);0!=(7&c);)c++;return c>>>3},i.F._bestMatch=function(e,r,n,f,a,o){var l=32767&r,s=n[l],u=l-s+32768&32767;if(s==l||f!=i.F._hash(e,r-u))return 0;for(var c=0,h=0,d=Math.min(32767,r);u<=d&&0!=--o&&s!=l;){if(0==c||e[r+c]==e[r+c-u]){var g=i.F._howLong(e,r,u);if(g>c){if(h=u,(c=g)>=a)break;u+2<g&&(g=u+2);for(var A=0,w=0;w<g-2;w++){var p=r-u+w+32768&32767,v=p-n[p]+32768&32767;v>A&&(A=v,s=p)}}}u+=(l=s)-(s=n[l])+32768&32767}return c<<16|h},i.F._howLong=function(e,r,n){if(e[r]!=e[r-n]||e[r+1]!=e[r+1-n]||e[r+2]!=e[r+2-n])return 0;var i=r,f=Math.min(e.length,r+258);for(r+=3;r<f&&e[r]==e[r-n];)r++;return r-i},i.F._hash=function(e,r){return(e[r]<<8|e[r+1])+(e[r+2]<<4)&65535},i.saved=0,i.F._writeBlock=function(e,r,n,f,a,o,l,s,u){var c,h,d,g,A,w,p,v,m,b,y,U=i.F.U,F=i.F._putsF,x=i.F._putsE;U.lhst[256]++,g=(d=i.F.getTrees())[0],A=d[1],w=d[2],p=d[3],v=d[4],m=d[5],b=d[6],y=d[7];var E=32+(0==(u+3&7)?0:8-(u+3&7))+(l<<3),_=f+i.F.contSize(U.fltree,U.lhst)+i.F.contSize(U.fdtree,U.dhst),C=f+i.F.contSize(U.ltree,U.lhst)+i.F.contSize(U.dtree,U.dhst);C+=14+3*m+i.F.contSize(U.itree,U.ihst)+(2*U.ihst[16]+3*U.ihst[17]+7*U.ihst[18]);for(var I=0;I<286;I++)U.lhst[I]=0;for(I=0;I<30;I++)U.dhst[I]=0;for(I=0;I<19;I++)U.ihst[I]=0;var B=E<_&&E<C?0:_<C?1:2;if(F(s,u,e),F(s,u+1,B),u+=3,0==B){for(;0!=(7&u);)u++;u=i.F._copyExact(a,o,l,s,u)}else{if(1==B&&(c=U.fltree,h=U.fdtree),2==B){i.F.makeCodes(U.ltree,g),i.F.revCodes(U.ltree,g),i.F.makeCodes(U.dtree,A),i.F.revCodes(U.dtree,A),i.F.makeCodes(U.itree,w),i.F.revCodes(U.itree,w),c=U.ltree,h=U.dtree,x(s,u,p-257),x(s,u+=5,v-1),x(s,u+=5,m-4),u+=4;for(var M=0;M<m;M++)x(s,u+3*M,U.itree[1+(U.ordr[M]<<1)]);u+=3*m,u=i.F._codeTiny(b,U.itree,s,u),u=i.F._codeTiny(y,U.itree,s,u)}for(var R=o,S=0;S<n;S+=2){for(var T=r[S],Q=T>>>23,H=R+(8388607&T);R<H;)u=i.F._writeLit(a[R++],c,s,u);if(0!=Q){var k=r[S+1],O=k>>16,P=k>>8&255,L=255&k;x(s,u=i.F._writeLit(257+P,c,s,u),Q-U.of0[P]),u+=U.exb[P],F(s,u=i.F._writeLit(L,h,s,u),O-U.df0[L]),u+=U.dxb[L],R+=Q}}u=i.F._writeLit(256,c,s,u)}return u},i.F._copyExact=function(e,r,n,i,f){var a=f>>>3;return i[a]=n,i[a+1]=n>>>8,i[a+2]=255-i[a],i[a+3]=255-i[a+1],a+=4,i.set(new Uint8Array(e.buffer,r,n),a),f+(n+4<<3)},i.F.getTrees=function(){for(var e=i.F.U,r=i.F._hufTree(e.lhst,e.ltree,15),n=i.F._hufTree(e.dhst,e.dtree,15),f=[],a=i.F._lenCodes(e.ltree,f),o=[],l=i.F._lenCodes(e.dtree,o),s=0;s<f.length;s+=2)e.ihst[f[s]]++;for(s=0;s<o.length;s+=2)e.ihst[o[s]]++;for(var u=i.F._hufTree(e.ihst,e.itree,7),c=19;c>4&&0==e.itree[1+(e.ordr[c-1]<<1)];)c--;return[r,n,u,a,l,c,f,o]},i.F.getSecond=function(e){for(var r=[],n=0;n<e.length;n+=2)r.push(e[n+1]);return r},i.F.nonZero=function(e){for(var r="",n=0;n<e.length;n+=2)0!=e[n+1]&&(r+=(n>>1)+",");return r},i.F.contSize=function(e,r){for(var n=0,i=0;i<r.length;i++)n+=r[i]*e[1+(i<<1)];return n},i.F._codeTiny=function(e,r,n,f){for(var a=0;a<e.length;a+=2){var o=e[a],l=e[a+1];f=i.F._writeLit(o,r,n,f);var s=16==o?2:17==o?3:7;o>15&&(i.F._putsE(n,f,l,s),f+=s)}return f},i.F._lenCodes=function(e,r){for(var n=e.length;2!=n&&0==e[n-1];)n-=2;for(var i=0;i<n;i+=2){var f=e[i+1],a=i+3<n?e[i+3]:-1,o=i+5<n?e[i+5]:-1,l=0==i?-1:e[i-1];if(0==f&&a==f&&o==f){for(var s=i+5;s+2<n&&e[s+2]==f;)s+=2;(u=Math.min(s+1-i>>>1,138))<11?r.push(17,u-3):r.push(18,u-11),i+=2*u-2}else if(f==l&&a==f&&o==f){for(s=i+5;s+2<n&&e[s+2]==f;)s+=2;var u=Math.min(s+1-i>>>1,6);r.push(16,u-3),i+=2*u-2}else r.push(f,0)}return n>>>1},i.F._hufTree=function(e,r,n){var f=[],a=e.length,o=r.length,l=0;for(l=0;l<o;l+=2)r[l]=0,r[l+1]=0;for(l=0;l<a;l++)0!=e[l]&&f.push({lit:l,f:e[l]});var s=f.length,u=f.slice(0);if(0==s)return 0;if(1==s){var c=f[0].lit;return u=+(0==c),r[1+(c<<1)]=1,r[1+(u<<1)]=1,1}f.sort(function(e,r){return e.f-r.f});var h=f[0],d=f[1],g=0,A=1,w=2;for(f[0]={lit:-1,f:h.f+d.f,l:h,r:d,d:0};A!=s-1;)h=g!=A&&(w==s||f[g].f<f[w].f)?f[g++]:f[w++],d=g!=A&&(w==s||f[g].f<f[w].f)?f[g++]:f[w++],f[A++]={lit:-1,f:h.f+d.f,l:h,r:d};var p=i.F.setDepth(f[A-1],0);for(p>n&&(i.F.restrictDepth(u,n,p),p=n),l=0;l<s;l++)r[1+(u[l].lit<<1)]=u[l].d;return p},i.F.setDepth=function(e,r){return -1!=e.lit?(e.d=r,r):Math.max(i.F.setDepth(e.l,r+1),i.F.setDepth(e.r,r+1))},i.F.restrictDepth=function(e,r,n){var i=0,f=1<<n-r,a=0;for(e.sort(function(e,r){return r.d==e.d?e.f-r.f:r.d-e.d}),i=0;i<e.length&&e[i].d>r;i++){var o=e[i].d;e[i].d=r,a+=f-(1<<n-o)}for(a>>>=n-r;a>0;)(o=e[i].d)<r?(e[i].d++,a-=1<<r-o-1):i++;for(;i>=0;i--)e[i].d==r&&a<0&&(e[i].d--,a++);0!=a&&console.log("debt left")},i.F._goodIndex=function(e,r){var n=0;return r[16]<=e&&(n|=16),r[8|n]<=e&&(n|=8),r[4|n]<=e&&(n|=4),r[2|n]<=e&&(n|=2),r[1|n]<=e&&(n|=1),n},i.F._writeLit=function(e,r,n,f){return i.F._putsF(n,f,r[e<<1]),f+r[1+(e<<1)]},i.F.inflate=function(e,r){var n=Uint8Array;if(3==e[0]&&0==e[1])return r||new n(0);var f=i.F,a=f._bitsF,o=f._bitsE,l=f._decodeTiny,s=f.makeCodes,u=f.codes2map,c=f._get17,h=f.U,d=null==r;d&&(r=new n(e.length>>>2<<3));for(var g,A,w=0,p=0,v=0,m=0,b=0,y=0,U=0,F=0,x=0;0==w;)if(w=a(e,x,1),p=a(e,x+1,2),x+=3,0!=p){if(d&&(r=i.F._check(r,F+131072)),1==p&&(g=h.flmap,A=h.fdmap,y=511,U=31),2==p){v=o(e,x,5)+257,m=o(e,x+5,5)+1,b=o(e,x+10,4)+4,x+=14;for(var E=0;E<38;E+=2)h.itree[E]=0,h.itree[E+1]=0;var _=1;for(E=0;E<b;E++){var C=o(e,x+3*E,3);h.itree[1+(h.ordr[E]<<1)]=C,C>_&&(_=C)}x+=3*b,s(h.itree,_),u(h.itree,_,h.imap),g=h.lmap,A=h.dmap,x=l(h.imap,(1<<_)-1,v+m,e,x,h.ttree);var I=f._copyOut(h.ttree,0,v,h.ltree);y=(1<<I)-1;var B=f._copyOut(h.ttree,v,m,h.dtree);U=(1<<B)-1,s(h.ltree,I),u(h.ltree,I,g),s(h.dtree,B),u(h.dtree,B,A)}for(;;){var M=g[c(e,x)&y];x+=15&M;var R=M>>>4;if(R>>>8==0)r[F++]=R;else{if(256==R)break;var S=F+R-254;if(R>264){var T=h.ldef[R-257];S=F+(T>>>3)+o(e,x,7&T),x+=7&T}var Q=A[c(e,x)&U];x+=15&Q;var H=Q>>>4,k=h.ddef[H],O=(k>>>4)+a(e,x,15&k);for(x+=15&k,d&&(r=i.F._check(r,F+131072));F<S;)r[F]=r[F++-O],r[F]=r[F++-O],r[F]=r[F++-O],r[F]=r[F++-O];F=S}}}else{0!=(7&x)&&(x+=8-(7&x));var P=4+(x>>>3),L=e[P-4]|e[P-3]<<8;d&&(r=i.F._check(r,F+L)),r.set(new n(e.buffer,e.byteOffset+P,L),F),x=P+L<<3,F+=L}return r.length==F?r:r.slice(0,F)},i.F._check=function(e,r){var n=e.length;if(r<=n)return e;var i=new Uint8Array(Math.max(n<<1,r));return i.set(e,0),i},i.F._decodeTiny=function(e,r,n,f,a,o){for(var l=i.F._bitsE,s=i.F._get17,u=0;u<n;){var c=e[s(f,a)&r];a+=15&c;var h=c>>>4;if(h<=15)o[u]=h,u++;else{var d=0,g=0;16==h?(g=3+l(f,a,2),a+=2,d=o[u-1]):17==h?(g=3+l(f,a,3),a+=3):18==h&&(g=11+l(f,a,7),a+=7);for(var A=u+g;u<A;)o[u]=d,u++}}return a},i.F._copyOut=function(e,r,n,i){for(var f=0,a=0,o=i.length>>>1;a<n;){var l=e[a+r];i[a<<1]=0,i[1+(a<<1)]=l,l>f&&(f=l),a++}for(;a<o;)i[a<<1]=0,i[1+(a<<1)]=0,a++;return f},i.F.makeCodes=function(e,r){for(var n,f,a,o,l=i.F.U,s=e.length,u=l.bl_count,c=0;c<=r;c++)u[c]=0;for(c=1;c<s;c+=2)u[e[c]]++;var h=l.next_code;for(n=0,u[0]=0,f=1;f<=r;f++)n=n+u[f-1]<<1,h[f]=n;for(a=0;a<s;a+=2)0!=(o=e[a+1])&&(e[a]=h[o],h[o]++)},i.F.codes2map=function(e,r,n){for(var f=e.length,a=i.F.U.rev15,o=0;o<f;o+=2)if(0!=e[o+1])for(var l=o>>1,s=e[o+1],u=l<<4|s,c=r-s,h=e[o]<<c,d=h+(1<<c);h!=d;)n[a[h]>>>15-r]=u,h++},i.F.revCodes=function(e,r){for(var n=i.F.U.rev15,f=15-r,a=0;a<e.length;a+=2){var o=e[a]<<r-e[a+1];e[a]=n[o]>>>f}},i.F._putsE=function(e,r,n){n<<=7&r;var i=r>>>3;e[i]|=n,e[i+1]|=n>>>8},i.F._putsF=function(e,r,n){n<<=7&r;var i=r>>>3;e[i]|=n,e[i+1]|=n>>>8,e[i+2]|=n>>>16},i.F._bitsE=function(e,r,n){return(e[r>>>3]|e[1+(r>>>3)]<<8)>>>(7&r)&(1<<n)-1},i.F._bitsF=function(e,r,n){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16)>>>(7&r)&(1<<n)-1},i.F._get17=function(e,r){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16)>>>(7&r)},i.F._get25=function(e,r){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16|e[3+(r>>>3)]<<24)>>>(7&r)},i.F.U=(r=Uint16Array,n=Uint32Array,{next_code:new r(16),bl_count:new r(16),ordr:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],of0:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],exb:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],ldef:new r(32),df0:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],dxb:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],ddef:new n(32),flmap:new r(512),fltree:[],fdmap:new r(32),fdtree:[],lmap:new r(32768),ltree:[],ttree:[],dmap:new r(32768),dtree:[],imap:new r(512),itree:[],rev15:new r(32768),lhst:new n(286),dhst:new n(30),ihst:new n(19),lits:new n(15e3),strt:new r(65536),prev:new r(32768)}),function(){for(var e=i.F.U,r=0;r<32768;r++){var n=r;n=(0xff00ff00&(n=(0xf0f0f0f0&(n=(0xcccccccc&(n=(0xaaaaaaaa&n)>>>1|(0x55555555&n)<<1))>>>2|(0x33333333&n)<<2))>>>4|(0xf0f0f0f&n)<<4))>>>8|(0xff00ff&n)<<8,e.rev15[r]=(n>>>16|n<<16)>>>17}function f(e,r,n){for(;0!=r--;)e.push(0,n)}for(r=0;r<32;r++)e.ldef[r]=e.of0[r]<<3|e.exb[r],e.ddef[r]=e.df0[r]<<4|e.dxb[r];f(e.fltree,144,8),f(e.fltree,112,9),f(e.fltree,24,7),f(e.fltree,8,8),i.F.makeCodes(e.fltree,9),i.F.codes2map(e.fltree,9,e.flmap),i.F.revCodes(e.fltree,9),f(e.fdtree,32,5),i.F.makeCodes(e.fdtree,5),i.F.codes2map(e.fdtree,5,e.fdmap),i.F.revCodes(e.fdtree,5),f(e.itree,19,0),f(e.ltree,286,0),f(e.dtree,30,0),f(e.ttree,320,0)}()}();var s=function(e,r){return r.forEach(function(r){r&&"string"!=typeof r&&!Array.isArray(r)&&Object.keys(r).forEach(function(n){if("default"!==n&&!(n in e)){var i=Object.getOwnPropertyDescriptor(r,n);Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:function(){return r[n]}})}})}),Object.freeze(e)}({__proto__:null,default:o},[o]);let u=function(){var e={nextZero(e,r){for(;0!=e[r];)r++;return r},readUshort:(e,r)=>e[r]<<8|e[r+1],writeUshort(e,r,n){e[r]=n>>8&255,e[r+1]=255&n},readUint:(e,r)=>0x1000000*e[r]+(e[r+1]<<16|e[r+2]<<8|e[r+3]),writeUint(e,r,n){e[r]=n>>24&255,e[r+1]=n>>16&255,e[r+2]=n>>8&255,e[r+3]=255&n},readASCII(e,r,n){let i="";for(let f=0;f<n;f++)i+=String.fromCharCode(e[r+f]);return i},writeASCII(e,r,n){for(let i=0;i<n.length;i++)e[r+i]=n.charCodeAt(i)},readBytes(e,r,n){let i=[];for(let f=0;f<n;f++)i.push(e[r+f]);return i},pad:e=>e.length<2?`0${e}`:e,readUTF8(r,n,i){let f,a="";for(let f=0;f<i;f++)a+=`%${e.pad(r[n+f].toString(16))}`;try{f=decodeURIComponent(a)}catch(f){return e.readASCII(r,n,i)}return f}};function r(r,n,i,f){let o=n*i,l=Math.ceil(n*a(f)/8),s=new Uint8Array(4*o),u=new Uint32Array(s.buffer),{ctype:c}=f,{depth:h}=f,d=e.readUshort;if(6==c){let e=o<<2;if(8==h)for(var g=0;g<e;g+=4)s[g]=r[g],s[g+1]=r[g+1],s[g+2]=r[g+2],s[g+3]=r[g+3];if(16==h)for(g=0;g<e;g++)s[g]=r[g<<1]}else if(2==c){let e=f.tabs.tRNS;if(null==e){if(8==h)for(g=0;g<o;g++){var A=3*g;u[g]=-0x1000000|r[A+2]<<16|r[A+1]<<8|r[A]}if(16==h)for(g=0;g<o;g++)A=6*g,u[g]=-0x1000000|r[A+4]<<16|r[A+2]<<8|r[A]}else{var w=e[0];let n=e[1],i=e[2];if(8==h)for(g=0;g<o;g++){var p=g<<2;A=3*g,u[g]=-0x1000000|r[A+2]<<16|r[A+1]<<8|r[A],r[A]==w&&r[A+1]==n&&r[A+2]==i&&(s[p+3]=0)}if(16==h)for(g=0;g<o;g++)p=g<<2,A=6*g,u[g]=-0x1000000|r[A+4]<<16|r[A+2]<<8|r[A],d(r,A)==w&&d(r,A+2)==n&&d(r,A+4)==i&&(s[p+3]=0)}}else if(3==c){let e=f.tabs.PLTE,a=f.tabs.tRNS,u=a?a.length:0;if(1==h)for(var v,m=0;m<i;m++){var b=m*l,y=m*n;for(g=0;g<n;g++){p=y+g<<2;var U=3*(v=r[b+(g>>3)]>>7-(7&g)&1);s[p]=e[U],s[p+1]=e[U+1],s[p+2]=e[U+2],s[p+3]=v<u?a[v]:255}}if(2==h)for(m=0;m<i;m++)for(b=m*l,y=m*n,g=0;g<n;g++)p=y+g<<2,U=3*(v=r[b+(g>>2)]>>6-((3&g)<<1)&3),s[p]=e[U],s[p+1]=e[U+1],s[p+2]=e[U+2],s[p+3]=v<u?a[v]:255;if(4==h)for(m=0;m<i;m++)for(b=m*l,y=m*n,g=0;g<n;g++)p=y+g<<2,U=3*(v=r[b+(g>>1)]>>4-((1&g)<<2)&15),s[p]=e[U],s[p+1]=e[U+1],s[p+2]=e[U+2],s[p+3]=v<u?a[v]:255;if(8==h)for(g=0;g<o;g++)p=g<<2,U=3*(v=r[g]),s[p]=e[U],s[p+1]=e[U+1],s[p+2]=e[U+2],s[p+3]=v<u?a[v]:255}else if(4==c){if(8==h)for(g=0;g<o;g++){p=g<<2;var F,x=r[F=g<<1];s[p]=x,s[p+1]=x,s[p+2]=x,s[p+3]=r[F+1]}if(16==h)for(g=0;g<o;g++)p=g<<2,x=r[F=g<<2],s[p]=x,s[p+1]=x,s[p+2]=x,s[p+3]=r[F+2]}else if(0==c)for(w=f.tabs.tRNS?f.tabs.tRNS:-1,m=0;m<i;m++){let e=m*l,i=m*n;if(1==h)for(var E=0;E<n;E++){var _=255*((x=255*(r[e+(E>>>3)]>>>7-(7&E)&1))!=255*w);u[i+E]=_<<24|x<<16|x<<8|x}else if(2==h)for(E=0;E<n;E++)_=255*((x=85*(r[e+(E>>>2)]>>>6-((3&E)<<1)&3))!=85*w),u[i+E]=_<<24|x<<16|x<<8|x;else if(4==h)for(E=0;E<n;E++)_=255*((x=17*(r[e+(E>>>1)]>>>4-((1&E)<<2)&15))!=17*w),u[i+E]=_<<24|x<<16|x<<8|x;else if(8==h)for(E=0;E<n;E++)_=255*((x=r[e+E])!=w),u[i+E]=_<<24|x<<16|x<<8|x;else if(16==h)for(E=0;E<n;E++)x=r[e+(E<<1)],_=255*(d(r,e+(E<<1))!=w),u[i+E]=_<<24|x<<16|x<<8|x}return s}function n(e,r,n,l){let s=new Uint8Array((Math.ceil(n*a(e)/8)+1+e.interlace)*l);return r=e.tabs.CgBI?f(r,s):i(r,s),0==e.interlace?r=o(r,e,0,n,l):1==e.interlace&&(r=function(e,r){let n=r.width,i=r.height,f=a(r),l=f>>3,s=Math.ceil(n*f/8),u=new Uint8Array(i*s),c=0,h=[0,0,4,0,2,0,1],d=[0,4,0,2,0,1,0],g=[8,8,8,4,4,2,2],A=[8,8,4,4,2,2,1],w=0;for(;w<7;){let a=g[w],v=A[w],m=0,b=0,y=h[w];for(;y<i;)y+=a,b++;let U=d[w];for(;U<n;)U+=v,m++;let F=Math.ceil(m*f/8);o(e,r,c,m,b);let x=0,E=h[w];for(;E<i;){let r=d[w],i=c+x*F<<3;for(;r<n;){var p;if(1==f&&(p=(p=e[i>>3])>>7-(7&i)&1,u[E*s+(r>>3)]|=p<<7-(7&r)),2==f&&(p=(p=e[i>>3])>>6-(7&i)&3,u[E*s+(r>>2)]|=p<<6-((3&r)<<1)),4==f&&(p=(p=e[i>>3])>>4-(7&i)&15,u[E*s+(r>>1)]|=p<<4-((1&r)<<2)),f>=8){let n=E*s+r*l;for(let r=0;r<l;r++)u[n+r]=e[(i>>3)+r]}i+=f,r+=v}x++,E+=a}m*b!=0&&(c+=b*(1+F)),w+=1}return u}(r,e)),r}function i(e,r){return f(new Uint8Array(e.buffer,2,e.length-6),r)}var f=function(){let e={H:{}};return e.H.N=function(r,n){let i=Uint8Array,f,a,o=0,l=0,s=0,u=0,c=0,h=0,d=0,g=0,A=0;if(3==r[0]&&0==r[1])return n||new i(0);let w=e.H,p=w.b,v=w.e,m=w.R,b=w.n,y=w.A,U=w.Z,F=w.m,x=null==n;for(x&&(n=new i(r.length>>>2<<5));0==o;)if(o=p(r,A,1),l=p(r,A+1,2),A+=3,0!=l){if(x&&(n=e.H.W(n,g+131072)),1==l&&(f=F.J,a=F.h,h=511,d=31),2==l){s=v(r,A,5)+257,u=v(r,A+5,5)+1,c=v(r,A+10,4)+4,A+=14;let e=1;for(var E=0;E<38;E+=2)F.Q[E]=0,F.Q[E+1]=0;for(E=0;E<c;E++){let n=v(r,A+3*E,3);F.Q[1+(F.X[E]<<1)]=n,n>e&&(e=n)}A+=3*c,b(F.Q,e),y(F.Q,e,F.u),f=F.w,a=F.d,A=m(F.u,(1<<e)-1,s+u,r,A,F.v);let n=w.V(F.v,0,s,F.C);h=(1<<n)-1;let i=w.V(F.v,s,u,F.D);d=(1<<i)-1,b(F.C,n),y(F.C,n,f),b(F.D,i),y(F.D,i,a)}for(;;){let e=f[U(r,A)&h];A+=15&e;let i=e>>>4;if(i>>>8==0)n[g++]=i;else{if(256==i)break;{let e=g+i-254;if(i>264){let n=F.q[i-257];e=g+(n>>>3)+v(r,A,7&n),A+=7&n}let f=a[U(r,A)&d];A+=15&f;let o=f>>>4,l=F.c[o],s=(l>>>4)+p(r,A,15&l);for(A+=15&l;g<e;)n[g]=n[g++-s],n[g]=n[g++-s],n[g]=n[g++-s],n[g]=n[g++-s];g=e}}}}else{0!=(7&A)&&(A+=8-(7&A));let f=4+(A>>>3),a=r[f-4]|r[f-3]<<8;x&&(n=e.H.W(n,g+a)),n.set(new i(r.buffer,r.byteOffset+f,a),g),A=f+a<<3,g+=a}return n.length==g?n:n.slice(0,g)},e.H.W=function(e,r){let n=e.length;if(r<=n)return e;let i=new Uint8Array(n<<1);return i.set(e,0),i},e.H.R=function(r,n,i,f,a,o){let l=e.H.e,s=e.H.Z,u=0;for(;u<i;){let e=r[s(f,a)&n];a+=15&e;let i=e>>>4;if(i<=15)o[u]=i,u++;else{let e=0,r=0;16==i?(r=3+l(f,a,2),a+=2,e=o[u-1]):17==i?(r=3+l(f,a,3),a+=3):18==i&&(r=11+l(f,a,7),a+=7);let n=u+r;for(;u<n;)o[u]=e,u++}}return a},e.H.V=function(e,r,n,i){let f=0,a=0,o=i.length>>>1;for(;a<n;){let n=e[a+r];i[a<<1]=0,i[1+(a<<1)]=n,n>f&&(f=n),a++}for(;a<o;)i[a<<1]=0,i[1+(a<<1)]=0,a++;return f},e.H.n=function(r,n){let i,f,a,o,l=e.H.m,s=r.length,u=l.j;for(var c=0;c<=n;c++)u[c]=0;for(c=1;c<s;c+=2)u[r[c]]++;let h=l.K;for(i=0,u[0]=0,f=1;f<=n;f++)i=i+u[f-1]<<1,h[f]=i;for(a=0;a<s;a+=2)0!=(o=r[a+1])&&(r[a]=h[o],h[o]++)},e.H.A=function(r,n,i){let f=r.length,a=e.H.m.r;for(let e=0;e<f;e+=2)if(0!=r[e+1]){let f=e>>1,o=r[e+1],l=f<<4|o,s=n-o,u=r[e]<<s,c=u+(1<<s);for(;u!=c;)i[a[u]>>>15-n]=l,u++}},e.H.l=function(r,n){let i=e.H.m.r,f=15-n;for(let e=0;e<r.length;e+=2){let a=r[e]<<n-r[e+1];r[e]=i[a]>>>f}},e.H.M=function(e,r,n){n<<=7&r;let i=r>>>3;e[i]|=n,e[i+1]|=n>>>8},e.H.I=function(e,r,n){n<<=7&r;let i=r>>>3;e[i]|=n,e[i+1]|=n>>>8,e[i+2]|=n>>>16},e.H.e=function(e,r,n){return(e[r>>>3]|e[1+(r>>>3)]<<8)>>>(7&r)&(1<<n)-1},e.H.b=function(e,r,n){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16)>>>(7&r)&(1<<n)-1},e.H.Z=function(e,r){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16)>>>(7&r)},e.H.i=function(e,r){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16|e[3+(r>>>3)]<<24)>>>(7&r)},e.H.m=function(){let e=Uint16Array,r=Uint32Array;return{K:new e(16),j:new e(16),X:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],T:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],q:new e(32),p:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],z:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],c:new r(32),J:new e(512),_:[],h:new e(32),$:[],w:new e(32768),C:[],v:[],d:new e(32768),D:[],u:new e(512),Q:[],r:new e(32768),s:new r(286),Y:new r(30),a:new r(19),t:new r(15e3),k:new e(65536),g:new e(32768)}}(),function(){let r=e.H.m;for(var n=0;n<32768;n++){let e=n;e=(0xff00ff00&(e=(0xf0f0f0f0&(e=(0xcccccccc&(e=(0xaaaaaaaa&e)>>>1|(0x55555555&e)<<1))>>>2|(0x33333333&e)<<2))>>>4|(0xf0f0f0f&e)<<4))>>>8|(0xff00ff&e)<<8,r.r[n]=(e>>>16|e<<16)>>>17}function i(e,r,n){for(;0!=r--;)e.push(0,n)}for(n=0;n<32;n++)r.q[n]=r.S[n]<<3|r.T[n],r.c[n]=r.p[n]<<4|r.z[n];i(r._,144,8),i(r._,112,9),i(r._,24,7),i(r._,8,8),e.H.n(r._,9),e.H.A(r._,9,r.J),e.H.l(r._,9),i(r.$,32,5),e.H.n(r.$,5),e.H.A(r.$,5,r.h),e.H.l(r.$,5),i(r.Q,19,0),i(r.C,286,0),i(r.D,30,0),i(r.v,320,0)}(),e.H.N}();function a(e){return[1,null,3,1,2,null,4][e.ctype]*e.depth}function o(e,r,n,i,f){let o,s,u=a(r),c=Math.ceil(i*u/8);u=Math.ceil(u/8);let h=e[n],d=0;if(h>1&&(e[n]=[0,0,1][h-2]),3==h)for(d=u;d<c;d++)e[d+1]=e[d+1]+(e[d+1-u]>>>1)&255;for(let r=0;r<f;r++)if(h=e[(s=(o=n+r*c)+r+1)-1],d=0,0==h)for(;d<c;d++)e[o+d]=e[s+d];else if(1==h){for(;d<u;d++)e[o+d]=e[s+d];for(;d<c;d++)e[o+d]=e[s+d]+e[o+d-u]}else if(2==h)for(;d<c;d++)e[o+d]=e[s+d]+e[o+d-c];else if(3==h){for(;d<u;d++)e[o+d]=e[s+d]+(e[o+d-c]>>>1);for(;d<c;d++)e[o+d]=e[s+d]+(e[o+d-c]+e[o+d-u]>>>1)}else{for(;d<u;d++)e[o+d]=e[s+d]+l(0,e[o+d-c],0);for(;d<c;d++)e[o+d]=e[s+d]+l(e[o+d-u],e[o+d-c],e[o+d-u-c])}return e}function l(e,r,n){let i=e+r-n,f=i-e,a=i-r,o=i-n;return f*f<=a*a&&f*f<=o*o?e:a*a<=o*o?r:n}function s(e,r,n,i,f,a,o,l,s){let u=Math.min(r,f),c=Math.min(n,a),h=0,d=0;for(let n=0;n<c;n++)for(let a=0;a<u;a++)if(o>=0&&l>=0?(h=n*r+a<<2,d=(l+n)*f+o+a<<2):(h=(-l+n)*r-o+a<<2,d=n*f+a<<2),0==s)i[d]=e[h],i[d+1]=e[h+1],i[d+2]=e[h+2],i[d+3]=e[h+3];else if(1==s){var g=e[h+3]*(1/255),A=e[h]*g,w=e[h+1]*g,p=e[h+2]*g,v=i[d+3]*(1/255),m=i[d]*v,b=i[d+1]*v,y=i[d+2]*v;let r=1-g,n=g+v*r,f=0==n?0:1/n;i[d+3]=255*n,i[d+0]=(A+m*r)*f,i[d+1]=(w+b*r)*f,i[d+2]=(p+y*r)*f}else if(2==s)g=e[h+3],A=e[h],w=e[h+1],p=e[h+2],v=i[d+3],m=i[d],b=i[d+1],y=i[d+2],g==v&&A==m&&w==b&&p==y?(i[d]=0,i[d+1]=0,i[d+2]=0,i[d+3]=0):(i[d]=A,i[d+1]=w,i[d+2]=p,i[d+3]=g);else if(3==s){if(g=e[h+3],A=e[h],w=e[h+1],p=e[h+2],v=i[d+3],m=i[d],b=i[d+1],y=i[d+2],g==v&&A==m&&w==b&&p==y)continue;if(g<220&&v>20)return!1}return!0}return{decode:function(r){let a=new Uint8Array(r),o=8,l=e.readUshort,s=e.readUint,u={tabs:{},frames:[]},c=new Uint8Array(a.length),h,d=0,g=0,A=[137,80,78,71,13,10,26,10];for(var w,p=0;p<8;p++)if(a[p]!=A[p])throw"The input is not a PNG file!";for(;o<a.length;){let r=e.readUint(a,o);o+=4;let A=e.readASCII(a,o,4);if(o+=4,"IHDR"==A)v=o,u.width=e.readUint(a,v),v+=4,u.height=e.readUint(a,v),u.depth=a[v+=4],u.ctype=a[++v],u.compress=a[++v],u.filter=a[++v],u.interlace=a[++v],v++;else if("iCCP"==A){for(var v,m,b=o;0!=a[b];)b++;e.readASCII(a,o,b-o),a[b+1];let n=a.slice(b+2,o+r),l=null;try{l=i(n)}catch(e){l=f(n)}u.tabs[A]=l}else if("CgBI"==A)u.tabs[A]=a.slice(o,o+4);else if("IDAT"==A){for(p=0;p<r;p++)c[d+p]=a[o+p];d+=r}else if("acTL"==A)u.tabs[A]={num_frames:s(a,o),num_plays:s(a,o+4)},h=new Uint8Array(a.length);else if("fcTL"==A){0!=g&&((w=u.frames[u.frames.length-1]).data=n(u,h.slice(0,g),w.rect.width,w.rect.height),g=0);let e={x:s(a,o+12),y:s(a,o+16),width:s(a,o+4),height:s(a,o+8)},r=l(a,o+22),i={rect:e,delay:Math.round(1e3*(r=l(a,o+20)/(0==r?100:r))),dispose:a[o+24],blend:a[o+25]};u.frames.push(i)}else if("fdAT"==A){for(p=0;p<r-4;p++)h[g+p]=a[o+p+4];g+=r-4}else if("pHYs"==A)u.tabs[A]=[e.readUint(a,o),e.readUint(a,o+4),a[o+8]];else if("cHRM"==A)for(p=0,u.tabs[A]=[];p<8;p++)u.tabs[A].push(e.readUint(a,o+4*p));else if("tEXt"==A||"zTXt"==A){null==u.tabs[A]&&(u.tabs[A]={});var y=e.nextZero(a,o),U=e.readASCII(a,o,y-o),F=o+r-y-1;if("tEXt"==A)m=e.readASCII(a,y+1,F);else{var x=i(a.slice(y+2,y+2+F));m=e.readUTF8(x,0,x.length)}u.tabs[A][U]=m}else if("iTXt"==A){null==u.tabs[A]&&(u.tabs[A]={}),y=0,b=o,y=e.nextZero(a,b),U=e.readASCII(a,b,y-b);let n=a[b=y+1];a[b+1],b+=2,y=e.nextZero(a,b),e.readASCII(a,b,y-b),b=y+1,y=e.nextZero(a,b),e.readUTF8(a,b,y-b),F=r-((b=y+1)-o),0==n?m=e.readUTF8(a,b,F):(x=i(a.slice(b,b+F)),m=e.readUTF8(x,0,x.length)),u.tabs[A][U]=m}else if("PLTE"==A)u.tabs[A]=e.readBytes(a,o,r);else if("hIST"==A){let e=u.tabs.PLTE.length/3;for(p=0,u.tabs[A]=[];p<e;p++)u.tabs[A].push(l(a,o+2*p))}else if("tRNS"==A)3==u.ctype?u.tabs[A]=e.readBytes(a,o,r):0==u.ctype?u.tabs[A]=l(a,o):2==u.ctype&&(u.tabs[A]=[l(a,o),l(a,o+2),l(a,o+4)]);else if("gAMA"==A)u.tabs[A]=e.readUint(a,o)/1e5;else if("sRGB"==A)u.tabs[A]=a[o];else if("bKGD"==A)0==u.ctype||4==u.ctype?u.tabs[A]=[l(a,o)]:2==u.ctype||6==u.ctype?u.tabs[A]=[l(a,o),l(a,o+2),l(a,o+4)]:3==u.ctype&&(u.tabs[A]=a[o]);else if("IEND"==A)break;o+=r,e.readUint(a,o),o+=4}return 0!=g&&((w=u.frames[u.frames.length-1]).data=n(u,h.slice(0,g),w.rect.width,w.rect.height)),u.data=n(u,c,u.width,u.height),delete u.compress,delete u.interlace,delete u.filter,u},toRGBA8:function(e){let n=e.width,i=e.height;if(null==e.tabs.acTL)return[r(e.data,n,i,e).buffer];let f=[];null==e.frames[0].data&&(e.frames[0].data=e.data);let a=n*i*4,o=new Uint8Array(a),l=new Uint8Array(a),u=new Uint8Array(a);for(let h=0;h<e.frames.length;h++){let d=e.frames[h],g=d.rect.x,A=d.rect.y,w=d.rect.width,p=d.rect.height,v=r(d.data,w,p,e);if(0!=h)for(var c=0;c<a;c++)u[c]=o[c];if(0==d.blend?s(v,w,p,o,n,i,g,A,0):1==d.blend&&s(v,w,p,o,n,i,g,A,1),f.push(o.buffer.slice(0)),0==d.dispose);else if(1==d.dispose)s(l,w,p,o,n,i,g,A,0);else if(2==d.dispose)for(c=0;c<a;c++)o[c]=u[c]}return f},_paeth:l,_copyTile:s,_bin:e}}();!function(){let{_copyTile:e}=u,{_bin:r}=u,n=u._paeth;var i={table:function(){let e=new Uint32Array(256);for(let r=0;r<256;r++){let n=r;for(let e=0;e<8;e++)1&n?n=0xedb88320^n>>>1:n>>>=1;e[r]=n}return e}(),update(e,r,n,f){for(let a=0;a<f;a++)e=i.table[255&(e^r[n+a])]^e>>>8;return e},crc:(e,r,n)=>0xffffffff^i.update(0xffffffff,e,r,n)};function f(e,r,n,i){r[n]+=e[0]*i>>4,r[n+1]+=e[1]*i>>4,r[n+2]+=e[2]*i>>4,r[n+3]+=e[3]*i>>4}function a(e){return Math.max(0,Math.min(255,e))}function o(e,r){let n=e[0]-r[0],i=e[1]-r[1],f=e[2]-r[2],a=e[3]-r[3];return n*n+i*i+f*f+a*a}function l(e,r,n,i,l,s,u){null==u&&(u=1);let c=i.length,h=[];for(var d,g=0;g<c;g++){let e=i[g];h.push([e>>>0&255,e>>>8&255,e>>>16&255,e>>>24&255])}for(g=0;g<c;g++){let e=0xffffffff;for(var A=0,w=0;w<c;w++){var p=o(h[g],h[w]);w!=g&&p<e&&(e=p,A=w)}}let v=new Uint32Array(l.buffer),m=new Int16Array(r*n*4),b=[0,8,2,10,12,4,14,6,3,11,1,9,15,7,13,5];for(g=0;g<b.length;g++)b[g]=255*((b[g]+.5)/16-.5);for(let l=0;l<n;l++)for(let y=0;y<r;y++){g=4*(l*r+y),2!=u?d=[a(e[g]+m[g]),a(e[g+1]+m[g+1]),a(e[g+2]+m[g+2]),a(e[g+3]+m[g+3])]:(p=b[4*(3&l)+(3&y)],d=[a(e[g]+p),a(e[g+1]+p),a(e[g+2]+p),a(e[g+3]+p)]),A=0;let U=0xffffff;for(w=0;w<c;w++){let e=o(d,h[w]);e<U&&(U=e,A=w)}let F=h[A],x=[d[0]-F[0],d[1]-F[1],d[2]-F[2],d[3]-F[3]];1==u&&(y!=r-1&&f(x,m,g+4,7),l!=n-1&&(0!=y&&f(x,m,g+4*r-4,3),f(x,m,g+4*r,5),y!=r-1&&f(x,m,g+4*r+4,1))),s[g>>2]=A,v[g>>2]=i[A]}}function c(e,n,f,a,o){null==o&&(o={});let{crc:l}=i,s=r.writeUint,u=r.writeUshort,c=r.writeASCII,h=8,d=e.frames.length>1,g,A=!1,w=33+20*!!d;if(null!=o.sRGB&&(w+=13),null!=o.pHYs&&(w+=21),null!=o.iCCP&&(w+=21+(g=pako.deflate(o.iCCP)).length+4),3==e.ctype){for(var p=e.plte.length,v=0;v<p;v++)e.plte[v]>>>24!=255&&(A=!0);w+=8+3*p+4+(A?8+ +p+4:0)}for(var m=0;m<e.frames.length;m++)d&&(w+=38),w+=(F=e.frames[m]).cimg.length+12,0!=m&&(w+=4);let b=new Uint8Array(w+=12),y=[137,80,78,71,13,10,26,10];for(v=0;v<8;v++)b[v]=y[v];if(s(b,h,13),c(b,h+=4,"IHDR"),s(b,h+=4,n),s(b,h+=4,f),b[h+=4]=e.depth,b[++h]=e.ctype,b[++h]=0,b[++h]=0,b[++h]=0,s(b,++h,l(b,h-17,17)),h+=4,null!=o.sRGB&&(s(b,h,1),c(b,h+=4,"sRGB"),b[h+=4]=o.sRGB,s(b,++h,l(b,h-5,5)),h+=4),null!=o.iCCP){let e=13+g.length;s(b,h,e),c(b,h+=4,"iCCP"),c(b,h+=4,"ICC profile"),h+=11,h+=2,b.set(g,h),s(b,h+=g.length,l(b,h-(e+4),e+4)),h+=4}if(null!=o.pHYs&&(s(b,h,9),c(b,h+=4,"pHYs"),s(b,h+=4,o.pHYs[0]),s(b,h+=4,o.pHYs[1]),b[h+=4]=o.pHYs[2],s(b,++h,l(b,h-13,13)),h+=4),d&&(s(b,h,8),c(b,h+=4,"acTL"),s(b,h+=4,e.frames.length),s(b,h+=4,null!=o.loop?o.loop:0),s(b,h+=4,l(b,h-12,12)),h+=4),3==e.ctype){for(s(b,h,3*(p=e.plte.length)),c(b,h+=4,"PLTE"),h+=4,v=0;v<p;v++){let r=3*v,n=e.plte[v],i=255&n,f=n>>>8&255,a=n>>>16&255;b[h+r+0]=i,b[h+r+1]=f,b[h+r+2]=a}if(s(b,h+=3*p,l(b,h-3*p-4,3*p+4)),h+=4,A){for(s(b,h,p),c(b,h+=4,"tRNS"),h+=4,v=0;v<p;v++)b[h+v]=e.plte[v]>>>24&255;s(b,h+=p,l(b,h-p-4,p+4)),h+=4}}let U=0;for(m=0;m<e.frames.length;m++){var F=e.frames[m];d&&(s(b,h,26),c(b,h+=4,"fcTL"),s(b,h+=4,U++),s(b,h+=4,F.rect.width),s(b,h+=4,F.rect.height),s(b,h+=4,F.rect.x),s(b,h+=4,F.rect.y),u(b,h+=4,a[m]),u(b,h+=2,1e3),b[h+=2]=F.dispose,b[++h]=F.blend,s(b,++h,l(b,h-30,30)),h+=4);let r=F.cimg;s(b,h,(p=r.length)+4*(0!=m));let n=h+=4;c(b,h,0==m?"IDAT":"fdAT"),h+=4,0!=m&&(s(b,h,U++),h+=4),b.set(r,h),s(b,h+=p,l(b,n,h-n)),h+=4}return s(b,h,0),c(b,h+=4,"IEND"),s(b,h+=4,l(b,h-4,4)),h+=4,b.buffer}function h(e,r,i){for(let f=0;f<e.frames.length;f++){let a=e.frames[f];a.rect.width;let o=a.rect.height,l=new Uint8Array(o*a.bpl+o);a.cimg=function(e,r,i,f,a,o,l){let u=[],c,h=[0,1,2,3,4];-1!=o?h=[o]:(r*f>5e5||1==i)&&(h=[0]),l&&(c={level:0});for(var d=0;d<h.length;d++){for(let o=0;o<r;o++)!function(e,r,i,f,a,o){let l=i*f,s=l+i;if(e[s]=o,s++,0==o)if(f<500)for(var u=0;u<f;u++)e[s+u]=r[l+u];else e.set(new Uint8Array(r.buffer,l,f),s);else if(1==o){for(u=0;u<a;u++)e[s+u]=r[l+u];for(u=a;u<f;u++)e[s+u]=r[l+u]-r[l+u-a]+256&255}else if(0==i){for(u=0;u<a;u++)e[s+u]=r[l+u];if(2==o)for(u=a;u<f;u++)e[s+u]=r[l+u];if(3==o)for(u=a;u<f;u++)e[s+u]=r[l+u]-(r[l+u-a]>>1)+256&255;if(4==o)for(u=a;u<f;u++)e[s+u]=r[l+u]-n(r[l+u-a],0,0)+256&255}else{if(2==o)for(u=0;u<f;u++)e[s+u]=r[l+u]+256-r[l+u-f]&255;if(3==o){for(u=0;u<a;u++)e[s+u]=r[l+u]+256-(r[l+u-f]>>1)&255;for(u=a;u<f;u++)e[s+u]=r[l+u]+256-(r[l+u-f]+r[l+u-a]>>1)&255}if(4==o){for(u=0;u<a;u++)e[s+u]=r[l+u]+256-n(0,r[l+u-f],0)&255;for(u=a;u<f;u++)e[s+u]=r[l+u]+256-n(r[l+u-a],r[l+u-f],r[l+u-a-f])&255}}}(a,e,o,f,i,h[d]);u.push(s.deflate(a,c))}let g,A=1e9;for(d=0;d<u.length;d++)u[d].length<A&&(g=d,A=u[d].length);return u[g]}(a.img,o,a.bpp,a.bpl,l,r,i)}}function d(r,n,i,f,a){let o=a[0],s=a[1],u=a[2],c=a[3],h=a[4],d=a[5],p=6,v=8,m=255;for(var b=0;b<r.length;b++){let e=new Uint8Array(r[b]);for(var y=e.length,U=0;U<y;U+=4)m&=e[U+3]}let F=255!=m,x=function(r,n,i,f,a,o){let l=[];for(var s,u=0;u<r.length;u++){let s=new Uint8Array(r[u]),g=new Uint32Array(s.buffer),w=0,p=0,v=n,m=i,b=+!!f;if(0!=u){let y=o||f||1==u||0!=l[u-2].dispose?1:2,U=0,F=1e9;for(let e=0;e<y;e++){var c,h=new Uint8Array(r[u-1-e]);let f=new Uint32Array(r[u-1-e]),o=n,l=i,s=-1,A=-1;for(let e=0;e<i;e++)for(let r=0;r<n;r++)g[d=e*n+r]!=f[d]&&(r<o&&(o=r),r>s&&(s=r),e<l&&(l=e),e>A&&(A=e));-1==s&&(o=l=s=A=0),a&&(1==(1&o)&&o--,1==(1&l)&&l--);let b=(s-o+1)*(A-l+1);b<F&&(F=b,U=e,w=o,p=l,v=s-o+1,m=A-l+1)}h=new Uint8Array(r[u-1-U]),1==U&&(l[u-1].dispose=2),e(h,n,i,c=new Uint8Array(v*m*4),v,m,-w,-p,0),1==(b=+!!e(s,n,i,c,v,m,-w,-p,3))?A(s,n,i,c,{x:w,y:p,width:v,height:m}):e(s,n,i,c,v,m,-w,-p,0)}else c=s.slice(0);l.push({rect:{x:w,y:p,width:v,height:m},img:c,blend:b,dispose:0})}if(f)for(u=0;u<l.length;u++){if(1==(s=l[u]).blend)continue;let e=s.rect,f=l[u-1].rect,o=Math.min(e.x,f.x),c=Math.min(e.y,f.y),h={x:o,y:c,width:Math.max(e.x+e.width,f.x+f.width)-o,height:Math.max(e.y+e.height,f.y+f.height)-c};l[u-1].dispose=1,u-1!=0&&g(r,n,i,l,u-1,h,a),g(r,n,i,l,u,h,a)}if(1!=r.length)for(var d=0;d<l.length;d++)(s=l[d]).rect.width,s.rect.height;return l}(r,n,i,o,s,u),E={},_=[],C=[];if(0!=f){let e=[];for(U=0;U<x.length;U++)e.push(x[U].img.buffer);let r=w(function(e){let r=0;for(var n=0;n<e.length;n++)r+=e[n].byteLength;let i=new Uint8Array(r),f=0;for(n=0;n<e.length;n++){let r=new Uint8Array(e[n]),a=r.length;for(let e=0;e<a;e+=4){let n=r[e],a=r[e+1],o=r[e+2],l=r[e+3];0==l&&(n=a=o=0),i[f+e]=n,i[f+e+1]=a,i[f+e+2]=o,i[f+e+3]=l}f+=a}return i.buffer}(e),f);for(U=0;U<r.plte.length;U++)_.push(r.plte[U].est.rgba);let n=0;for(U=0;U<x.length;U++){let e=(B=x[U]).img.length;var I=new Uint8Array(r.inds.buffer,n>>2,e>>2);C.push(I);let i=new Uint8Array(r.abuf,n,e);d&&l(B.img,B.rect.width,B.rect.height,_,i,I),B.img.set(i),n+=e}}else for(b=0;b<x.length;b++){var B=x[b];let e=new Uint32Array(B.img.buffer);var M=B.rect.width;for(I=new Uint8Array(y=e.length),C.push(I),U=0;U<y;U++){let r=e[U];if(0!=U&&r==e[U-1])I[U]=I[U-1];else if(U>M&&r==e[U-M])I[U]=I[U-M];else{let e=E[r];if(null==e&&(E[r]=e=_.length,_.push(r),_.length>=300))break;I[U]=e}}}let R=_.length;for(R<=256&&0==h&&(v=Math.max(v=R<=2?1:R<=4?2:R<=16?4:8,c)),b=0;b<x.length;b++){(B=x[b]).rect.x,B.rect.y,M=B.rect.width;let e=B.rect.height,r=B.img;new Uint32Array(r.buffer);let n=4*M,i=4;if(R<=256&&0==h){var S=new Uint8Array((n=Math.ceil(v*M/8))*e);let f=C[b];for(let r=0;r<e;r++){U=r*n;let e=r*M;if(8==v)for(var T=0;T<M;T++)S[U+T]=f[e+T];else if(4==v)for(T=0;T<M;T++)S[U+(T>>1)]|=f[e+T]<<4-4*(1&T);else if(2==v)for(T=0;T<M;T++)S[U+(T>>2)]|=f[e+T]<<6-2*(3&T);else if(1==v)for(T=0;T<M;T++)S[U+(T>>3)]|=f[e+T]<<7-(7&T)}r=S,p=3,i=1}else if(0==F&&1==x.length){S=new Uint8Array(M*e*3);let f=M*e;for(U=0;U<f;U++){let e=3*U,n=4*U;S[e]=r[n],S[e+1]=r[n+1],S[e+2]=r[n+2]}r=S,p=2,i=3,n=3*M}B.img=r,B.bpl=n,B.bpp=i}return{ctype:p,depth:v,plte:_,frames:x}}function g(r,n,i,f,a,o,l){let s=Uint8Array,u=Uint32Array,c=new s(r[a-1]),h=new u(r[a-1]),d=a+1<r.length?new s(r[a+1]):null,g=new s(r[a]),w=new u(g.buffer),p=n,v=i,m=-1,b=-1;for(let e=0;e<o.height;e++)for(let r=0;r<o.width;r++){let i=o.x+r,l=o.y+e,s=l*n+i,u=w[s];0==u||0==f[a-1].dispose&&h[s]==u&&(null==d||0!=d[4*s+3])||(i<p&&(p=i),i>m&&(m=i),l<v&&(v=l),l>b&&(b=l))}-1==m&&(p=v=m=b=0),l&&(1==(1&p)&&p--,1==(1&v)&&v--),o={x:p,y:v,width:m-p+1,height:b-v+1};let y=f[a];y.rect=o,y.blend=1,y.img=new Uint8Array(o.width*o.height*4),0==f[a-1].dispose?(e(c,n,i,y.img,o.width,o.height,-o.x,-o.y,0),A(g,n,i,y.img,o)):e(g,n,i,y.img,o.width,o.height,-o.x,-o.y,0)}function A(r,n,i,f,a){e(r,n,i,f,a.width,a.height,-a.x,-a.y,2)}function w(e,r){let n,i=new Uint8Array(e),f=i.slice(0),a=new Uint32Array(f.buffer),o=p(f,r),l=o[0],s=o[1],u=i.length,c=new Uint8Array(u>>2);if(i.length<2e7)for(var h=0;h<u;h+=4)n=v(l,d=i[h]*(1/255),g=i[h+1]*(1/255),A=i[h+2]*(1/255),w=i[h+3]*(1/255)),c[h>>2]=n.ind,a[h>>2]=n.est.rgba;else for(h=0;h<u;h+=4){var d=i[h]*(1/255),g=i[h+1]*(1/255),A=i[h+2]*(1/255),w=i[h+3]*(1/255);for(n=l;n.left;)n=0>=m(n.est,d,g,A,w)?n.left:n.right;c[h>>2]=n.ind,a[h>>2]=n.est.rgba}return{abuf:f.buffer,inds:c,plte:s}}function p(e,r,n){null==n&&(n=1e-4);let i=new Uint32Array(e.buffer),f={i0:0,i1:e.length,bst:null,est:null,tdst:0,left:null,right:null};f.bst=y(e,f.i0,f.i1),f.est=U(f.bst);let a=[f];for(;a.length<r;){let r=0,f=0;for(var o=0;o<a.length;o++)a[o].est.L>r&&(r=a[o].est.L,f=o);if(r<n)break;let l=a[f],s=function(e,r,n,i,f,a){for(i-=4;n<i;){for(;b(e,n,f)<=a;)n+=4;for(;b(e,i,f)>a;)i-=4;if(n>=i)break;let o=r[n>>2];r[n>>2]=r[i>>2],r[i>>2]=o,n+=4,i-=4}for(;b(e,n,f)>a;)n-=4;return n+4}(e,i,l.i0,l.i1,l.est.e,l.est.eMq255);if(l.i0>=s||l.i1<=s){l.est.L=0;continue}let u={i0:l.i0,i1:s,bst:null,est:null,tdst:0,left:null,right:null};u.bst=y(e,u.i0,u.i1),u.est=U(u.bst);let c={i0:s,i1:l.i1,bst:null,est:null,tdst:0,left:null,right:null};for(o=0,c.bst={R:[],m:[],N:l.bst.N-u.bst.N};o<16;o++)c.bst.R[o]=l.bst.R[o]-u.bst.R[o];for(o=0;o<4;o++)c.bst.m[o]=l.bst.m[o]-u.bst.m[o];c.est=U(c.bst),l.left=u,l.right=c,a[f]=u,a.push(c)}for(a.sort((e,r)=>r.bst.N-e.bst.N),o=0;o<a.length;o++)a[o].ind=o;return[f,a]}function v(e,r,n,i,f){if(null==e.left)return e.tdst=function(e,r,n,i,f){let a=r-e[0],o=n-e[1],l=i-e[2],s=f-e[3];return a*a+o*o+l*l+s*s}(e.est.q,r,n,i,f),e;let a=m(e.est,r,n,i,f),o=e.left,l=e.right;a>0&&(o=e.right,l=e.left);let s=v(o,r,n,i,f);if(s.tdst<=a*a)return s;let u=v(l,r,n,i,f);return u.tdst<s.tdst?u:s}function m(e,r,n,i,f){let{e:a}=e;return a[0]*r+a[1]*n+a[2]*i+a[3]*f-e.eMq}function b(e,r,n){return e[r]*n[0]+e[r+1]*n[1]+e[r+2]*n[2]+e[r+3]*n[3]}function y(e,r,n){let i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],f=[0,0,0,0];for(let a=r;a<n;a+=4){let r=e[a]*(1/255),n=e[a+1]*(1/255),o=e[a+2]*(1/255),l=e[a+3]*(1/255);f[0]+=r,f[1]+=n,f[2]+=o,f[3]+=l,i[0]+=r*r,i[1]+=r*n,i[2]+=r*o,i[3]+=r*l,i[5]+=n*n,i[6]+=n*o,i[7]+=n*l,i[10]+=o*o,i[11]+=o*l,i[15]+=l*l}return i[4]=i[1],i[8]=i[2],i[9]=i[6],i[12]=i[3],i[13]=i[7],i[14]=i[11],{R:i,m:f,N:n-r>>2}}function U(e){let{R:r}=e,{m:n}=e,{N:i}=e,f=n[0],a=n[1],o=n[2],l=n[3],s=0==i?0:1/i,u=[r[0]-f*f*s,r[1]-f*a*s,r[2]-f*o*s,r[3]-f*l*s,r[4]-a*f*s,r[5]-a*a*s,r[6]-a*o*s,r[7]-a*l*s,r[8]-o*f*s,r[9]-o*a*s,r[10]-o*o*s,r[11]-o*l*s,r[12]-l*f*s,r[13]-l*a*s,r[14]-l*o*s,r[15]-l*l*s],c=[Math.random(),Math.random(),Math.random(),Math.random()],h=0,d=0;if(0!=i)for(let e=0;e<16&&(c=F.multVec(u,c),d=Math.sqrt(F.dot(c,c)),c=F.sml(1/d,c),!(0!=e&&1e-9>Math.abs(d-h)));e++)h=d;let g=[f*s,a*s,o*s,l*s];return{Cov:u,q:g,e:c,L:h,eMq255:F.dot(F.sml(255,g),c),eMq:F.dot(c,g),rgba:(Math.round(255*g[3])<<24|Math.round(255*g[2])<<16|Math.round(255*g[1])<<8|(0|Math.round(255*g[0])))>>>0}}var F={multVec:(e,r)=>[e[0]*r[0]+e[1]*r[1]+e[2]*r[2]+e[3]*r[3],e[4]*r[0]+e[5]*r[1]+e[6]*r[2]+e[7]*r[3],e[8]*r[0]+e[9]*r[1]+e[10]*r[2]+e[11]*r[3],e[12]*r[0]+e[13]*r[1]+e[14]*r[2]+e[15]*r[3]],dot:(e,r)=>e[0]*r[0]+e[1]*r[1]+e[2]*r[2]+e[3]*r[3],sml:(e,r)=>[e*r[0],e*r[1],e*r[2],e*r[3]]};u.encode=function(e,r,n,i,f,a,o){null==i&&(i=0),null==o&&(o=!1);let l=d(e,r,n,i,[!1,!1,!1,0,o,!1]);return h(l,-1),c(l,r,n,f,a)},u.encodeLL=function(e,r,n,i,f,a,o,l){let s={ctype:0+2*(1!=i)+4*(0!=f),depth:a,frames:[]},u=(i+f)*a,d=u*r;for(let i=0;i<e.length;i++)s.frames.push({rect:{x:0,y:0,width:r,height:n},img:new Uint8Array(e[i]),blend:0,dispose:1,bpp:Math.ceil(u/8),bpl:Math.ceil(d/8)});return h(s,0,!0),c(s,r,n,o,l)},u.encode.compress=d,u.encode.dither=l,u.quantize=w,u.quantize.getKDtree=p,u.quantize.getNearest=v}();let c={toArrayBuffer(e,r){let n=e.width,i=e.height,f=n<<2,a=new Uint32Array(e.getContext("2d").getImageData(0,0,n,i).data.buffer),o=(32*n+31)/32<<2,l=o*i,s=122+l,u=new ArrayBuffer(s),h=new DataView(u),d,g,A,w,p=1048576,v=0,m=0,b=0;function y(e){h.setUint16(m,e,!0),m+=2}function U(e){h.setUint32(m,e,!0),m+=4}y(19778),U(s),m+=4,U(122),U(108),U(n),U(-i>>>0),y(1),y(32),U(3),U(l),U(2835),U(2835),m+=8,U(0xff0000),U(65280),U(255),U(0xff000000),U(0x57696e20),function e(){for(;v<i&&p>0;){for(w=122+v*o,d=0;d<f;)p--,A=(g=a[b++])>>>24,h.setUint32(w+d,g<<8|A),d+=4;v++}b<a.length?(p=1048576,setTimeout(e,c._dly)):r(u)}()},toBlob(e,r){this.toArrayBuffer(e,e=>{r(new Blob([e],{type:"image/bmp"}))})},_dly:9};var h={CHROME:"CHROME",FIREFOX:"FIREFOX",DESKTOP_SAFARI:"DESKTOP_SAFARI",IE:"IE",IOS:"IOS",ETC:"ETC"},d={[h.CHROME]:16384,[h.FIREFOX]:11180,[h.DESKTOP_SAFARI]:16384,[h.IE]:8192,[h.IOS]:4096,[h.ETC]:8192};let g="undefined"!=typeof window,A="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,w=g&&window.cordova&&window.cordova.require&&window.cordova.require("cordova/modulemapper"),p=(g||A)&&(w&&w.getOriginalSymbol(window,"File")||"undefined"!=typeof File&&File),v=(g||A)&&(w&&w.getOriginalSymbol(window,"FileReader")||"undefined"!=typeof FileReader&&FileReader);function m(e,r,n=Date.now()){return new Promise(i=>{let f=e.split(","),a=f[0].match(/:(.*?);/)[1],o=globalThis.atob(f[1]),l=o.length,s=new Uint8Array(l);for(;l--;)s[l]=o.charCodeAt(l);let u=new Blob([s],{type:a});u.name=r,u.lastModified=n,i(u)})}function b(e){return new Promise((r,n)=>{let i=new v;i.onload=()=>r(i.result),i.onerror=e=>n(e),i.readAsDataURL(e)})}function y(e){return new Promise((r,n)=>{let i=new Image;i.onload=()=>r(i),i.onerror=e=>n(e),i.src=e})}function U(){if(void 0!==U.cachedResult)return U.cachedResult;let e=h.ETC,{userAgent:r}=navigator;return/Chrom(e|ium)/i.test(r)?e=h.CHROME:/iP(ad|od|hone)/i.test(r)&&/WebKit/i.test(r)?e=h.IOS:/Safari/i.test(r)?e=h.DESKTOP_SAFARI:/Firefox/i.test(r)?e=h.FIREFOX:(/MSIE/i.test(r)||!0==!!document.documentMode)&&(e=h.IE),U.cachedResult=e,U.cachedResult}function F(e,r){let n=d[U()],i=e,f=r,a=i*f,o=i>f?f/i:i/f;for(;a>n*n;){let e=(n+i)/2,r=(n+f)/2;e<r?(f=r,i=r*o):(f=e*o,i=e),a=i*f}return{width:i,height:f}}function x(e,r){let n,i;try{if(i=(n=new OffscreenCanvas(e,r)).getContext("2d"),null===i)throw Error("getContext of OffscreenCanvas returns null")}catch(e){i=(n=document.createElement("canvas")).getContext("2d")}return n.width=e,n.height=r,[n,i]}function E(e,r){let{width:n,height:i}=F(e.width,e.height),[f,a]=x(n,i);return r&&/jpe?g/.test(r)&&(a.fillStyle="white",a.fillRect(0,0,f.width,f.height)),a.drawImage(e,0,0,f.width,f.height),f}function _(){return void 0!==_.cachedResult||(_.cachedResult=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"undefined"!=typeof document&&"ontouchend"in document),_.cachedResult}function C(e,r={}){return new Promise(function(n,i){let f,a;var o=function(){try{return a=E(f,r.fileType||e.type),n([f,a])}catch(e){return i(e)}},l=function(r){try{var n=function(e){try{throw e}catch(e){return i(e)}};try{let r;return b(e).then(function(e){try{return r=e,y(r).then(function(e){try{return f=e,function(){try{return o()}catch(e){return i(e)}}()}catch(e){return n(e)}},n)}catch(e){return n(e)}},n)}catch(e){n(e)}}catch(e){return i(e)}};try{if(_()||[h.DESKTOP_SAFARI,h.MOBILE_SAFARI].includes(U()))throw Error("Skip createImageBitmap on IOS and Safari");return createImageBitmap(e).then(function(e){try{return f=e,o()}catch(e){return l()}},l)}catch(e){l()}})}function I(e,r,n,i,f=1){return new Promise(function(a,o){let l;if("image/png"===r){let a,o;return a=e.getContext("2d"),{data:o}=a.getImageData(0,0,e.width,e.height),(l=new Blob([u.encode([o.buffer],e.width,e.height,4096*f)],{type:r})).name=n,l.lastModified=i,d.call(this)}if("image/bmp"===r)return new Promise(r=>c.toBlob(e,r)).then((function(e){try{return(l=e).name=n,l.lastModified=i,h.call(this)}catch(e){return o(e)}}).bind(this),o);if("function"==typeof OffscreenCanvas&&e instanceof OffscreenCanvas)return e.convertToBlob({type:r,quality:f}).then((function(e){try{return(l=e).name=n,l.lastModified=i,s.call(this)}catch(e){return o(e)}}).bind(this),o);return m(e.toDataURL(r,f),n,i).then((function(e){try{return l=e,s.call(this)}catch(e){return o(e)}}).bind(this),o);function s(){return h.call(this)}function h(){return d.call(this)}function d(){return a(l)}})}function B(e){e.width=0,e.height=0}function M(){return new Promise(function(e,r){let n,i,f,a;return void 0!==M.cachedResult?e(M.cachedResult):m("data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==","test.jpg",Date.now()).then(function(o){try{return n=o,C(n).then(function(o){try{return i=o[1],I(i,n.type,n.name,n.lastModified).then(function(n){try{return f=n,B(i),C(f).then(function(n){try{return a=n[0],M.cachedResult=1===a.width&&2===a.height,e(M.cachedResult)}catch(e){return r(e)}},r)}catch(e){return r(e)}},r)}catch(e){return r(e)}},r)}catch(e){return r(e)}},r)})}function R(e){return new Promise((r,n)=>{let i=new v;i.onload=e=>{let n=new DataView(e.target.result);if(65496!=n.getUint16(0,!1))return r(-2);let i=n.byteLength,f=2;for(;f<i&&!(8>=n.getUint16(f+2,!1));){let e=n.getUint16(f,!1);if(f+=2,65505==e){if(0x45786966!=n.getUint32(f+=2,!1))return r(-1);let e=18761==n.getUint16(f+=6,!1);f+=n.getUint32(f+4,e);let i=n.getUint16(f,e);f+=2;for(let a=0;a<i;a++)if(274==n.getUint16(f+12*a,e))return r(n.getUint16(f+12*a+8,e))}else{if(65280!=(65280&e))break;f+=n.getUint16(f,!1)}}return r(-1)},i.onerror=e=>n(e),i.readAsArrayBuffer(e)})}function S(e,r){let{width:n}=e,{height:i}=e,{maxWidthOrHeight:f}=r,a,o=e;return isFinite(f)&&(n>f||i>f)&&([o,a]=x(n,i),n>i?(o.width=f,o.height=i/n*f):(o.width=n/i*f,o.height=f),a.drawImage(e,0,0,o.width,o.height),B(e)),o}function T(e,r){let{width:n}=e,{height:i}=e,[f,a]=x(n,i);switch(r>4&&r<9?(f.width=i,f.height=n):(f.width=n,f.height=i),r){case 2:a.transform(-1,0,0,1,n,0);break;case 3:a.transform(-1,0,0,-1,n,i);break;case 4:a.transform(1,0,0,-1,0,i);break;case 5:a.transform(0,1,1,0,0,0);break;case 6:a.transform(0,1,-1,0,i,0);break;case 7:a.transform(0,-1,-1,0,i,n);break;case 8:a.transform(0,-1,1,0,0,n)}return a.drawImage(e,0,0,n,i),B(e),f}function Q(e,r,n=0){return new Promise(function(i,f){let a,o,l,s,u,c,h,d,g,A,w,p,v,m,b,y,U,F,E,_;function Q(e=5){if(r.signal&&r.signal.aborted)throw r.signal.reason;a+=e,r.onProgress(Math.min(a,100))}function H(e){if(r.signal&&r.signal.aborted)throw r.signal.reason;a=Math.min(Math.max(e,a),100),r.onProgress(a)}return a=n,o=r.maxIteration||10,l=1024*r.maxSizeMB*1024,Q(),C(e,r).then((function(n){try{return[,s]=n,Q(),u=S(s,r),Q(),new Promise(function(n,i){var f;if(!(f=r.exifOrientation))return R(e).then((function(e){try{return f=e,a.call(this)}catch(e){return i(e)}}).bind(this),i);function a(){return n(f)}return a.call(this)}).then((function(n){try{return c=n,Q(),M().then((function(n){try{return h=n?u:T(u,c),Q(),d=r.initialQuality||1,g=r.fileType||e.type,I(h,g,e.name,e.lastModified,d).then((function(n){try{var a;if(A=n,Q(),w=A.size>l,p=A.size>e.size,!w&&!p)return H(100),i(A);function c(){if(o--&&(b>l||b>v)){let r,n;return r=_?.95*E.width:E.width,n=_?.95*E.height:E.height,[U,F]=x(r,n),F.drawImage(E,0,0,r,n),d*="image/png"===g?.85:.95,I(U,g,e.name,e.lastModified,d).then(function(e){try{return y=e,B(E),E=U,b=y.size,H(Math.min(99,Math.floor((m-b)/(m-l)*100))),c}catch(e){return f(e)}},f)}return[1]}return v=e.size,b=m=A.size,E=h,_=!r.alwaysKeepResolution&&w,(a=(function(e){for(;e;){if(e.then)return void e.then(a,f);try{if(e.pop){if(e.length)return e.pop()?C.call(this):e;e=c}else e=e.call(this)}catch(e){return f(e)}}}).bind(this))(c);function C(){return B(E),B(U),B(u),B(h),B(s),H(100),i(y)}}catch(e){return f(e)}}).bind(this),f)}catch(e){return f(e)}}).bind(this),f)}catch(e){return f(e)}}).bind(this),f)}catch(e){return f(e)}}).bind(this),f)})}function H(e,r){return new Promise(function(n,a){let o,l,s,u,c,h;if(o={...r},s=0,{onProgress:u}=o,o.maxSizeMB=o.maxSizeMB||Number.POSITIVE_INFINITY,c="boolean"!=typeof o.useWebWorker||o.useWebWorker,delete o.useWebWorker,o.onProgress=e=>{s=e,"function"==typeof u&&u(s)},!(e instanceof Blob||e instanceof p))return a(Error("The file given is not an instance of Blob or File"));if(!/^image/.test(e.type))return a(Error("The file given is not an image"));if(h="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,!c||"function"!=typeof Worker||h)return Q(e,o).then((function(e){try{return l=e,A.call(this)}catch(e){return a(e)}}).bind(this),a);var d=(function(){try{return A.call(this)}catch(e){return a(e)}}).bind(this),g=function(r){try{return Q(e,o).then(function(e){try{return l=e,d()}catch(e){return a(e)}},a)}catch(e){return a(e)}};try{return o.libURL=o.libURL||"https://cdn.jsdelivr.net/npm/browser-image-compression@2.0.2/dist/browser-image-compression.js",new Promise((r,n)=>{i||(i=function(e){let r=[];return"function"==typeof e?r.push(`(${e})()`):r.push(e),URL.createObjectURL(new Blob(r))}("\nlet scriptImported = false\nself.addEventListener('message', async (e) => {\n  const { file, id, imageCompressionLibUrl, options } = e.data\n  options.onProgress = (progress) => self.postMessage({ progress, id })\n  try {\n    if (!scriptImported) {\n      // console.log('[worker] importScripts', imageCompressionLibUrl)\n      self.importScripts(imageCompressionLibUrl)\n      scriptImported = true\n    }\n    // console.log('[worker] self', self)\n    const compressedFile = await imageCompression(file, options)\n    self.postMessage({ file: compressedFile, id })\n  } catch (e) {\n    // console.error('[worker] error', e)\n    self.postMessage({ error: e.message + '\\n' + e.stack, id })\n  }\n})\n"));let f=new Worker(i);f.addEventListener("message",function(e){if(o.signal&&o.signal.aborted)f.terminate();else if(void 0===e.data.progress){if(e.data.error)return n(Error(e.data.error)),void f.terminate();r(e.data.file),f.terminate()}else o.onProgress(e.data.progress)}),f.addEventListener("error",n),o.signal&&o.signal.addEventListener("abort",()=>{n(o.signal.reason),f.terminate()}),f.postMessage({file:e,imageCompressionLibUrl:o.libURL,options:{...o,onProgress:void 0,signal:void 0}})}).then(function(e){try{return l=e,d()}catch(e){return g()}},g)}catch(e){g()}function A(){try{l.name=e.name,l.lastModified=e.lastModified}catch(e){}try{o.preserveExif&&"image/jpeg"===e.type&&(!o.fileType||o.fileType&&o.fileType===e.type)&&(l=f(e,l))}catch(e){}return n(l)}})}H.getDataUrlFromFile=b,H.getFilefromDataUrl=m,H.loadImage=y,H.drawImageInCanvas=E,H.drawFileInCanvas=C,H.canvasToFile=I,H.getExifOrientation=R,H.handleMaxWidthOrHeight=S,H.followExifOrientation=T,H.cleanupCanvasMemory=B,H.isAutoOrientationInBrowser=M,H.approximateBelowMaximumCanvasSizeOfBrowser=F,H.copyExifWithoutOrientation=f,H.getBrowserName=U,H.version="2.0.2"}};