"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3651],{22934:(e,t,a)=>{a.d(t,{A:()=>m});var r=a(95155),l=a(12115),i=a(60760),s=a(76408),n=a(13052),o=a(5196),d=a(54416),c=a(71539),u=a(80722);let m=e=>{let{isOpen:t,onClose:a,onSelect:m,initialData:g,colorScheme:p="red"}=e,[f,x]=(0,l.useState)(1),[b,h]=(0,l.useState)(!1),[y,v]=(0,l.useState)(null),j={spinner:"blue"===p?"border-blue-600":"border-red-600",button:"blue"===p?"bg-blue-600 hover:bg-blue-700":"bg-red-600 hover:bg-red-700",selected:"blue"===p?"border-blue-500 bg-blue-50":"border-red-500 bg-red-50",checkButton:"blue"===p?"border-blue-500 bg-blue-50 text-blue-700":"border-red-500 bg-red-50 text-red-700"},[N,k]=(0,l.useState)([]),[D,C]=(0,l.useState)([]),[S,w]=(0,l.useState)([]),[R,M]=(0,l.useState)([]),[I,P]=(0,l.useState)({}),[A,V]=(0,l.useState)(0),[E,z]=(0,l.useState)(0),[O,B]=(0,l.useState)(0),[F,U]=(0,l.useState)({}),[G,K]=(0,l.useState)(""),[W,_]=(0,l.useState)(""),[H,L]=(0,l.useState)(""),Q=l.useCallback(function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];x(1),V(0),z(0),B(0),U({}),v(null),e||a()},[a]),q=l.useCallback(async()=>{try{h(!0),v(null);let e=await u.jU.getBrands();e.success?k(e.data):v(e.error||"Markalar y\xfcklenirken bir hata oluştu")}catch(e){v("Markalar y\xfcklenirken bir hata oluştu")}finally{h(!1)}},[]);(0,l.useEffect)(()=>{t&&(g&&g.brandId>0?(V(g.brandId),z(g.categoryId),B(g.subCategoryId),U(g.selectedFeatures||{}),console.log("\uD83C\uDFAF ProductCategorySelector initialData.selectedFeatures:",g.selectedFeatures),g.subCategoryId>0?x(4):g.categoryId>0?x(3):x(2)):Q(!0),q())},[t,g,Q,q]);let J=l.useCallback(async e=>{try{h(!0),v(null);let t=await u.jU.getSubCategoryFeatures(e);t.success&&(M(t.data),t.data.forEach(e=>{Z(e.featureDefinitionId)}))}catch(e){console.error("\xd6zellik tanımları y\xfcklenirken hata:",e),v("\xd6zellik tanımları y\xfcklenirken bir hata oluştu.")}finally{h(!1)}},[]),T=l.useCallback(async e=>{try{h(!0),v(null);let t=await u.jU.getCategoriesByBrand(e);t.success?C(t.data):v(t.error||"Kategoriler y\xfcklenirken bir hata oluştu")}catch(e){v("Kategoriler y\xfcklenirken bir hata oluştu")}finally{h(!1)}},[]),X=l.useCallback(async e=>{try{h(!0),v(null);let t=await u.jU.getSubCategories(e);t.success?w(t.data):v(t.error||"Alt kategoriler y\xfcklenirken bir hata oluştu")}catch(e){v("Alt kategoriler y\xfcklenirken bir hata oluştu")}finally{h(!1)}},[]);(0,l.useEffect)(()=>{O>0&&J(O)},[O,J]),(0,l.useEffect)(()=>{A>0&&T(A)},[A,T]),(0,l.useEffect)(()=>{E>0&&X(E)},[E,X]),(0,l.useEffect)(()=>{if(t){let e=window.innerWidth-document.documentElement.clientWidth;document.body.style.overflow="hidden",document.body.style.paddingRight="".concat(e,"px")}else document.body.style.overflow="unset",document.body.style.paddingRight="0px";return()=>{document.body.style.overflow="unset",document.body.style.paddingRight="0px"}},[t]);let Z=async e=>{try{let t=await u.jU.getFeatureValues(e);t.success&&P(a=>({...a,[e]:t.data}))}catch(e){console.error("\xd6zellik değerleri y\xfcklenirken hata:",e)}},Y=e=>{let t=N.find(t=>t.id===e);V(e),K((null==t?void 0:t.name)||""),g&&g.brandId===e||(z(0),B(0),U({})),x(2)},$=e=>{let t=D.find(t=>t.id===e);z(e),_((null==t?void 0:t.name)||""),g&&g.categoryId===e||(B(0),U({})),x(3)},ee=async e=>{let t=S.find(t=>t.id===e);B(e),L((null==t?void 0:t.name)||""),g&&g.subCategoryId===e||U({}),v(null),h(!0);let a=await u.jU.getSubCategoryFeatures(e);h(!1),a.success&&a.data.length>0?(M(a.data),a.data.forEach(e=>{Z(e.featureDefinitionId)}),x(4)):(a.success&&0===a.data.length?v("Bu alt kategoriye ait \xf6zellik bulunmamaktadır."):v(a.error||"\xd6zellikler y\xfcklenirken bir hata oluştu."),M([]),P({}),x(3))},et=(e,t)=>{U(a=>{let r=a[e]||[],l=r.includes(t);return{...a,[e]:l?r.filter(e=>e!==t):[...r,t]}})},ea=()=>{let e=Object.entries(F).filter(e=>{let[,t]=e;return t.length>0});return 0===e.length?[{id:Date.now(),name:"Default",selectedFeatures:{},featureDetails:[],features:[],pricing:{price:0,stock:0,extraDiscount:0,ratios:{pvRatio:0,cvRatio:0,spRatio:0},points:{pv:0,cv:0,sp:0}},images:[],isActive:!0}]:e.map(e=>{let[t,a]=e;return a.map(e=>({featureDefinitionId:parseInt(t),valueId:e}))}).reduce((e,t)=>e.flatMap(e=>t.map(t=>[...e,t])),[[]]).map((e,t)=>{let a=e.map(e=>{var t,a;let r=R.find(t=>t.featureDefinitionId===e.featureDefinitionId),l=null==(t=I[e.featureDefinitionId])?void 0:t.find(t=>t.id===e.valueId);return{featureDefinitionId:e.featureDefinitionId,featureValueId:e.valueId,featureName:(null==r||null==(a=r.featureDefinition)?void 0:a.name)||"",featureValue:(null==l?void 0:l.value)||""}}),r=a.map(e=>e.featureValue).join(" - "),l={};return a.forEach(e=>{l[e.featureDefinitionId]=[e.featureValueId]}),{id:Date.now()+t,name:r,selectedFeatures:l,featureDetails:a,features:a,pricing:{price:0,stock:0,extraDiscount:0,ratios:{pvRatio:0,cvRatio:0,spRatio:0},points:{pv:0,cv:0,sp:0}},images:[],isActive:!0}})},er=(()=>{let e=Object.values(F).map(e=>e.length).filter(e=>e>0);return 0===e.length?1:e.reduce((e,t)=>e*t,1)})();return(0,r.jsx)(i.N,{children:t&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50",children:(0,r.jsxs)(s.P.div,{className:"bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:1===f?"Marka Se\xe7in":2===f?"Kategori Se\xe7in":3===f?"Alt Kategori Se\xe7in":"Varyant Oluştur"}),(0,r.jsx)("button",{onClick:()=>Q(),className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(d.A,{className:"h-6 w-6"})})]}),y&&(0,r.jsx)("div",{className:"p-4 bg-red-50 border-b border-red-200",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:y})}),(0,r.jsx)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-200px)]",children:(()=>{if(b)return(0,r.jsx)("div",{className:"flex justify-center items-center p-12",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 ".concat(j.spinner)})});let e=function(e,t,a){let l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"name";return(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.map(e=>(0,r.jsx)("button",{onClick:()=>a(e.id),className:"p-4 border rounded-lg text-left hover:bg-gray-50 transition-colors text-black ".concat(t===e.id?j.selected:"border-gray-200"),children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:String(e[l])}),(0,r.jsx)(n.A,{className:"h-5 w-5 text-gray-400"})]})},e.id))})};switch(f){case 1:return e(N,A,Y);case 2:return e(D,E,$);case 3:return e(S,O,ee);case 4:return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Varyant Oluşturma Talimatları"}),(0,r.jsxs)("div",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Her \xf6zellik grubundan"})," istediğiniz kadar se\xe7enek se\xe7ebilirsiniz"]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Se\xe7tiğiniz t\xfcm kombinasyonlar"})," otomatik olarak varyant haline gelecek"]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"\xd6rnek:"})," 2 renk + 3 beden = 6 farklı varyant oluşur"]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Hi\xe7bir \xf6zellik se\xe7mezseniz"})," tek bir varsayılan varyant oluşturulur"]})]})]})]})}),R.map(e=>{var t,a,l;return(0,r.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:(null==(t=e.featureDefinition)?void 0:t.name)||"\xd6zellik"}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:[(null==(a=F[e.featureDefinitionId])?void 0:a.length)||0," se\xe7ili"]})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2",children:null==(l=I[e.featureDefinitionId])?void 0:l.map(t=>{var a,l;return(0,r.jsxs)("button",{onClick:()=>et(e.featureDefinitionId,t.id),className:"p-2 border rounded text-sm transition-colors flex items-center justify-between ".concat((null==(a=F[e.featureDefinitionId])?void 0:a.includes(t.id))?j.checkButton:"border-gray-200 hover:bg-gray-50 text-black"),children:[(0,r.jsx)("span",{children:t.value}),(null==(l=F[e.featureDefinitionId])?void 0:l.includes(t.id))&&(0,r.jsx)(o.A,{className:"h-4 w-4"})]},t.id)})})]},e.id)}),Object.values(F).some(e=>e.length>0)&&(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-green-900 mb-2",children:"Oluşturulacak Varyantlar \xd6nizlemesi"}),(0,r.jsxs)("div",{className:"text-sm text-green-800",children:[(0,r.jsxs)("p",{className:"mb-2",children:[(0,r.jsxs)("strong",{children:[er," adet varyant"]})," oluşturulacak:"]}),(0,r.jsx)("div",{className:"space-y-1",children:Object.entries(F).filter(e=>{let[,t]=e;return t.length>0}).map(e=>{var t;let[a,l]=e,i=R.find(e=>e.featureDefinitionId===parseInt(a)),s=l.map(e=>{var t,r;return null==(r=I[parseInt(a)])||null==(t=r.find(t=>t.id===e))?void 0:t.value}).filter(Boolean);return(0,r.jsxs)("p",{children:[(0,r.jsxs)("strong",{children:[(null==i||null==(t=i.featureDefinition)?void 0:t.name)||"\xd6zellik",":"]})," ",s.join(", ")]},a)})})]})]})]});default:return null}})()}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-200 mt-auto bg-gray-50",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{children:f>1&&(0,r.jsx)("button",{onClick:()=>x(f-1),className:"px-4 py-2 border border-gray-300 rounded-lg text-black hover:bg-gray-50",children:"Geri"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[4===f&&(0,r.jsxs)("div",{className:"flex items-center text-sm text-blue-600 font-medium",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 mr-2"}),Object.values(F).every(e=>0===e.length)?(0,r.jsx)("span",{children:"Varyant se\xe7ilmedi"}):(0,r.jsxs)("span",{children:[er," varyant oluşturulacak"]})]}),(0,r.jsx)("button",{onClick:()=>Q(),className:"px-4 py-2 border border-gray-300 rounded-lg text-black hover:bg-gray-50",children:"İptal"}),4===f?(0,r.jsx)("button",{onClick:()=>{let e=ea(),t=[];Object.entries(F).forEach(e=>{let[a,r]=e,l=R.find(e=>e.featureDefinitionId===parseInt(a));r.forEach(e=>{var r,i;let s=null==(r=I[parseInt(a)])?void 0:r.find(t=>t.id===e);l&&s&&t.push({featureName:(null==(i=l.featureDefinition)?void 0:i.name)||"",featureValue:s.value})})}),m({brandId:A,categoryId:E,subCategoryId:O,brandName:G,categoryName:W,subCategoryName:H,generatedVariants:e,selectedFeatures:F,selectedFeatureDetails:t}),a()},disabled:Object.values(F).every(e=>0===e.length),className:"px-4 py-2 ".concat(j.button," text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"),children:"Se\xe7imi Tamamla"}):(0,r.jsx)("button",{onClick:()=>x(f+1),disabled:1===f?!A:2===f?!E:!O,className:"px-4 py-2 ".concat(j.button," text-white rounded-lg disabled:opacity-50"),children:"Devam Et"})]})]})})]})})})}},45106:(e,t,a)=>{a.d(t,{$N:()=>I,EI:()=>M,F0:()=>c,Gc:()=>d,HX:()=>w,JZ:()=>x,OG:()=>D,Ph:()=>p,QK:()=>m,QR:()=>P,S:()=>u,VS:()=>o,Zm:()=>k,_f:()=>S,c6:()=>h,fW:()=>C,gA:()=>b,hg:()=>y,ig:()=>v,lA:()=>g,nb:()=>N,qA:()=>R,u6:()=>f,vQ:()=>j});var r=a(65453),l=a(46786);let i={editPersonalInfo:!1,referenceRegistration:!1,addAddress:!1,setDefaultConfirmation:!1,banking:!1,addCard:!1,setDefaultCard:!1,registerSuccess:!1,successNotification:!1,productCategorySelector:!1,productVariantSetup:!1,productVariant:!1,productDeleteConfirmation:!1},s={editPersonalInfoUser:null,referenceRegistrationData:null,addAddressData:null,setDefaultConfirmationData:null,bankingData:null,addCardData:null,setDefaultCardData:null,successNotificationData:null,productCategorySelectorData:null,productVariantSetupData:null,productVariantData:null,productDeleteConfirmationData:null},n=(0,r.v)()((0,l.lt)(e=>({...i,...s,openModal:(t,a)=>{e(e=>({...e,[t]:!0,...a&&{["".concat(t,"User")]:a}}),!1,"modal/open/".concat(t))},closeModal:t=>{e(e=>({...e,[t]:!1,["".concat(t,"User")]:null}),!1,"modal/close/".concat(t))},closeAllModals:()=>{e({...i,...s},!1,"modal/closeAll")},openEditPersonalInfoModal:t=>{e({editPersonalInfo:!0,editPersonalInfoUser:t},!1,"modal/openEditPersonalInfo")},closeEditPersonalInfoModal:()=>{e({editPersonalInfo:!1,editPersonalInfoUser:null},!1,"modal/closeEditPersonalInfo")},openReferenceRegistrationModal:t=>{e({referenceRegistration:!0,referenceRegistrationData:t},!1,"modal/openReferenceRegistration")},closeReferenceRegistrationModal:()=>{e({referenceRegistration:!1,referenceRegistrationData:null},!1,"modal/closeReferenceRegistration")},openAddAddressModal:t=>{e({addAddress:!0,addAddressData:t},!1,"modal/openAddAddress")},closeAddAddressModal:()=>{e({addAddress:!1,addAddressData:null},!1,"modal/closeAddAddress")},openSetDefaultConfirmationModal:t=>{e({setDefaultConfirmation:!0,setDefaultConfirmationData:t},!1,"modal/openSetDefaultConfirmation")},closeSetDefaultConfirmationModal:()=>{e({setDefaultConfirmation:!1,setDefaultConfirmationData:null},!1,"modal/closeSetDefaultConfirmation")},openBankingModal:t=>{e({banking:!0,bankingData:t},!1,"modal/openBanking")},closeBankingModal:()=>{e({banking:!1,bankingData:null},!1,"modal/closeBanking")},openAddCardModal:t=>{e({addCard:!0,addCardData:t},!1,"modal/openAddCard")},closeAddCardModal:()=>{e({addCard:!1,addCardData:null},!1,"modal/closeAddCard")},openSetDefaultCardModal:t=>{e({setDefaultCard:!0,setDefaultCardData:t},!1,"modal/openSetDefaultCard")},closeSetDefaultCardModal:()=>{e({setDefaultCard:!1,setDefaultCardData:null},!1,"modal/closeSetDefaultCard")},openRegisterSuccessModal:()=>{e({registerSuccess:!0},!1,"modal/openRegisterSuccess")},closeRegisterSuccessModal:()=>{e({registerSuccess:!1},!1,"modal/closeRegisterSuccess")},openSuccessNotificationModal:t=>{e({successNotification:!0,successNotificationData:t},!1,"modal/openSuccessNotification")},closeSuccessNotificationModal:()=>{e({successNotification:!1,successNotificationData:null},!1,"modal/closeSuccessNotification")},openProductCategorySelector:t=>{e({productCategorySelector:!0,productCategorySelectorData:t},!1,"modal/openProductCategorySelector")},closeProductCategorySelector:()=>{e({productCategorySelector:!1,productCategorySelectorData:null},!1,"modal/closeProductCategorySelector")},openProductVariantSetup:t=>{e({productVariantSetup:!0,productVariantSetupData:t},!1,"modal/openProductVariantSetup")},closeProductVariantSetup:()=>{e({productVariantSetup:!1,productVariantSetupData:null},!1,"modal/closeProductVariantSetup")},openProductVariant:t=>{e({productVariant:!0,productVariantData:t},!1,"modal/openProductVariant")},closeProductVariant:()=>{e({productVariant:!1,productVariantData:null},!1,"modal/closeProductVariant")},openProductDeleteConfirmation:t=>{e({productDeleteConfirmation:!0,productDeleteConfirmationData:t},!1,"modal/openProductDeleteConfirmation")},closeProductDeleteConfirmation:()=>{e({productDeleteConfirmation:!1,productDeleteConfirmationData:null},!1,"modal/closeProductDeleteConfirmation")}}),{name:"modal-store",enabled:!1})),o=()=>n(e=>e.editPersonalInfo),d=()=>n(e=>e.editPersonalInfoUser),c=()=>n(e=>e.referenceRegistration),u=()=>n(e=>e.referenceRegistrationData),m=()=>n(e=>e.addAddress),g=()=>n(e=>e.addAddressData),p=()=>n(e=>e.setDefaultConfirmation),f=()=>n(e=>e.setDefaultConfirmationData),x=()=>n(e=>e.banking),b=()=>n(e=>e.bankingData),h=()=>n(e=>e.addCard),y=()=>n(e=>e.addCardData),v=()=>n(e=>e.setDefaultCard),j=()=>n(e=>e.setDefaultCardData),N=()=>n(e=>e.registerSuccess),k=()=>n(e=>e.successNotification),D=()=>n(e=>e.successNotificationData),C=()=>n(e=>e.productCategorySelector),S=()=>n(e=>e.productCategorySelectorData),w=()=>n(e=>e.productVariant),R=()=>n(e=>e.productVariantData),M=()=>n(e=>e.productDeleteConfirmation),I=()=>n(e=>e.productDeleteConfirmationData),P=()=>{let e=n(e=>e.openModal),t=n(e=>e.closeModal),a=n(e=>e.closeAllModals),r=n(e=>e.openEditPersonalInfoModal),l=n(e=>e.closeEditPersonalInfoModal),i=n(e=>e.openReferenceRegistrationModal),s=n(e=>e.closeReferenceRegistrationModal),o=n(e=>e.openAddAddressModal),d=n(e=>e.closeAddAddressModal),c=n(e=>e.openSetDefaultConfirmationModal),u=n(e=>e.closeSetDefaultConfirmationModal),m=n(e=>e.openBankingModal),g=n(e=>e.closeBankingModal),p=n(e=>e.openAddCardModal),f=n(e=>e.closeAddCardModal),x=n(e=>e.openSetDefaultCardModal),b=n(e=>e.closeSetDefaultCardModal),h=n(e=>e.openRegisterSuccessModal),y=n(e=>e.closeRegisterSuccessModal),v=n(e=>e.openSuccessNotificationModal),j=n(e=>e.closeSuccessNotificationModal),N=n(e=>e.openProductCategorySelector),k=n(e=>e.closeProductCategorySelector),D=n(e=>e.openProductVariantSetup),C=n(e=>e.closeProductVariantSetup),S=n(e=>e.openProductVariant),w=n(e=>e.closeProductVariant);return{openModal:e,closeModal:t,closeAllModals:a,openEditPersonalInfoModal:r,closeEditPersonalInfoModal:l,openReferenceRegistrationModal:i,closeReferenceRegistrationModal:s,openAddAddressModal:o,closeAddAddressModal:d,openSetDefaultConfirmationModal:c,closeSetDefaultConfirmationModal:u,openBankingModal:m,closeBankingModal:g,openAddCardModal:p,closeAddCardModal:f,openSetDefaultCardModal:x,closeSetDefaultCardModal:b,openRegisterSuccessModal:h,closeRegisterSuccessModal:y,openSuccessNotificationModal:v,closeSuccessNotificationModal:j,openProductCategorySelector:N,closeProductCategorySelector:k,openProductVariantSetup:D,closeProductVariantSetup:C,openProductVariant:S,closeProductVariant:w,openProductDeleteConfirmation:n(e=>e.openProductDeleteConfirmation),closeProductDeleteConfirmation:n(e=>e.closeProductDeleteConfirmation)}}},81087:(e,t,a)=>{a.d(t,{A:()=>m});var r=a(95155),l=a(12115),i=a(60760),s=a(76408),n=a(54416),o=a(81284),d=a(29869),c=a(62525),u=a(4229);let m=e=>{var t,a,m;let{isOpen:g,onClose:p,onSave:f,editingVariant:x,hidePvCvSp:b=!1,colorScheme:h="red"}=e,[y,v]=(0,l.useState)(null),[j,N]=(0,l.useState)(null),k={ring:"blue"===h?"focus:ring-blue-500":"focus:ring-red-500",button:"blue"===h?"bg-blue-600 hover:bg-blue-700":"bg-red-600 hover:bg-red-700"};if((0,l.useEffect)(()=>{if(g&&x){let e={...x};if(!e.pricing.points){let{price:t,ratios:a}=e.pricing;e.pricing.points={pv:Math.round(t*(a.pvRatio/100)),cv:Math.round(t*(a.cvRatio/100)),sp:Math.round(t*(a.spRatio/100))}}v(e)}else v(null),N(null)},[g,x]),(0,l.useEffect)(()=>{if(y&&y.pricing.price>0){let{price:e,ratios:t}=y.pricing,a=Math.round(e*(t.pvRatio/100)),r=Math.round(e*(t.cvRatio/100)),l=Math.round(e*(t.spRatio/100));y.pricing.points&&y.pricing.points.pv===a&&y.pricing.points.cv===r&&y.pricing.points.sp===l||v(e=>e?{...e,pricing:{...e.pricing,points:{pv:a,cv:r,sp:l}}}:null)}},[null==y?void 0:y.pricing.price,null==y?void 0:y.pricing.ratios.pvRatio,null==y?void 0:y.pricing.ratios.cvRatio,null==y?void 0:y.pricing.ratios.spRatio]),(0,l.useEffect)(()=>{if(g){let e=window.innerWidth-document.documentElement.clientWidth;document.body.style.overflow="hidden",document.body.style.paddingRight="".concat(e,"px")}else document.body.style.overflow="unset",document.body.style.paddingRight="0px";return()=>{document.body.style.overflow="unset",document.body.style.paddingRight="0px"}},[g]),!y)return null;let D=(e,t)=>{v(a=>a?{...a,pricing:{...a.pricing,[e]:t}}:null)},C=(e,t)=>{v(a=>a?{...a,pricing:{...a.pricing,ratios:{...a.pricing.ratios,[e]:t}}}:null)},S=e=>{v(t=>{if(!t)return null;let a=t.images.filter((t,a)=>a!==e);return t.images[e].isMain&&a.length>0&&(a[0].isMain=!0),{...t,images:a}})},w=e=>{v(t=>t?{...t,images:t.images.map((t,a)=>({...t,isMain:a===e}))}:null)},R=e=>e.pricing.price<=0?(N("Fiyat 0'dan b\xfcy\xfck olmalıdır"),!1):!(e.pricing.stock<0)||(N("Stok miktarı negatif olamaz"),!1);return(0,r.jsx)(i.N,{children:g&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50",children:(0,r.jsxs)(s.P.div,{className:"bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},transition:{duration:.2},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:"Varyant Detayları"}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Varyantın fiyat, stok ve g\xf6rsellerini d\xfczenleyin."})]}),(0,r.jsx)("button",{onClick:p,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(n.A,{className:"h-6 w-6"})})]}),j&&(0,r.jsx)("div",{className:"p-4 bg-red-50 border-b border-red-200 flex-shrink-0",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:j})}),(0,r.jsx)("div",{className:"p-6 overflow-y-auto flex-grow",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Varyant Bilgisi"}),(0,r.jsx)("p",{className:"text-gray-800 font-semibold text-xl",children:y.name}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:y.featureDetails.map(e=>"".concat(e.featureName,": ").concat(e.featureValue)).join(", ")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Fiyat ve Stok"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 ".concat(b?"md:grid-cols-2":"md:grid-cols-3"),children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Fiyat (₺) *"}),(0,r.jsx)("input",{type:"number",value:isNaN(y.pricing.price)?"":y.pricing.price,onChange:e=>D("price",parseFloat(e.target.value)),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ".concat(k.ring," focus:border-transparent text-black"),placeholder:"0.00"})]}),!b&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"İndirim (%)"}),(0,r.jsx)("input",{type:"number",value:isNaN(y.pricing.extraDiscount)?"":y.pricing.extraDiscount,onChange:e=>D("extraDiscount",parseInt(e.target.value)),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ".concat(k.ring," focus:border-transparent text-black"),placeholder:"0"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Stok Miktarı *"}),(0,r.jsx)("input",{type:"number",value:isNaN(y.pricing.stock)?"":y.pricing.stock,onChange:e=>D("stock",parseInt(e.target.value)),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ".concat(k.ring," focus:border-transparent text-black"),placeholder:"0"})]})]})]}),!b&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Puan Oranları"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"PV Oranı (%)"}),(0,r.jsx)("input",{type:"number",value:isNaN(y.pricing.ratios.pvRatio)?"":y.pricing.ratios.pvRatio,onChange:e=>C("pvRatio",parseInt(e.target.value)),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ".concat(k.ring," focus:border-transparent text-black"),placeholder:"0"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CV Oranı (%)"}),(0,r.jsx)("input",{type:"number",value:isNaN(y.pricing.ratios.cvRatio)?"":y.pricing.ratios.cvRatio,onChange:e=>C("cvRatio",parseInt(e.target.value)),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ".concat(k.ring," focus:border-transparent text-black"),placeholder:"0"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"SP Oranı (%)"}),(0,r.jsx)("input",{type:"number",value:isNaN(y.pricing.ratios.spRatio)?"":y.pricing.ratios.spRatio,onChange:e=>C("spRatio",parseInt(e.target.value)),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ".concat(k.ring," focus:border-transparent text-black"),placeholder:"0"})]})]}),(0,r.jsxs)("div",{className:"mt-4 p-3 bg-gray-100 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-800 mb-2",children:"Hesaplanan Puanlar:"}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center text-sm",children:[(0,r.jsxs)("div",{className:"font-semibold text-blue-600",children:["PV: ",(null==(t=y.pricing.points)?void 0:t.pv)||0]}),(0,r.jsxs)("div",{className:"font-semibold text-green-600",children:["CV: ",(null==(a=y.pricing.points)?void 0:a.cv)||0]}),(0,r.jsxs)("div",{className:"font-semibold text-purple-600",children:["SP: ",(null==(m=y.pricing.points)?void 0:m.sp)||0]})]})]})]}),b&&(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(o.A,{className:"h-5 w-5 text-blue-400"})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsxs)("p",{className:"text-sm text-blue-700",children:[(0,r.jsx)("strong",{children:"Not:"})," PV, CV, SP oranları ve indirim y\xfczdesi admin tarafından belirlenecektir."]})})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Varyant G\xf6rselleri"}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsxs)("label",{htmlFor:"variant-images",className:"flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center pt-5 pb-6",children:[(0,r.jsx)(d.A,{className:"w-8 h-8 mb-4 text-gray-500"}),(0,r.jsx)("p",{className:"mb-2 text-sm text-gray-500",children:(0,r.jsx)("span",{className:"font-semibold",children:"G\xf6rsel y\xfckle"})}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"PNG, JPG (MAX. 5MB)"})]}),(0,r.jsx)("input",{id:"variant-images",type:"file",multiple:!0,accept:"image/*",onChange:e=>{let t=e.target.files;if(t&&y){let e=Array.from(t).map((e,t)=>({url:URL.createObjectURL(e),isMain:0===y.images.length&&0===t,sortOrder:y.images.length+t,file:e}));v(t=>t?{...t,images:[...t.images,...e]}:null)}},className:"hidden"})]})}),y.images.length>0&&(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:y.images.map((e,t)=>(0,r.jsxs)("div",{className:"relative group aspect-square",children:[(0,r.jsx)("img",{src:e.url,alt:"Varyant g\xf6rseli ".concat(t+1),className:"w-full h-full object-cover rounded-lg"}),e.isMain&&(0,r.jsx)("div",{className:"absolute top-1 left-1 bg-red-600 text-white text-xs px-2 py-1 rounded",children:"Ana"}),(0,r.jsxs)("div",{className:"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center space-x-2",children:[!e.isMain&&(0,r.jsx)("button",{type:"button",onClick:()=>w(t),className:"px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700",children:"Ana Yap"}),(0,r.jsx)("button",{type:"button",onClick:()=>S(t),className:"p-1 bg-red-600 text-white rounded-full hover:bg-red-700",children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]})]},t))})]})]})}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 flex-shrink-0",children:[(0,r.jsx)("button",{onClick:p,className:"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"İptal"}),(0,r.jsxs)("button",{onClick:()=>{if(!y)return;let e={...y.pricing,price:isNaN(y.pricing.price)?0:y.pricing.price,stock:isNaN(y.pricing.stock)?0:y.pricing.stock,extraDiscount:isNaN(y.pricing.extraDiscount)?0:y.pricing.extraDiscount,ratios:{...y.pricing.ratios,pvRatio:isNaN(y.pricing.ratios.pvRatio)?0:y.pricing.ratios.pvRatio,cvRatio:isNaN(y.pricing.ratios.cvRatio)?0:y.pricing.ratios.cvRatio,spRatio:isNaN(y.pricing.ratios.spRatio)?0:y.pricing.ratios.spRatio}},t={...y,pricing:e};R(t)&&(f(t),p())},className:"px-6 py-2 ".concat(k.button," text-white rounded-lg transition-colors flex items-center"),children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Değişiklikleri Kaydet"]})]})]})})})}}}]);