(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9718],{23903:(e,a,t)=>{"use strict";t.d(a,{Q6:()=>l,Z9:()=>n,e$:()=>d});var r=t(65453),i=t(46786);let s={isModalOpen:!1,selectedProductId:null,selectedVariantIndex:0,currentImageIndex:0,cachedProducts:new Map,lastFetchTime:new Map,isLoading:!1,error:null,prefetchQueue:new Set},n=(0,r.v)()((0,i.lt)((e,a)=>({...s,openModal:a=>{e({isModalOpen:!0,selectedProductId:a,selectedVariantIndex:0,currentImageIndex:0,error:null},!1,"productDetail/openModal")},closeModal:()=>{e({isModalOpen:!1,selectedProductId:null,selectedVariantIndex:0,currentImageIndex:0,error:null},!1,"productDetail/closeModal")},setSelectedVariant:a=>{e({selectedVariantIndex:a,currentImageIndex:0},!1,"productDetail/setSelectedVariant")},setCurrentImage:a=>{e({currentImageIndex:a},!1,"productDetail/setCurrentImage")},nextImage:()=>{let{selectedProductId:t,selectedVariantIndex:r,currentImageIndex:i,cachedProducts:s}=a();if(!t)return;let n=s.get(t);if(!n||!n.variants[r])return;let l=n.variants[r].images.length;l>0&&e({currentImageIndex:(i+1)%l},!1,"productDetail/nextImage")},prevImage:()=>{let{selectedProductId:t,selectedVariantIndex:r,currentImageIndex:i,cachedProducts:s}=a();if(!t)return;let n=s.get(t);if(!n||!n.variants[r])return;let l=n.variants[r].images.length;l>0&&e({currentImageIndex:(i-1+l)%l},!1,"productDetail/prevImage")},setCachedProduct:(t,r)=>{let{cachedProducts:i,lastFetchTime:s}=a(),n=new Map(i),l=new Map(s);n.set(t,r),l.set(t,Date.now()),e({cachedProducts:n,lastFetchTime:l},!1,"productDetail/setCachedProduct")},getCachedProduct:e=>{let{cachedProducts:t}=a();return t.get(e)||null},isCacheValid:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3e5,{lastFetchTime:r}=a(),i=r.get(e);return!!i&&Date.now()-i<t},clearCache:()=>{e({cachedProducts:new Map,lastFetchTime:new Map},!1,"productDetail/clearCache")},clearProductCache:t=>{let{cachedProducts:r,lastFetchTime:i}=a(),s=new Map(r),n=new Map(i);s.delete(t),n.delete(t),e({cachedProducts:s,lastFetchTime:n},!1,"productDetail/clearProductCache")},addToPrefetchQueue:t=>{let{prefetchQueue:r}=a(),i=new Set(r);i.add(t),e({prefetchQueue:i},!1,"productDetail/addToPrefetchQueue")},removeFromPrefetchQueue:t=>{let{prefetchQueue:r}=a(),i=new Set(r);i.delete(t),e({prefetchQueue:i},!1,"productDetail/removeFromPrefetchQueue")},clearPrefetchQueue:()=>{e({prefetchQueue:new Set},!1,"productDetail/clearPrefetchQueue")},setLoading:a=>{e({isLoading:a},!1,"productDetail/setLoading")},setError:a=>{e({error:a},!1,"productDetail/setError")},reset:()=>{e({...s,cachedProducts:new Map,lastFetchTime:new Map},!1,"productDetail/reset")}}),{name:"product-detail-store",enabled:!1})),l=()=>n(e=>e.selectedVariantIndex),d=()=>n(e=>e.currentImageIndex)},32208:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>C});var r=t(95155),i=t(12115),s=t(87220),n=t(35695),l=t(76408),d=t(35169),o=t(75525),c=t(81284),u=t(43332),m=t(62525),g=t(71539),p=t(27213),x=t(4229),h=t(6874),f=t.n(h),y=t(80722),b=t(22934),v=t(81087),I=t(37348),N=t(23903),j=t(45106),D=t(26715),V=t(32960),w=t(5041);let k=e=>{let{params:a}=e,{user:t,isLoading:h}=(0,s.A)(),k=(0,n.useRouter)(),C=(0,n.useSearchParams)(),F=(0,D.jE)(),P=(0,N.Z9)(e=>e.clearProductCache),S="pending-products"===(C.get("from")||"products")?{url:"/admin/pending-products",text:"\xdcr\xfcn Onay Listesi"}:{url:"/admin/products",text:"\xdcr\xfcn Listesi"},[M,E]=i.useState(null),[A,R]=i.useState(!1),{formData:O,selectedNames:z,variants:Q,error:K,availableFeatures:T,originalProductId:q}=(0,I.K_)(),{handleInputChange:L,setCategorySelection:_,clearAllSelections:U,deleteVariant:Y,removeImage:G,setMainImage:H,setError:W,reset:B,setVariants:Z,saveVariant:X,initializeWithProduct:$,setOriginalProductId:J}=(0,I.K_)(e=>e),{openProductCategorySelector:ee,closeProductCategorySelector:ea,openProductVariant:et,closeProductVariant:er}=(0,j.QR)(),ei=(0,j.fW)(),es=(0,j._f)(),en=(0,j.HX)(),el=(0,j.qA)();(0,i.useEffect)(()=>{(async()=>{let e=await a;E(e.id),J(parseInt(e.id))})()},[a,J]);let{data:ed,isLoading:eo}=(0,V.I)({queryKey:["productDetail",M],queryFn:()=>y.jU.getProductDetail(parseInt(M)),enabled:!!M});(0,i.useEffect)(()=>{(null==ed?void 0:ed.success)&&(null==ed?void 0:ed.data)&&(console.log("\uD83D\uDD0D Raw product data from API:",ed.data),$(ed.data))},[ed,$]),(0,i.useEffect)(()=>{console.log("\uD83C\uDFAF Edit page variants:",Q),console.log("\uD83C\uDFAF Edit page variants length:",Q.length),console.log("\uD83C\uDFAF Edit page formData.subCategoryId:",O.subCategoryId),console.log("\uD83C\uDFAF Edit page formData.hasVariants:",O.hasVariants)},[Q,O.subCategoryId,O.hasVariants]);let ec=e=>{let a={};return e.forEach(e=>{e.featureDetails.forEach(e=>{a[e.featureDefinitionId]||(a[e.featureDefinitionId]=[]),a[e.featureDefinitionId].includes(e.featureValueId)||a[e.featureDefinitionId].push(e.featureValueId)})}),a},{mutate:eu,isPending:em}=(0,w.n)({mutationFn:e=>y.jU.updateFullProduct(e),onSuccess:()=>{console.log("✅ \xdcr\xfcn başarıyla g\xfcncellendi. Cache temizleniyor..."),F.invalidateQueries({queryKey:["products"]}),F.invalidateQueries({queryKey:["adminProducts"]}),F.invalidateQueries({queryKey:["adminProductStatistics"]}),F.invalidateQueries({queryKey:["productDetail",M]}),M&&P(parseInt(M)),k.push(S.url),B()},onError:e=>{W(e.message||"\xdcr\xfcn g\xfcncellenirken bir hata oluştu")}});(0,i.useEffect)(()=>()=>{B()},[B]),(0,i.useEffect)(()=>{t&&"admin"!==t.role&&k.push("/"),h||t||k.push("/login")},[t,h,k]);let eg=e=>{et({editingVariant:e,availableFeatures:T,existingVariants:Q})},ep=async e=>{e.preventDefault(),W(null);try{if(!O.name.trim())throw Error("\xdcr\xfcn adı gereklidir");if(!O.description.trim())throw Error("\xdcr\xfcn a\xe7ıklaması gereklidir");if(O.brandId<=0)throw Error("Marka ve kategori se\xe7imi gereklidir");if(0===Q.length)throw Error("En az bir varyant oluşturmalısınız.");for(let e of Q){if(e.pricing.price<=0)throw Error("".concat(e.name," varyantı i\xe7in fiyat 0'dan b\xfcy\xfck olmalıdır"));if(e.pricing.stock<0)throw Error("".concat(e.name," varyantı i\xe7in stok miktarı negatif olamaz"))}let e=new FormData,t=await a;for(let[a,r]of(e.append("Product.Id",t.id),e.append("Product.Name",O.name),e.append("Product.Description",O.description),e.append("Product.BrandId",O.brandId.toString()),e.append("Product.SubCategoryId",O.subCategoryId.toString()),Q.forEach((a,t)=>{if(!a.id)throw console.error("❌ Variant ID eksik:",a),Error("Variant ".concat(t+1," i\xe7in ID eksik"));e.append("Variant[".concat(t,"].Id"),a.id.toString()),e.append("Variant[".concat(t,"].Stock"),a.pricing.stock.toString()),e.append("Variant[".concat(t,"].Price"),a.pricing.price.toString()),e.append("Variant[".concat(t,"].ExtraDiscount"),Math.round(a.pricing.extraDiscount||0).toString()),e.append("Variant[".concat(t,"].Pv"),Math.round(a.pricing.ratios.pvRatio||0).toString()),e.append("Variant[".concat(t,"].Cv"),Math.round(a.pricing.ratios.cvRatio||0).toString()),e.append("Variant[".concat(t,"].Sp"),Math.round(a.pricing.ratios.spRatio||0).toString());let r=[],i=a.featureDetails.filter(e=>e.featureValueId&&e.featureValueId>0).map(e=>e.featureValueId);i.length>0?(r=i,console.log("✅ Variant ".concat(t," - featureDetails'den alınan featureValueIds:"),r)):(r=Object.values(a.selectedFeatures).flat(),console.log("⚠️ Variant ".concat(t," - selectedFeatures'dan alınan featureValueIds:"),r)),console.log("\uD83D\uDD0D Variant ".concat(t," final featureValueIds:"),r),console.log("\uD83D\uDD0D Variant ".concat(t," featureDetails:"),a.featureDetails),console.log("\uD83D\uDD0D Variant ".concat(t," selectedFeatures:"),a.selectedFeatures),r.forEach((a,r)=>{e.append("Variant[".concat(t,"].FeatureValueIds[").concat(r,"]"),a.toString())});let s=0;a.images.forEach(a=>{a.file?(e.append("Variant[".concat(t,"].Images[").concat(s,"].File"),a.file),e.append("Variant[".concat(t,"].Images[").concat(s,"].IsMain"),a.isMain.toString()),e.append("Variant[".concat(t,"].Images[").concat(s,"].SortOrder"),s.toString()),console.log("\uD83D\uDCE4 Yeni fotoğraf g\xf6nderiliyor:",a.file.name,"Index:",s),s++):a.id&&a.url&&!a.url.startsWith("blob:")&&(e.append("Variant[".concat(t,"].Images[").concat(s,"].Id"),a.id.toString()),e.append("Variant[".concat(t,"].Images[").concat(s,"].IsMain"),a.isMain.toString()),e.append("Variant[".concat(t,"].Images[").concat(s,"].SortOrder"),s.toString()),console.log("\uD83D\uDD04 Mevcut fotoğraf korunuyor:",a.url,"ID:",a.id,"Index:",s),s++)}),console.log("✅ Variant ".concat(t+1," (ID: ").concat(a.id,") hazırlandı"))}),console.log("\uD83D\uDCCB FormData i\xe7eriği:"),e.entries()))console.log("".concat(a,":"),r);console.log("\uD83D\uDE80 \xdcr\xfcn g\xfcncelleme isteği g\xf6nderiliyor..."),eu(e)}catch(e){W(e.message||"\xdcr\xfcn g\xfcncellenirken bir hata oluştu")}};return h||eo?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):t&&"admin"===t.role?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:[(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(f(),{href:S.url,className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 mr-2"}),S.text]}),(0,r.jsx)("span",{className:"text-gray-300",children:"/"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"\xdcr\xfcn D\xfczenle"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 bg-red-100 px-4 py-2 rounded-lg",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 text-red-600"}),(0,r.jsx)("span",{className:"text-red-800 font-medium",children:"Admin Erişimi"})]})]})}),K&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsx)("p",{className:"text-red-800",children:K}),(0,r.jsx)("button",{onClick:()=>W(null),className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Kapat"})]}),(0,r.jsxs)("form",{onSubmit:ep,className:"space-y-8",children:[(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(c.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Temel Bilgiler"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xdcr\xfcn Adı *"}),(0,r.jsx)("input",{type:"text",value:O.name,onChange:e=>L("name",e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-black",placeholder:"\xdcr\xfcn adını girin...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"A\xe7ıklama *"}),(0,r.jsx)("textarea",{value:O.description,onChange:e=>L("description",e.target.value),rows:4,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-black",placeholder:"\xdcr\xfcn a\xe7ıklamasını girin...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Marka, Kategori ve Detaylar"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{type:"button",onClick:async()=>{if(O&&O.subCategoryId>0)try{R(!0);let e=Q.map(e=>{let a=e.features||[],t=[];return a.forEach(e=>{void 0!==e.featureDefinitionId&&void 0!==e.featureValueId&&t.push({featureDefinitionId:e.featureDefinitionId,featureValueId:e.featureValueId,featureName:e.featureName,featureValue:e.featureValue})}),{...e,featureDetails:t}}),a=ec(e);Z(e),ee({initialData:{brandId:O.brandId,categoryId:O.categoryId,subCategoryId:O.subCategoryId,selectedFeatures:a}})}catch(e){console.error("\xd6zellik se\xe7imleri y\xfcklenirken hata:",e),ee({initialData:{brandId:O.brandId,categoryId:O.categoryId,subCategoryId:O.subCategoryId,selectedFeatures:{}}})}finally{R(!1)}else ee({initialData:{brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{}}})},disabled:A,className:"flex-grow px-4 py-3 border-2 border-dashed border-gray-300 rounded-lg hover:border-red-500 transition-colors flex items-center justify-center text-gray-600 hover:text-red-600 disabled:opacity-50 disabled:cursor-not-allowed",children:A?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"}),"Y\xfckleniyor..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"h-5 w-5 mr-2"}),O.brandId>0?"Kategori Se\xe7imini D\xfczenle":"Marka, Kategori ve Detaylar Se\xe7in"]})}),O.brandId>0&&(0,r.jsx)("button",{type:"button",onClick:U,title:"T\xfcm se\xe7imleri temizle",className:"flex-shrink-0 p-3 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors",children:(0,r.jsx)(m.A,{className:"h-5 w-5"})})]}),O.brandId>0&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-gray-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"Se\xe7ili:"})," Marka: ",z.brandName,O.categoryId>0&&", Kategori: ".concat(z.categoryName),O.subCategoryId>0&&", Alt Kategori: ".concat(z.subCategoryName)]})})]})]})]}),(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"h-6 w-6 text-red-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Varyant Y\xf6netimi"})]})}),O.subCategoryId<=0&&(0,r.jsx)("div",{className:"p-4 text-center bg-gray-50 rounded-lg",children:(0,r.jsx)("p",{className:"text-gray-500 font-medium",children:"Tekli \xfcr\xfcn eklemek veya varyant oluşturmak i\xe7in l\xfctfen \xf6nce marka, kategori ve alt kategori se\xe7in."})}),O.subCategoryId>0&&0===Q.length&&(0,r.jsxs)("div",{className:"p-8 text-center bg-gray-50 rounded-lg",children:[(0,r.jsx)(g.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-3"}),(0,r.jsx)("p",{className:"text-gray-500 font-medium",children:"Hen\xfcz varyant oluşturulmadı."}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1 mb-4",children:'Yukarıdaki "Marka, Kategori ve Detaylar Se\xe7in" kısmı ile d\xfczenleyebilirsiniz.'})]}),Q.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-blue-600 mr-2 mt-0.5"}),(0,r.jsx)("p",{className:"text-sm text-blue-800",children:"Oluşturulan her bir varyant kombinasyonu i\xe7in Fiyat, Stok ve Puan bilgilerini girin. Tek bir \xfcr\xfcn ekliyorsanız bile, bu \xfcr\xfcn bir varyant olarak kabul edilir."})]})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Varyant"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fiyat (₺)"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stok"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"PV Oranı (%)"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"CV Oranı (%)"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"SP Oranı (%)"}),(0,r.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,r.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:Q.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.featureDetails.map(e=>e.featureValue).join(", ")})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.price})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.stock})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.ratios.pvRatio})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.ratios.cvRatio})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.ratios.spRatio})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,r.jsx)("button",{type:"button",onClick:()=>eg(e),className:"text-red-600 hover:text-red-900 mr-3",children:"Detay"}),(0,r.jsx)("button",{type:"button",onClick:()=>Y("number"==typeof e.id?e.id:0),className:"text-gray-500 hover:text-gray-800",children:"Sil"})]})]},e.id))})]})})]})]}),(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(p.A,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"\xdcr\xfcn Fotoğrafları"})]}),(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(c.A,{className:"h-5 w-5 text-blue-400"})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-blue-700",children:'Her bir varyantın fotoğraflarını, varyant tablosundaki "Detay" butonuna tıklayarak a\xe7ılan pencereden y\xf6netebilirsiniz.'})})]})}),Q.some(e=>e.images.length>0)&&(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"T\xfcm Varyant G\xf6rselleri (\xd6nizleme)"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:Q.flatMap(e=>e.images.map(a=>({...a,variantName:e.name}))).map((e,a)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg overflow-hidden",children:(0,r.jsx)("img",{src:e.url,alt:"".concat(e.variantName," g\xf6rseli"),className:"w-full h-full object-cover"})}),(0,r.jsx)("div",{className:"absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded",children:e.variantName}),e.isMain&&(0,r.jsx)("div",{className:"absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded",children:"Ana"})]},a))})]})]}),(0,r.jsxs)(l.P.div,{className:"flex justify-end space-x-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,r.jsx)(f(),{href:"/admin/products",className:"px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"İptal"}),(0,r.jsx)("button",{type:"submit",disabled:em,className:"px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:em?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"G\xfcncelleniyor..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"\xdcr\xfcn\xfc G\xfcncelle"]})})]})]})]}),(0,r.jsx)(b.A,{isOpen:ei,onClose:ea,onSelect:e=>{_({brandId:e.brandId,categoryId:e.categoryId,subCategoryId:e.subCategoryId,brandName:e.brandName,categoryName:e.categoryName,subCategoryName:e.subCategoryName,selectedFeatures:e.selectedFeatures,selectedFeatureDetails:e.selectedFeatureDetails}),Z(e.generatedVariants),ea(),W(null)},initialData:(null==es?void 0:es.initialData)&&"object"==typeof es.initialData&&"selectedFeatures"in es.initialData?es.initialData:{brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{}}}),(0,r.jsx)(v.A,{isOpen:en,onClose:er,onSave:e=>{X(e,"number"==typeof e.id?e.id:void 0),er()},editingVariant:null==el?void 0:el.editingVariant,availableFeatures:T,existingVariants:Q})]}):null},C=e=>{let{params:a}=e;return(0,r.jsx)(i.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(k,{params:a})})}},37348:(e,a,t)=>{"use strict";t.d(a,{K_:()=>d});var r=t(65453),i=t(46786);let s={formData:{name:"",description:"",brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{},price:0,stock:0,extraDiscount:0,ratios:{pvRatio:0,cvRatio:0,spRatio:0},points:{pv:0,cv:0,sp:0},hasVariants:!1,variants:[],images:[],isActive:!0},selectedNames:{brandName:"",categoryName:"",subCategoryName:""},selectedFeatureDetails:[],variants:[],availableFeatures:[],error:null,originalProductId:null},n=(e,a)=>({pv:Math.round(e*(a.pvRatio/100)),cv:Math.round(e*(a.cvRatio/100)),sp:Math.round(e*(a.spRatio/100))}),l=e=>{let a={},t=[];return e.forEach(e=>{e.featureDetails.forEach(e=>{a[e.featureDefinitionId]||(a[e.featureDefinitionId]=[]),a[e.featureDefinitionId].includes(e.featureValueId)||a[e.featureDefinitionId].push(e.featureValueId),t.some(a=>a.featureName===e.featureName&&a.featureValue===e.featureValue)||t.push({featureName:e.featureName,featureValue:e.featureValue})})}),{newSelectedFeatures:a,newSelectedFeatureDetails:t}},d=(0,r.v)()((0,i.lt)((e,a)=>({...s,setFormData:a=>e(e=>({formData:{...e.formData,...a}})),handleInputChange:(t,r)=>{let i={...a().formData,[t]:r};if(("price"===t||"ratios"===t)&&!i.hasVariants){let e=n(i.price,i.ratios);i={...i,points:e}}e({formData:i})},handleRatioChange:(t,r)=>{let i=a().formData,s={...i.ratios,[t]:r},l={...i,ratios:s};if(!l.hasVariants){let e=n(l.price,s);l={...l,points:e}}e({formData:l})},setCategorySelection:t=>{e({formData:{...a().formData,brandId:t.brandId,categoryId:t.categoryId,subCategoryId:t.subCategoryId,selectedFeatures:t.selectedFeatures,hasVariants:!1},selectedNames:{brandName:t.brandName,categoryName:t.categoryName,subCategoryName:t.subCategoryName},selectedFeatureDetails:t.selectedFeatureDetails,error:null})},clearAllSelections:()=>{e({formData:{...a().formData,brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{},hasVariants:!1},selectedNames:{brandName:"",categoryName:"",subCategoryName:""},selectedFeatureDetails:[],availableFeatures:[],variants:[],error:null})},setAvailableFeatures:a=>e({availableFeatures:a}),setVariants:a=>e({variants:a}),saveVariant:(t,r)=>{let i,{variants:s}=a(),{newSelectedFeatures:n,newSelectedFeatureDetails:d}=l(i=r?s.map(e=>e.id===r?t:e):[...s,{...t,id:Date.now()}]);e(e=>({variants:i,formData:{...e.formData,selectedFeatures:n,hasVariants:i.length>1},selectedFeatureDetails:d}))},deleteVariant:t=>{let{variants:r}=a(),i=r.filter(e=>e.id!==t),{newSelectedFeatures:s,newSelectedFeatureDetails:n}=l(i);e(e=>({variants:i,formData:{...e.formData,selectedFeatures:s,hasVariants:i.length>1},selectedFeatureDetails:n}))},generateVariants:a=>{let{newSelectedFeatures:t,newSelectedFeatureDetails:r}=l(a);e(e=>({formData:{...e.formData,hasVariants:!0,selectedFeatures:t},selectedFeatureDetails:r,variants:a,error:null}))},handleImageUpload:t=>{let{formData:r}=a(),i=Array.from(t).map((e,a)=>({url:URL.createObjectURL(e),isMain:0===r.images.length&&0===a,sortOrder:r.images.length+a,file:e}));e({formData:{...r,images:[...r.images,...i]}})},removeImage:t=>{let{formData:r}=a(),i=r.images.filter((e,a)=>a!==t);r.images[t].isMain&&i.length>0&&(i[0].isMain=!0),e({formData:{...r,images:i}})},setMainImage:t=>{let{formData:r}=a(),i=r.images.map((e,a)=>({...e,isMain:a===t}));e({formData:{...r,images:i}})},setError:a=>e({error:a}),reset:()=>e({...s}),initializeWithProduct:t=>{var r,i,s,l,d,o,c;console.log("\uD83D\uDD04 Initializing product data:",t),console.log("\uD83D\uDDBC️ Variant images from API:",null==(r=t.variants)?void 0:r.map(e=>{var a;return{variantId:e.id,hasImages:!!e.images,imagesCount:(null==(a=e.images)?void 0:a.length)||0,images:e.images}}));let u=(null==(i=t.variants)?void 0:i.map((e,a)=>{var t,r;let i=(null==(t=e.features)?void 0:t.map(e=>({featureDefinitionId:e.featureDefinitionId,featureValueId:e.featureValueId,featureName:e.featureName||"",featureValue:e.featureValue||""})))||[],s={};i.forEach(e=>{s[e.featureDefinitionId]||(s[e.featureDefinitionId]=[]),s[e.featureDefinitionId].includes(e.featureValueId)||s[e.featureDefinitionId].push(e.featureValueId)});let l=i.length>0?i.map(e=>e.featureValue).join(" - "):"Varyant ".concat(a+1),d=e.price||0,o={pvRatio:e.pv||0,cvRatio:e.cv||0,spRatio:e.sp||0};return{id:e.id||Date.now()+a,name:l,pricing:{price:d,stock:e.stock||0,extraDiscount:e.extraDiscount||0,ratios:o,points:n(d,o)},selectedFeatures:s,features:i,featureDetails:i,images:(null==(r=e.images)?void 0:r.map(e=>({id:e.id,url:e.url,isMain:e.isMain,sortOrder:e.sortOrder,file:null})))||[],isActive:void 0===e.isActive||e.isActive}}))||[],m=[],g={};u.forEach(e=>{var a;null==(a=e.featureDetails)||a.forEach(e=>{g[e.featureDefinitionId]||(g[e.featureDefinitionId]=[]),g[e.featureDefinitionId].includes(e.featureValueId)||g[e.featureDefinitionId].push(e.featureValueId),m.some(a=>a.featureName===e.featureName&&a.featureValue===e.featureValue)||m.push({featureName:e.featureName,featureValue:e.featureValue})})}),console.log("\uD83D\uDD04 Converted variants:",u),console.log("\uD83D\uDD04 All feature details:",m),console.log("\uD83D\uDD04 Global selected features:",g),e({formData:{name:t.name||"",description:t.description||"",brandId:t.brandId||1,categoryId:t.categoryId||1,subCategoryId:t.subCategoryId||1,selectedFeatures:g,price:(null==(s=u[0])?void 0:s.pricing.price)||0,stock:(null==(l=u[0])?void 0:l.pricing.stock)||0,extraDiscount:(null==(d=u[0])?void 0:d.pricing.extraDiscount)||0,ratios:(null==(o=u[0])?void 0:o.pricing.ratios)||{pvRatio:0,cvRatio:0,spRatio:0},points:(null==(c=u[0])?void 0:c.pricing.points)||{pv:0,cv:0,sp:0},hasVariants:u.length>0,variants:[],images:[],isActive:void 0===t.isActive||t.isActive},selectedNames:{brandName:t.brandName||"",categoryName:t.categoryName||"",subCategoryName:t.subCategoryName||""},selectedFeatureDetails:m,variants:u,error:null}),console.log("✅ Product initialized successfully"),console.log("\uD83D\uDCCA Form data:",a().formData),console.log("\uD83C\uDFF7️ Selected names:",a().selectedNames),console.log("\uD83C\uDFAF Variants:",a().variants),console.log("\uD83D\uDD0D Has variants:",a().formData.hasVariants),console.log("\uD83D\uDD0D Variants length:",a().variants.length)},setOriginalProductId:a=>e({originalProductId:a})}),{name:"edit-product-store",enabled:!1}))},52948:(e,a,t)=>{Promise.resolve().then(t.bind(t,32208))}},e=>{var a=a=>e(e.s=a);e.O(0,[6408,6874,7323,1531,6681,3651,8441,1684,7358],()=>a(52948)),_N_E=e.O()}]);