(()=>{var e={};e.id=817,e.ids=[817],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6943:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},10331:(e,a,r)=>{Promise.resolve().then(r.bind(r,26875))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},14952:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17313:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20451:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>g,tree:()=>o});var t=r(65239),i=r(48088),l=r(88170),s=r.n(l),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(a,d);let o={children:["",{children:["admin",{children:["category-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,26875)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\category-management\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\category-management\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},g=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/category-management/page",pathname:"/admin/category-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},21820:e=>{"use strict";e.exports=require("os")},26875:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\admin\\\\category-management\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\category-management\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32622:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>B});var t=r(60687),i=r(43210),l=r(15908),s=r(16189),n=r(88920),d=r(26001),o=r(5336),c=r(96882),u=r(43649),g=r(35071),m=r(11860),x=r(75068);let b=()=>{let e=(0,x.Zm)(),a=(0,x.OG)(),{closeSuccessNotificationModal:r}=(0,x.QR)(),[l,s]=(0,i.useState)(0);(0,i.useEffect)(()=>{if(e&&a?.autoClose){s(0);let e=a.duration||5e3,r=setInterval(()=>{s(a=>a>=99?(clearInterval(r),100):a+100/(e/50))},50);return()=>{clearInterval(r)}}},[e,a]),(0,i.useEffect)(()=>{if(l>=100&&e&&a?.autoClose){let e=setTimeout(()=>{r()},100);return()=>clearTimeout(e)}},[l,e,a,r]);let b=(()=>{if(!a)return o.A;switch(a.icon){case"success":default:return o.A;case"info":return c.A;case"warning":return u.A;case"error":return g.A}})();return(0,t.jsx)(n.N,{mode:"wait",children:e&&a&&(0,t.jsxs)(d.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:r,children:[(0,t.jsx)("div",{className:"absolute inset-0"}),(0,t.jsxs)(d.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:25,duration:.5}},exit:{scale:.7,opacity:0,y:50,transition:{type:"spring",stiffness:300,damping:25,duration:.5}},onClick:e=>e.stopPropagation(),children:[(0,t.jsx)(d.P.button,{whileHover:{scale:1.1},whileTap:{scale:.95},onClick:r,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors",children:(0,t.jsx)(m.A,{className:"w-6 h-6"})}),(0,t.jsx)("div",{className:"flex justify-center mb-6",children:(0,t.jsx)(d.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1,duration:.3,type:"spring",stiffness:300},className:`w-20 h-20 ${(()=>{if(!a)return"bg-green-100";switch(a.icon){case"success":default:return"bg-green-100";case"info":return"bg-blue-100";case"warning":return"bg-yellow-100";case"error":return"bg-red-100"}})()} rounded-full flex items-center justify-center`,children:(0,t.jsx)(d.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,duration:.5,ease:"easeInOut"},children:(0,t.jsx)(b,{className:`w-12 h-12 ${(()=>{if(!a)return"text-green-500";switch(a.icon){case"success":default:return"text-green-500";case"info":return"text-blue-500";case"warning":return"text-yellow-500";case"error":return"text-red-500"}})()}`})})})}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(d.P.h2,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.15,duration:.3},className:"text-2xl font-bold text-gray-800 mb-4",children:a.title}),(0,t.jsx)(d.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2,duration:.3},className:"text-gray-600 mb-6 leading-relaxed",children:a.message}),(0,t.jsx)(d.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.25,duration:.3},className:"flex flex-col gap-3",children:(0,t.jsx)(d.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:r,className:`w-full bg-gradient-to-r ${(()=>{if(!a)return"from-green-600 to-emerald-600";switch(a.icon){case"success":default:return"from-green-600 to-emerald-600";case"info":return"from-blue-600 to-indigo-600";case"warning":return"from-yellow-600 to-orange-600";case"error":return"from-red-600 to-pink-600"}})()} text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transition-all duration-300`,children:"Tamam"})})]}),a.autoClose&&(0,t.jsx)(d.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.35,duration:.3},className:"mt-6",children:(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 overflow-hidden",children:(0,t.jsx)(d.P.div,{className:`h-full bg-gradient-to-r ${(()=>{if(!a)return"from-green-600 to-emerald-600";switch(a.icon){case"success":default:return"from-green-600 to-emerald-600";case"info":return"from-blue-600 to-indigo-600";case"warning":return"from-yellow-600 to-orange-600";case"error":return"from-red-600 to-pink-600"}})()} rounded-full`,initial:{width:"0%"},animate:{width:`${l}%`},transition:{duration:.1,ease:"linear"}})})}),a.autoClose&&(0,t.jsx)(d.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4,duration:.3},className:"mt-3 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[Math.ceil((100-l)/20)," saniye sonra otomatik olarak kapanacak."]})})]})]})})};var y=r(51423),h=r(8693),p=r(54050),f=r(64298),k=r(37349);let j=()=>(0,y.I)({queryKey:["brands"],queryFn:async()=>(await f.Ay.get(k.S.GET_BRANDS)).data,staleTime:3e4}),v=()=>(0,y.I)({queryKey:["categories"],queryFn:async()=>(await f.Ay.get(k.S.GET_CATEGORIES)).data,staleTime:3e4}),N=e=>(0,y.I)({queryKey:["subCategories",e],queryFn:async()=>{let a=k.S.GET_SUBCATEGORIES_BY_CATEGORY.replace("{categoryId}",e.toString());return(await f.Ay.get(a)).data},enabled:!!e,staleTime:3e4}),w=e=>(0,y.I)({queryKey:["featureValues",e],queryFn:async()=>{let a=k.S.GET_FEATURE_VALUES_BY_DEFINITION_ID.replace("{definitionId}",e.toString());return(await f.Ay.get(a)).data},enabled:!!e,staleTime:3e4}),C=e=>(0,y.I)({queryKey:["categoriesByBrand",e],queryFn:async()=>{if(!e)return[];let a=k.S.GET_CATEGORIES_BY_BRAND_ID.replace("{brandId}",e.toString());return(await f.Ay.get(a)).data},enabled:!!e,staleTime:3e4}),A=e=>(0,y.I)({queryKey:["subCategoryFeatures",e],queryFn:async()=>{let a=k.S.GET_SUBCATEGORY_FEATURES_BY_ID.replace("{subCategoryId}",e.toString()),r=await f.Ay.get(a);return console.log(`SubCategory ${e} features:`,r.data),r.data},enabled:!!e,staleTime:3e4}),I=()=>(0,y.I)({queryKey:["allFeatureDefinitions"],queryFn:async()=>(await f.Ay.get(k.S.GET_ALL_FEATURE_DEFINITIONS)).data,staleTime:3e4}),z=()=>{let e=(0,h.jE)();return(0,p.n)({mutationFn:async e=>(await f.Ay.post(k.S.CREATE_BRAND,e)).data,onSuccess:()=>{e.invalidateQueries({queryKey:["brands"]})},onError:e=>{console.error("Marka oluşturma hatası:",e)}})},S=()=>{let e=(0,h.jE)();return(0,p.n)({mutationFn:async e=>(await f.Ay.post(k.S.CREATE_CATEGORY,e)).data,onSuccess:()=>{e.invalidateQueries({queryKey:["categories"]})},onError:e=>{console.error("Kategori oluşturma hatası:",e)}})},E=()=>{let e=(0,h.jE)();return(0,p.n)({mutationFn:async e=>(await f.Ay.post(k.S.CREATE_SUBCATEGORY,e)).data,onSuccess:(a,r)=>{e.invalidateQueries({queryKey:["subCategories"]}),e.invalidateQueries({queryKey:["subCategories",r.categoryId]})},onError:e=>{console.error("Alt kategori oluşturma hatası:",e)}})},D=()=>{let e=(0,h.jE)();return(0,p.n)({mutationFn:async e=>(await f.Ay.post(k.S.CREATE_FEATURE_DEFINITION,e)).data,onSuccess:()=>{e.invalidateQueries({queryKey:["allFeatureDefinitions"]})},onError:e=>{console.error("\xd6zellik tanımı oluşturma hatası:",e)}})},K=()=>{let e=(0,h.jE)();return(0,p.n)({mutationFn:async e=>(await f.Ay.post(k.S.CREATE_FEATURE_VALUE,e)).data,onSuccess:(a,r)=>{e.invalidateQueries({queryKey:["featureValues"]}),e.invalidateQueries({queryKey:["featureValues",r.featureDefinitionId]})},onError:e=>{console.error("\xd6zellik değeri oluşturma hatası:",e)}})},M=()=>{let e=(0,h.jE)();return(0,p.n)({mutationFn:async e=>(await f.Ay.post(k.S.CREATE_SUBCATEGORY_FEATURE,e)).data,onSuccess:(a,r)=>{e.invalidateQueries({queryKey:["subCategoryFeatures"]}),e.invalidateQueries({queryKey:["subCategoryFeatures",r.subCategoryId]})},onError:e=>{console.error("Alt kategori-\xf6zellik ilişkisi oluşturma hatası:",e)}})},q=()=>{let e=(0,h.jE)();return(0,p.n)({mutationFn:async e=>(await f.Ay.post(k.S.CREATE_BRAND_CATEGORY,e)).data,onSuccess:(a,r)=>{e.invalidateQueries({queryKey:["categoriesByBrand"]}),e.invalidateQueries({queryKey:["categoriesByBrand",r.brandId]})},onError:e=>{console.error("Marka-kategori ilişkisi oluşturma hatası:",e)}})};var T=r(17313),F=r(37360),_=r(6943),R=r(84027),O=r(63143);let L=(0,r(62688).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);var U=r(14952);let B=()=>{let{user:e,isLoading:a}=(0,l.A)(),r=(0,s.useRouter)(),{openSuccessNotificationModal:n}=(0,x.QR)(),[o,c]=(0,i.useState)("brands"),[u,g]=(0,i.useState)(""),[m,y]=(0,i.useState)(null),[h,p]=(0,i.useState)(null),[f,k]=(0,i.useState)(null),[B,G]=(0,i.useState)(null),[P,Y]=(0,i.useState)(null),[V,$]=(0,i.useState)(null),[Q,H]=(0,i.useState)(null),[Z,W]=(0,i.useState)(null),[X,J]=(0,i.useState)(null),[ee,ea]=(0,i.useState)(null),[er,et]=(0,i.useState)(null),[ei,el]=(0,i.useState)(null),{data:es=[],isLoading:en}=j(),{data:ed=[],isLoading:eo}=v(),{data:ec=[],isLoading:eu}=N(m??void 0),{data:eg=[],isLoading:em}=N(f??void 0),{data:ex=[],isLoading:eb}=N(P??void 0),{data:ey=[],isLoading:eh}=N(Q??void 0),{data:ep=[],isLoading:ef}=N(X??void 0),{data:ek=[],isLoading:ej}=N(er??void 0),{data:ev=[],isLoading:eN}=A(V??void 0),{data:ew=[],isLoading:eC}=A(Z??void 0),{data:eA=[],isLoading:eI}=A(ei??void 0),{data:ez=[],isLoading:eS}=I(),{data:eE=[],isLoading:eD}=w(h??void 0),[eK,eM]=(0,i.useState)(null),{data:eq=[],isLoading:eT}=C(eK||void 0),{data:eF=[],isLoading:e_}=A(B??void 0),eR=z(),eO=S(),eL=E(),eU=D(),eB=K(),eG=q(),eP=M(),[eY,eV]=(0,i.useState)(!1),[e$,eQ]=(0,i.useState)({brand:{name:"",logoUrl:""},category:{name:""},subCategory:{name:"",categoryId:""},featureDefinition:{name:"",description:"",isRequired:!1,isMultiSelect:!1},featureValue:{featureDefinitionId:"",value:""},brandCategory:{brandId:"",categoryId:""},subCategoryFeature:{subCategoryId:"",featureDefinitionId:""}}),{data:eH=[]}=C(e$.brandCategory.brandId?parseInt(e$.brandCategory.brandId):void 0);if((0,i.useEffect)(()=>{a||e&&"admin"===e.role||r.push("/login")},[e,a,r]),a)return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})});if(!e||"admin"!==e.role)return null;let eZ=async e=>{eV(!0);try{switch(e){case"featureDefinition":if(!e$.featureDefinition.name||!ee){n({title:"Eksik Bilgi",message:"L\xfctfen \xf6zellik adını girin ve bir alt kategori se\xe7in.",icon:"warning",autoClose:!0,duration:5e3}),eV(!1);return}let a=await eU.mutateAsync({name:e$.featureDefinition.name,description:e$.featureDefinition.description,isRequired:e$.featureDefinition.isRequired,isMultiSelect:e$.featureDefinition.isMultiSelect});if(a&&a.data){await eP.mutateAsync({subCategoryId:ee,featureDefinitionId:Number(a.data)});let e=ep.find(e=>e.id===ee)?.name||"";n({title:"\xd6zellik Tanımı Oluşturuldu!",message:`'${e$.featureDefinition.name}' \xf6zelliği oluşturuldu ve '${e}' alt kategorisine bağlandı.`,icon:"success",autoClose:!0,duration:5e3}),eQ(e=>({...e,featureDefinition:{name:"",description:"",isRequired:!1,isMultiSelect:!1}}))}else throw Error("\xd6zellik oluşturuldu ancak ID alınamadı.");break;case"subCategoryFeature":await eP.mutateAsync({subCategoryId:parseInt(e$.subCategoryFeature.subCategoryId),featureDefinitionId:parseInt(e$.subCategoryFeature.featureDefinitionId)}),eQ(e=>({...e,subCategoryFeature:{subCategoryId:"",featureDefinitionId:""}})),n({title:"Kategori-\xd6zellik İlişkisi Oluşturuldu!",message:"Alt kategori ve \xf6zellik arasında ilişki başarıyla kuruldu.",icon:"success",autoClose:!0,duration:5e3});break;case"featureValue":await eB.mutateAsync({featureDefinitionId:parseInt(e$.featureValue.featureDefinitionId),value:e$.featureValue.value}),eQ(e=>({...e,featureValue:{featureDefinitionId:"",value:""}})),n({title:"\xd6zellik Değeri Oluşturuldu!",message:`"${e$.featureValue.value}" değeri başarıyla eklendi.`,icon:"success",autoClose:!0,duration:5e3});break;case"brand":await eR.mutateAsync(e$.brand),eQ(e=>({...e,brand:{name:"",logoUrl:""}})),n({title:"Marka Oluşturuldu!",message:`"${e$.brand.name}" markası başarıyla oluşturuldu.`,icon:"success",autoClose:!0,duration:5e3});break;case"category":await eO.mutateAsync(e$.category),eQ(e=>({...e,category:{name:""}})),n({title:"Kategori Oluşturuldu!",message:`"${e$.category.name}" kategorisi başarıyla oluşturuldu.`,icon:"success",autoClose:!0,duration:5e3});break;case"subCategory":await eL.mutateAsync({name:e$.subCategory.name,categoryId:parseInt(e$.subCategory.categoryId)}),eQ(e=>({...e,subCategory:{name:"",categoryId:""}})),n({title:"Alt Kategori Oluşturuldu!",message:`"${e$.subCategory.name}" alt kategorisi başarıyla oluşturuldu.`,icon:"success",autoClose:!0,duration:5e3});break;case"brandCategory":await eG.mutateAsync({brandId:parseInt(e$.brandCategory.brandId),categoryId:parseInt(e$.brandCategory.categoryId)}),eQ(e=>({...e,brandCategory:{brandId:"",categoryId:""}})),n({title:"Marka-Kategori İlişkisi Oluşturuldu!",message:"Marka ve kategori arasında ilişki başarıyla kuruldu.",icon:"success",autoClose:!0,duration:5e3});break;default:console.log(`Unhandled form type: ${e}`)}}catch(e){console.error("Form g\xf6nderilirken hata:",e),n({title:"İşlem Hatası",message:"İşlem sırasında bir hata oluştu. L\xfctfen tekrar deneyin.",icon:"error",autoClose:!0,duration:5e3})}finally{eV(!1)}},eW=[{id:"brands",label:"Markalar",icon:T.A},{id:"categories",label:"Kategoriler",icon:F.A},{id:"subCategories",label:"Alt Kategoriler",icon:_.A},{id:"features",label:"\xd6zellikler",icon:R.A},{id:"featureValues",label:"\xd6zellik Değerleri",icon:O.A},{id:"brandCategories",label:"Marka-Kategori İlişkileri",icon:L},{id:"subCategoryFeatures",label:"Kategori-\xd6zellik İlişkileri",icon:U.A}],eX=()=>(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(T.A,{className:"w-5 h-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-blue-900 mb-2",children:"Marka Y\xf6netimi Talimatları"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,t.jsx)("li",{children:"• Sistem genelinde kullanılacak markaları buradan ekleyebilirsiniz"}),(0,t.jsx)("li",{children:"• Marka adı zorunlu alan olup, logo URL'si opsiyoneldir"}),(0,t.jsx)("li",{children:"• Eklenen markalar daha sonra marka-kategori ilişkileri tabında kategorilerle eşleştirilebilir"}),(0,t.jsx)("li",{children:"• Marka logosu i\xe7in ge\xe7erli bir URL adresi kullanın (\xf6rn: https://example.com/logo.png)"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Yeni Marka Ekle"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Marka Adı"}),(0,t.jsx)("input",{type:"text",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black",value:e$.brand.name,onChange:e=>eQ(a=>({...a,brand:{...a.brand,name:e.target.value}})),placeholder:"Marka adını girin"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Logo URL (Opsiyonel)"}),(0,t.jsx)("input",{type:"url",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black",value:e$.brand.logoUrl,onChange:e=>eQ(a=>({...a,brand:{...a.brand,logoUrl:e.target.value}})),placeholder:"Logo URL'sini girin"})]})]}),(0,t.jsx)("button",{onClick:()=>eZ("brand"),disabled:!e$.brand.name||eY,className:"mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition",children:eY?"Oluşturuluyor...":"Marka Oluştur"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Mevcut Markalar"}),(0,t.jsx)("div",{className:"space-y-3",children:es.length>0?es.map(e=>(0,t.jsx)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[e.logoUrl?(0,t.jsx)("img",{src:e.logoUrl,alt:e.name,className:"w-12 h-12 object-contain rounded-md bg-white p-1 border"}):(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center",children:(0,t.jsx)(T.A,{className:"w-6 h-6 text-gray-400"})}),(0,t.jsx)("span",{className:"font-semibold text-gray-800",children:e.name})]})},e.id)):(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Hen\xfcz marka eklenmemiş."})})]})]}),eJ=()=>(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(F.A,{className:"w-5 h-5 text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-green-900 mb-2",children:"Kategori Y\xf6netimi Talimatları"}),(0,t.jsxs)("ul",{className:"text-sm text-green-800 space-y-1",children:[(0,t.jsx)("li",{children:"• Ana kategorileri buradan oluşturabilirsiniz (\xf6rn: Elektronik, Giyim, Ev & Yaşam)"}),(0,t.jsx)("li",{children:"• Kategori adı zorunlu alan olup, benzersiz olmalıdır"}),(0,t.jsx)("li",{children:"• Oluşturulan kategoriler alt kategoriler tabında ana kategori olarak se\xe7ilebilir"}),(0,t.jsx)("li",{children:"• Kategoriler marka-kategori ilişkileri tabında markalarla eşleştirilebilir"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Yeni Kategori Ekle"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategori Adı"}),(0,t.jsx)("input",{type:"text",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black",value:e$.category.name,onChange:e=>eQ(a=>({...a,category:{...a.category,name:e.target.value}})),placeholder:"Kategori adını girin"})]})}),(0,t.jsx)("button",{onClick:()=>eZ("category"),disabled:!e$.category.name||eY,className:"mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition",children:eY?"Oluşturuluyor...":"Kategori Oluştur"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Mevcut Kategoriler"}),(0,t.jsx)("div",{className:"space-y-3",children:ed.length>0?ed.map(e=>(0,t.jsx)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,t.jsx)("span",{className:"font-semibold text-gray-800",children:e.name})},e.id)):(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Hen\xfcz kategori eklenmemiş."})})]})]}),e0=()=>(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-purple-50 border border-purple-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(_.A,{className:"w-5 h-5 text-purple-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-purple-900 mb-2",children:"Alt Kategori Y\xf6netimi Talimatları"}),(0,t.jsxs)("ul",{className:"text-sm text-purple-800 space-y-1",children:[(0,t.jsx)("li",{children:"• Alt kategoriler bir ana kategoriye bağlı olarak oluşturulur (\xf6rn: Elektronik > Telefon)"}),(0,t.jsx)("li",{children:"• Alt kategori adı ve ana kategori se\xe7imi zorunlu alanlardır"}),(0,t.jsx)("li",{children:"• Alt kategoriler \xfcr\xfcn \xf6zelliklerini tanımlamak i\xe7in kategori-\xf6zellik ilişkileri tabında kullanılır"}),(0,t.jsx)("li",{children:"• Listeleme sırasında filtrelemek i\xe7in ana kategori se\xe7ebilirsiniz"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Yeni Alt Kategori Ekle"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alt Kategori Adı"}),(0,t.jsx)("input",{type:"text",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black",value:e$.subCategory.name,onChange:e=>eQ(a=>({...a,subCategory:{...a.subCategory,name:e.target.value}})),placeholder:"Alt kategori adını girin"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ana Kategori"}),(0,t.jsxs)("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",value:e$.subCategory.categoryId,onChange:e=>eQ(a=>({...a,subCategory:{...a.subCategory,categoryId:e.target.value}})),children:[(0,t.jsx)("option",{value:"",children:"Kategori se\xe7in"}),ed.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]}),(0,t.jsx)("button",{onClick:()=>eZ("subCategory"),disabled:!e$.subCategory.name||!e$.subCategory.categoryId||eY,className:"mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition",children:eY?"Oluşturuluyor...":"Alt Kategori Oluştur"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Mevcut Alt Kategoriler"}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategoriye G\xf6re Filtrele"}),(0,t.jsxs)("select",{onChange:e=>y(e.target.value?Number(e.target.value):null),value:m??"",className:"w-full max-w-xs px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",children:[(0,t.jsx)("option",{value:"",children:"Kategori Se\xe7in"}),ed.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),eu&&(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Alt kategoriler y\xfckleniyor..."}),!eu&&m&&(0,t.jsx)("div",{className:"space-y-3",children:ec.length>0?ec.map(e=>(0,t.jsx)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"font-semibold text-gray-800",children:e.name}),(0,t.jsx)(U.A,{className:"h-5 w-5 text-gray-400"}),(0,t.jsx)("span",{className:"text-md text-gray-600",children:ed.find(a=>a.id===e.categoryId)?.name})]})},e.id)):(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Bu kategoriye ait alt kategori bulunamadı."})}),!m&&!eu&&(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Alt kategorileri g\xf6r\xfcnt\xfclemek i\xe7in bir kategori se\xe7in."})]})]}),e2=()=>(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(R.A,{className:"w-5 h-5 text-orange-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-orange-900 mb-2",children:"\xd6zellik Tanımları Y\xf6netimi Talimatları"}),(0,t.jsxs)("ul",{className:"text-sm text-orange-800 space-y-1",children:[(0,t.jsx)("li",{children:"• \xd6zellik tanımları \xfcr\xfcn \xf6zelliklerini kategorize etmek i\xe7in kullanılır (\xf6rn: Renk, Beden, Marka)"}),(0,t.jsx)("li",{children:"• \xd6zellik oluşturulurken mutlaka bir alt kategori se\xe7melisiniz"}),(0,t.jsx)("li",{children:'• "Zorunlu \xd6zellik" işaretlenirse, bu \xf6zellik o kategorideki t\xfcm \xfcr\xfcnlerde bulunmak zorundadır'}),(0,t.jsx)("li",{children:'• "\xc7oklu Se\xe7im" aktifse, bu \xf6zellik i\xe7in birden fazla değer se\xe7ilebilir'}),(0,t.jsx)("li",{children:"• \xd6zellik tanımları oluşturulduktan sonra \xf6zellik değerleri tabında değerler eklenebilir"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Yeni \xd6zellik Tanımı Ekle"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Eklenecek Kategori"}),(0,t.jsxs)("select",{value:X||"",onChange:e=>{J(e.target.value?Number(e.target.value):null),ea(null)},className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",children:[(0,t.jsx)("option",{value:"",children:"\xd6nce Kategori Se\xe7in"}),ed.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Eklenecek Alt Kategori"}),(0,t.jsxs)("select",{value:ee||"",onChange:e=>ea(e.target.value?Number(e.target.value):null),disabled:!X||ef,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",children:[(0,t.jsx)("option",{value:"",children:"Alt Kategori Se\xe7in"}),ep.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xd6zellik Adı"}),(0,t.jsx)("input",{type:"text",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black",value:e$.featureDefinition.name,onChange:e=>eQ(a=>({...a,featureDefinition:{...a.featureDefinition,name:e.target.value}})),placeholder:"\xd6rn: Renk, Beden"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"A\xe7ıklama"}),(0,t.jsx)("input",{type:"text",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black",value:e$.featureDefinition.description,onChange:e=>eQ(a=>({...a,featureDefinition:{...a.featureDefinition,description:e.target.value}})),placeholder:"\xd6zellik a\xe7ıklaması"})]})]}),(0,t.jsxs)("div",{className:"mt-6 flex items-center space-x-8",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500",checked:e$.featureDefinition.isRequired,onChange:e=>eQ(a=>({...a,featureDefinition:{...a.featureDefinition,isRequired:e.target.checked}}))}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Zorunlu \xd6zellik"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500",checked:e$.featureDefinition.isMultiSelect,onChange:e=>eQ(a=>({...a,featureDefinition:{...a.featureDefinition,isMultiSelect:e.target.checked}}))}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"\xc7oklu Se\xe7im"})]})]}),(0,t.jsx)("button",{onClick:()=>eZ("featureDefinition"),disabled:!e$.featureDefinition.name||!ee||eY,className:"mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition",children:eY?"Oluşturuluyor...":"\xd6zellik Tanımı Oluştur"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Mevcut \xd6zellik Tanımları"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategoriye G\xf6re Filtrele"}),(0,t.jsxs)("select",{value:P||"",onChange:e=>{Y(e.target.value?Number(e.target.value):null),$(null)},className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",children:[(0,t.jsx)("option",{value:"",children:"\xd6nce Kategori Se\xe7in"}),ed.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alt Kategoriye G\xf6re Filtrele"}),(0,t.jsxs)("select",{value:V||"",onChange:e=>$(e.target.value?Number(e.target.value):null),disabled:!P||eb,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",children:[(0,t.jsx)("option",{value:"",children:"Alt Kategori Se\xe7in"}),ex.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:eN?(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"\xd6zellikler y\xfckleniyor..."}):V&&ev.length>0?ev.map(e=>(0,t.jsx)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,t.jsx)("span",{className:"font-semibold text-gray-800",children:e.featureDefinitionName})},e.featureDefinitionId)):(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:V?"Bu alt kategoriye ait \xf6zellik bulunamadı.":"\xd6zellik tanımlarını g\xf6rmek i\xe7in bir kategori ve alt kategori se\xe7in."})})]})]}),e1=()=>(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-teal-50 border border-teal-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(O.A,{className:"w-5 h-5 text-teal-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-teal-900 mb-2",children:"\xd6zellik Değerleri Y\xf6netimi Talimatları"}),(0,t.jsxs)("ul",{className:"text-sm text-teal-800 space-y-1",children:[(0,t.jsx)("li",{children:"• \xd6zellik değerleri, \xf6zellik tanımları altında yer alan se\xe7ilebilir değerlerdir"}),(0,t.jsx)("li",{children:'• \xd6rneğin "Renk" \xf6zelliği i\xe7in "Kırmızı, Mavi, Yeşil" gibi değerler ekleyebilirsiniz'}),(0,t.jsx)("li",{children:"• Değer eklemek i\xe7in \xf6nce kategori ve alt kategori se\xe7in, sonra o alt kategorideki \xf6zellik tanımlarından birini se\xe7in"}),(0,t.jsx)("li",{children:"• Listeleme yaparken de aynı filtre sırasını takip edin: Kategori > Alt Kategori > \xd6zellik Tanımı"}),(0,t.jsx)("li",{children:"• Bu değerler \xfcr\xfcn ekleme sayfasında kullanıcıya se\xe7enek olarak sunulur"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Yeni \xd6zellik Değeri Ekle"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategori"}),(0,t.jsxs)("select",{value:P||"",onChange:e=>{Y(e.target.value?Number(e.target.value):null),$(null)},className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",children:[(0,t.jsx)("option",{value:"",children:"\xd6nce Kategori Se\xe7in"}),ed.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alt Kategori"}),(0,t.jsxs)("select",{value:V||"",onChange:e=>$(e.target.value?Number(e.target.value):null),disabled:!P||eb,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",children:[(0,t.jsx)("option",{value:"",children:"Alt Kategori Se\xe7in"}),ex.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xd6zellik Tanımı"}),(0,t.jsxs)("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",value:e$.featureValue.featureDefinitionId,onChange:e=>eQ(a=>({...a,featureValue:{...a.featureValue,featureDefinitionId:e.target.value}})),disabled:!V||eN,children:[(0,t.jsx)("option",{value:"",children:"\xd6zellik se\xe7in"}),ev.map(e=>(0,t.jsx)("option",{value:e.featureDefinitionId,children:e.featureDefinitionName},e.featureDefinitionId))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Değer"}),(0,t.jsx)("input",{type:"text",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black",value:e$.featureValue.value,onChange:e=>eQ(a=>({...a,featureValue:{...a.featureValue,value:e.target.value}})),placeholder:"\xd6rn: Kırmızı, XL"})]})]}),(0,t.jsx)("button",{onClick:()=>eZ("featureValue"),disabled:!e$.featureValue.featureDefinitionId||!e$.featureValue.value||eY,className:"mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition",children:eY?"Oluşturuluyor...":"Değer Ekle"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Mevcut \xd6zellik Değerleri"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategori"}),(0,t.jsxs)("select",{value:er||"",onChange:e=>{et(e.target.value?Number(e.target.value):null),el(null),p(null)},className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",children:[(0,t.jsx)("option",{value:"",children:"Kategori Se\xe7in"}),ed.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alt Kategori"}),(0,t.jsxs)("select",{value:ei||"",onChange:e=>{el(e.target.value?Number(e.target.value):null),p(null)},disabled:!er||ej,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",children:[(0,t.jsx)("option",{value:"",children:"Alt Kategori Se\xe7in"}),ek.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"G\xf6r\xfcnt\xfclenecek \xd6zelliği Se\xe7in"}),(0,t.jsxs)("select",{value:h||"",onChange:e=>p(e.target.value?Number(e.target.value):null),disabled:!ei||eI,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",children:[(0,t.jsx)("option",{value:"",children:"\xd6zellik Se\xe7in"}),eA.map(e=>(0,t.jsx)("option",{value:e.featureDefinitionId,children:e.featureDefinitionName},e.featureDefinitionId))]})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:eD?(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Değerler y\xfckleniyor..."}):h&&eE.length>0?eE.map(e=>(0,t.jsx)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,t.jsx)("span",{className:"font-semibold text-gray-800",children:e.value})},e.id)):(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:h?"Bu \xf6zelliğe ait değer bulunamadı.":"Değerleri g\xf6rmek i\xe7in yukarıdan bir kategori, alt kategori ve \xf6zellik tanımı se\xe7in."})})]})]}),e6=()=>{let e=e$.brandCategory.categoryId?parseInt(e$.brandCategory.categoryId):void 0,a=eH.some(a=>a.id===e);return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-indigo-50 border border-indigo-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(L,{className:"w-5 h-5 text-indigo-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-indigo-900 mb-2",children:"Marka-Kategori İlişkileri Y\xf6netimi Talimatları"}),(0,t.jsxs)("ul",{className:"text-sm text-indigo-800 space-y-1",children:[(0,t.jsx)("li",{children:"• Markalar ve kategoriler arasında ilişki kurmak i\xe7in bu b\xf6l\xfcm\xfc kullanın"}),(0,t.jsx)("li",{children:"• Bu ilişkiler, \xfcr\xfcn ekleme sırasında hangi markanın hangi kategorilerde kullanılabileceğini belirler"}),(0,t.jsx)("li",{children:"• Bir marka birden fazla kategoriyle ilişkilendirilebilir"}),(0,t.jsx)("li",{children:"• Sistem mevcut ilişkileri kontrol eder ve tekrar ilişki kurulmasını engeller"}),(0,t.jsx)("li",{children:"• Marka se\xe7erek o markaya ait kategorileri g\xf6r\xfcnt\xfcleyebilirsiniz"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Yeni Marka-Kategori İlişkisi Ekle"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Marka"}),(0,t.jsxs)("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",value:e$.brandCategory.brandId,onChange:e=>eQ(a=>({...a,brandCategory:{...a.brandCategory,brandId:e.target.value}})),children:[(0,t.jsx)("option",{value:"",children:"Marka se\xe7in"}),es.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategori"}),(0,t.jsxs)("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",value:e$.brandCategory.categoryId,onChange:e=>eQ(a=>({...a,brandCategory:{...a.brandCategory,categoryId:e.target.value}})),children:[(0,t.jsx)("option",{value:"",children:"Kategori se\xe7in"}),ed.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-6",children:[(0,t.jsx)("button",{onClick:()=>eZ("brandCategory"),disabled:!e$.brandCategory.brandId||!e$.brandCategory.categoryId||eY||a,className:"bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition",children:eY?"Oluşturuluyor...":"İlişki Oluştur"}),a&&(0,t.jsx)("p",{className:"text-sm text-yellow-600 font-medium",children:"Bu ilişki zaten mevcut."})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Marka Kategorilerini G\xf6r\xfcnt\xfcle"}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Marka Se\xe7in"}),(0,t.jsxs)("select",{value:eK||"",onChange:e=>eM(e.target.value?Number(e.target.value):null),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",children:[(0,t.jsx)("option",{value:"",children:"Marka se\xe7in"}),es.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),eT?(0,t.jsx)("div",{className:"text-center py-4 text-gray-500",children:"Y\xfckleniyor..."}):eK?(0,t.jsx)("div",{className:"space-y-3",children:eq.length>0?eq.map(e=>(0,t.jsx)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsx)("span",{className:"font-semibold text-gray-800",children:e.name})})},e.id)):(0,t.jsx)("div",{className:"text-center py-4 text-gray-500",children:"Bu markaya ait kategori bulunamadı."})}):(0,t.jsx)("div",{className:"text-center py-4 text-gray-500",children:"Kategorileri g\xf6r\xfcnt\xfclemek i\xe7in bir marka se\xe7in."})]})]})},e3=()=>{let e=e$.subCategoryFeature.featureDefinitionId?parseInt(e$.subCategoryFeature.featureDefinitionId):void 0,a=ew.some(a=>a.featureDefinitionId===e);return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-pink-50 border border-pink-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(U.A,{className:"w-5 h-5 text-pink-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-pink-900 mb-2",children:"Kategori-\xd6zellik İlişkileri Y\xf6netimi Talimatları"}),(0,t.jsxs)("ul",{className:"text-sm text-pink-800 space-y-1",children:[(0,t.jsx)("li",{children:"• Alt kategoriler ve \xf6zellik tanımları arasında ilişki kurmak i\xe7in bu b\xf6l\xfcm\xfc kullanın"}),(0,t.jsx)("li",{children:"• Bu ilişkiler, o alt kategorideki \xfcr\xfcnlerde hangi \xf6zelliklerin kullanılabileceğini belirler"}),(0,t.jsx)("li",{children:"• Bir alt kategori birden fazla \xf6zellik tanımıyla ilişkilendirilebilir"}),(0,t.jsx)("li",{children:"• Sistem mevcut ilişkileri kontrol eder ve tekrar ilişki kurulmasını engeller"}),(0,t.jsx)("li",{children:'• \xd6zellik tanımları "\xd6zellikler" tabından daha kolay bir şekilde alt kategoriye bağlı olarak oluşturulabilir'}),(0,t.jsx)("li",{children:"• Listeleme yaparken kategori ve alt kategori se\xe7erek ilişkileri g\xf6r\xfcnt\xfcleyebilirsiniz"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Yeni Kategori-\xd6zellik İlişkisi Ekle"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategori"}),(0,t.jsxs)("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",value:Q||"",onChange:e=>{H(e.target.value?Number(e.target.value):null),W(null),eQ(e=>({...e,subCategoryFeature:{subCategoryId:"",featureDefinitionId:""}}))},children:[(0,t.jsx)("option",{value:"",children:"Kategori se\xe7in"}),ed.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alt Kategori"}),(0,t.jsxs)("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",value:e$.subCategoryFeature.subCategoryId,disabled:!Q||eh,onChange:e=>{let a=e.target.value;W(a?Number(a):null),eQ(e=>({...e,subCategoryFeature:{...e.subCategoryFeature,subCategoryId:a,featureDefinitionId:""}}))},children:[(0,t.jsx)("option",{value:"",children:"Alt Kategori se\xe7in"}),ey.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xd6zellik Tanımı"}),(0,t.jsxs)("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",value:e$.subCategoryFeature.featureDefinitionId,disabled:!Z||eS,onChange:e=>eQ(a=>({...a,subCategoryFeature:{...a.subCategoryFeature,featureDefinitionId:e.target.value}})),children:[(0,t.jsx)("option",{value:"",children:"\xd6zellik se\xe7in"}),ez.map(e=>(0,t.jsx)("option",{value:e.featureDefinitionId,children:e.featureDefinitionName},e.featureDefinitionId))]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-6",children:[(0,t.jsx)("button",{onClick:()=>eZ("subCategoryFeature"),disabled:!e$.subCategoryFeature.subCategoryId||!e$.subCategoryFeature.featureDefinitionId||eY||a,className:"bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition",children:eY?"Oluşturuluyor...":"İlişki Oluştur"}),a&&(0,t.jsx)("p",{className:"text-sm text-yellow-600 font-medium",children:"Bu ilişki zaten mevcut."})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Mevcut Kategori-\xd6zellik İlişkileri"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategoriye G\xf6re Filtrele"}),(0,t.jsxs)("select",{value:f||"",onChange:e=>{k(e.target.value?Number(e.target.value):null),G(null)},className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",children:[(0,t.jsx)("option",{value:"",children:"\xd6nce Kategori Se\xe7in"}),ed.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alt Kategoriye G\xf6re Filtrele"}),(0,t.jsxs)("select",{value:B||"",onChange:e=>G(e.target.value?Number(e.target.value):null),disabled:!f||em,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",children:[(0,t.jsx)("option",{value:"",children:"Alt Kategori Se\xe7in"}),eg.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:e_?(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"İlişkiler y\xfckleniyor..."}):B&&eF.length>0?eF.map(e=>(0,t.jsx)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"font-semibold text-gray-800",children:e.subCategoryName}),(0,t.jsx)(U.A,{className:"h-5 w-5 text-gray-400"}),(0,t.jsx)("span",{className:"text-md text-gray-600",children:e.featureDefinitionName})]})},e.id)):(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:B?"Bu alt kategoriye ait \xf6zellik ilişkisi bulunamadı.":"İlişkileri g\xf6rmek i\xe7in bir kategori ve alt kategori se\xe7in."})})]})]})};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:[(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Kategori Y\xf6netimi"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Marka, kategori ve \xf6zellik tanımlarını y\xf6netin"})]}),(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsx)("nav",{className:"-mb-px flex space-x-8 overflow-x-auto",children:eW.map(e=>(0,t.jsx)("button",{onClick:()=>c(e.id),className:`whitespace-nowrap py-3 px-1 border-b-2 font-semibold text-sm transition-colors ${o===e.id?"border-red-500 text-red-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(e.icon,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:e.label})]})},e.id))})})}),(0,t.jsx)(d.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:(()=>{switch(o){case"brands":return eX();case"categories":return eJ();case"subCategories":return e0();case"features":return e2();case"featureValues":return e1();case"brandCategories":return e6();case"subCategoryFeatures":return e3();default:return null}})()},o)]}),(0,t.jsx)(b,{})]})}},33873:e=>{"use strict";e.exports=require("path")},35071:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37360:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},43649:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62688:(e,a,r)=>{"use strict";r.d(a,{A:()=>u});var t=r(43210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,a,r)=>r?r.toUpperCase():a.toLowerCase()),s=e=>{let a=l(e);return a.charAt(0).toUpperCase()+a.slice(1)},n=(...e)=>e.filter((e,a,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===a).join(" ").trim(),d=e=>{for(let a in e)if(a.startsWith("aria-")||"role"===a||"title"===a)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,t.forwardRef)(({color:e="currentColor",size:a=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:l="",children:s,iconNode:c,...u},g)=>(0,t.createElement)("svg",{ref:g,...o,width:a,height:a,stroke:e,strokeWidth:i?24*Number(r)/Number(a):r,className:n("lucide",l),...!s&&!d(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,a])=>(0,t.createElement)(e,a)),...Array.isArray(s)?s:[s]])),u=(e,a)=>{let r=(0,t.forwardRef)(({className:r,...l},d)=>(0,t.createElement)(c,{ref:d,iconNode:a,className:n(`lucide-${i(s(e))}`,`lucide-${e}`,r),...l}));return r.displayName=s(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},70440:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>i});var t=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},86771:(e,a,r)=>{Promise.resolve().then(r.bind(r,32622))},94735:e=>{"use strict";e.exports=require("events")},96882:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};var a=require("../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[447,181,658,85],()=>r(20451));module.exports=t})();