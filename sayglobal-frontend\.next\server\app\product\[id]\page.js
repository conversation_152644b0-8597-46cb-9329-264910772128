(()=>{var e={};e.id=0,e.ids=[0],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10888:(e,t,r)=>{Promise.resolve().then(r.bind(r,99501))},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},14952:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},18337:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=r(65239),s=r(48088),i=r(88170),l=r.n(i),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d={children:["",{children:["product",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85948)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\product\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\product\\[id]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/product/[id]/page",pathname:"/product/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39553:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(60687),s=r(88920),i=r(26001),l=r(85814),n=r.n(l),o=r(30474);function d({isOpen:e,onClose:t,product:r,isAdded:l}){return(0,a.jsx)(s.N,{children:e&&(0,a.jsxs)(i.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:t,children:[(0,a.jsx)("div",{className:"absolute inset-0"}),(0,a.jsxs)(i.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.7,opacity:0,y:50},transition:{type:"spring",stiffness:300,damping:25,duration:.5},onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)(i.P.div,{className:"text-center mb-6",initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},children:[(0,a.jsx)("div",{className:`mx-auto w-20 h-20 ${l?"bg-red-100":"bg-gray-100"} rounded-full flex items-center justify-center mb-4`,children:(0,a.jsx)(i.P.svg,{className:`w-10 h-10 ${l?"text-red-600":"text-gray-500"}`,fill:l?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24",initial:{scale:0},animate:{scale:1},transition:{delay:.3,type:"spring",stiffness:200},children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})}),(0,a.jsx)(i.P.h2,{className:"text-2xl font-bold text-gray-800 mb-2",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:l?"Favorilere Eklendi!":"Favorilerden \xc7ıkarıldı!"}),(0,a.jsx)(i.P.p,{className:"text-gray-600",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4},children:l?"\xdcr\xfcn favori listenize başarıyla eklendi.":"\xdcr\xfcn favori listenizden \xe7ıkarıldı."})]}),r&&(0,a.jsxs)(i.P.div,{className:`${l?"bg-red-50 border-red-200":"bg-gray-50 border-gray-200"} border rounded-lg p-4 mb-6 flex items-center space-x-3`,initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,a.jsx)("div",{className:"relative w-16 h-16 flex-shrink-0",children:(0,a.jsx)(o.default,{src:r.thumbnail,alt:r.title,fill:!0,className:"object-cover rounded-lg"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 truncate",children:r.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:r.brand}),(0,a.jsx)("p",{className:"text-sm font-medium text-purple-600",children:r.discountPercentage?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("span",{className:"line-through text-gray-400 mr-2",children:["₺",r.price.toFixed(2)]}),"₺",(r.price*(1-r.discountPercentage/100)).toFixed(2)]}):`₺${r.price.toFixed(2)}`})]})]}),(0,a.jsxs)(i.P.div,{className:"flex flex-col space-y-3",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},children:[(0,a.jsx)(n(),{href:"/account?tab=favorites",children:(0,a.jsx)(i.P.button,{className:`w-full ${l?"bg-gradient-to-r from-red-600 to-pink-600":"bg-gradient-to-r from-gray-600 to-gray-700"} text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300`,whileHover:{scale:1.02},whileTap:{scale:.98},onClick:t,children:"Favorilerime Git"})}),(0,a.jsx)(i.P.button,{className:"w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},onClick:t,children:"Alışverişe Devam Et"})]}),(0,a.jsx)(i.P.button,{onClick:t,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors",initial:{opacity:0},animate:{opacity:1},transition:{delay:.7},whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]})})}},47033:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},47336:(e,t,r)=>{Promise.resolve().then(r.bind(r,85948))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(43210);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:i="",children:l,iconNode:c,...u},x)=>(0,a.createElement)("svg",{ref:x,...d,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:n("lucide",i),...!l&&!o(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(l)?l:[l]])),u=(e,t)=>{let r=(0,a.forwardRef)(({className:r,...i},o)=>(0,a.createElement)(c,{ref:o,iconNode:t,className:n(`lucide-${s(l(e))}`,`lucide-${e}`,r),...i}));return r.displayName=l(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85948:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\product\\[id]\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},99501:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>W});var a=r(60687),s=r(43210),i=r(16189),l=r(30474),n=r(85814),o=r.n(n),d=r(26001),c=r(88920),u=r(47033),x=r(14952),m=r(28253),p=r(83820),h=r(25635),g=r(39553),f=r(11860),w=r(26787),v=r(59350);let y=null,b=null,j=null,N=null,k=[],P=null,C=[],D={productData:null,selectedVariantIndex:0,currentImageIndex:0,slideDirection:0,isLoading:!1,error:null,cache:new Map,quantity:1},I=(0,w.v)()((0,v.lt)((e,t)=>({...D,setProductData:r=>{t().productData!==r&&e({productData:r,selectedVariantIndex:0,currentImageIndex:0,slideDirection:0,error:null},!1,"catalogProductDetail/setProductData")},setLoading:t=>{e({isLoading:t},!1,"catalogProductDetail/setLoading")},setError:t=>{e({error:t,isLoading:!1},!1,"catalogProductDetail/setError")},setSelectedVariant:r=>{let a=t(),s=a.productData?.data.variants.length||0;r>=0&&r<s&&e({selectedVariantIndex:r,currentImageIndex:0},!1,"catalogProductDetail/setSelectedVariant")},setCurrentImage:r=>{let a=t(),s=a.productData?.data.variants[a.selectedVariantIndex],i=s?.images.length||0;r>=0&&r<i&&e({currentImageIndex:r},!1,"catalogProductDetail/setCurrentImage")},nextImage:()=>{let r=t(),a=r.productData?.data.variants[r.selectedVariantIndex],s=a?.images.length||0;s>0&&e({currentImageIndex:(r.currentImageIndex+1)%s,slideDirection:1},!1,"catalogProductDetail/nextImage")},prevImage:()=>{let r=t(),a=r.productData?.data.variants[r.selectedVariantIndex],s=a?.images.length||0;s>0&&e({currentImageIndex:0===r.currentImageIndex?s-1:r.currentImageIndex-1,slideDirection:-1},!1,"catalogProductDetail/prevImage")},setQuantity:t=>{t>=1&&e({quantity:t},!1,"catalogProductDetail/setQuantity")},increaseQuantity:()=>{e({quantity:t().quantity+1},!1,"catalogProductDetail/increaseQuantity")},decreaseQuantity:()=>{let r=t();r.quantity>1&&e({quantity:r.quantity-1},!1,"catalogProductDetail/decreaseQuantity")},setCachedProduct:(r,a)=>{let s=new Map(t().cache);s.set(r,{data:a,timestamp:Date.now()}),e({cache:s},!1,"catalogProductDetail/setCachedProduct")},getCachedProduct:e=>{let r=t().cache.get(e);return r?.data||null},isCacheValid:(e,r=3e5)=>{let a=t().cache.get(e);return!!a&&Date.now()-a.timestamp<r},clearCache:()=>{e({cache:new Map},!1,"catalogProductDetail/clearCache")},clearProductCache:r=>{let a=new Map(t().cache);a.delete(r),e({cache:a},!1,"catalogProductDetail/clearProductCache")},resetState:()=>{y=null,b=null,j=null,N=null,k=C,P=null,e({...D,cache:new Map},!1,"catalogProductDetail/resetState")}}),{name:"catalog-product-detail-store"})),L=()=>I(e=>e.slideDirection),S=()=>I(e=>e.currentImageIndex),M=()=>I(e=>e.quantity),A=()=>I(e=>{let{productData:t,selectedVariantIndex:r}=e;if(!t?.data)return null;let a=`${t.data.id}-${r}`;if(a===b&&null!==y)return y;let s=t.data.variants[r]||null;return y=s,b=a,s}),q=()=>I(e=>{let{productData:t,selectedVariantIndex:r,currentImageIndex:a}=e;if(!t?.data)return null;let s=`${t.data.id}-${r}-${a}`;if(console.log("\uD83D\uDD0D useCurrentImage called:",{currentKey:s,lastCurrentImageKey:N,hasCache:!!j}),s===N&&null!==j)return console.log("✅ Returning cached image"),j;let i=t.data.variants[r],l=i?.images[a]||null;return j=l,N=s,console.log("\uD83D\uDD04 Updating image cache"),l}),z=()=>I(e=>{let{productData:t,selectedVariantIndex:r}=e;if(!t?.data)return C;let a=t.data.variants[r];if(a?.id===P)return k;let s=a?.images??C;return k=s,P=a?.id??null,s});function $({isOpen:e,onClose:t,images:r,currentImageIndex:i,onImageChange:n,productName:o}){let m=(0,s.useRef)(null),p=L();if(!e||0===r.length)return null;let h=r[i];return(0,a.jsx)(c.N,{children:e&&(0,a.jsxs)(d.P.div,{ref:m,className:"fixed inset-0 z-50 flex items-center justify-center bg-black/90 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},onClick:e=>{e.target===m.current&&t()},children:[(0,a.jsx)(d.P.button,{className:"absolute top-4 right-4 z-10 bg-white/10 hover:bg-white/20 text-white rounded-full p-2 transition-colors",onClick:t,whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,a.jsx)(f.A,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{className:"absolute top-4 left-4 z-10 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:[i+1," / ",r.length]}),(0,a.jsxs)("div",{className:"relative w-full h-full max-w-6xl max-h-[90vh] mx-4",children:[(0,a.jsx)(c.N,{mode:"wait",children:(0,a.jsx)(d.P.div,{className:"relative w-full h-full",initial:0!==p?{x:p>0?300:-300,opacity:0}:{opacity:1},animate:{x:0,opacity:1},exit:0!==p?{x:p>0?-300:300,opacity:0}:{opacity:0},transition:{type:"tween",ease:"easeOut",duration:.2*(0!==p),delay:.1*(0!==p)},children:(0,a.jsx)(l.default,{src:h.url,alt:`${o} - ${i+1}`,fill:!0,className:"object-contain",priority:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"})},i)}),r.length>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.P.button,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/10 hover:bg-white/20 text-white rounded-full p-3 transition-colors",onClick:()=>{if(r.length>0){let e=i>0?i-1:r.length-1;I.setState({slideDirection:-1}),setTimeout(()=>{n(e)},0)}},whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,a.jsx)(u.A,{className:"h-8 w-8"})}),(0,a.jsx)(d.P.button,{className:"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/10 hover:bg-white/20 text-white rounded-full p-3 transition-colors",onClick:()=>{if(r.length>0){let e=i<r.length-1?i+1:0;I.setState({slideDirection:1}),setTimeout(()=>{n(e)},0)}},whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,a.jsx)(x.A,{className:"h-8 w-8"})})]})]}),r.length>1&&(0,a.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10",children:(0,a.jsx)("div",{className:"flex space-x-2 bg-black/50 rounded-lg p-2 max-w-xs overflow-x-auto",children:r.map((e,t)=>(0,a.jsx)(d.P.button,{className:`relative w-12 h-12 rounded-md overflow-hidden border-2 transition-all ${t===i?"border-white":"border-transparent opacity-60 hover:opacity-80"}`,onClick:()=>{I.setState({slideDirection:t>i?1:t<i?-1:0}),setTimeout(()=>{n(t)},0)},whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsx)(l.default,{src:e.url,alt:`${o} thumbnail ${t+1}`,fill:!0,className:"object-cover",sizes:"48px"})},e.id))})}),(0,a.jsx)("div",{className:"absolute bottom-20 left-1/2 transform -translate-x-1/2 z-10 bg-black/50 text-white px-4 py-2 rounded-lg text-center max-w-md",children:(0,a.jsx)("h3",{className:"font-medium truncate",children:o})})]})})}var E=r(51423),F=r(64298);let T=e=>{let t=I(),r=(0,s.useMemo)(()=>e?t.getCachedProduct(e):null,[e,t.getCachedProduct]),a=(0,s.useMemo)(()=>!!e&&t.isCacheValid(e),[e,t.isCacheValid]),i=(0,E.I)({queryKey:["catalogProductDetail",e],queryFn:async()=>{if(!e)throw Error("Product ID is required");if(a&&r)return console.log("\uD83C\uDFAF Using cached catalog product data for ID:",e),r;console.log("\uD83C\uDF10 Fetching catalog product from API for ID:",e),t.setLoading(!0);try{let r=await F.jU.getCatalogProductDetail(e);if(!r.success)throw Error(r.error||"\xdcr\xfcn detayı alınamadı");return t.setCachedProduct(e,r.data),console.log("✅ Catalog product fetched and cached for ID:",e),r.data}catch(e){throw console.error("❌ Error fetching catalog product:",e),t.setError(e instanceof Error?e.message:"\xdcr\xfcn detayı alınamadı"),e}finally{t.setLoading(!1)}},enabled:!!e,staleTime:3e5,gcTime:6e5,refetchOnWindowFocus:!1,retry:(e,t)=>(!t||!("status"in t)||404!==t.status)&&e<2});return(0,s.useEffect)(()=>{i.data?(t.setProductData(i.data),t.setError(null)):i.error&&(t.setError(i.error instanceof Error?i.error.message:"\xdcr\xfcn detayı alınamadı"),t.setProductData(null))},[i.data,i.error]),(0,s.useEffect)(()=>{t.setLoading(i.isLoading)},[i.isLoading]),{data:i.data,isLoading:i.isLoading,error:i.error,isError:i.isError,refetch:i.refetch,isFetching:i.isFetching,isSuccess:i.isSuccess}};var V=r(45840),B=r(83515),R=r(42112);function W(){let{id:e}=(0,i.useParams)(),[t,r]=(0,s.useState)(null),[n,f]=(0,s.useState)(!1),[w,v]=(0,s.useState)(null),[y,b]=(0,s.useState)(!1),[j,N]=(0,s.useState)(!0),[k,P]=(0,s.useState)(!1),{addToCart:C}=(0,m._)(),{addToFavorites:D,removeFromFavorites:E,isFavorite:F}=(0,p.r)(),{data:W,isLoading:_}=(0,V.P)(),H=(0,B.AP)(),{isCustomerPrice:U}=(0,R.w)(),{setSelectedVariant:O,setCurrentImage:G,increaseQuantity:Q,decreaseQuantity:K,resetState:Z}=I(),X=A(),Y=q(),J=z(),ee=M(),et=L(),er=S(),{data:ea,isLoading:es,error:ei}=T(t);if(es)return(0,a.jsx)("div",{className:"container mx-auto px-4 py-16 text-center",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-md max-w-lg mx-auto",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-purple-600 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"\xdcr\xfcn Y\xfckleniyor..."}),(0,a.jsx)("p",{className:"text-gray-600",children:"\xdcr\xfcn detayları getiriliyor, l\xfctfen bekleyin."})]})});if(ei||!ea)return(0,a.jsx)("div",{className:"container mx-auto px-4 py-16 text-center",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-md max-w-lg mx-auto",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 mx-auto text-gray-400 mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"\xdcr\xfcn Bulunamadı"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:ei?.message||"Aradığınız \xfcr\xfcn bulunamadı veya artık mevcut değil."}),(0,a.jsx)(o(),{href:"/products",className:"inline-block bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300",children:"\xdcr\xfcnlere D\xf6n"})]})});let el=ea.data,en=e=>{switch(e){case 0:return{text:"Stokta yok",color:"text-red-600"};case 1:return{text:"Az stok",color:"text-yellow-600"};case 2:return{text:"Stokta var",color:"text-green-600"};default:return{text:"Bilinmiyor",color:"text-gray-600"}}},eo=(e,t)=>Math.round(e/100*t),ed=(e,t,r=!0)=>{let a=e,s=W?.discountRate||null;return r&&!U&&s&&s>0&&(a*=1-s/100),t>0&&(a*=1-t/100),a},ec=(e,t=!0)=>{let r=W?.discountRate||null;return t&&r&&r>0||e>0},eu=e=>{e&&e.stopPropagation();let{setCurrentImage:t}=I.getState(),r=I.getState(),a=r.productData?.data.variants[r.selectedVariantIndex],s=a?.images.length||0;if(s>0){let e=0===r.currentImageIndex?s-1:r.currentImageIndex-1;I.setState({slideDirection:-1}),setTimeout(()=>{t(e)},0)}},ex=e=>{e&&e.stopPropagation();let{setCurrentImage:t}=I.getState(),r=I.getState(),a=r.productData?.data.variants[r.selectedVariantIndex],s=a?.images.length||0;if(s>0){let e=(r.currentImageIndex+1)%s;I.setState({slideDirection:1}),setTimeout(()=>{t(e)},0)}},em=async()=>{if(el&&X&&Y)try{await H.mutateAsync({productVariantId:X.id,quantity:ee,isCustomerPrice:U});let e=W?.discountRate||null,t=X.extraDiscount||0,r={id:X.id,title:el.name,price:X.price,discountedPrice:ed(X.price,X.extraDiscount,!U),thumbnail:Y.url,brand:el.brandName,membershipDiscount:!U&&e&&e>0?e:0,extraDiscount:t,pvPoints:eo(X.pv,X.price),cvPoints:eo(X.cv,X.price),spPoints:eo(X.sp,X.price),quantity:ee};C(r),v(r),f(!0)}catch(e){console.error("Sepete ekleme sırasında hata:",e)}};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)(o(),{href:"/products",className:"inline-flex items-center text-gray-600 hover:text-purple-600 transition-colors",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"\xdcr\xfcnlere D\xf6n"]})}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-10",children:[(0,a.jsxs)(d.P.div,{className:"lg:w-1/2",initial:{opacity:0,x:-30},animate:{opacity:1,x:0},transition:{duration:.5},children:[(0,a.jsx)("div",{className:"bg-white rounded-xl overflow-hidden shadow-md mb-4",children:(0,a.jsxs)("div",{className:"relative h-96 w-full cursor-pointer",onClick:()=>P(!0),children:[(0,a.jsx)(c.N,{mode:"wait",children:Y&&(0,a.jsx)(d.P.div,{initial:0!==et?{x:et>0?300:-300,opacity:0}:{opacity:1},animate:{x:0,opacity:1},exit:0!==et?{x:et>0?-300:300,opacity:0}:{opacity:0},transition:{type:"tween",ease:"easeOut",duration:.2*(0!==et),delay:.1*(0!==et)},className:"absolute inset-0",children:(0,a.jsx)(l.default,{src:Y.url,alt:el.name,fill:!0,className:"object-contain"})},er)}),J.length>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:e=>eu(e),className:"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 hover:text-gray-900 rounded-full p-2 shadow-lg transition-all duration-300 z-10",children:(0,a.jsx)(u.A,{className:"h-6 w-6"})}),(0,a.jsx)(d.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:e=>ex(e),className:"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 hover:text-gray-900 rounded-full p-2 shadow-lg transition-all duration-300 z-10",children:(0,a.jsx)(x.A,{className:"h-6 w-6"})})]}),X&&X.extraDiscount>0&&(0,a.jsxs)("div",{className:"absolute top-4 right-4 bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg",children:["%",X.extraDiscount," İndirim"]}),!U&&W?.discountRate&&W.discountRate>0&&(0,a.jsxs)("div",{className:`absolute ${X&&X.extraDiscount>0?"top-12":"top-4"} right-4 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg`,children:["%",W.discountRate," \xdcye İndirimi"]}),X&&(0,a.jsxs)("div",{className:"absolute top-4 left-4 flex flex-col space-y-2",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,a.jsx)("span",{className:"font-bold",children:"PV"}),(0,a.jsx)("span",{children:eo(X.pv,X.price)})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,a.jsx)("span",{className:"font-bold",children:"CV"}),(0,a.jsx)("span",{children:eo(X.cv,X.price)})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,a.jsx)("span",{className:"font-bold",children:"SP"}),(0,a.jsx)("span",{children:eo(X.sp,X.price)})]})]})]})}),(0,a.jsx)("div",{className:"grid grid-cols-5 gap-2",children:J.map((e,t)=>(0,a.jsx)(d.P.div,{className:`relative border-2 rounded-lg overflow-hidden cursor-pointer transition-all duration-200 ${Y?.id===e.id?"border-purple-500":"border-gray-200"}`,whileHover:{scale:1.05},onClick:()=>{I.setState({slideDirection:t>er?1:t<er?-1:0}),setTimeout(()=>{G(t)},0)},children:(0,a.jsx)("div",{className:"relative h-16 w-full",children:(0,a.jsx)(l.default,{src:e.url,alt:`${el.name} - ${t+1}`,fill:!0,className:"object-cover"})})},e.id))})]}),(0,a.jsx)(d.P.div,{className:"lg:w-1/2",initial:{opacity:0,x:30},animate:{opacity:1,x:0},transition:{duration:.5,delay:.2},children:(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-md p-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:el.name}),(0,a.jsx)("p",{className:"text-gray-600 mb-2",children:el.brandName})]}),el.averageRating&&(0,a.jsxs)("div",{className:"flex items-center bg-yellow-50 px-3 py-1 rounded-full",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),(0,a.jsx)("span",{className:"ml-1 font-medium text-yellow-700",children:el.averageRating.toFixed(1)})]})]}),(0,a.jsxs)("div",{className:"border-b border-gray-100 pb-6 mb-6",children:[el.variants&&el.variants.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"font-semibold mb-3 text-gray-700",children:"Varyant Se\xe7imi"}),(0,a.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-3",children:el.variants.map((e,t)=>(0,a.jsx)(d.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>O(t),className:`p-3 border-2 rounded-lg text-sm transition-all duration-200 ${X?.id===e.id?"border-purple-500 bg-purple-50 text-purple-700":"border-gray-200 hover:border-purple-300"}`,children:(0,a.jsxs)("div",{className:"text-left",children:[e.features.map((e,t)=>(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:[e.featureName,": ",(0,a.jsx)("span",{className:"font-medium",children:e.featureValue})]},t)),(0,a.jsx)("div",{className:"font-semibold mt-1",children:ec(e.extraDiscount,!U)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("span",{className:X?.id===e.id?"text-purple-700":"text-gray-600",children:[ed(e.price,e.extraDiscount).toFixed(2)," ₺"]}),(0,a.jsxs)("span",{className:"text-gray-500 line-through text-xs ml-1",children:[e.price.toFixed(2)," ₺"]})]}):(0,a.jsxs)("span",{className:X?.id===e.id?"text-purple-700":"text-gray-600",children:[e.price.toFixed(2)," ₺"]})}),(0,a.jsx)("div",{className:`text-xs mt-1 ${en(e.stockStatus).color}`,children:en(e.stockStatus).text}),(0,a.jsxs)("div",{className:"flex space-x-1 mt-2",children:[(0,a.jsxs)("span",{className:"bg-purple-100 text-purple-700 px-2 py-1 rounded text-xs font-medium",children:["PV ",eo(e.pv,e.price)]}),(0,a.jsxs)("span",{className:"bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium",children:["CV ",eo(e.cv,e.price)]}),(0,a.jsxs)("span",{className:"bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium",children:["SP ",eo(e.sp,e.price)]})]})]})},e.id))})]}),X&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"flex items-baseline mb-4",children:ec(X.extraDiscount,!U)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("span",{className:"text-3xl font-bold text-purple-700 mr-2",children:[ed(X.price,X.extraDiscount).toFixed(2)," ₺"]}),(0,a.jsxs)("span",{className:"text-lg text-gray-500 line-through",children:[X.price.toFixed(2)," ₺"]}),(0,a.jsxs)("div",{className:"flex flex-col gap-1 ml-3",children:[!U&&W?.discountRate&&W.discountRate>0&&(0,a.jsxs)("span",{className:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-2 py-1 rounded-full text-xs font-medium",children:["%",W.discountRate," \xdcye İndirimi"]}),X.extraDiscount>0&&(0,a.jsxs)("span",{className:"bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-medium",children:["%",X.extraDiscount," İndirim"]})]})]}):(0,a.jsxs)("span",{className:"text-3xl font-bold text-purple-700 mr-2",children:[X.price.toFixed(2)," ₺"]})}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Bu \xfcr\xfcn\xfc satın alarak kazanacağınız puanlar:"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,a.jsx)("span",{className:"font-bold",children:"PV"}),(0,a.jsxs)("span",{children:[eo(X.pv,X.price)," Puan"]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,a.jsx)("span",{className:"font-bold",children:"CV"}),(0,a.jsxs)("span",{children:[eo(X.cv,X.price)," Puan"]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,a.jsx)("span",{className:"font-bold",children:"SP"}),(0,a.jsxs)("span",{children:[eo(X.sp,X.price)," Puan"]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:`text-sm flex items-center ${en(X.stockStatus).color}`,children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),en(X.stockStatus).text]}),(0,a.jsxs)("div",{className:"text-sm flex items-center text-gray-600",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})}),"Kategori: ",el.categoryName]})]})]})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center border border-gray-200 rounded-md text-gray-600",children:[(0,a.jsx)(d.P.button,{whileTap:{scale:.9},onClick:K,className:"px-4 py-2 text-gray-600 hover:text-purple-700 focus:outline-none",children:"-"}),(0,a.jsx)("div",{className:"w-12 text-center",children:ee}),(0,a.jsx)(d.P.button,{whileTap:{scale:.9},onClick:Q,className:"px-4 py-2 text-gray-600 hover:text-purple-700 focus:outline-none",children:"+"})]}),(0,a.jsxs)(d.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:em,className:"w-full sm:w-auto bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 flex items-center justify-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"})}),"Sepete Ekle"]}),(0,a.jsx)(d.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>{if(!el||!X||!Y)return;let e=W?.discountRate||null,t=0;e&&e>0&&(t+=e),X.extraDiscount&&X.extraDiscount>0&&(t=t>0?t+X.extraDiscount-t*X.extraDiscount/100:X.extraDiscount);let r={id:el.id,title:el.name,price:X.price,thumbnail:Y.url,brand:el.brandName,discountPercentage:t,points:eo(X.pv,X.price)};F(el.id)?(E(el.id),N(!1)):(D(r),N(!0)),b(!0)},className:`w-12 h-12 flex items-center justify-center border rounded-full transition-colors ${F(el.id)?"border-red-500 text-red-500 bg-red-50":"border-gray-200 text-gray-400 hover:text-red-500 hover:border-red-500"}`,children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:F(el.id)?"currentColor":"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-3 text-gray-700",children:"\xdcr\xfcn A\xe7ıklaması"}),(0,a.jsx)("div",{className:"text-gray-700 leading-relaxed",children:el.description})]})]})})]}),(0,a.jsx)(h.A,{isOpen:n,onClose:()=>f(!1),product:w,quantity:ee}),(0,a.jsx)(g.A,{isOpen:y,onClose:()=>b(!1),product:X&&Y?{id:el.id,title:el.name,price:X.price,thumbnail:Y.url,brand:el.brandName,discountPercentage:X.extraDiscount,points:eo(X.pv,X.price)}:null,isAdded:j}),(0,a.jsx)($,{isOpen:k,onClose:()=>P(!1),images:J,currentImageIndex:J.findIndex(e=>e.id===Y?.id),onImageChange:e=>{G(e)},productName:el.name})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,181,658,85,938],()=>r(18337));module.exports=a})();