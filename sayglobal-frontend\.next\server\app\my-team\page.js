(()=>{var e={};e.id=305,e.ids=[305],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},14952:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23928:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25334:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28947:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36259:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var r=s(65239),a=s(48088),l=s(88170),i=s.n(l),n=s(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let d={children:["",{children:["my-team",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,40007)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\my-team\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\my-team\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/my-team/page",pathname:"/my-team",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},40007:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\my-team\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\my-team\\page.tsx","default")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41550:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},45583:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},45680:(e,t,s)=>{Promise.resolve().then(s.bind(s,54985))},48340:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},54220:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},54985:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>D});var r=s(60687),a=s(43210),l=s.n(a),i=s(84299),n=s(26001),c=s(67268),d=s(64398),o=s(41312),m=s(11860),x=s(86561),h=s(40228),p=s(58559),u=s(41550),g=s(48340),y=s(25541),j=s(45583),f=s(23928),v=s(14952),b=s(97992),N=s(58869),w=s(28947);let A=({member:e,isOpen:t,onClose:s})=>{let[l,i]=(0,a.useState)("overview");if((0,a.useEffect)(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),!t||!e)return null;let A={monthlyPoints:Math.floor(200*Math.random())+50,weeklyActivity:Math.floor(40*Math.random())+60,totalSales:Math.floor(5e3*Math.random())+1e3,teamMembers:Math.floor(10*Math.random())+1,monthlyGrowth:Math.floor(30*Math.random())+5,achievements:[{title:"İlk Satış",date:"2023-02-15",icon:c.A},{title:"Y\xfcksek Performans",date:"2023-03-10",icon:d.A},{title:"Ekip Lideri",date:"2023-04-05",icon:o.A}],recentActivity:[{action:"Yeni \xfcr\xfcn satışı",date:"2024-01-15",points:25},{action:"Ekip \xfcyesi ekledi",date:"2024-01-12",points:50},{action:"Hedef tamamlandı",date:"2024-01-10",points:100}]},k=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric"}),M=Math.floor((new Date().getTime()-new Date(e.joinDate).getTime())/864e5),S=(e=>{switch(e){case 1:return{name:"Bronz \xdcye",color:"from-amber-400 to-yellow-600",bgColor:"bg-amber-100",textColor:"text-amber-800"};case 2:return{name:"G\xfcm\xfcş \xdcye",color:"from-gray-400 to-gray-600",bgColor:"bg-gray-100",textColor:"text-gray-800"};case 3:return{name:"Altın \xdcye",color:"from-yellow-400 to-yellow-600",bgColor:"bg-yellow-100",textColor:"text-yellow-800"};default:return{name:"\xdcye",color:"from-blue-400 to-blue-600",bgColor:"bg-blue-100",textColor:"text-blue-800"}}})(e.level);return(0,r.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-lg flex items-center justify-center p-4 z-50",children:(0,r.jsxs)(n.P.div,{className:"bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.2},children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-indigo-600 px-6 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center",children:(0,r.jsxs)("span",{className:"text-white font-bold text-xl",children:[e.firstName.charAt(0),e.lastName.charAt(0)]})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold text-white",children:[e.firstName," ",e.lastName]}),(0,r.jsx)("p",{className:"text-purple-100",children:S.name}),(0,r.jsx)("div",{className:"flex items-center mt-1",children:(0,r.jsx)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${e.isActive?"bg-green-500 bg-opacity-90 text-white border-green-400":"bg-red-500 bg-opacity-90 text-white border-red-400"}`,children:e.isActive?"Aktif":"Pasif"})})]})]}),(0,r.jsx)("button",{onClick:s,className:"p-2 hover:bg-white/20 hover:bg-opacity-20 rounded-lg transition-colors text-white",children:(0,r.jsx)(m.A,{className:"w-6 h-6"})})]})}),(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsxs)("nav",{className:"flex px-6",children:[(0,r.jsx)("button",{onClick:()=>i("overview"),className:`py-4 px-4 border-b-2 font-medium text-sm transition-colors ${"overview"===l?"border-purple-500 text-purple-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Genel Bakış"}),(0,r.jsx)("button",{onClick:()=>i("performance"),className:`py-4 px-4 border-b-2 font-medium text-sm transition-colors ${"performance"===l?"border-purple-500 text-purple-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Performans"}),(0,r.jsx)("button",{onClick:()=>i("contact"),className:`py-4 px-4 border-b-2 font-medium text-sm transition-colors ${"contact"===l?"border-purple-500 text-purple-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"İletişim"})]})}),(0,r.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["overview"===l&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-purple-600",children:"Toplam Puan"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-purple-900",children:e.points.toLocaleString("tr-TR")})]}),(0,r.jsx)(d.A,{className:"h-8 w-8 text-purple-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-blue-600",children:"Seviye"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-blue-900",children:e.level})]}),(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-green-600",children:"\xdcyelik S\xfcresi"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-900",children:M}),(0,r.jsx)("p",{className:"text-xs text-green-600",children:"g\xfcn"})]}),(0,r.jsx)(h.A,{className:"h-8 w-8 text-green-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-orange-600",children:"Ekip Boyutu"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-orange-900",children:A.teamMembers})]}),(0,r.jsx)(o.A,{className:"h-8 w-8 text-orange-500"})]})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"\xdcye Bilgileri"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Katılım Tarihi"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:k(e.joinDate)})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Distrib\xfct\xf6r Seviyesi"}),(0,r.jsx)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${S.bgColor} ${S.textColor} mt-1`,children:S.name})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Durum"}),(0,r.jsx)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-1 ${e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isActive?"Aktif \xdcye":"Pasif \xdcye"})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Toplam Puan"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.points.toLocaleString("tr-TR")," puan"]})]})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Hızlı İşlemler"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"Mesaj G\xf6nder"]}),(0,r.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors",children:[(0,r.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Telefon Et"]}),(0,r.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors",children:[(0,r.jsx)(y.A,{className:"h-5 w-5 mr-2"}),"Rapor G\xf6r\xfcnt\xfcle"]})]})]})]}),"performance"===l&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-blue-600",children:"Aylık Puan"}),(0,r.jsx)("p",{className:"text-xl font-bold text-blue-900",children:A.monthlyPoints})]}),(0,r.jsx)(j.A,{className:"h-6 w-6 text-blue-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-green-600",children:"Haftalık Aktivite"}),(0,r.jsxs)("p",{className:"text-xl font-bold text-green-900",children:[A.weeklyActivity,"%"]})]}),(0,r.jsx)(p.A,{className:"h-6 w-6 text-green-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-purple-600",children:"Toplam Satış"}),(0,r.jsxs)("p",{className:"text-xl font-bold text-purple-900",children:["₺",A.totalSales.toLocaleString("tr-TR")]})]}),(0,r.jsx)(f.A,{className:"h-6 w-6 text-purple-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-orange-600",children:"Aylık B\xfcy\xfcme"}),(0,r.jsxs)("p",{className:"text-xl font-bold text-orange-900",children:["%",A.monthlyGrowth]})]}),(0,r.jsx)(y.A,{className:"h-6 w-6 text-orange-500"})]})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Başarılar"}),(0,r.jsx)("div",{className:"space-y-3",children:A.achievements.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)(e.icon,{className:"h-5 w-5 text-yellow-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:k(e.date)})]})]}),(0,r.jsx)(c.A,{className:"h-5 w-5 text-yellow-500"})]},t))})]}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Son Aktiviteler"}),(0,r.jsx)("div",{className:"space-y-3",children:A.recentActivity.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.action}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:k(e.date)})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-green-600",children:["+",e.points," puan"]}),(0,r.jsx)(v.A,{className:"h-4 w-4 text-gray-400 ml-1"})]})]},t))})]})]}),"contact"===l&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"İletişim Bilgileri"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-gray-400 mr-3 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"E-posta"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.firstName.toLowerCase(),".",e.lastName.toLowerCase(),"@sayglobal.com"]})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(g.A,{className:"h-5 w-5 text-gray-400 mr-3 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Telefon"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"+90 5XX XXX XX XX"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(b.A,{className:"h-5 w-5 text-gray-400 mr-3 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Adres"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"İstanbul, T\xfcrkiye"})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"İletişim Se\xe7enekleri"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"E-posta G\xf6nder"]}),(0,r.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,r.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Telefon Et"]}),(0,r.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[(0,r.jsx)(N.A,{className:"h-5 w-5 mr-2"}),"Profil Detayları"]}),(0,r.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors",children:[(0,r.jsx)(w.A,{className:"h-5 w-5 mr-2"}),"Hedef Belirle"]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Son İletişim Ge\xe7mişi"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"flex items-center justify-between p-3 bg-white rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)(u.A,{className:"h-4 w-4 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"E-posta g\xf6nderildi"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"2 g\xfcn \xf6nce"})]})]})}),(0,r.jsx)("div",{className:"flex items-center justify-between p-3 bg-white rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)(g.A,{className:"h-4 w-4 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"Telefon g\xf6r\xfcşmesi"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"1 hafta \xf6nce"})]})]})})]})]})]})]})]})})};var k=s(25334),M=s(93508),S=s(99270),P=s(54220);let D=()=>{let[e,t]=(0,a.useState)(i.x1),[s,n]=(0,a.useState)(""),[c,m]=(0,a.useState)("all"),[p,g]=(0,a.useState)("all"),[j,f]=(0,a.useState)("joinDate"),[v,b]=(0,a.useState)("desc"),[w,D]=(0,a.useState)(null),[C,E]=(0,a.useState)(!1),q=e=>{D(e),E(!0)},T=()=>{let e=i.x1;s&&(e=e.filter(e=>`${e.firstName} ${e.lastName}`.toLowerCase().includes(s.toLowerCase()))),"all"!==c&&(e=e.filter(e=>e.level===parseInt(c))),"all"!==p&&(e=e.filter(e=>"active"===p?e.isActive:!e.isActive)),t(e)},z=s=>{let r=j===s&&"desc"===v?"asc":"desc";f(s),b(r),t([...e].sort((e,t)=>{let a,l;switch(s){case"name":a=`${e.firstName} ${e.lastName}`,l=`${t.firstName} ${t.lastName}`;break;case"level":a=e.level,l=t.level;break;case"points":a=e.points,l=t.points;break;case"joinDate":a=new Date(e.joinDate),l=new Date(t.joinDate);break;default:return 0}return"asc"===r?a>l?1:-1:a<l?1:-1}))};l().useEffect(()=>{T()},[s,c,p]);let L=i.x1.filter(e=>e.isActive).length,$=i.x1.reduce((e,t)=>e+t.points,0),H=Math.round($/i.x1.length);return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100 py-8",children:[(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Ekip Y\xf6netimi"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Ekibinizdeki \xfcyeleri y\xf6netin ve performanslarını takip edin"})]}),(0,r.jsx)("div",{className:"mt-4 sm:mt-0",children:(0,r.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500",children:[(0,r.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Yeni \xdcye Davet Et"]})})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Ekip"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:i.x1.length})]}),(0,r.jsx)("div",{className:"bg-purple-100 p-3 rounded-full",children:(0,r.jsx)(o.A,{className:"h-6 w-6 text-purple-600"})})]})}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Aktif \xdcyeler"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:L})]}),(0,r.jsx)("div",{className:"bg-green-100 p-3 rounded-full",children:(0,r.jsx)(M.A,{className:"h-6 w-6 text-green-600"})})]})}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-yellow-500",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Puan"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:$.toLocaleString("tr-TR")})]}),(0,r.jsx)("div",{className:"bg-yellow-100 p-3 rounded-full",children:(0,r.jsx)(d.A,{className:"h-6 w-6 text-yellow-600"})})]})}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Ortalama Puan"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:H})]}),(0,r.jsx)("div",{className:"bg-blue-100 p-3 rounded-full",children:(0,r.jsx)(y.A,{className:"h-6 w-6 text-blue-600"})})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 mb-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(S.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"\xdcye ara...",value:s,onChange:e=>n(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 placeholder:text-gray-600 text-black"})]}),(0,r.jsxs)("select",{value:c,onChange:e=>m(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-600",children:[(0,r.jsx)("option",{value:"all",children:"T\xfcm Seviyeler"}),(0,r.jsx)("option",{value:"1",children:"Seviye 1"}),(0,r.jsx)("option",{value:"2",children:"Seviye 2"}),(0,r.jsx)("option",{value:"3",children:"Seviye 3"})]}),(0,r.jsxs)("select",{value:p,onChange:e=>g(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-600",children:[(0,r.jsx)("option",{value:"all",children:"T\xfcm Durumlar"}),(0,r.jsx)("option",{value:"active",children:"Aktif"}),(0,r.jsx)("option",{value:"inactive",children:"Pasif"})]}),(0,r.jsx)("button",{onClick:()=>{n(""),m("all"),g("all"),t(i.x1)},className:"px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Filtreleri Temizle"})]})}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:e.map(e=>(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-indigo-600 px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center",children:(0,r.jsxs)("span",{className:"text-white font-bold text-lg",children:[e.firstName.charAt(0),e.lastName.charAt(0)]})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-white",children:[e.firstName," ",e.lastName]}),(0,r.jsxs)("p",{className:"text-purple-100 text-sm",children:["Seviye ",e.level]})]})]}),(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-semibold rounded-full ${e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isActive?"Aktif":"Pasif"})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-yellow-500 mr-1"}),(0,r.jsx)("span",{className:"text-lg font-bold text-gray-900",children:e.points})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Puan"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 text-blue-500 mr-1"}),(0,r.jsx)("span",{className:"text-lg font-bold text-gray-900",children:Math.floor((new Date().getTime()-new Date(e.joinDate).getTime())/864e5)})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"G\xfcn"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Katılım Tarihi:"}),(0,r.jsx)("span",{className:"font-medium text-gray-900",children:new Date(e.joinDate).toLocaleDateString("tr-TR")})]}),(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsxs)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${1===e.level?"bg-green-100 text-green-800":2===e.level?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:[(0,r.jsx)(x.A,{className:"h-3 w-3 mr-1"}),"Seviye ",e.level," Distrib\xfct\xf6r"]})}),(0,r.jsxs)("div",{className:"flex space-x-2 pt-4",children:[(0,r.jsxs)("button",{onClick:()=>q(e),className:"flex-1 bg-purple-100 text-purple-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-purple-200 transition-colors",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 inline mr-1"}),"Detaylar"]}),(0,r.jsxs)("button",{className:"flex-1 bg-blue-100 text-blue-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-blue-200 transition-colors",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 inline mr-1"}),"Mesaj"]})]})]})})]},e.id))}),0===e.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsxs)("div",{className:"text-gray-500",children:[(0,r.jsx)(o.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"\xdcye bulunamadı"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Se\xe7ilen kriterlere uygun \xfcye bulunmuyor."})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Detaylı Ekip Listesi"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>z("name"),className:"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50",children:["İsim ",(0,r.jsx)(P.A,{className:"h-4 w-4 ml-1"})]}),(0,r.jsxs)("button",{onClick:()=>z("points"),className:"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50",children:["Puan ",(0,r.jsx)(P.A,{className:"h-4 w-4 ml-1"})]}),(0,r.jsxs)("button",{onClick:()=>z("joinDate"),className:"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50",children:["Tarih ",(0,r.jsx)(P.A,{className:"h-4 w-4 ml-1"})]})]})]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"\xdcye"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Seviye"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Puan"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Katılım Tarihi"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Durum"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"İşlemler"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-purple-400 to-indigo-500 rounded-full flex items-center justify-center mr-3",children:(0,r.jsxs)("span",{className:"text-white font-semibold text-sm",children:[e.firstName.charAt(0),e.lastName.charAt(0)]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:[e.firstName," ",e.lastName]})})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${1===e.level?"bg-green-100 text-green-800":2===e.level?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:["Seviye ",e.level]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-yellow-500 mr-1"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.points.toLocaleString("tr-TR")})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:new Date(e.joinDate).toLocaleDateString("tr-TR")}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isActive?"Aktif":"Pasif"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>q(e),className:"text-purple-600 hover:text-purple-900 transition-colors",children:"Detaylar"}),(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-900 transition-colors",children:"Mesaj"})]})})]},e.id))})]})})]}),(0,r.jsx)("div",{className:"mt-8 text-center",children:(0,r.jsx)("a",{href:"/panel",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors",children:"Distrib\xfct\xf6r Paneline D\xf6n"})})]}),(0,r.jsx)(A,{member:w,isOpen:C,onClose:()=>{E(!1),D(null)}})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},58869:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:o,...m},x)=>(0,r.createElement)("svg",{ref:x,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:n("lucide",l),...!i&&!c(m)&&{"aria-hidden":"true"},...m},[...o.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(i)?i:[i]])),m=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...l},c)=>(0,r.createElement)(o,{ref:c,iconNode:t,className:n(`lucide-${a(i(e))}`,`lucide-${e}`,s),...l}));return s.displayName=i(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},67268:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84299:(e,t,s)=>{"use strict";s.d(t,{At:()=>i,Zq:()=>r,dt:()=>n,wI:()=>l,x1:()=>a});let r=[{id:1,distributorId:2,date:"2023-04-15",reference:"Aylin Şahin",points:120,amount:240.5,level:1,percentage:8},{id:2,distributorId:2,date:"2023-04-20",reference:"Emre Kılı\xe7",points:85,amount:170,level:2,percentage:5},{id:3,distributorId:2,date:"2023-04-28",reference:"Arda Altun",points:150,amount:300.75,level:1,percentage:8},{id:4,distributorId:2,date:"2023-05-05",reference:"Burak \xd6zt\xfcrk",points:60,amount:120.25,level:3,percentage:3},{id:5,distributorId:2,date:"2023-05-12",reference:"Selin Kara",points:200,amount:400,level:1,percentage:8}],a=[{id:101,firstName:"Aylin",lastName:"Şahin",level:1,joinDate:"2023-02-10",points:450,isActive:!0},{id:102,firstName:"Emre",lastName:"Kılı\xe7",level:2,joinDate:"2023-02-15",points:320,isActive:!0},{id:103,firstName:"Arda",lastName:"Altun",level:1,joinDate:"2023-03-01",points:580,isActive:!0},{id:104,firstName:"Burak",lastName:"\xd6zt\xfcrk",level:3,joinDate:"2023-03-12",points:150,isActive:!1},{id:105,firstName:"Selin",lastName:"Kara",level:1,joinDate:"2023-03-25",points:650,isActive:!0},{id:106,firstName:"Murat",lastName:"Aydın",level:2,joinDate:"2023-04-05",points:280,isActive:!0},{id:107,firstName:"Elif",lastName:"\xc7elik",level:3,joinDate:"2023-04-18",points:120,isActive:!1}],l={totalEarnings:4250.75,monthlyPoints:350,monthlyActivityPercentage:75,teamSize:a.length,monthlyBalance:1230.5,totalBalance:4250.75,totalPoints:2150,organizationPoints:5680,monthlyEarnings:[{month:"Ocak",earnings:850.25,activity:65},{month:"Şubat",earnings:920.5,activity:70},{month:"Mart",earnings:1050.75,activity:80},{month:"Nisan",earnings:1230.5,activity:75},{month:"Mayıs",earnings:980.25,activity:72}],monthlyPointsHistory:[{month:"Ocak",points:280,target:300},{month:"Şubat",points:320,target:300},{month:"Mart",points:390,target:350},{month:"Nisan",points:420,target:350},{month:"Mayıs",points:350,target:400},{month:"Haziran",points:310,target:400}],monthlyActivityTrend:[{month:"Ocak",activityPercentage:65,teamSize:4,newMembers:1},{month:"Şubat",activityPercentage:70,teamSize:5,newMembers:1},{month:"Mart",activityPercentage:80,teamSize:6,newMembers:1},{month:"Nisan",activityPercentage:75,teamSize:6,newMembers:0},{month:"Mayıs",activityPercentage:72,teamSize:7,newMembers:1},{month:"Haziran",activityPercentage:78,teamSize:7,newMembers:0}],nextLevelPoints:500,currentLevel:"G\xfcm\xfcş",nextLevel:"Altın"},i={id:1,name:"Distrib\xfct\xf6r (Sen)",level:3,points:2150,joinDate:"2023-01-01",isActive:!0,totalEarnings:4250.75,monthlyPoints:350,children:{left:{id:101,name:"Aylin Şahin",level:1,points:450,joinDate:"2023-02-10",isActive:!0,parentId:1,position:"left",totalEarnings:1200.5,monthlyPoints:120,children:{left:{id:103,name:"Arda Altun",level:1,points:580,joinDate:"2023-03-01",isActive:!0,parentId:101,position:"left",totalEarnings:850.25,monthlyPoints:95,children:{left:{id:107,name:"Elif \xc7elik",level:3,points:120,joinDate:"2023-04-18",isActive:!1,parentId:103,position:"left",totalEarnings:240,monthlyPoints:25}}},right:{id:105,name:"Selin Kara",level:1,points:650,joinDate:"2023-03-25",isActive:!0,parentId:101,position:"right",totalEarnings:980.75,monthlyPoints:135}}},right:{id:102,name:"Emre Kılı\xe7",level:2,points:320,joinDate:"2023-02-15",isActive:!0,parentId:1,position:"right",totalEarnings:750.25,monthlyPoints:85,children:{left:{id:104,name:"Burak \xd6zt\xfcrk",level:3,points:150,joinDate:"2023-03-12",isActive:!1,parentId:102,position:"left",totalEarnings:320,monthlyPoints:40},right:{id:106,name:"Murat Aydın",level:2,points:280,joinDate:"2023-04-05",isActive:!0,parentId:102,position:"right",totalEarnings:480.5,monthlyPoints:65}}}}},n={totalMembers:7,activeMembers:5,totalLevels:3,totalPoints:2550,totalEarnings:8950,monthlyGrowth:12.5}},86561:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},87536:(e,t,s)=>{Promise.resolve().then(s.bind(s,40007))},93508:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},94735:e=>{"use strict";e.exports=require("events")},97992:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},99270:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,181,658,85],()=>s(36259));module.exports=r})();