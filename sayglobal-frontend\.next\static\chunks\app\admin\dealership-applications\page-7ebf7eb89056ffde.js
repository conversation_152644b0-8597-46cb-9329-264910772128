(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4784],{14186:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},20181:(e,a,s)=>{"use strict";s.d(a,{CM:()=>r,nA:()=>l,vM:()=>t});let t=[{id:1,userId:6,userName:"Mehmet Yılmaz",userEmail:"<EMAIL>",applicationData:{firstName:"Mehmet",lastName:"Yılmaz",email:"<EMAIL>",phone:"+90 ************",mainProductCategory:"Elektronik",subProductCategories:["Telefon","Bilgisayar","Aksesuar"],estimatedProductCount:"50-100 adet",sampleProductListUrl:"https://example.com/products",companyName:"Yılmaz Elektronik Ltd. Şti.",taxNumber:"1234567890",taxOffice:"İstanbul Vergi Dairesi",companyAddress:"Atat\xfcrk Cad. No:123 Kadık\xf6y/İstanbul",authorizedPersonName:"Mehmet Yılmaz",authorizedPersonTcId:"12345678901",alternativeContactNumber:"+90 ************",userAgreementAccepted:!0,dealershipAgreementAccepted:!0,privacyPolicyAccepted:!0},status:"pending",submittedAt:"2024-01-15T10:30:00Z"},{id:2,userId:7,userName:"Ayşe Kaya",userEmail:"<EMAIL>",applicationData:{firstName:"Ayşe",lastName:"Kaya",email:"<EMAIL>",phone:"+90 ************",mainProductCategory:"Ev & Yaşam",subProductCategories:["Mutfak","Dekorasyon"],estimatedProductCount:"20-50 adet",companyName:"Kaya Ev Tekstili",taxNumber:"0987654321",taxOffice:"Ankara Vergi Dairesi",companyAddress:"Kızılay Cad. No:456 \xc7ankaya/Ankara",authorizedPersonName:"Ayşe Kaya",authorizedPersonTcId:"10987654321",alternativeContactNumber:"+90 ************",userAgreementAccepted:!0,dealershipAgreementAccepted:!0,privacyPolicyAccepted:!0},status:"approved",submittedAt:"2024-01-10T14:20:00Z",reviewedAt:"2024-01-12T09:15:00Z",reviewedBy:1,adminNotes:"Başvuru uygun bulunmuştur."}],r=["Elektronik","Ev & Yaşam","Giyim & Aksesuar","Spor & Outdoor","Kozmetik & Kişisel Bakım","Kitap & Kırtasiye","Oyuncak & Hobi","Otomotiv","Anne & Bebek","Diğer"],l=["1-10 adet","11-25 adet","26-50 adet","51-100 adet","101-250 adet","250+ adet"]},33786:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},37054:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>D});var t=s(95155),r=s(12115),l=s(87220),i=s(35695),n=s(20181),d=s(14186),c=s(40646),m=s(54861),o=s(71007),x=s(23227),u=s(37108),h=s(33786);let p=(0,s(19946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),y=e=>{let{application:a,isOpen:s,onClose:l,onApprove:i,onReject:n,adminNotes:y,setAdminNotes:g,isProcessing:j,onShowWarning:N}=e;return((0,r.useEffect)(()=>(s?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[s]),s&&a)?(0,t.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-sm overflow-y-auto h-full w-full z-50",children:(0,t.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white",children:(0,t.jsxs)("div",{className:"mt-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Başvuru Detayları"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(e=>{switch(e){case"pending":return(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[(0,t.jsx)(d.A,{className:"w-3 h-3 mr-1"}),"Beklemede"]});case"approved":return(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,t.jsx)(c.A,{className:"w-3 h-3 mr-1"}),"Onaylandı"]});case"rejected":return(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[(0,t.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"Reddedildi"]})}})(a.status),(0,t.jsx)("button",{onClick:l,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,t.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}),(0,t.jsxs)("div",{className:"space-y-6 max-h-96 overflow-y-auto",children:[(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Başvuran Bilgileri"]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Ad Soyad:"}),(0,t.jsxs)("span",{className:"ml-2 font-medium text-gray-700",children:[a.applicationData.firstName," ",a.applicationData.lastName]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"E-posta:"}),(0,t.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:a.applicationData.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Telefon:"}),(0,t.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:a.applicationData.phone})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Alternatif Tel:"}),(0,t.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:a.applicationData.alternativeContactNumber})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Şirket Bilgileri"]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Şirket Adı:"}),(0,t.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:a.applicationData.companyName})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Vergi No:"}),(0,t.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:a.applicationData.taxNumber})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Vergi Dairesi:"}),(0,t.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:a.applicationData.taxOffice})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Adres:"}),(0,t.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:a.applicationData.companyAddress})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"\xdcr\xfcn Bilgileri"]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Ana Kategori:"}),(0,t.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:a.applicationData.mainProductCategory})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Tahmini \xdcr\xfcn Sayısı:"}),(0,t.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:a.applicationData.estimatedProductCount})]}),a.applicationData.sampleProductListUrl&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"\xd6rnek URL:"}),(0,t.jsxs)("a",{href:a.applicationData.sampleProductListUrl,target:"_blank",rel:"noopener noreferrer",className:"ml-2 text-blue-600 hover:text-blue-800 flex items-center",children:["Link ",(0,t.jsx)(h.A,{className:"h-3 w-3 ml-1"})]})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"Yetkili Kişi"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Ad Soyad:"}),(0,t.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:a.applicationData.authorizedPersonName})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"T.C. No:"}),(0,t.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:a.applicationData.authorizedPersonTcId})]})]})]}),a.adminNotes&&(0,t.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,t.jsxs)("h4",{className:"font-medium text-gray-900 mb-2 flex items-center",children:[(0,t.jsx)(p,{className:"h-4 w-4 mr-2"}),"Admin Notları"]}),(0,t.jsx)("p",{className:"text-sm text-gray-700",children:a.adminNotes}),a.reviewedAt&&(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["İncelenme: ",new Date(a.reviewedAt).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})]})]})]}),"pending"===a.status&&(0,t.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Admin Notları"}),(0,t.jsx)("textarea",{value:y,onChange:e=>g(e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 text-black",placeholder:"Başvuru hakkında notlarınızı yazın..."})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsxs)("button",{onClick:()=>{if(!y.trim()){N&&N();return}n(a.id,y)},disabled:j,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 flex items-center",children:[j?(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Reddet"]}),(0,t.jsxs)("button",{onClick:()=>{i(a.id,y)},disabled:j,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center",children:[j?(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Onayla"]})]})]})]})})}):null};var g=s(60760),j=s(76408),N=s(85339),f=s(54416);let v=e=>{let{isOpen:a,onClose:s,type:l,title:i,message:n,autoClose:d=!0,autoCloseDelay:o=3e3}=e;return r.useEffect(()=>{if(a&&d){let e=setTimeout(()=>{s()},o);return()=>clearTimeout(e)}},[a,d,o,s]),(0,t.jsx)(g.N,{children:a&&(0,t.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,t.jsxs)("div",{className:"flex min-h-full items-center justify-center p-4",children:[(0,t.jsx)(j.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:s,className:"fixed inset-0 bg-black/20 backdrop-blur-sm transition-opacity"}),(0,t.jsxs)(j.P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{type:"spring",duration:.5},className:"relative transform overflow-hidden rounded-xl bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6 border-2 ".concat((()=>{switch(l){case"success":default:return"bg-green-50 border-green-200";case"error":return"bg-red-50 border-red-200";case"warning":return"bg-yellow-50 border-yellow-200"}})()),children:[(0,t.jsx)("button",{onClick:s,className:"absolute right-4 top-4 text-gray-400 hover:text-gray-600 transition-colors",children:(0,t.jsx)(f.A,{className:"h-5 w-5"})}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(j.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring",stiffness:200},className:"mx-auto flex h-12 w-12 items-center justify-center",children:(()=>{switch(l){case"success":default:return(0,t.jsx)(c.A,{className:"h-12 w-12 text-green-600"});case"error":return(0,t.jsx)(m.A,{className:"h-12 w-12 text-red-600"});case"warning":return(0,t.jsx)(N.A,{className:"h-12 w-12 text-yellow-600"})}})()}),(0,t.jsxs)(j.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},className:"mt-3 text-center sm:mt-5",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold leading-6 text-gray-900",children:i}),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsx)("p",{className:"text-sm text-gray-600",children:n})})]})]}),(0,t.jsx)(j.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},className:"mt-5 sm:mt-6",children:(0,t.jsx)("button",{type:"button",onClick:s,className:"inline-flex w-full justify-center rounded-lg px-3 py-2 text-sm font-semibold text-white shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ".concat((()=>{switch(l){case"success":default:return"bg-green-600 hover:bg-green-700 focus:ring-green-500";case"error":return"bg-red-600 hover:bg-red-700 focus:ring-red-500";case"warning":return"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500"}})()),children:"Tamam"})}),d&&(0,t.jsx)(j.P.div,{initial:{width:"100%"},animate:{width:"0%"},transition:{duration:o/1e3,ease:"linear"},className:"absolute bottom-0 left-0 h-1 rounded-b-xl ".concat("success"===l?"bg-green-400":"error"===l?"bg-red-400":"bg-yellow-400")})]})]})})})};var b=s(75525),w=s(57434),k=s(47924),A=s(69074),C=s(92657);let D=()=>{let{user:e,isLoading:a,updateUserRole:s}=(0,l.A)(),x=(0,i.useRouter)(),[u,h]=(0,r.useState)(n.vM),[p,g]=(0,r.useState)(null),[j,N]=(0,r.useState)("all"),[f,D]=(0,r.useState)(""),[P,B]=(0,r.useState)(!1),[z,O]=(0,r.useState)(""),[T,S]=(0,r.useState)({isOpen:!1,type:"success",title:"",message:""});if((0,r.useEffect)(()=>{a||e&&"admin"===e.role||x.push("/login")},[e,a,x]),a)return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})});if(!e||"admin"!==e.role)return null;let E=u.filter(e=>{let a="all"===j||e.status===j,s=""===f||e.userName.toLowerCase().includes(f.toLowerCase())||e.userEmail.toLowerCase().includes(f.toLowerCase())||e.applicationData.companyName.toLowerCase().includes(f.toLowerCase());return a&&s}),L=(e,a,s)=>{S({isOpen:!0,type:e,title:a,message:s})},M=async(a,t,r)=>{let l=r||z;B(!0);try{let r=u.find(e=>e.id===a);if(!r)throw Error("Başvuru bulunamadı");await new Promise(e=>setTimeout(e,1500)),h(s=>s.map(s=>s.id===a?{...s,status:"approve"===t?"approved":"rejected",reviewedAt:new Date().toISOString(),reviewedBy:e.id,adminNotes:l.trim()||("approve"===t?"Başvuru onaylandı.":"Başvuru reddedildi.")}:s)),"approve"===t?s(r.userId,"dealership",!0,"approved"):"reject"===t&&s(r.userId,"customer",!1,"rejected"),g(null),O(""),"approve"===t?L("success","Başvuru Onaylandı!","Satıcı başvurusu başarıyla onaylandı ve kullanıcı hesabı aktif edildi."):L("error","Başvuru Reddedildi!","Satıcı başvurusu reddedildi ve kullanıcıya bilgilendirme e-postası g\xf6nderildi.")}catch(e){L("error","İşlem Başarısız!","İşlem sırasında bir hata oluştu. L\xfctfen tekrar deneyin.")}finally{B(!1)}},K=e=>{switch(e){case"pending":return(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[(0,t.jsx)(d.A,{className:"w-3 h-3 mr-1"}),"Beklemede"]});case"approved":return(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,t.jsx)(c.A,{className:"w-3 h-3 mr-1"}),"Onaylandı"]});case"rejected":return(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[(0,t.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"Reddedildi"]})}},R=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Satıcı Başvuruları"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Platforma katılmak isteyen satıcı başvurularını y\xf6netin"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 bg-red-100 px-4 py-2 rounded-lg",children:[(0,t.jsx)(b.A,{className:"h-5 w-5 text-red-600"}),(0,t.jsx)("span",{className:"text-red-800 font-medium",children:"Admin Paneli"})]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-yellow-500",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Bekleyen"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:u.filter(e=>"pending"===e.status).length})]}),(0,t.jsx)(d.A,{className:"h-8 w-8 text-yellow-600"})]})}),(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Onaylanan"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:u.filter(e=>"approved"===e.status).length})]}),(0,t.jsx)(c.A,{className:"h-8 w-8 text-green-600"})]})}),(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Reddedilen"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:u.filter(e=>"rejected"===e.status).length})]}),(0,t.jsx)(m.A,{className:"h-8 w-8 text-red-600"})]})}),(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:u.length})]}),(0,t.jsx)(w.A,{className:"h-8 w-8 text-blue-600"})]})})]}),(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 mb-8",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(k.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)("input",{type:"text",placeholder:"Ad, email veya şirket ara...",value:f,onChange:e=>D(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 w-64 text-black"})]}),(0,t.jsxs)("select",{value:j,onChange:e=>N(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-600",children:[(0,t.jsx)("option",{value:"all",children:"T\xfcm Durumlar"}),(0,t.jsx)("option",{value:"pending",children:"Bekleyen"}),(0,t.jsx)("option",{value:"approved",children:"Onaylanan"}),(0,t.jsx)("option",{value:"rejected",children:"Reddedilen"})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[E.length," sonu\xe7 g\xf6steriliyor"]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",children:[(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,t.jsx)("thead",{className:"bg-gray-50",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Başvuran"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Şirket"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kategori"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Başvuru Tarihi"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Durum"}),(0,t.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"İşlemler"})]})}),(0,t.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:E.map(e=>(0,t.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,t.jsx)(o.A,{className:"h-5 w-5 text-gray-600"})})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:e.userEmail})]})]})}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,t.jsx)("div",{className:"text-sm text-gray-900",children:e.applicationData.companyName}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:e.applicationData.taxNumber})]}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,t.jsx)("div",{className:"text-sm text-gray-900",children:e.applicationData.mainProductCategory}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:e.applicationData.estimatedProductCount})]}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-900",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-1 text-gray-400"}),R(e.submittedAt)]})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:K(e.status)}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,t.jsxs)("button",{onClick:()=>g(e),className:"text-red-600 hover:text-red-900 flex items-center",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-1"}),"İncele"]})})]},e.id))})]})}),0===E.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(w.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,t.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Başvuru bulunamadı"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Arama kriterlerinize uygun başvuru bulunmuyor."})]})]}),(0,t.jsx)(y,{application:p,isOpen:!!p,onClose:()=>g(null),onApprove:(e,a)=>M(e,"approve",a),onReject:(e,a)=>M(e,"reject",a),adminNotes:z,setAdminNotes:O,isProcessing:P,onShowWarning:()=>L("warning","A\xe7ıklama Gerekli","Red işlemi i\xe7in a\xe7ıklama yazmanız gerekmektedir.")}),(0,t.jsx)(v,{isOpen:T.isOpen,onClose:()=>{S(e=>({...e,isOpen:!1}))},type:T.type,title:T.title,message:T.message})]})})}},47924:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},54861:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},69074:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},72756:(e,a,s)=>{Promise.resolve().then(s.bind(s,37054))},75525:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[6408,7323,7028,6681,8441,1684,7358],()=>a(72756)),_N_E=e.O()}]);