exports.id=85,exports.ids=[85],exports.modules={10535:(e,t,a)=>{"use strict";a.d(t,{y:()=>o});var r=a(64298),s=a(37349);let o={clearAuthCookies(){},async login(e,t){console.log("\uD83D\uDEAA AuthService login başlıyor...",{email:e}),console.log("\uD83D\uDD0D Login başlangıcında logout flag kontrol\xfc:",{hasLoggedOut:null});let a={email:e,password:t};console.log("\uD83D\uDCE4 G\xf6nderilecek veri:",a),console.log("\uD83D\uDCE1 API URL:",r.Ay.defaults.baseURL),console.log("\uD83D\uDD27 API Config:",{baseURL:r.Ay.defaults.baseURL,withCredentials:r.Ay.defaults.withCredentials,headers:r.Ay.defaults.headers});try{console.log(`📡 POST ${s.S.LOGIN} \xe7ağrısı yapılıyor...`);let e=await r.Ay.post(s.S.LOGIN,a);console.log("\uD83D\uDCE1 Login response:",e.data),console.log("\uD83D\uDCCA Response status:",e.status),console.log("\uD83C\uDF6A Response headers:",e.headers),console.log("\uD83C\uDF6A Backend Set-Cookie ile token g\xf6nderdi"),console.log("\uD83D\uDD0D Response structure debug:",{data:e.data,status:e.status,message:e.data?.message,hasData:!!e.data,statusType:e.data?.statusType,dataKeys:e.data?Object.keys(e.data):[]});let t=200===e.status,o=e.headers["set-cookie"]||e.headers["Set-Cookie"]||e.headers["SET-COOKIE"],n=e.data?.message==="Giriş başarılı."||e.data?.message==="Login successful"||e.data?.message?.includes("başarılı")||e.data?.message?.includes("successful"),l=e.data?.status===0||e.data?.status===200||e.data?.success===!0,i=!e.data?.message?.includes("hatalı")&&!e.data?.message?.includes("error")&&!e.data?.message?.includes("failed")&&!e.data?.message?.includes("invalid")&&!e.data?.error,d=t&&(n||l||i);if(console.log("\uD83D\uDD0D Success detection:",{httpSuccess:t,hasSetCookie:!!o,messageSuccess:n,statusSuccess:l,noErrorMessage:i,finalSuccess:d}),d)return console.log("✅ Login başarılı!"),console.log("\uD83C\uDF6A Backend Set-Cookie header ile token g\xf6nderdi"),console.log("\uD83D\uDD04 Cookie browser tarafından otomatik set edilecek"),!0;return console.log("❌ Login başarısız - Response criteria not met"),this.clearAuthCookies(),!1}catch(e){throw e}},async register(e){console.log("\uD83D\uDCE4 Register request:",e);try{let t=await r.Ay.post(s.S.REGISTER,e);console.log("✅ Register response:",t.data);let a=t.data;return{success:0===a.status,message:a.message,user:a.data?{id:0,firstName:e.firstName,lastName:e.lastName,email:e.email,phoneNumber:e.phoneNumber||""}:void 0}}catch(e){throw console.error("❌ Register error:",e),e}},refreshToken:async()=>(await r.Ay.get(s.S.REFRESH_TOKEN)).data,async logout(){try{await r.Ay.get(s.S.LOGOUT),console.log("✅ Backend logout başarılı")}catch(e){console.error("❌ Backend logout hatası:",e)}finally{}},async getUserInfo(){console.log("\uD83D\uDD0E AuthService getUserInfo başlıyor..."),console.log("\uD83C\uDF6A withCredentials ile cookie otomatik g\xf6nderilecek");try{let e=await r.Ay.get(s.S.USER_INFO);return console.log("✅ getUserInfo başarılı:",e.data),e.data}catch(e){if(e.response?.status===401)return console.log("ℹ️ Kullanıcı giriş yapmamış. (Bu beklenen bir durumdur)"),null;throw console.error("❌ getUserInfo sırasında beklenmedik bir hata oluştu:",e),e}},async addReference(e){try{await r.Ay.post(s.S.ADD_REFERENCE,{referansCode:e})}catch(e){throw console.error("❌ Referans eklenirken hata:",e),e}},async makeAdmin(e){try{await r.Ay.post(s.S.MAKE_ADMIN,{userIdOrEmail:e})}catch(e){throw console.error("❌ Admin yapma hatası:",e),e}},test:async()=>(await r.Ay.get(s.S.TEST_AUTH)).data,debugClaims:async()=>(await r.Ay.get(s.S.DEBUG_CLAIMS)).data,async getProfileInfo(){console.log("\uD83D\uDD0E AuthService getProfileInfo başlıyor...");try{let e=await r.Ay.get(s.S.PROFILE_INFO);if(console.log("✅ getProfileInfo raw response:",e.data),0===e.data.status&&e.data.data)return console.log("✅ getProfileInfo başarılı:",e.data.data),e.data.data;throw Error(e.data.message||"Profil bilgileri alınamadı")}catch(e){throw console.error("❌ getProfileInfo sırasında hata oluştu:",e),e}},async updateProfile(e){try{return(await r.Ay.post(s.S.UPDATE_PROFILE,e)).data}catch(e){throw console.error("❌ Update profile error:",e),e}},async updateProfilePicture(e){console.log("\uD83D\uDCE4 Update profile picture request");try{let t=new FormData;t.append("file",e);let a=await r.Ay.put(s.S.PROFILE_PICTURE,t,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ Update profile picture response:",a.data),a.data}catch(e){throw console.error("❌ Update profile picture error:",e),e}},async deleteProfilePicture(){console.log("\uD83D\uDDD1️ Delete profile picture request");try{let e=await r.Ay.delete(s.S.DELETE_PROFILE_PICTURE);return console.log("✅ Delete profile picture response:",e.data),e.data}catch(e){throw console.error("❌ Delete profile picture error:",e),e}}}},14467:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},15908:(e,t,a)=>{"use strict";a.d(t,{A:()=>d,O:()=>i});var r=a(60687),s=a(43210),o=a(42243),n=a(87979);let l=(0,s.createContext)(void 0),i=({children:e})=>{let{data:t,isLoading:a,isSuccess:i}=(0,n.Py)(),{error:d,clearError:c}=(0,o.n)(),u=(0,n._L)(),p=(0,n.Ng)(),m=(0,n.ge)();(0,s.useEffect)(()=>{let e=()=>{p.isPending||p.mutate()};return window.addEventListener("auth:force-logout",e),()=>{window.removeEventListener("auth:force-logout",e)}},[p]);let g={isAuthenticated:i&&!!t,user:t||null,login:async e=>{try{return await u.mutateAsync(e),!0}catch(e){return console.error("❌ AuthContext login error:",e),!1}},register:async e=>{try{return await m.mutateAsync(e),!0}catch(e){return console.error("❌ AuthContext register error:",e),!1}},logout:async()=>{await p.mutateAsync()},updateUserRole:async(e,t,a,r)=>{console.log("User role update requested:",{userId:e,newRole:t,isDealershipApproved:a,applicationStatus:r})},isLoading:u.isPending||m.isPending||p.isPending||a,error:d};return(0,r.jsx)(l.Provider,{value:g,children:e})},d=()=>{let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},28253:(e,t,a)=>{"use strict";a.d(t,{_:()=>l,e:()=>n});var r=a(60687),s=a(43210);let o=(0,s.createContext)(void 0);function n({children:e}){let[t,a]=(0,s.useState)([]),n=e=>{a(t=>t.filter(t=>t.id!==e))};return(0,r.jsx)(o.Provider,{value:{items:t,addToCart:e=>{a(t=>{let a=t.find(t=>t.id===e.id),r=e.quantity||1;return a?t.map(t=>t.id===e.id?{...t,quantity:t.quantity+r,points:e.points||t.points||0}:t):[...t,{id:e.id,title:e.title,price:e.price,thumbnail:e.thumbnail,brand:e.brand,quantity:r,discountPercentage:e.discountPercentage,points:e.points||0}]})},removeFromCart:n,updateQuantity:(e,t)=>{if(t<=0)return void n(e);a(a=>a.map(a=>a.id===e?{...a,quantity:t}:a))},clearCart:()=>{a([])},getTotalItems:()=>t.reduce((e,t)=>e+t.quantity,0),getTotalPrice:()=>t.reduce((e,t)=>e+(t.discountPercentage?t.price*(1-t.discountPercentage/100):t.price)*t.quantity,0),getTotalPoints:()=>t.reduce((e,t)=>e+(t.points||0)*t.quantity,0)},children:e})}function l(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},28539:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},37349:(e,t,a)=>{"use strict";a.d(t,{S:()=>r});let r={LOGIN:"/api/Account/login",LOGOUT:"/api/Account/logout",REGISTER:"/api/Account/register",REFRESH_TOKEN:"/api/Account/refresh",USER_INFO:"/api/User/me",PROFILE_INFO:"/api/User/getprofileinfo",UPDATE_PROFILE:"/api/User/update-profile",PROFILE_PICTURE:"/api/User/profile-picture",DELETE_PROFILE_PICTURE:"/api/User/delete-profile-picture",USER_ADDRESSES:"/api/User/addresses",DELETE_ADDRESS:"/api/User/delete-address",SET_DEFAULT_ADDRESS:"/api/User/set-default-address",CREATE_ADDRESS:"/api/User/add-address",ADD_REFERENCE:"/api/User/Account/add-reference",MAKE_ADMIN:"/api/User/make-admin",TEST_AUTH:"/api/Account/test",DEBUG_CLAIMS:"/api/Account/debug-claims",GET_BRANDS:"/api/Products/brands",GET_SUBCATEGORIES:"/api/Products",CREATE_FULL_PRODUCT:"/api/Products/create-full-product",CREATE_DEALERSHIP_PRODUCT:"/api/Products/create-dealership-product",ADD_PRODUCT_IMAGE:"/api/Products/addimage",DELETE_PRODUCT_IMAGE:"/api/Products/image",REPLACE_PRODUCT_IMAGE:"/api/Products/image/replace",GET_ADMIN_PRODUCTS:"/api/Products/products-admin",DELETE_PRODUCT:"/api/Products/deleteproduct",GET_PRODUCTS:"/api/Products/getproducts",GET_PRODUCT_DETAIL:"/api/Products/productdetail",GET_PRODUCT_VARIANTS:"/api/Products/{productId}/variants",GET_ADMIN_PRODUCT_STATISTICS:"/api/Products/admin/product-statistics",GET_CATEGORIES_BY_BRAND:"/api/Products/categories-by-brand",GET_FEATURE_VALUES:"/api/Products/feature-values",GET_PRODUCT_FEATURES:"/api/Products/product-features",CREATE_BRAND:"/api/Products/createbrand",CREATE_CATEGORY:"/api/Products/createcategory",CREATE_SUBCATEGORY:"/api/Products/createsubcategory",CREATE_FEATURE_DEFINITION:"/api/Products/createdefinition",CREATE_FEATURE_VALUE:"/api/Products/createvalue",CREATE_SUBCATEGORY_FEATURE:"/api/Products/createsubfeature",CREATE_BRAND_CATEGORY:"/api/Products/brand-category",GET_CATEGORIES:"/api/Products/categories",GET_SUBCATEGORY_FEATURES:"/api/Products/subcategoryfeatures",GET_SUBCATEGORY_FEATURES_BY_ID:"/api/Products/subcategoryfeatures/{subCategoryId}",GET_FEATURE_VALUES_BY_DEFINITION_ID:"/api/Products/feature-values/{definitionId}",GET_PRODUCT_FEATURES_BY_PRODUCT_ID:"/api/Products/product-features/{productId}",GET_CATEGORIES_BY_BRAND_ID:"/api/Products/categories-by-brand/{brandId}",GET_SUBCATEGORIES_BY_CATEGORY:"/api/Products/{categoryId}/subcategories",GET_ALL_FEATURE_DEFINITIONS:"/api/Products/features",UPDATE_FULL_PRODUCT:"/api/Products/update-full-product",UPDATE_PRODUCT_STATUS:"/api/Products/updateproductstatus",GET_PRODUCT_MESSAGE:"/api/Products/productmessage",GET_USERS:"/api/User/getusers",GET_USER_ROLE_COUNTS:"/api/User/user-role-counts",GET_DISCOUNT_RATE:"/api/User/discount-rate",UPDATE_CART_TYPE:"/api/User/update-cart-type",GET_MY_PRODUCTS:"/api/Products/my-products",GET_MY_PRODUCT_STATISTICS:"/api/Products/myproductstats",UPDATE_SIMPLE_PRODUCT:"/api/Products/update-simple",GET_DEALERSHIP_PRODUCT_DETAIL:"/api/Products/dealership-product-detail",FILTER_PRODUCTS:"/api/catalog/products/filter",GET_REFERENCE_DATA:"/api/catalog/reference-data",GET_CATALOG_PRODUCT_DETAIL:"/api/catalog/product-detail",ADD_TO_CART:"/api/User/cart/add",GET_CART_ITEMS:"/api/User/cart/items",GET_CART_COUNT:"/api/User/cart/count",REMOVE_FROM_CART:"/api/User/cart/remove",UPDATE_CART_QUANTITY:"/api/User/cart/update-quantity"}},38429:(e,t,a)=>{"use strict";a.d(t,{N$:()=>d,Xk:()=>g,_c:()=>u,aO:()=>p,fB:()=>m});var r=a(8693),s=a(51423),o=a(54050),n=a(64298),l=a(87979),i=a(42243);let d={all:["addresses"],lists:()=>[...d.all,"list"],list:e=>[...d.lists(),{userId:e}],detail:e=>[...d.all,"detail",e],favorites:()=>[...d.all,"favorites"],search:e=>[...d.all,"search",{query:e}]},c=()=>{let e=(0,i.n)(),{data:t,isLoading:a}=(0,l.Py)(),r=e.user||t;return{isAuthReady:e.isAuthenticated&&!e.isLoading&&!!r?.id,user:r}},u=()=>{let{isAuthReady:e,user:t}=c();return(0,r.jE)(),(0,s.I)({queryKey:t?.id?d.list(t.id):["addresses","no-user"],queryFn:async()=>{if(!t?.id)return console.log("⚠️ useAddresses: No user ID available, returning empty array"),[];console.log("\uD83D\uDCCD Fetching addresses for user:",t.id);let e=await n.qd.getAddresses();if(!e.success){let t=Error(e.error||"Adresler y\xfcklenemedi");throw t.name="AddressLoadError",t}let a=(e.data||[]).sort((e,t)=>e.isDefault&&!t.isDefault?-1:!e.isDefault&&t.isDefault?1:e.id||t.id?e.id?t.id?e.id-t.id:-1:1:0);return console.log("\uD83D\uDCCD Addresses sorted (default first, then by ID):",a.map(e=>({id:e.id,title:e.title,isDefault:e.isDefault}))),a},enabled:e,staleTime:3e5,gcTime:6e5,refetchOnMount:!0,refetchOnWindowFocus:!1,refetchOnReconnect:!0,retry:(e,t)=>"AddressLoadError"===t.name?e<2:e<3,retryDelay:e=>Math.min(1e3*2**e,3e4)})},p=()=>{let e=(0,r.jE)(),t=(0,i.n)(),{data:a}=(0,l.Py)(),s=t.user||a;return(0,o.n)({mutationFn:async a=>{if(console.log("\uD83D\uDD04 TanStack Query: Creating address...",a),!t.isAuthenticated||!s?.id){console.error("❌ Authentication Error:",{isAuthenticated:t.isAuthenticated,hasUser:!!s,userId:s?.id});let e=Error("Kullanıcı oturum a\xe7mamış veya kullanıcı bilgileri y\xfcklenmemiş");throw e.name="AuthenticationError",e}let r=0===(e.getQueryData(d.list(s.id))||[]).length,o={...a,isDefault:!!r||a.isDefault};r&&console.log("\uD83C\uDFE0 İlk adres ekleniyor - otomatik varsayılan olarak ayarlandı");let l=await n.qd.createAddress(o);if(!l.success){let e=Error(l.error||"Adres eklenemedi");throw e.name="AddressCreateError",e}return console.log("✅ TanStack Query: Address created successfully"),l.data},onMutate:async t=>{let a=d.list(s?.id||0);await e.cancelQueries({queryKey:a});let r=e.getQueryData(a);if(r){let s=0===r.length,o={id:Date.now(),title:t.title,fullAddress:t.fullAddress,city:t.city,district:t.district,postalCode:t.postalCode,isDefault:!!s||t.isDefault};s&&console.log("\uD83C\uDFE0 Optimistic: İlk adres - otomatik varsayılan olarak ayarlandı");let n=[...r,o].sort((e,t)=>e.isDefault&&!t.isDefault?-1:!e.isDefault&&t.isDefault?1:e.id||t.id?e.id?t.id?e.id-t.id:-1:1:0);e.setQueryData(a,n),console.log("\uD83D\uDE80 Optimistic update: Address added to cache with smart sorting (default first)")}return{previousAddresses:r}},onSuccess:(t,a,r)=>{console.log("✅ TanStack Query: Address creation confirmed by server");let o=d.list(s?.id||0);e.setQueryData(o,(e=[])=>{let a=[...e.filter(e=>!("number"==typeof e.id&&e.id>Date.now()-1e4)),t].sort((e,t)=>e.isDefault&&!t.isDefault?-1:!e.isDefault&&t.isDefault?1:e.id||t.id?e.id?t.id?e.id-t.id:-1:1:0);return console.log("\uD83D\uDCCD Cache updated with smart sorted addresses:",a.map(e=>({id:e.id,title:e.title,isDefault:e.isDefault}))),a})},onError:(t,a,r)=>{if(console.error("❌ TanStack Query: Address creation failed:",t),r?.previousAddresses){let t=d.list(s?.id||0);e.setQueryData(t,r.previousAddresses),console.log("\uD83D\uDD04 Rollback: Optimistic update reverted")}},onSettled:()=>{let t=d.list(s?.id||0);e.invalidateQueries({queryKey:t})}})},m=e=>{let t=(0,r.jE)(),{data:a}=(0,l.Py)();return(0,o.n)({mutationFn:async e=>{console.log("\uD83D\uDD04 TanStack Query: Deleting address:",e);let t=await n.qd.deleteAddress(e,a?.id||0);if(!t.success){let e=Error(t.error||"Adres silinemedi");throw e.name="AddressDeleteError",e}return console.log("✅ TanStack Query: Address deleted successfully"),e},onMutate:async e=>{let r=d.list(a?.id||0);await t.cancelQueries({queryKey:r});let s=t.getQueryData(r),o=s?.find(t=>t.id===e),n=o?.isDefault,l=s?.filter(t=>t.id!==e)||[];if(s){let a=s.filter(t=>t.id!==e);if(n&&a.length>0){let e=a.sort((e,t)=>(e.id||0)-(t.id||0))[0];a=a.map(t=>({...t,isDefault:t.id===e.id})),console.log("\uD83C\uDFE0 Default address removed, setting next address as default:",e.title)}let o=a.sort((e,t)=>e.isDefault&&!t.isDefault?-1:!e.isDefault&&t.isDefault?1:(e.id||0)-(t.id||0));t.setQueryData(r,o),console.log("\uD83D\uDE80 Optimistic update: Address removed from cache with auto-default handling")}return{previousAddresses:s,removedAddress:o,wasDefault:n,remainingAddresses:l}},onSuccess:(t,a,r)=>{if(console.log("✅ TanStack Query: Address deletion confirmed by server"),r?.wasDefault&&r?.remainingAddresses.length>0){let t=r.remainingAddresses.sort((e,t)=>(e.id||0)-(t.id||0))[0];console.log("\uD83C\uDFE0 Default address was removed, next default should be:",t.title),e&&t&&e(t,r.removedAddress)}},onError:(e,r,s)=>{if(console.error("❌ TanStack Query: Address deletion failed:",e),s?.previousAddresses){let e=d.list(a?.id||0);t.setQueryData(e,s.previousAddresses),console.log("\uD83D\uDD04 Rollback: Deleted address restored to cache")}},onSettled:()=>{let e=d.list(a?.id||0);t.invalidateQueries({queryKey:e})}})},g=()=>{let e=(0,r.jE)(),{data:t}=(0,l.Py)();return(0,o.n)({mutationFn:async e=>{console.log("\uD83D\uDD04 TanStack Query: Setting address as default:",e);let t=await n.qd.setDefaultAddress(e);if(!t.success){let e=Error(t.error||"Varsayılan adres ayarlanamadı");throw e.name="SetDefaultAddressError",e}return console.log("✅ TanStack Query: Address set as default successfully"),t.data},onMutate:async a=>{let r=d.list(t?.id||0);await e.cancelQueries({queryKey:r});let s=e.getQueryData(r);if(s){let t=s.map(e=>({...e,isDefault:e.id===a})).sort((e,t)=>e.isDefault&&!t.isDefault?-1:!e.isDefault&&t.isDefault?1:e.id||t.id?e.id?t.id?e.id-t.id:-1:1:0);e.setQueryData(r,t),console.log("\uD83D\uDE80 Optimistic update: Default address changed with smart sorting")}return{previousAddresses:s}},onSuccess:(a,r,s)=>{console.log("✅ TanStack Query: Set default address confirmed by server");let o=d.list(t?.id||0);e.setQueryData(o,(e=[])=>{let t=e.map(e=>({...e,isDefault:e.id===r})).sort((e,t)=>e.isDefault&&!t.isDefault?-1:!e.isDefault&&t.isDefault?1:e.id||t.id?e.id?t.id?e.id-t.id:-1:1:0);return console.log("\uD83D\uDCCD Cache updated with new default address (smart sorted):",t.map(e=>({id:e.id,title:e.title,isDefault:e.isDefault}))),t})},onError:(a,r,s)=>{if(console.error("❌ TanStack Query: Set default address failed:",a),s?.previousAddresses){let a=d.list(t?.id||0);e.setQueryData(a,s.previousAddresses),console.log("\uD83D\uDD04 Rollback: Default address change reverted")}},onSettled:()=>{let a=d.list(t?.id||0);e.invalidateQueries({queryKey:a})}})}},42112:(e,t,a)=>{"use strict";a.d(t,{w:()=>o});var r=a(26787),s=a(59350);let o=(0,r.v)()((0,s.Zr)(e=>({isCustomerPrice:!1,setIsCustomerPrice:t=>e({isCustomerPrice:t}),resetCustomerPrice:()=>e({isCustomerPrice:!1}),_hasHydrated:!1,setHasHydrated:t=>e({_hasHydrated:t})}),{name:"customer-price-store",onRehydrateStorage:()=>e=>{e?.setHasHydrated(!0)}}))},42243:(e,t,a)=>{"use strict";a.d(t,{n:()=>o});var r=a(26787),s=a(59350);let o=(0,r.v)()((0,s.lt)(e=>({user:null,isLoading:!1,isAuthenticated:!1,error:null,clearError:()=>e({error:null}),setLoading:t=>e({isLoading:t})}),{name:"auth-store"}))},44263:()=>{},45840:(e,t,a)=>{"use strict";a.d(t,{M:()=>n,P:()=>l});var r=a(51423),s=a(64298),o=a(15908);let n={all:["discountRate"],user:e=>[...n.all,"user",e]},l=()=>{let{user:e,isAuthenticated:t}=(0,o.A)();return(0,r.I)({queryKey:n.user(e?.id||null),queryFn:async()=>{let e=await s.Dv.getDiscountRate();if(!e.success)throw Error(e.error||"İndirim oranı alınamadı");return e.data},enabled:t&&!!e?.id,staleTime:3e5,refetchOnWindowFocus:!1,refetchOnMount:!0,retry:(e,t)=>t?.response?.status!==401&&e<2,retryDelay:e=>Math.min(1e3*2**e,3e4)})}},46771:(e,t,a)=>{Promise.resolve().then(a.bind(a,55488))},53197:(e,t,a)=>{"use strict";a.d(t,{V:()=>r});let r=(0,a(26787).v)(e=>({status:"online",setStatus:t=>e({status:t})}))},55488:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>P});var r=a(60687),s=a(84369),o=a.n(s),n=a(39091),l=a(8693),i=a(43210);a(44263);var d=a(85814),c=a.n(d),u=a(30474),p=a(26001),m=a(15908),g=a(83515);function h(){let[e,t]=(0,i.useState)(!1),[a,s]=(0,i.useState)(!1),[o,n]=(0,i.useState)(!1),{user:l,isLoading:d,isAuthenticated:h,logout:y}=(0,m.A)(),{data:f=0,isLoading:x}=(0,g.$9)(),k=(0,i.useRef)(null),b=(0,i.useRef)(null);return(0,r.jsx)(p.P.header,{className:"bg-white shadow-md sticky top-0 z-50 ",initial:{y:-100},animate:{y:0},transition:{type:"spring",stiffness:100,damping:20},children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-20",children:[(0,r.jsx)(c(),{href:"/",className:"flex items-center",children:(0,r.jsxs)(p.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:17},className:"flex items-center",children:[(0,r.jsx)(u.default,{src:"/assets/sayglobal_logo.png",alt:"Say Global Logo",width:180,height:72,className:"h-16 w-auto object-contain",priority:!0}),(0,r.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 text-transparent bg-clip-text hidden sm:block",children:"Say Global"})]})}),(0,r.jsx)(p.P.button,{type:"button",className:"md:hidden text-gray-600 hover:text-gray-900 focus:outline-none",onClick:()=>t(!e),whileTap:{scale:.9},children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),(0,r.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[[{text:"Anasayfa",route:"/"},{text:"\xdcr\xfcnler",route:"/products"},{text:"Duyurular",route:"/announcements"}].map((e,t)=>(0,r.jsx)(p.P.div,{whileHover:{y:-2},whileTap:{y:0},children:(0,r.jsxs)(c(),{href:e.route,className:"text-gray-700 hover:text-purple-600 font-medium relative overflow-hidden group",children:[e.text,(0,r.jsx)("span",{className:"absolute bottom-0 left-0 w-full h-0.5 bg-purple-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out"})]})},t)),(0,r.jsxs)("div",{className:"relative",ref:k,children:[(0,r.jsx)(p.P.button,{className:"text-gray-700 hover:text-purple-600 font-medium focus:outline-none p-2 rounded-full hover:bg-gray-100",onClick:()=>s(!a),whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),a&&(0,r.jsx)(p.P.div,{className:"absolute right-0 mt-2 w-72 bg-white rounded-lg shadow-xl p-3 border border-gray-100",initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{duration:.2},children:(0,r.jsxs)("div",{className:"flex items-center bg-gray-50 rounded-md",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 ml-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),(0,r.jsx)("input",{type:"text",placeholder:"\xdcr\xfcn ara...",className:"w-full px-3 py-2 text-black bg-transparent border-none focus:outline-none focus:ring-0"})]})})]})]}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,r.jsx)(p.P.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsxs)(c(),{href:"/cart",className:"text-gray-700 hover:text-purple-600 relative p-2",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})}),(0,r.jsx)("span",{className:"absolute top-0 right-0 md:top-4 md:left-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:x?"...":f})]})}),(0,r.jsxs)("div",{className:"relative",ref:b,children:[(0,r.jsxs)(p.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>n(!o),className:"text-gray-700 hover:text-purple-600 p-2 flex items-center space-x-2",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),d?(0,r.jsx)("div",{className:"hidden md:block w-20 h-4 bg-gray-200 rounded animate-pulse"}):h&&l?(0,r.jsxs)("span",{className:"hidden md:block text-sm font-medium",children:[l.firstName," ",l.lastName]}):null]}),o&&(0,r.jsx)(p.P.div,{className:"absolute right-0 mt-2 py-2 w-56 bg-white rounded-lg shadow-xl z-20 border border-gray-100",initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{duration:.2},children:h&&l?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"px-4 py-2 border-b border-gray-100",children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[l.firstName," ",l.lastName]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:l.email}),(0,r.jsx)("span",{className:`inline-block mt-1 px-2 py-1 rounded-full text-xs font-medium ${"admin"===l.role?"bg-red-100 text-red-800":"dealership"===l.role?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"}`,children:"admin"===l.role?"Y\xf6netici":"dealership"===l.role?"Satıcı":"M\xfcşteri"})]}),(0,r.jsx)(c(),{href:"/account",className:"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600",onClick:()=>n(!1),children:"Hesabım"}),(0,r.jsx)(c(),{href:"/account?tab=orders",className:"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600",onClick:()=>n(!1),children:"Siparişlerim"}),(0,r.jsx)(c(),{href:"/account?tab=favorites",className:"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600",onClick:()=>n(!1),children:"Favorilerim"}),("customer"===l.role||"dealership"===l.role||"admin"===l.role)&&(0,r.jsx)(c(),{href:"/panel",className:"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600",onClick:()=>n(!1),children:"Kontrol Paneli"}),"admin"===l.role&&(0,r.jsx)(c(),{href:"/admin",className:"block px-4 py-2 text-gray-700 hover:bg-red-50 hover:text-red-600",onClick:()=>n(!1),children:"Y\xf6netici Paneli"}),(0,r.jsx)("div",{className:"border-t border-gray-100 my-2"}),(0,r.jsx)("button",{onClick:()=>{y(),t(!1),n(!1)},className:"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-600 rounded-md",children:"\xc7ıkış Yap"})]}):(0,r.jsxs)("div",{className:"px-2 py-1 space-y-2",children:[(0,r.jsx)(c(),{href:"/login",className:"block w-full text-center rounded-md bg-gradient-to-r from-purple-600 to-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-all duration-300 hover:from-purple-700 hover:to-indigo-700",onClick:()=>n(!1),children:"Giriş Yap"}),(0,r.jsx)(c(),{href:"/register",className:"block w-full text-center rounded-md bg-white px-4 py-2 text-sm font-medium text-gray-800 border border-gray-300 transition-all duration-300 hover:bg-gray-100",onClick:()=>n(!1),children:"Kayıt Ol"})]})})]}),h&&l&&"customer"===l.role&&!l.isDealershipApproved&&(0,r.jsx)(p.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:17},children:(0,r.jsxs)(c(),{href:"/become-dealer",className:"bg-gradient-to-r from-green-600 to-emerald-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow duration-300 flex items-center space-x-1",children:[(0,r.jsx)("span",{children:"Satıcı Ol"}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})]})}),h&&l&&"admin"===l.role&&(0,r.jsx)(p.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:17},children:(0,r.jsxs)(c(),{href:"/admin",className:"bg-gradient-to-r from-red-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow duration-300 flex items-center space-x-1",children:[(0,r.jsx)("span",{children:"Y\xf6netici Paneli"}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]})]}),e&&(0,r.jsx)(p.P.div,{className:"md:hidden pb-4",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,r.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,r.jsx)(c(),{href:"/",className:"text-gray-700 hover:text-purple-600 font-medium py-2",onClick:()=>t(!1),children:"Anasayfa"}),(0,r.jsx)(c(),{href:"/products",className:"text-gray-700 hover:text-purple-600 font-medium py-2",onClick:()=>t(!1),children:"\xdcr\xfcnler"}),(0,r.jsx)(c(),{href:"/announcements",className:"text-gray-700 hover:text-purple-600 font-medium py-2",onClick:()=>t(!1),children:"Duyurular"}),(0,r.jsx)("div",{className:"py-2",children:(0,r.jsxs)("div",{className:"flex items-center bg-gray-50 rounded-md",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 ml-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),(0,r.jsx)("input",{type:"text",placeholder:"\xdcr\xfcn ara...",className:"w-full px-3 py-2 bg-transparent border-none focus:outline-none focus:ring-0 text-black"})]})}),(0,r.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,r.jsxs)(c(),{href:"/cart",className:"text-gray-700 hover:text-purple-600 relative p-3 bg-gray-50 rounded-lg flex items-center justify-center",onClick:()=>t(!1),children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-7 w-7",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})}),(0,r.jsx)("span",{className:"absolute -top-1 -right-1 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-medium shadow-lg",children:x?"...":f})]}),(0,r.jsxs)("button",{onClick:()=>n(!o),className:"text-gray-700 hover:text-purple-600 flex items-center space-x-3 p-2 bg-gray-50 rounded-lg flex-1 ml-4",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),d?(0,r.jsx)("div",{className:"w-20 h-4 bg-gray-200 rounded animate-pulse"}):h&&l?(0,r.jsxs)("span",{className:"text-sm font-medium truncate",children:[l.firstName," ",l.lastName]}):(0,r.jsx)("span",{className:"text-sm font-medium",children:"Giriş Yap"})]})]}),o&&(0,r.jsx)(p.P.div,{className:"bg-gray-50 rounded-lg p-4 mt-3",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:h&&l?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-3 mb-3",children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[l.firstName," ",l.lastName]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:l.email}),(0,r.jsx)("span",{className:`inline-block mt-1 px-2 py-1 rounded-full text-xs font-medium ${"admin"===l.role?"bg-red-100 text-red-800":"dealership"===l.role?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"}`,children:"admin"===l.role?"Y\xf6netici":"dealership"===l.role?"Satıcı":"M\xfcşteri"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c(),{href:"/account",className:"block py-2 text-gray-700 hover:text-purple-600 font-medium",onClick:()=>{t(!1),n(!1)},children:"Hesabım"}),(0,r.jsx)(c(),{href:"/account?tab=orders",className:"block py-2 text-gray-700 hover:text-purple-600 font-medium",onClick:()=>{t(!1),n(!1)},children:"Siparişlerim"}),(0,r.jsx)(c(),{href:"/account?tab=favorites",className:"block py-2 text-gray-700 hover:text-purple-600 font-medium",onClick:()=>{t(!1),n(!1)},children:"Favorilerim"}),("customer"===l.role||"dealership"===l.role||"admin"===l.role)&&(0,r.jsx)(c(),{href:"/panel",className:"block py-2 text-gray-700 hover:text-purple-600 font-medium",onClick:()=>{t(!1),n(!1)},children:"Kontrol Paneli"}),"admin"===l.role&&(0,r.jsx)(c(),{href:"/admin",className:"block py-2 text-gray-700 hover:text-red-600 font-medium",onClick:()=>{t(!1),n(!1)},children:"Y\xf6netici Paneli"}),(0,r.jsx)("button",{onClick:()=>{y(),t(!1),n(!1)},className:"w-full text-left py-2 text-red-600 hover:text-red-700 font-medium",children:"\xc7ıkış Yap"})]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c(),{href:"/login",className:"block w-full rounded-lg bg-gradient-to-r from-purple-600 to-indigo-600 px-4 py-3 text-center text-base font-semibold text-white shadow-md transition-all duration-300 hover:from-purple-700 hover:to-indigo-700",onClick:()=>{t(!1),n(!1)},children:"Giriş Yap"}),(0,r.jsx)(c(),{href:"/register",className:"block w-full rounded-lg bg-white px-4 py-3 text-center text-base font-semibold text-gray-800 border border-gray-300 transition-all duration-300 hover:bg-gray-100",onClick:()=>{t(!1),n(!1)},children:"Kayıt Ol"})]})}),h&&l&&"customer"===l.role&&!l.isDealershipApproved&&(0,r.jsx)(c(),{href:"/become-dealer",className:"bg-gradient-to-r from-green-600 to-emerald-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow duration-300 text-center",onClick:()=>t(!1),children:"Satıcı Ol"}),h&&l&&"admin"===l.role&&(0,r.jsx)(c(),{href:"/admin",className:"bg-gradient-to-r from-red-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow duration-300 text-center",onClick:()=>t(!1),children:"Y\xf6netici Paneli"})]})})]})})}function y(){let e={initial:{y:20,opacity:0},animate:{y:0,opacity:1,transition:{duration:.6}}};return(0,r.jsx)("footer",{className:"bg-gradient-to-br from-gray-900 to-gray-800 text-white pt-16 pb-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)(p.P.div,{className:"grid grid-cols-1 md:grid-cols-4 gap-10",variants:{animate:{transition:{staggerChildren:.1}}},initial:"initial",whileInView:"animate",viewport:{once:!0,amount:.3},children:[(0,r.jsxs)(p.P.div,{variants:e,className:"col-span-1",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-indigo-400 text-transparent bg-clip-text",children:"Say Global"}),(0,r.jsx)("p",{className:"text-gray-300 mb-6 text-sm leading-relaxed",children:"Sağlıklı yaşam \xfcr\xfcnleri ve kozmetik alanında T\xfcrkiye'nin lider markası. Doğal i\xe7erikli \xfcr\xfcnlerle sağlığınıza sağlık katın."}),(0,r.jsx)("div",{className:"flex space-x-4",children:[{name:"Facebook",path:"M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z"},{name:"Instagram",path:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"},{name:"Twitter",path:"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"},{name:"YouTube",path:"M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"}].map((e,t)=>(0,r.jsx)(p.P.a,{href:"#",className:"text-gray-400 hover:text-white transition-colors duration-300 transform hover:scale-110",whileHover:{scale:1.2,rotate:5},whileTap:{scale:.9},children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:e.path})})},t))})]}),(0,r.jsxs)(p.P.div,{variants:e,className:"col-span-1",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-6 relative inline-block",children:["Hızlı Linkler",(0,r.jsx)("span",{className:"absolute bottom-0 left-0 w-1/2 h-0.5 bg-gradient-to-r from-purple-400 to-indigo-400"})]}),(0,r.jsx)("ul",{className:"space-y-3",children:[{name:"Anasayfa",path:"/"},{name:"\xdcr\xfcnler",path:"/products"},{name:"Duyurular",path:"/announcements"},{name:"Distrib\xfct\xf6r Paneli",path:"/dashboard"},{name:"Giriş Yap",path:"/login"},{name:"Kayıt Ol",path:"/register"}].map((e,t)=>(0,r.jsx)(p.P.li,{whileHover:{x:5},transition:{type:"spring",stiffness:400,damping:10},children:(0,r.jsxs)(c(),{href:e.path,className:"text-gray-300 hover:text-white group flex items-center",children:[(0,r.jsx)("svg",{className:"h-3 w-3 mr-2 text-purple-400 opacity-0 group-hover:opacity-100 transition-opacity",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),e.name]})},t))})]}),(0,r.jsxs)(p.P.div,{variants:e,className:"col-span-1",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-6 relative inline-block",children:["Kategoriler",(0,r.jsx)("span",{className:"absolute bottom-0 left-0 w-1/2 h-0.5 bg-gradient-to-r from-purple-400 to-indigo-400"})]}),(0,r.jsx)("ul",{className:"space-y-3",children:[{name:"Cilt Bakımı",path:"/products?category=skin-care"},{name:"Sa\xe7 Bakımı",path:"/products?category=hair-care"},{name:"V\xfccut Bakımı",path:"/products?category=body-care"},{name:"Takviye \xdcr\xfcnler",path:"/products?category=supplements"},{name:"Parf\xfcm & Deodorant",path:"/products?category=perfume-deodorant"}].map((e,t)=>(0,r.jsx)(p.P.li,{whileHover:{x:5},transition:{type:"spring",stiffness:400,damping:10},children:(0,r.jsxs)(c(),{href:e.path,className:"text-gray-300 hover:text-white group flex items-center",children:[(0,r.jsx)("svg",{className:"h-3 w-3 mr-2 text-purple-400 opacity-0 group-hover:opacity-100 transition-opacity",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),e.name]})},t))})]}),(0,r.jsxs)(p.P.div,{variants:e,className:"col-span-1",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-6 relative inline-block",children:["İletişim",(0,r.jsx)("span",{className:"absolute bottom-0 left-0 w-1/2 h-0.5 bg-gradient-to-r from-purple-400 to-indigo-400"})]}),(0,r.jsxs)("ul",{className:"space-y-4",children:[(0,r.jsxs)(p.P.li,{className:"flex items-start",whileHover:{scale:1.02},children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-3 text-purple-400 flex-shrink-0 mt-0.5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,r.jsxs)("span",{className:"text-gray-300 text-sm leading-relaxed",children:["Atat\xfcrk Cad. No:123, 34100",(0,r.jsx)("br",{}),"Kadık\xf6y / İstanbul"]})]}),(0,r.jsxs)(p.P.li,{className:"flex items-center",whileHover:{scale:1.02},children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-3 text-purple-400 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),(0,r.jsx)("span",{className:"text-gray-300",children:"0212 123 45 67"})]}),(0,r.jsxs)(p.P.li,{className:"flex items-center",whileHover:{scale:1.02},children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-3 text-purple-400 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),(0,r.jsx)("span",{className:"text-gray-300",children:"<EMAIL>"})]}),(0,r.jsx)(p.P.li,{whileHover:{scale:1.05},children:(0,r.jsx)(p.P.div,{className:"mt-6 bg-gradient-to-r from-purple-600 to-indigo-600 p-0.5 rounded-lg",whileHover:{scale:1.02},children:(0,r.jsxs)("div",{className:"bg-gray-900 rounded-md p-4",children:[(0,r.jsx)("p",{className:"text-sm text-gray-300 mb-3",children:"B\xfcltenimize abone olun"}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("input",{type:"email",placeholder:"E-posta adresiniz",className:"flex-1 bg-gray-800 border-none rounded-l-md text-sm px-3 py-2 focus:outline-none focus:ring-1 focus:ring-purple-500 text-gray-100"}),(0,r.jsx)("button",{className:"bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-r-md px-3 py-2 text-sm font-medium",children:"Abone Ol"})]})]})})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-700 mt-12 pt-8",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,r.jsxs)(p.P.p,{className:"text-gray-400 text-sm mb-4 md:mb-0",initial:{opacity:0},whileInView:{opacity:1},transition:{delay:.5},children:["\xa9 ",new Date().getFullYear()," Say Global. T\xfcm hakları saklıdır."]}),(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-4",children:[{name:"Gizlilik Politikası",path:"/privacy-policy"},{name:"Kullanım Koşulları",path:"/terms-of-use"},{name:"\xc7erez Politikası",path:"/cookie-policy"}].map((e,t)=>(0,r.jsx)(p.P.div,{whileHover:{y:-2},whileTap:{y:0},children:(0,r.jsxs)(c(),{href:e.path,className:"text-gray-400 hover:text-white text-sm relative overflow-hidden group",children:[e.name,(0,r.jsx)("span",{className:"absolute bottom-0 top-6 left-0 w-full h-0.5 bg-purple-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out "})]})},t))})]})})]})})}var f=a(28253),x=a(83820),k=a(88920),b=a(16189),w=a(76180),v=a.n(w),A=a(53197);let E={INITIAL_DELAY:1e3,MAX_DELAY:3e4,MAX_ATTEMPTS:10,BACKOFF_MULTIPLIER:2},S=()=>(0,r.jsxs)("div",{className:"jsx-c1e7004345d34c2d network-status-banner",children:[(0,r.jsxs)("div",{className:"jsx-c1e7004345d34c2d network-status-content",children:[(0,r.jsx)("div",{className:"jsx-c1e7004345d34c2d network-status-icon",children:(0,r.jsx)("span",{className:"jsx-c1e7004345d34c2d network-status-spinner"})}),(0,r.jsxs)("div",{className:"jsx-c1e7004345d34c2d network-status-text",children:[(0,r.jsx)("div",{className:"jsx-c1e7004345d34c2d network-status-title",children:"İnternet bağlantınızda sorun yaşanıyor"}),(0,r.jsx)("div",{className:"jsx-c1e7004345d34c2d network-status-subtitle",children:"Bağlantı kontrol ediliyor..."})]})]}),(0,r.jsx)(v(),{id:"c1e7004345d34c2d",children:".network-status-banner.jsx-c1e7004345d34c2d{position:fixed;top:0;left:0;width:100%;background:-webkit-linear-gradient(315deg,#ffc107 0%,#ffb300 100%);background:-moz-linear-gradient(315deg,#ffc107 0%,#ffb300 100%);background:-o-linear-gradient(315deg,#ffc107 0%,#ffb300 100%);background:linear-gradient(135deg,#ffc107 0%,#ffb300 100%);color:#212529;z-index:9999;-webkit-box-shadow:0 2px 8px rgba(0,0,0,.1);-moz-box-shadow:0 2px 8px rgba(0,0,0,.1);box-shadow:0 2px 8px rgba(0,0,0,.1);-webkit-animation:slideDown.3s ease-out;-moz-animation:slideDown.3s ease-out;-o-animation:slideDown.3s ease-out;animation:slideDown.3s ease-out}.network-status-content.jsx-c1e7004345d34c2d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;padding:12px 20px;gap:12px}.network-status-icon.jsx-c1e7004345d34c2d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.network-status-spinner.jsx-c1e7004345d34c2d{width:20px;height:20px;border:2px solid#212529;border-top:2px solid transparent;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;-webkit-animation:spin 1s linear infinite;-moz-animation:spin 1s linear infinite;-o-animation:spin 1s linear infinite;animation:spin 1s linear infinite}.network-status-text.jsx-c1e7004345d34c2d{text-align:center}.network-status-title.jsx-c1e7004345d34c2d{font-size:14px;font-weight:600;margin-bottom:2px}.network-status-subtitle.jsx-c1e7004345d34c2d{font-size:12px;font-weight:400;opacity:.8}@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes slideDown{from{-webkit-transform:translatey(-100%);transform:translatey(-100%);opacity:0}to{-webkit-transform:translatey(0);transform:translatey(0);opacity:1}}@-moz-keyframes slideDown{from{-moz-transform:translatey(-100%);transform:translatey(-100%);opacity:0}to{-moz-transform:translatey(0);transform:translatey(0);opacity:1}}@-o-keyframes slideDown{from{-o-transform:translatey(-100%);transform:translatey(-100%);opacity:0}to{-o-transform:translatey(0);transform:translatey(0);opacity:1}}@keyframes slideDown{from{-webkit-transform:translatey(-100%);-moz-transform:translatey(-100%);-o-transform:translatey(-100%);transform:translatey(-100%);opacity:0}to{-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0);opacity:1}}@media(max-width:768px){.network-status-content.jsx-c1e7004345d34c2d{padding:10px 16px;gap:8px}.network-status-title.jsx-c1e7004345d34c2d{font-size:13px}.network-status-subtitle.jsx-c1e7004345d34c2d{font-size:11px}}"})]}),T=()=>{let{status:e,setStatus:t}=(0,A.V)(),[a,s]=(0,i.useState)(0),[o,n]=(0,i.useState)(0),l=(0,i.useRef)(null),d=(0,i.useRef)(null);(0,i.useEffect)(()=>{(async()=>{navigator.onLine?(console.log("\uD83C\uDF10 Browser online - status kontrol ediliyor"),"online"!==e&&await u()&&(console.log("✅ Network connectivity confirmed - status online yapılıyor"),t("online"))):(console.log("\uD83C\uDF10 Browser offline - status offline yapılıyor"),t("offline"))})()},[]),(0,i.useEffect)(()=>{let e=()=>{console.log("\uD83C\uDF10 Browser online event detected"),t("online")},a=()=>{console.log("\uD83C\uDF10 Browser offline event detected"),t("offline")};return window.addEventListener("online",e),window.addEventListener("offline",a),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",a)}},[t]);let c=e=>Math.min(E.INITIAL_DELAY*Math.pow(E.BACKOFF_MULTIPLIER,e),E.MAX_DELAY),u=async()=>{try{let e=new AbortController,t=setTimeout(()=>e.abort(),5e3),a=await fetch(window.location.origin,{method:"HEAD",signal:e.signal,cache:"no-cache"});return clearTimeout(t),a.ok||a.status<500}catch{return!1}},p=e=>{n(e);let t=()=>{n(e=>e<=1?0:(d.current=setTimeout(t,1e3),e-1))};d.current=setTimeout(t,1e3)},m=async()=>{let e=a+1;s(e),console.log(`🔄 Yeniden bağlanma denemesi ${e}/${E.MAX_ATTEMPTS}`);try{if(await u()){console.log("✅ Bağlantı başarılı, online moda ge\xe7iliyor."),t("online"),s(0);return}if(e>=E.MAX_ATTEMPTS){console.log("❌ Maksimum deneme sayısına ulaşıldı, offline modda kalınıyor."),t("offline"),s(0);return}let a=c(e),r=Math.ceil(a/1e3);console.log(`❌ Bağlantı denemesi başarısız, ${r}s sonra yeniden denenecek.`),p(r),l.current=setTimeout(m,a)}catch(a){if(console.log("❌ Bağlantı kontrol hatası:",a),e<E.MAX_ATTEMPTS){let t=c(e);p(Math.ceil(t/1e3)),l.current=setTimeout(m,t)}else t("offline"),s(0)}};return((0,i.useEffect)(()=>{let t=()=>{l.current&&(clearTimeout(l.current),l.current=null),d.current&&(clearTimeout(d.current),d.current=null)};return"reconnecting"===e?(s(0),m()):(t(),s(0),n(0)),t},[e]),"reconnecting"!==e)?null:(0,r.jsx)(S,{})};var C=a(75068);function D(){let e=(0,C.nb)(),{closeRegisterSuccessModal:t}=(0,C.QR)(),a=(0,b.useRouter)(),[s,o]=(0,i.useState)(0),n=()=>{t(),a.push("/login")};return(0,r.jsx)(k.N,{children:e&&(0,r.jsxs)(p.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:n,children:[(0,r.jsx)("div",{className:"absolute inset-0"}),(0,r.jsxs)(p.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.7,opacity:0,y:50},transition:{type:"spring",stiffness:300,damping:25,duration:.5},onClick:e=>e.stopPropagation(),children:[(0,r.jsx)(p.P.button,{whileHover:{scale:1.1},whileTap:{scale:.95},onClick:n,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})}),(0,r.jsx)("div",{className:"flex justify-center mb-6",children:(0,r.jsx)(p.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1,duration:.3,type:"spring",stiffness:300},className:"w-20 h-20 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(p.P.svg,{initial:{pathLength:0},animate:{pathLength:1},transition:{delay:.2,duration:.5,ease:"easeInOut"},className:"w-12 h-12 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)(p.P.path,{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})})}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(p.P.h2,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.15,duration:.3},className:"text-2xl font-bold text-gray-800 mb-4",children:"Hesabınız Başarıyla Oluşturuldu!"}),(0,r.jsx)(p.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2,duration:.3},className:"text-gray-600 mb-6 leading-relaxed",children:"Kayıt işleminiz tamamlandı. Şimdi giriş yaparak SayGlobal platformunu kullanmaya başlayabilirsiniz."}),(0,r.jsxs)(p.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.25,duration:.3},className:"flex flex-col gap-3",children:[(0,r.jsx)(p.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:n,className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transition-all duration-300",children:"Giriş Yap"}),(0,r.jsx)(p.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:n,className:"w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-6 rounded-lg transition-all duration-300",children:"Kapat"})]})]}),(0,r.jsx)(p.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.35,duration:.3},className:"mt-6",children:(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 overflow-hidden",children:(0,r.jsx)(p.P.div,{className:"h-full bg-gradient-to-r from-purple-600 to-indigo-600 rounded-full",initial:{width:"0%"},animate:{width:`${s}%`},transition:{duration:.1,ease:"linear"}})})}),(0,r.jsx)(p.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4,duration:.3},className:"mt-3 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[Math.ceil((100-s)/20)," saniye sonra otomatik olarak giriş sayfasına y\xf6nlendirileceksiniz."]})})]})]})})}function P({children:e}){let t=(0,b.usePathname)(),[a]=(0,i.useState)(()=>new n.E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:3,refetchOnWindowFocus:!1},mutations:{retry:1}}}));return(0,r.jsx)("html",{lang:"tr",className:"scroll-smooth",children:(0,r.jsx)("body",{className:`${o().className} bg-gray-50 min-h-screen flex flex-col`,children:(0,r.jsxs)(l.Ht,{client:a,children:[(0,r.jsxs)(m.O,{children:[(0,r.jsx)(T,{}),(0,r.jsx)(f.e,{children:(0,r.jsxs)(x.H,{children:[(0,r.jsx)(h,{}),(0,r.jsx)(k.N,{mode:"wait",children:(0,r.jsx)(p.P.main,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:.3},className:"flex-grow",children:e},t)}),(0,r.jsx)(y,{}),(0,r.jsx)(D,{})]})})]}),!1]})})})}},64298:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>p,CV:()=>y,Dv:()=>g,jU:()=>h,qd:()=>m});var r=a(51060),s=a(75550),o=a(37349),n=a(53197);let l=e=>{if("undefined"==typeof document)return null;let t=`; ${document.cookie}`.split(`; ${e}=`);return 2===t.length&&t.pop()?.split(";").shift()||null},i=r.A.create({baseURL:"https://api.sayglobalweb.com",headers:{"Content-Type":"application/json",Accept:"application/json"},timeout:3e4,withCredentials:!0});(0,s.Ay)(i,{retries:3,retryCondition:e=>(!e.response?.status||!(e.response.status>=400)||!(e.response.status<500))&&(s.Ay.isNetworkError(e)||s.Ay.isIdempotentRequestError(e)),retryDelay:(e,t)=>(console.warn(`[axios-retry] Request failed: ${t.message}. Retry attempt #${e}...`),1e3*Math.pow(2,e-1))});let d=!1,c=[],u=(e,t=null)=>{c.forEach(({resolve:a,reject:r})=>{e?r(e):a(t)}),c=[]};o.S.LOGIN,o.S.REGISTER,o.S.REFRESH_TOKEN,o.S.LOGOUT,i.interceptors.request.use(e=>{console.log("\uD83D\uDCE1 API Request başlıyor:",e.method?.toUpperCase(),e.url),console.log("\uD83C\uDF6A withCredentials:",e.withCredentials);let t=l("AccessToken");return t?(e.headers.Authorization=`Bearer ${t}`,console.log("\uD83D\uDD11 Authorization header eklendi (JS readable cookie)")):console.log("\uD83D\uDD11 Cookie HttpOnly olabilir - browser otomatik g\xf6nderecek"),e},e=>Promise.reject(e)),i.interceptors.response.use(e=>("online"!==n.V.getState().status&&(console.log("✅ API başarılı - Network status online'a \xe7ekiliyor"),n.V.getState().setStatus("online")),e),async e=>{let t=e.config;if((t.url?.includes(o.S.LOGIN)||t.url?.includes(o.S.REFRESH_TOKEN))&&e.response?.status===401)return Promise.reject(e);if(e.response?.status===401&&!t._retry){if(t._retry=!0,d)return new Promise((e,t)=>{c.push({resolve:e,reject:t})}).then(()=>i(t)).catch(e=>Promise.reject(e));d=!0;let a=async(e=0)=>{try{return await i.get(o.S.REFRESH_TOKEN)}catch(o){let t=r.A.isAxiosError(o)&&!o.response,s=r.A.isAxiosError(o)&&o.response?.status===401;if((t||s)&&e<2){console.log(`🔄 Refresh token denemesi ${e+1}/3 başarısız (${t?"Ağ hatası":"401 - Timeout olabilir"}). ${e<1?"Tekrar deneniyor...":"Son deneme yapılıyor..."}`);let r=1e3*Math.pow(2,e);return await new Promise(e=>setTimeout(e,r)),a(e+1)}throw o}};try{return await a(),u(null),d=!1,i(t)}catch(t){if(d=!1,r.A.isAxiosError(t)&&t.response?.status===401)return console.log("\uD83D\uDEAA 3 deneme sonrasında da 401 hatası. Kullanıcı ger\xe7ekten giriş yapmamış."),u(e,null),Promise.reject(e);return u(t,null),r.A.isAxiosError(t)&&!t.response?(console.log("\uD83D\uDD0C 3 deneme sonrasında refresh token yenilenemedi. Ağ bağlantısı sorunlu."),n.V.getState().setStatus("reconnecting")):(console.log("\uD83D\uDCA5 Beklenmedik hata sonrası auth:force-logout event g\xf6nderiliyor"),window.dispatchEvent(new CustomEvent("auth:force-logout"))),Promise.reject(t)}}return r.A.isAxiosError(e)&&!e.response?(console.log("\uD83D\uDD0C Genel ağ hatası algılandı. Yeniden bağlanma moduna ge\xe7iliyor."),n.V.getState().setStatus("reconnecting"),new Promise(()=>{})):Promise.reject(e)});let p=i,m={async getAddresses(){try{console.log("\uD83D\uDCCD Adresler alınıyor...");let e=await i.get(o.S.USER_ADDRESSES);console.log("\uD83D\uDCCD API Response:",e),console.log("\uD83D\uDCCD Response data:",e.data),console.log("\uD83D\uDCCD Response status:",e.status),console.log("\uD83D\uDCCD Response data type:",typeof e.data),console.log("\uD83D\uDCCD Response data is array:",Array.isArray(e.data)),"object"==typeof e.data&&null!==e.data&&(console.log("\uD83D\uDCCD Response data keys:",Object.keys(e.data)),console.log("\uD83D\uDCCD Response data values:",Object.values(e.data)),e.data.data&&(console.log("\uD83D\uDCCD Nested data found:",e.data.data),console.log("\uD83D\uDCCD Nested data is array:",Array.isArray(e.data.data))),e.data.addresses&&(console.log("\uD83D\uDCCD Addresses property found:",e.data.addresses),console.log("\uD83D\uDCCD Addresses is array:",Array.isArray(e.data.addresses))),e.data.result&&(console.log("\uD83D\uDCCD Result property found:",e.data.result),console.log("\uD83D\uDCCD Result is array:",Array.isArray(e.data.result))));let t=e.data;return e.data.data&&Array.isArray(e.data.data)?(t=e.data.data,console.log("\uD83D\uDCCD Using nested data array")):e.data.addresses&&Array.isArray(e.data.addresses)?(t=e.data.addresses,console.log("\uD83D\uDCCD Using addresses property")):e.data.result&&Array.isArray(e.data.result)?(t=e.data.result,console.log("\uD83D\uDCCD Using result property")):Array.isArray(e.data)?(t=e.data,console.log("\uD83D\uDCCD Using direct response data")):(console.warn("\uD83D\uDCCD No valid array found in response, using empty array"),t=[]),console.log("\uD83D\uDCCD Final address data:",t),console.log("\uD83D\uDCCD Final address data type:",typeof t),console.log("\uD83D\uDCCD Final address data is array:",Array.isArray(t)),console.log("\uD83D\uDCCD Final address count:",t.length),{success:!0,data:t}}catch(e){return console.error("❌ Adresler alınırken hata:",e),console.error("❌ Error response:",e.response),console.error("❌ Error data:",e.response?.data),{success:!1,error:e.response?.data?.message||e.message||"Adresler alınırken bir hata oluştu",data:[]}}},async createAddress(e){try{console.log("➕ Yeni adres oluşturuluyor:",e);let t=await i.post(o.S.CREATE_ADDRESS,e);return console.log("✅ Adres oluşturma başarılı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ Adres eklenirken hata:",e),{success:!1,error:e.response?.data?.message||"Adres eklenirken bir hata oluştu"}}},async deleteAddress(e,t){try{console.log("\uD83D\uDDD1️ Adres siliniyor:",{addressId:e,userId:t});let a=await i.post(o.S.DELETE_ADDRESS,{addressId:e,userId:t});return console.log("✅ Adres silme başarılı:",a.data),{success:!0,data:a.data}}catch(e){return console.error("❌ Adres silinirken hata:",e),{success:!1,error:e.response?.data?.message||"Adres silinirken bir hata oluştu"}}},async setDefaultAddress(e){try{console.log("⭐ Varsayılan adres ayarlanıyor:",{addressId:e});let t=await i.post(`${o.S.SET_DEFAULT_ADDRESS}?addressId=${e}`);return console.log("✅ Varsayılan adres ayarlama başarılı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ Varsayılan adres ayarlanırken hata:",e),{success:!1,error:e.response?.data?.message||"Varsayılan adres ayarlanırken bir hata oluştu"}}}},g={async updateProfile(e){try{console.log("\uD83D\uDC64 Profil g\xfcncelleniyor:",e);let t=await i.post(o.S.UPDATE_PROFILE,e);return console.log("✅ Profil g\xfcncelleme başarılı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ Profil g\xfcncellenirken hata:",e),{success:!1,error:e.response?.data?.message||"Profil g\xfcncellenirken bir hata oluştu"}}},async getUsers(e){try{let t={page:e.page||1,pageSize:e.pageSize||10,search:e.search||""};e.roleId&&e.roleId>0&&(t.roleId=e.roleId),void 0!==e.isActive&&(t.isActive=e.isActive);let a=await i.post(o.S.GET_USERS,t);return{success:!0,data:a.data}}catch(e){return console.error("❌ Kullanıcılar alınırken hata:",e),{success:!1,error:e.response?.data?.message||e.message||"Kullanıcılar alınırken bir hata oluştu",data:[]}}},async getUserRoleCounts(){try{let e=await i.get(o.S.GET_USER_ROLE_COUNTS);return{success:!0,data:e.data}}catch(e){return console.error("❌ Kullanıcı rol sayıları alınırken hata:",e),{success:!1,error:e.response?.data?.message||e.message||"Kullanıcı rol sayıları alınırken bir hata oluştu",data:null}}},async getDiscountRate(){try{console.log("\uD83D\uDCB0 Kullanıcı indirim oranı alınıyor...");let e=await i.get(o.S.GET_DISCOUNT_RATE);return console.log("✅ Kullanıcı indirim oranı başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(e){return console.error("❌ Kullanıcı indirim oranı alınırken hata:",e),{success:!1,error:e.response?.data?.message||e.message||"Kullanıcı indirim oranı alınırken bir hata oluştu",data:null}}},async updateCartType(){try{console.log("\uD83D\uDED2 Sepet tipi g\xfcncelleniyor...");let e=await i.post(o.S.UPDATE_CART_TYPE);return console.log("✅ Sepet tipi başarıyla g\xfcncellendi:",e.data),{success:!0,data:e.data}}catch(e){return console.error("❌ Sepet tipi g\xfcncellenirken hata:",e),{success:!1,error:e.response?.data?.message||e.message||"Sepet tipi g\xfcncellenirken bir hata oluştu",data:null}}}},h={async getBrands(){try{console.log("\uD83C\uDFF7️ Markalar alınıyor...");let e=await i.get(o.S.GET_BRANDS);return console.log("✅ Markalar başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(e){return console.error("❌ Markalar alınırken hata:",e),{success:!1,error:e.response?.data?.message||"Markalar alınırken bir hata oluştu",data:[]}}},async getCategoriesByBrand(e){try{console.log("\uD83D\uDCC2 Kategoriler alınıyor, brandId:",e);let t=await i.get(`${o.S.GET_CATEGORIES_BY_BRAND}/${e}`);return console.log("✅ Kategoriler başarıyla alındı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ Kategoriler alınırken hata:",e),{success:!1,error:e.response?.data?.message||"Kategoriler alınırken bir hata oluştu",data:[]}}},async getSubCategories(e){try{console.log("\uD83D\uDCC1 Alt kategoriler alınıyor, categoryId:",e);let t=await i.get(`${o.S.GET_SUBCATEGORIES}/${e}/subcategories`);return console.log("✅ Alt kategoriler başarıyla alındı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ Alt kategoriler alınırken hata:",e),{success:!1,error:e.response?.data?.message||"Alt kategoriler alınırken bir hata oluştu",data:[]}}},async getSubCategoryFeatures(e){try{console.log("\uD83D\uDD27 Alt kategori \xf6zellikleri alınıyor, subCategoryId:",e);let t=await i.get(`${o.S.GET_SUBCATEGORY_FEATURES}/${e}`);return console.log("✅ Alt kategori \xf6zellikleri başarıyla alındı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ Alt kategori \xf6zellikleri alınırken hata:",e),{success:!1,error:e.response?.data?.message||"Alt kategori \xf6zellikleri alınırken bir hata oluştu",data:[]}}},async getFeatureValues(e){try{console.log("\uD83C\uDFF7️ \xd6zellik değerleri alınıyor, definitionId:",e);let t=await i.get(`${o.S.GET_FEATURE_VALUES}/${e}`);return console.log("✅ \xd6zellik değerleri başarıyla alındı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ \xd6zellik değerleri alınırken hata:",e),{success:!1,error:e.response?.data?.message||"\xd6zellik değerleri alınırken bir hata oluştu",data:[]}}},async getAllFeatureDefinitions(){try{console.log("\uD83D\uDD0D T\xfcm \xf6zellik tanımları alınıyor...");let e=await i.get(o.S.GET_ALL_FEATURE_DEFINITIONS);return console.log("✅ T\xfcm \xf6zellik tanımları başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(e){return console.error("❌ T\xfcm \xf6zellik tanımları alınırken hata:",e),{success:!1,error:e.response?.data?.message||"T\xfcm \xf6zellik tanımları alınırken bir hata oluştu",data:[]}}},async createFullProduct(e){try{console.log("➕ Tam \xfcr\xfcn oluşturuluyor:",e);let t=await i.post(o.S.CREATE_FULL_PRODUCT,e,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ Tam \xfcr\xfcn oluşturma başarılı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ Tam \xfcr\xfcn oluşturulurken hata:",e),{success:!1,error:e.response?.data?.message||"\xdcr\xfcn oluşturulurken bir hata oluştu"}}},async createDealershipProduct(e){try{console.log("➕ Satıcı \xfcr\xfcn\xfc oluşturuluyor:",e);let t=await i.post(o.S.CREATE_DEALERSHIP_PRODUCT,e,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ Satıcı \xfcr\xfcn\xfc oluşturma başarılı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ Satıcı \xfcr\xfcn\xfc oluşturulurken hata:",e),{success:!1,error:e.response?.data?.message||"\xdcr\xfcn oluşturulurken bir hata oluştu"}}},async addProductImage(e){try{console.log("\uD83D\uDDBC️ \xdcr\xfcn g\xf6rseli ekleniyor:",e);let t=await i.post(o.S.ADD_PRODUCT_IMAGE,e,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ \xdcr\xfcn g\xf6rseli ekleme başarılı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ \xdcr\xfcn g\xf6rseli eklenirken hata:",e),{success:!1,error:e.response?.data?.message||"\xdcr\xfcn g\xf6rseli eklenirken bir hata oluştu"}}},async deleteProductImage(e){try{console.log("\uD83D\uDDD1️ \xdcr\xfcn g\xf6rseli siliniyor, imageId:",e);let t=await i.delete(`${o.S.DELETE_PRODUCT_IMAGE}/${e}`);return console.log("✅ \xdcr\xfcn g\xf6rseli silme başarılı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ \xdcr\xfcn g\xf6rseli silinirken hata:",e),{success:!1,error:e.response?.data?.message||"\xdcr\xfcn g\xf6rseli silinirken bir hata oluştu"}}},async replaceProductImage(e,t){try{console.log("\uD83D\uDD04 \xdcr\xfcn g\xf6rseli değiştiriliyor, imageId:",e);let a=new FormData;a.append("file",t);let r=await i.put(`${o.S.REPLACE_PRODUCT_IMAGE}/${e}`,a,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ \xdcr\xfcn g\xf6rseli değiştirme başarılı:",r.data),{success:!0,data:r.data}}catch(e){return console.error("❌ \xdcr\xfcn g\xf6rseli değiştirilirken hata:",e),{success:!1,error:e.response?.data?.message||"\xdcr\xfcn g\xf6rseli değiştirilirken bir hata oluştu"}}},async getAdminProducts(e){try{let t=await i.get(o.S.GET_ADMIN_PRODUCTS,{params:e});return Array.isArray(t.data)?t.data:[]}catch(e){return console.error("❌ Admin \xfcr\xfcnleri alınırken hata:",e),[]}},async getMyProducts(e){try{console.log("\uD83D\uDCE6 Kullanıcıya ait \xfcr\xfcnler alınıyor:",e);let t=await i.post(o.S.GET_MY_PRODUCTS,e);return console.log("✅ Kullanıcıya ait \xfcr\xfcnler başarıyla alındı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ Kullanıcıya ait \xfcr\xfcnler alınırken hata:",e),{success:!1,error:e.response?.data?.message||"\xdcr\xfcnler alınırken bir hata oluştu",data:null}}},async getDealershipProductDetail(e){try{console.log("\uD83D\uDCE6 Dealership \xfcr\xfcn detayı alınıyor, productId:",e);let t=await i.get(`${o.S.GET_DEALERSHIP_PRODUCT_DETAIL}/${e}`);return console.log("✅ Dealership \xfcr\xfcn detayı başarıyla alındı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ Dealership \xfcr\xfcn detayı alınırken hata:",e),{success:!1,error:e.response?.data?.message||"\xdcr\xfcn detayı alınırken bir hata oluştu",data:null}}},async deleteProduct(e){try{console.log("\uD83D\uDDD1️ \xdcr\xfcn siliniyor, productId:",e);let t=await i.get(o.S.DELETE_PRODUCT,{params:{productId:e}});return console.log("✅ \xdcr\xfcn silme başarılı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ \xdcr\xfcn silinirken hata:",e),{success:!1,error:e.response?.data?.message||"\xdcr\xfcn silinirken bir hata oluştu"}}},async updateFullProduct(e){try{for(let[t,a]of(console.log("\uD83D\uDD04 Tam \xfcr\xfcn g\xfcncelleniyor:",e),console.log("\uD83D\uDCCB FormData i\xe7eriği:"),e.entries()))console.log(`${t}:`,a);let t=await i.post(o.S.UPDATE_FULL_PRODUCT,e,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ Tam \xfcr\xfcn g\xfcncelleme başarılı:",t.data),{success:!0,data:t.data}}catch(e){throw console.error("❌ Tam \xfcr\xfcn g\xfcncellenirken hata:",e),console.error("❌ Hata detayları:",e.response?.data),console.error("❌ HTTP Status:",e.response?.status),Error(e.response?.data?.message||e.response?.data?.title||e.message||"\xdcr\xfcn g\xfcncellenirken bir hata oluştu")}},async getAdminProductStatistics(){try{console.log("\uD83D\uDCCA Admin \xfcr\xfcn istatistikleri alınıyor...");let e=await i.get(o.S.GET_ADMIN_PRODUCT_STATISTICS);return console.log("✅ Admin \xfcr\xfcn istatistikleri başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(e){return console.error("❌ Admin \xfcr\xfcn istatistikleri alınırken hata:",e),{success:!1,error:e.response?.data?.message||"İstatistikler alınırken bir hata oluştu",data:null}}},async getMyProductStatistics(){try{console.log("\uD83D\uDCCA Kullanıcı \xfcr\xfcn istatistikleri alınıyor...");let e=await i.get(o.S.GET_MY_PRODUCT_STATISTICS);return console.log("✅ Kullanıcı \xfcr\xfcn istatistikleri başarıyla alındı:",e.data),{success:!0,data:e.data.data}}catch(e){return console.error("❌ Kullanıcı \xfcr\xfcn istatistikleri alınırken hata:",e),{success:!1,error:e.response?.data?.message||"İstatistikler alınırken bir hata oluştu",data:null}}},async updateSimpleProduct(e){for(let[t,a]of(console.log("\uD83D\uDD04 API Service: Basit \xfcr\xfcn g\xfcncelleniyor..."),console.log("\uD83D\uDD17 Endpoint:",o.S.UPDATE_SIMPLE_PRODUCT),console.log("\uD83D\uDCCB API Service: FormData contents:"),e.entries()))a instanceof File?console.log(`  ${t}: File(${a.name}, ${a.size} bytes, ${a.type})`):console.log(`  ${t}: ${a}`);let t=await i.post(o.S.UPDATE_SIMPLE_PRODUCT,e,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ API Service: Basit \xfcr\xfcn başarıyla g\xfcncellendi"),console.log("\uD83D\uDCC4 Response status:",t.status),console.log("\uD83D\uDCC4 Response data:",t.data),t.data},async getProductDetail(e){try{console.log("\uD83D\uDCE6 \xdcr\xfcn detayı alınıyor, productId:",e);let t=await i.get(`${o.S.GET_PRODUCT_DETAIL}/${e}`);return console.log("✅ \xdcr\xfcn detayı başarıyla alındı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ \xdcr\xfcn detayı alınırken hata:",e),{success:!1,error:e.response?.data?.message||"\xdcr\xfcn detayı alınırken bir hata oluştu",data:null}}},async getCatalogProductDetail(e){try{console.log("\uD83D\uDCE6 Catalog \xfcr\xfcn detayı alınıyor, productId:",e);let t=await i.get(`${o.S.GET_CATALOG_PRODUCT_DETAIL}/${e}`);return console.log("✅ Catalog \xfcr\xfcn detayı başarıyla alındı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ Catalog \xfcr\xfcn detayı alınırken hata:",e),{success:!1,error:e.response?.data?.message||"\xdcr\xfcn detayı alınırken bir hata oluştu",data:null}}},async updateProductStatus(e,t,a){try{console.log("\uD83D\uDD04 \xdcr\xfcn durumu g\xfcncelleniyor:",{productId:e,isApproved:t,message:a});let r=await i.post(o.S.UPDATE_PRODUCT_STATUS,{productId:e,isApproved:t,message:a});return console.log("✅ \xdcr\xfcn durumu başarıyla g\xfcncellendi:",r.data),{success:!0,data:r.data}}catch(e){return console.error("❌ \xdcr\xfcn durumu g\xfcncellenirken hata:",e),{success:!1,error:e.response?.data?.message||"\xdcr\xfcn durumu g\xfcncellenirken bir hata oluştu"}}},async getProductMessage(e){try{console.log("\uD83D\uDCDD \xdcr\xfcn admin notu alınıyor, productId:",e);let t=await i.get(`${o.S.GET_PRODUCT_MESSAGE}/${e}`);return console.log("✅ \xdcr\xfcn admin notu başarıyla alındı:",t.data),{success:!0,data:t.data}}catch(e){if(console.error("❌ \xdcr\xfcn admin notu alınırken hata:",e),e.response?.status===404)return{success:!0,data:null};return{success:!1,error:e.response?.data?.message||"\xdcr\xfcn admin notu alınırken bir hata oluştu",data:null}}},async getCategories(){try{console.log("\uD83D\uDCC2 Kategoriler alınıyor...");let e=await i.get(o.S.GET_CATEGORIES);return console.log("✅ Kategoriler başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(e){return console.error("❌ Kategoriler alınırken hata:",e),{success:!1,error:e.response?.data?.message||"Kategoriler alınırken bir hata oluştu",data:[]}}},async getSubCategoriesByCategory(e){try{console.log("\uD83D\uDCC1 Alt kategoriler alınıyor, categoryId:",e);let t=await i.get(`${o.S.GET_SUBCATEGORIES_BY_CATEGORY.replace("{categoryId}",e.toString())}`);return console.log("✅ Alt kategoriler başarıyla alındı:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ Alt kategoriler alınırken hata:",e),{success:!1,error:e.response?.data?.message||"Alt kategoriler alınırken bir hata oluştu",data:[]}}},async filterProducts(e){try{console.log("\uD83D\uDD0D \xdcr\xfcnler filtreleniyor:",e);let t=await i.post(o.S.FILTER_PRODUCTS,e);return console.log("✅ \xdcr\xfcnler başarıyla filtrelendi:",t.data),{success:!0,data:t.data}}catch(e){return console.error("❌ \xdcr\xfcnler filtrelenirken hata:",e),{success:!1,error:e.response?.data?.message||"\xdcr\xfcnler filtrelenirken bir hata oluştu",data:null}}},async getReferenceData(){try{console.log("\uD83D\uDCCB Reference data alınıyor...");let e=await i.get(o.S.GET_REFERENCE_DATA);return console.log("✅ Reference data başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(e){return console.error("❌ Reference data alınırken hata:",e),{success:!1,error:e.response?.data?.message||"Reference data alınırken bir hata oluştu",data:null}}}},y={async addToCart(e,t,a){try{console.log("\uD83D\uDED2 Sepete \xfcr\xfcn ekleniyor:",{productVariantId:e,quantity:t,isCustomerPrice:a});let r=await i.post(o.S.ADD_TO_CART,{productVariantId:e,quantity:t,isCustomerPrice:a});return console.log("✅ \xdcr\xfcn sepete başarıyla eklendi:",r.data),{success:!0,data:r.data}}catch(e){return console.error("❌ Sepete \xfcr\xfcn eklenirken hata:",e),{success:!1,error:e.response?.data?.message||"\xdcr\xfcn sepete eklenirken bir hata oluştu"}}},async getCartItems(){try{console.log("\uD83D\uDED2 Sepet i\xe7erikleri alınıyor...");let e=await i.get(o.S.GET_CART_ITEMS);return console.log("✅ Sepet i\xe7erikleri başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(e){return console.error("❌ Sepet i\xe7erikleri alınırken hata:",e),{success:!1,error:e.response?.data?.message||"Sepet i\xe7erikleri alınırken bir hata oluştu",data:null}}},async getCartCount(){try{console.log("\uD83D\uDED2 Sepet \xfcr\xfcn sayısı alınıyor...");let e=await i.get(o.S.GET_CART_COUNT);return console.log("✅ Sepet \xfcr\xfcn sayısı başarıyla alındı:",e.data),{success:!0,data:e.data}}catch(e){return console.error("❌ Sepet \xfcr\xfcn sayısı alınırken hata:",e),{success:!1,error:e.response?.data?.message||"Sepet \xfcr\xfcn sayısı alınırken bir hata oluştu",data:0}}},async removeFromCart(e){try{console.log("\uD83D\uDDD1️ Sepetten \xfcr\xfcn \xe7ıkarılıyor:",{productVariantId:e});let t=`${o.S.REMOVE_FROM_CART}/${e}`;console.log("\uD83D\uDD0D API URL:",t),console.log("\uD83D\uDD0D API_ENDPOINTS.REMOVE_FROM_CART:",o.S.REMOVE_FROM_CART);let a=await i.delete(t);return console.log("✅ \xdcr\xfcn sepetten başarıyla \xe7ıkarıldı:",a.data),{success:!0,data:a.data}}catch(e){return console.error("❌ Sepetten \xfcr\xfcn \xe7ıkarılırken hata:",e),console.error("❌ Error response:",e.response),console.error("❌ Error status:",e.response?.status),console.error("❌ Error data:",e.response?.data),{success:!1,error:e.response?.data?.message||"\xdcr\xfcn sepetten \xe7ıkarılırken bir hata oluştu"}}},async updateCartQuantity(e,t){try{console.log("\uD83D\uDD04 Sepet \xfcr\xfcn miktarı g\xfcncelleniyor:",{productVariantId:e,quantity:t});let a=await i.post(o.S.UPDATE_CART_QUANTITY,{productVariantId:e,quantity:t});return console.log("✅ Sepet \xfcr\xfcn miktarı başarıyla g\xfcncellendi:",a.data),{success:!0,data:a.data}}catch(e){return console.error("❌ Sepet \xfcr\xfcn miktarı g\xfcncellenirken hata:",e),{success:!1,error:e.response?.data?.message||"\xdcr\xfcn miktarı g\xfcncellenirken bir hata oluştu"}}}}},75068:(e,t,a)=>{"use strict";a.d(t,{$N:()=>j,EI:()=>P,F0:()=>c,Gc:()=>d,HX:()=>C,JZ:()=>y,OG:()=>E,Ph:()=>g,QK:()=>p,QR:()=>R,S:()=>u,VS:()=>i,Zm:()=>A,_f:()=>T,c6:()=>x,fW:()=>S,gA:()=>f,hg:()=>k,ig:()=>b,lA:()=>m,nb:()=>v,qA:()=>D,u6:()=>h,vQ:()=>w});var r=a(26787),s=a(59350);let o={editPersonalInfo:!1,referenceRegistration:!1,addAddress:!1,setDefaultConfirmation:!1,banking:!1,addCard:!1,setDefaultCard:!1,registerSuccess:!1,successNotification:!1,productCategorySelector:!1,productVariantSetup:!1,productVariant:!1,productDeleteConfirmation:!1},n={editPersonalInfoUser:null,referenceRegistrationData:null,addAddressData:null,setDefaultConfirmationData:null,bankingData:null,addCardData:null,setDefaultCardData:null,successNotificationData:null,productCategorySelectorData:null,productVariantSetupData:null,productVariantData:null,productDeleteConfirmationData:null},l=(0,r.v)()((0,s.lt)(e=>({...o,...n,openModal:(t,a)=>{e(e=>({...e,[t]:!0,...a&&{[`${t}User`]:a}}),!1,`modal/open/${t}`)},closeModal:t=>{e(e=>({...e,[t]:!1,[`${t}User`]:null}),!1,`modal/close/${t}`)},closeAllModals:()=>{e({...o,...n},!1,"modal/closeAll")},openEditPersonalInfoModal:t=>{e({editPersonalInfo:!0,editPersonalInfoUser:t},!1,"modal/openEditPersonalInfo")},closeEditPersonalInfoModal:()=>{e({editPersonalInfo:!1,editPersonalInfoUser:null},!1,"modal/closeEditPersonalInfo")},openReferenceRegistrationModal:t=>{e({referenceRegistration:!0,referenceRegistrationData:t},!1,"modal/openReferenceRegistration")},closeReferenceRegistrationModal:()=>{e({referenceRegistration:!1,referenceRegistrationData:null},!1,"modal/closeReferenceRegistration")},openAddAddressModal:t=>{e({addAddress:!0,addAddressData:t},!1,"modal/openAddAddress")},closeAddAddressModal:()=>{e({addAddress:!1,addAddressData:null},!1,"modal/closeAddAddress")},openSetDefaultConfirmationModal:t=>{e({setDefaultConfirmation:!0,setDefaultConfirmationData:t},!1,"modal/openSetDefaultConfirmation")},closeSetDefaultConfirmationModal:()=>{e({setDefaultConfirmation:!1,setDefaultConfirmationData:null},!1,"modal/closeSetDefaultConfirmation")},openBankingModal:t=>{e({banking:!0,bankingData:t},!1,"modal/openBanking")},closeBankingModal:()=>{e({banking:!1,bankingData:null},!1,"modal/closeBanking")},openAddCardModal:t=>{e({addCard:!0,addCardData:t},!1,"modal/openAddCard")},closeAddCardModal:()=>{e({addCard:!1,addCardData:null},!1,"modal/closeAddCard")},openSetDefaultCardModal:t=>{e({setDefaultCard:!0,setDefaultCardData:t},!1,"modal/openSetDefaultCard")},closeSetDefaultCardModal:()=>{e({setDefaultCard:!1,setDefaultCardData:null},!1,"modal/closeSetDefaultCard")},openRegisterSuccessModal:()=>{e({registerSuccess:!0},!1,"modal/openRegisterSuccess")},closeRegisterSuccessModal:()=>{e({registerSuccess:!1},!1,"modal/closeRegisterSuccess")},openSuccessNotificationModal:t=>{e({successNotification:!0,successNotificationData:t},!1,"modal/openSuccessNotification")},closeSuccessNotificationModal:()=>{e({successNotification:!1,successNotificationData:null},!1,"modal/closeSuccessNotification")},openProductCategorySelector:t=>{e({productCategorySelector:!0,productCategorySelectorData:t},!1,"modal/openProductCategorySelector")},closeProductCategorySelector:()=>{e({productCategorySelector:!1,productCategorySelectorData:null},!1,"modal/closeProductCategorySelector")},openProductVariantSetup:t=>{e({productVariantSetup:!0,productVariantSetupData:t},!1,"modal/openProductVariantSetup")},closeProductVariantSetup:()=>{e({productVariantSetup:!1,productVariantSetupData:null},!1,"modal/closeProductVariantSetup")},openProductVariant:t=>{e({productVariant:!0,productVariantData:t},!1,"modal/openProductVariant")},closeProductVariant:()=>{e({productVariant:!1,productVariantData:null},!1,"modal/closeProductVariant")},openProductDeleteConfirmation:t=>{e({productDeleteConfirmation:!0,productDeleteConfirmationData:t},!1,"modal/openProductDeleteConfirmation")},closeProductDeleteConfirmation:()=>{e({productDeleteConfirmation:!1,productDeleteConfirmationData:null},!1,"modal/closeProductDeleteConfirmation")}}),{name:"modal-store",enabled:!1})),i=()=>l(e=>e.editPersonalInfo),d=()=>l(e=>e.editPersonalInfoUser),c=()=>l(e=>e.referenceRegistration),u=()=>l(e=>e.referenceRegistrationData),p=()=>l(e=>e.addAddress),m=()=>l(e=>e.addAddressData),g=()=>l(e=>e.setDefaultConfirmation),h=()=>l(e=>e.setDefaultConfirmationData),y=()=>l(e=>e.banking),f=()=>l(e=>e.bankingData),x=()=>l(e=>e.addCard),k=()=>l(e=>e.addCardData),b=()=>l(e=>e.setDefaultCard),w=()=>l(e=>e.setDefaultCardData),v=()=>l(e=>e.registerSuccess),A=()=>l(e=>e.successNotification),E=()=>l(e=>e.successNotificationData),S=()=>l(e=>e.productCategorySelector),T=()=>l(e=>e.productCategorySelectorData),C=()=>l(e=>e.productVariant),D=()=>l(e=>e.productVariantData),P=()=>l(e=>e.productDeleteConfirmation),j=()=>l(e=>e.productDeleteConfirmationData),R=()=>{let e=l(e=>e.openModal),t=l(e=>e.closeModal),a=l(e=>e.closeAllModals),r=l(e=>e.openEditPersonalInfoModal),s=l(e=>e.closeEditPersonalInfoModal),o=l(e=>e.openReferenceRegistrationModal),n=l(e=>e.closeReferenceRegistrationModal),i=l(e=>e.openAddAddressModal),d=l(e=>e.closeAddAddressModal),c=l(e=>e.openSetDefaultConfirmationModal),u=l(e=>e.closeSetDefaultConfirmationModal),p=l(e=>e.openBankingModal),m=l(e=>e.closeBankingModal),g=l(e=>e.openAddCardModal),h=l(e=>e.closeAddCardModal),y=l(e=>e.openSetDefaultCardModal),f=l(e=>e.closeSetDefaultCardModal),x=l(e=>e.openRegisterSuccessModal),k=l(e=>e.closeRegisterSuccessModal),b=l(e=>e.openSuccessNotificationModal),w=l(e=>e.closeSuccessNotificationModal),v=l(e=>e.openProductCategorySelector),A=l(e=>e.closeProductCategorySelector),E=l(e=>e.openProductVariantSetup),S=l(e=>e.closeProductVariantSetup),T=l(e=>e.openProductVariant),C=l(e=>e.closeProductVariant);return{openModal:e,closeModal:t,closeAllModals:a,openEditPersonalInfoModal:r,closeEditPersonalInfoModal:s,openReferenceRegistrationModal:o,closeReferenceRegistrationModal:n,openAddAddressModal:i,closeAddAddressModal:d,openSetDefaultConfirmationModal:c,closeSetDefaultConfirmationModal:u,openBankingModal:p,closeBankingModal:m,openAddCardModal:g,closeAddCardModal:h,openSetDefaultCardModal:y,closeSetDefaultCardModal:f,openRegisterSuccessModal:x,closeRegisterSuccessModal:k,openSuccessNotificationModal:b,closeSuccessNotificationModal:w,openProductCategorySelector:v,closeProductCategorySelector:A,openProductVariantSetup:E,closeProductVariantSetup:S,openProductVariant:T,closeProductVariant:C,openProductDeleteConfirmation:l(e=>e.openProductDeleteConfirmation),closeProductDeleteConfirmation:l(e=>e.closeProductDeleteConfirmation)}}},83515:(e,t,a)=>{"use strict";a.d(t,{$9:()=>d,AP:()=>u,IM:()=>p,PL:()=>c,Yu:()=>m,vG:()=>g,y$:()=>i});var r=a(51423),s=a(8693),o=a(54050),n=a(64298),l=a(15908);let i=()=>{let{isAuthenticated:e}=(0,l.A)();return(0,r.I)({queryKey:["cartItems",e],queryFn:async()=>{let e=await n.CV.getCartItems();if(e.success)return e.data.data;throw Error(e.error||"Sepet i\xe7erikleri alınamadı")},staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0})},d=()=>{let{isAuthenticated:e}=(0,l.A)();return(0,r.I)({queryKey:["cartCount",e],queryFn:async()=>{let e=await n.CV.getCartCount();if(e.success)return e.data.data;throw Error(e.error||"Sepet \xfcr\xfcn sayısı alınamadı")},staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0})},c=()=>(0,r.I)({queryKey:["discountRate"],queryFn:async()=>{try{let e=await n.Dv.getDiscountRate();if(console.log("\uD83D\uDD0D Discount Rate API Response:",e),e.success)return e.data||{discountRate:0};return console.warn("İndirim oranı alınamadı:",e.error),{discountRate:0}}catch(e){return console.warn("İndirim oranı alınırken hata:",e),{discountRate:0}}},staleTime:3e5,refetchOnWindowFocus:!1,refetchOnMount:!0,retry:!1}),u=()=>{let e=(0,s.jE)();return(0,o.n)({mutationFn:async({productVariantId:e,quantity:t,isCustomerPrice:a})=>{let r=await n.CV.addToCart(e,t,a);if(!r.success)throw Error(r.error||"\xdcr\xfcn sepete eklenemedi");return r.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepete \xfcr\xfcn ekleme hatası:",e)}})},p=()=>{let e=(0,s.jE)();return(0,o.n)({mutationFn:async e=>{let t=await n.CV.removeFromCart(e);if(!t.success)throw Error(t.error||"\xdcr\xfcn sepetten \xe7ıkarılamadı");return t.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepetten \xfcr\xfcn \xe7ıkarma hatası:",e)}})},m=()=>{let e=(0,s.jE)();return(0,o.n)({mutationFn:async({productVariantId:e,quantity:t})=>{let a=await n.CV.updateCartQuantity(e,t);if(!a.success)throw Error(a.error||"\xdcr\xfcn miktarı g\xfcncellenemedi");return a.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepet \xfcr\xfcn miktarı g\xfcncelleme hatası:",e)}})},g=()=>{let e=(0,s.jE)();return(0,o.n)({mutationFn:async()=>{let e=await n.Dv.updateCartType();if(!e.success)throw Error(e.error||"Sepet tipi g\xfcncellenemedi");return e.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepet tipi g\xfcncelleme hatası:",e)}})}},83820:(e,t,a)=>{"use strict";a.d(t,{H:()=>n,r:()=>l});var r=a(60687),s=a(43210);a(94396);let o=(0,s.createContext)(void 0);function n({children:e}){let[t,a]=(0,s.useState)([]),[n,l]=(0,s.useState)(!1),i=(0,s.useCallback)(e=>{a(t=>t.find(t=>t.productId===e.id)?t:[...t,{id:Date.now(),productId:e.id,product:e,addedAt:new Date().toISOString()}])},[]),d=(0,s.useCallback)(e=>{a(t=>t.filter(t=>t.productId!==e))},[]),c=(0,s.useCallback)(e=>t.some(t=>t.productId===e),[t]),u=(0,s.useCallback)(()=>t.length,[t]),p=(0,s.useMemo)(()=>({favorites:t,addToFavorites:i,removeFromFavorites:d,isFavorite:c,getFavoritesCount:u}),[t,i,d,c,u]);return(0,r.jsx)(o.Provider,{value:p,children:e})}function l(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useFavorites must be used within a FavoritesProvider");return e}},87979:(e,t,a)=>{"use strict";a.d(t,{Ng:()=>y,Py:()=>g,ZF:()=>m,_L:()=>h,dS:()=>x,ge:()=>f});var r=a(51423),s=a(8693),o=a(54050),n=a(43210),l=a(10535),i=a(42243),d=a(38429),c=a(45840),u=a(16189),p=a(42112);let m={all:["auth"],user:()=>[...m.all,"user"],profile:()=>[...m.all,"profile"],profileInfo:()=>[...m.all,"profileInfo"]},g=()=>{(0,u.useRouter)();let e=(0,r.I)({queryKey:m.user(),queryFn:async()=>{try{let e=await l.y.getUserInfo();if(!e)return null;let t=e.user||e;return t.userId||t.id||t.email||(t={userId:1,email:"<EMAIL>",firstName:"User",lastName:"User",phoneNumber:"",isActive:!0,registeredAt:new Date().toISOString(),membershipLevelId:1,careerRankId:1,referenceId:0,roles:["Customer"]}),{id:void 0!==t.userId?t.userId:void 0!==t.id?t.id:1,firstName:t.firstName||"User",lastName:t.lastName||"User",email:t.email||"<EMAIL>",phoneNumber:t.phoneNumber||"",isActive:void 0===t.isActive||t.isActive,registeredAt:t.registeredAt||new Date().toISOString(),membershipLevelId:void 0!==t.membershipLevelId?t.membershipLevelId:1,careerRankId:void 0!==t.careerRankId?t.careerRankId:1,referenceId:void 0!==t.referenceId?t.referenceId:void 0!==t.referanceId?t.referanceId:0,roles:t.roles||(t.role?[t.role]:["Customer"]),role:t.role?t.role.toLowerCase():t.roles&&t.roles.includes("Admin")?"admin":t.roles&&t.roles.includes("Dealership")?"dealership":"customer",membershipLevel:void 0!==t.membershipLevelId?t.membershipLevelId:0,joinDate:t.registeredAt?new Date(t.registeredAt).toISOString().split("T")[0]:"",isDealershipApproved:t.roles&&t.roles.includes("Dealership")}}catch(e){throw e}},enabled:!0,staleTime:9e5,gcTime:18e5,refetchOnWindowFocus:!1,refetchOnMount:"always",refetchOnReconnect:!0,refetchInterval:!1,refetchIntervalInBackground:!1,retry:(e,t)=>t?.response?.status!==401&&t?.response?.status!==403&&e<2,retryDelay:e=>Math.min(1e3*2**e,3e4),throwOnError:e=>e.response?.status!==401});return(0,n.useEffect)(()=>{e.isSuccess&&e.data&&i.n.setState({user:e.data,isAuthenticated:!0,error:null})},[e.isSuccess,e.data]),(0,n.useEffect)(()=>{e.isError&&e.error&&e.error?.response?.status===401&&i.n.setState({user:null,isAuthenticated:!1})},[e.isError,e.error]),e},h=()=>{let e=(0,s.jE)();(0,u.useRouter)();let{user:t,setUser:a,clearAuth:r,setLoading:n}=(0,i.n)();return(0,o.n)({mutationFn:async e=>{let t=await l.y.login(e.email,e.password);if(!t)throw Error("Login failed: Invalid credentials from service");return t},onSuccess:async()=>{try{await e.invalidateQueries({queryKey:m.user()});let t=await e.fetchQuery({queryKey:m.user()});if(!t||!t.id)throw Error("Fetched user data is invalid or missing ID.");i.n.setState({isAuthenticated:!0,error:null,isLoading:!1,user:t}),e.invalidateQueries({queryKey:d.N$.all}),t.id&&(e.invalidateQueries({queryKey:d.N$.list(t.id)}),e.removeQueries({queryKey:d.N$.list(t.id)})),e.invalidateQueries({queryKey:c.M.all}),t.id&&(e.invalidateQueries({queryKey:c.M.user(t.id)}),e.removeQueries({queryKey:c.M.user(t.id)})),p.w.getState().resetCustomerPrice(),e.invalidateQueries({queryKey:["cartCount"]}),e.invalidateQueries({queryKey:["cartItems"]});try{console.log("✅ Background checkAuth başarılı - user bilgisi g\xfcncellendi")}catch(e){console.log("⚠️ Background checkAuth başarısız - mevcut user bilgisi korunuyor:",e.message)}}catch(e){i.n.setState({error:"Giriş başarılı fakat kullanıcı verileri alınamadı.",isAuthenticated:!1,user:null})}},onError:e=>{i.n.setState({error:e.response?.data?.message||"Giriş başarısız",isAuthenticated:!1,user:null})}})},y=()=>{let e=(0,s.jE)();return(0,o.n)({mutationFn:async()=>{await l.y.logout()},onSuccess:()=>{e.clear(),i.n.setState({user:null,isAuthenticated:!1,error:null}),p.w.getState().resetCustomerPrice()},onError:t=>{e.clear(),i.n.setState({user:null,isAuthenticated:!1}),p.w.getState().resetCustomerPrice()}})},f=()=>(0,o.n)({mutationFn:async e=>{if(e.password!==e.confirmPassword)throw Error("Şifreler eşleşmiyor");return(await l.y.register({firstName:e.firstName,lastName:e.lastName,email:e.email,password:e.password,phoneNumber:e.phoneNumber||"",referansCode:e.referansCode})).success},onSuccess:()=>{i.n.setState({error:null})},onError:e=>{i.n.setState({error:e.message||"Kayıt başarısız"})}}),x=()=>(0,r.I)({queryKey:m.profileInfo(),queryFn:async()=>{try{let e=await l.y.getProfileInfo();return console.log("\uD83D\uDCCB Profile Info Data:",e),e}catch(e){throw console.error("❌ Profile Info Error:",e),e}},enabled:!0,staleTime:3e5,gcTime:9e5,refetchOnWindowFocus:!1,refetchOnMount:"always",refetchOnReconnect:!0,refetchInterval:!1,refetchIntervalInBackground:!1,retry:(e,t)=>t?.response?.status!==401&&t?.response?.status!==403&&e<2,retryDelay:e=>Math.min(1e3*2**e,3e4),throwOnError:e=>e.response?.status!==401})},93627:(e,t,a)=>{Promise.resolve().then(a.bind(a,94431))},94396:(e,t,a)=>{"use strict";a.d(t,{ZE:()=>r});let r=[{id:1,title:"Vitamin C Serum",description:"Cilt lekelerine karşı etkili, antioksidan \xf6zellikli vitamin C serumu. Cildinizi dış etkenlere karşı korur ve ışıltı verir.",price:349.9,discountPercentage:10,rating:4.7,stock:120,brand:"Say Beauty",category:"Cilt Bakımı",thumbnail:"https://picsum.photos/id/1/200/200",images:["https://picsum.photos/id/1/800/800","https://picsum.photos/id/2/800/800","https://picsum.photos/id/3/800/800"],points:30},{id:2,title:"Kolajen Takviyesi",description:"Eklem ve cilt sağlığı i\xe7in g\xfcnl\xfck kolajen takviyesi. İ\xe7eriğindeki Tip I ve Tip II kolajen ile cildinizin elastikiyetini destekler.",price:299.9,discountPercentage:void 0,rating:4.5,stock:85,brand:"Say Health",category:"Takviye \xdcr\xfcnler",thumbnail:"https://picsum.photos/id/4/200/200",images:["https://picsum.photos/id/4/800/800","https://picsum.photos/id/5/800/800","https://picsum.photos/id/6/800/800"],points:30},{id:3,title:"Argan Yağlı Şampuan",description:"Kuru ve yıpranmış sa\xe7lar i\xe7in onarıcı argan yağı i\xe7eren şampuan. Sa\xe7larınızı besler ve kolay taranmasını sağlar.",price:129.9,discountPercentage:15,rating:4.3,stock:200,brand:"Say Beauty",category:"Sa\xe7 Bakımı",thumbnail:"https://picsum.photos/id/7/200/200",images:["https://picsum.photos/id/7/800/800","https://picsum.photos/id/8/800/800","https://picsum.photos/id/9/800/800"],points:10},{id:4,title:"Probiyotik Kompleks",description:"Bağırsak florası ve sindirim sistemi sağlığı i\xe7in g\xfcnl\xfck probiyotik takviyesi. 10 farklı probiyotik suşu i\xe7erir.",price:199.9,discountPercentage:5,rating:4.8,stock:150,brand:"Say Health",category:"Takviye \xdcr\xfcnler",thumbnail:"https://picsum.photos/id/10/200/200",images:["https://picsum.photos/id/10/800/800","https://picsum.photos/id/11/800/800","https://picsum.photos/id/12/800/800"],points:20},{id:5,title:"Retinol Gece Kremi",description:"Yaşlanma karşıtı, kırışıklık giderici ve cilt yenileyici retinol i\xe7erikli gece kremi. D\xfczenli kullanımda ince \xe7izgilerin g\xf6r\xfcn\xfcm\xfcn\xfc azaltır.",price:399.9,discountPercentage:void 0,rating:4.6,stock:70,brand:"Say Beauty",category:"Cilt Bakımı",thumbnail:"https://picsum.photos/id/13/200/200",images:["https://picsum.photos/id/13/800/800","https://picsum.photos/id/14/800/800","https://picsum.photos/id/15/800/800"],points:40},{id:6,title:"Multivitamin",description:"G\xfcnl\xfck vitamin ve mineral ihtiyacınızı karşılayan multivitamin takviyesi. Adan Zye t\xfcm vitaminleri i\xe7erir.",price:179.9,discountPercentage:10,rating:4.4,stock:250,brand:"Say Health",category:"Takviye \xdcr\xfcnler",thumbnail:"https://picsum.photos/id/16/200/200",images:["https://picsum.photos/id/16/800/800","https://picsum.photos/id/17/800/800","https://picsum.photos/id/18/800/800"],points:15},{id:7,title:"Hyaluronik Asit Serumu",description:"Yoğun nemlendirici hyaluronik asit serumu. Cildin nem bariyerini g\xfc\xe7lendirir ve dolgunluk sağlar.",price:279.9,discountPercentage:void 0,rating:4.9,stock:100,brand:"Say Beauty",category:"Cilt Bakımı",thumbnail:"https://picsum.photos/id/19/200/200",images:["https://picsum.photos/id/19/800/800","https://picsum.photos/id/20/800/800","https://picsum.photos/id/21/800/800"],points:25},{id:8,title:"Yeşil \xc7ay \xd6zl\xfc Tonik",description:"G\xf6zenek sıkılaştırıcı ve yağ dengeleyici yeşil \xe7ay \xf6zl\xfc tonik. Cildi temizler ve ferahlatır.",price:149.9,discountPercentage:5,rating:4.2,stock:180,brand:"Say Beauty",category:"Cilt Bakımı",thumbnail:"https://picsum.photos/id/22/200/200",images:["https://picsum.photos/id/22/800/800","https://picsum.photos/id/23/800/800","https://picsum.photos/id/24/800/800"],points:15}];r[0],r[2],r[4]},94431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx","default")}};