(()=>{var e={};e.id=307,e.ids=[307],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22635:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var i=s(60687),a=s(43210),r=s(15908),l=s(16189),n=s(84299);let d=(0,s(62688).A)("wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]]);var o=s(64398),c=s(41312),m=s(25541),x=s(93508),h=s(67268),p=s(86561),g=s(53411),y=s(28947),u=s(48482),f=s(71638),v=s(85168),j=s(27747),b=s(9920),N=s(38246),k=s(57359),w=s(49481);let A=()=>{let{user:e,isLoading:t}=(0,r.A)(),s=(0,l.useRouter)(),A=n.wI;(0,a.useEffect)(()=>{t||e&&("dealership"===e.role||"admin"===e.role||"customer"===e.role)||s.push("/login")},[e,t,s]);let P=e?.role==="dealership"||e?.role==="admin";if(t)return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600 mx-auto"}),(0,i.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})});if(!e||"dealership"!==e.role&&"admin"!==e.role&&"customer"!==e.role)return null;let S=A.totalPoints+A.nextLevelPoints,M=A.totalPoints/S*100;return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Kontrol Paneli"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Kazan\xe7larınızı, ekibinizi ve performansınızı takip edin"}),(0,i.jsxs)("div",{className:"mt-4 flex items-center space-x-4",children:[(0,i.jsx)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${"admin"===e.role?"bg-red-100 text-red-800":"dealership"===e.role?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"}`,children:"admin"===e.role?"Y\xf6netici":"dealership"===e.role?"Satıcı":"M\xfcşteri"}),!P&&(0,i.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800",children:"\xdcr\xfcn Y\xf6netimi: Erişim Yok"}),P&&(0,i.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800",children:"\xdcr\xfcn Y\xf6netimi: Aktif"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,i.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Kazan\xe7"}),(0,i.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₺",A.totalEarnings.toLocaleString("tr-TR",{minimumFractionDigits:2})]})]}),(0,i.jsx)("div",{className:"bg-green-100 p-3 rounded-full",children:(0,i.jsx)(d,{className:"h-6 w-6 text-green-600"})})]})}),(0,i.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Aylık Puan"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:A.monthlyPoints})]}),(0,i.jsx)("div",{className:"bg-blue-100 p-3 rounded-full",children:(0,i.jsx)(o.A,{className:"h-6 w-6 text-blue-600"})})]})}),(0,i.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Ekip Sayısı"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:A.teamSize})]}),(0,i.jsx)("div",{className:"bg-purple-100 p-3 rounded-full",children:(0,i.jsx)(c.A,{className:"h-6 w-6 text-purple-600"})})]})}),(0,i.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-orange-500",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Aylık Bakiye"}),(0,i.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₺",A.monthlyBalance.toLocaleString("tr-TR",{minimumFractionDigits:2})]})]}),(0,i.jsx)("div",{className:"bg-orange-100 p-3 rounded-full",children:(0,i.jsx)(m.A,{className:"h-6 w-6 text-orange-600"})})]})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Aylık Aktiflik"}),(0,i.jsx)(x.A,{className:"h-6 w-6 text-gray-400"})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Bu Ay"}),(0,i.jsxs)("span",{className:"font-semibold text-gray-900",children:["%",A.monthlyActivityPercentage]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,i.jsx)("div",{className:"bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full transition-all duration-300",style:{width:`${A.monthlyActivityPercentage}%`}})}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"Minimum 75 puanlık sipariş ile aktif olunur"})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Puanlarım"}),(0,i.jsx)(h.A,{className:"h-6 w-6 text-gray-400"})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Toplam Puan"}),(0,i.jsx)("span",{className:"font-semibold text-gray-900",children:A.totalPoints.toLocaleString("tr-TR")})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Organizasyon"}),(0,i.jsx)("span",{className:"font-semibold text-gray-900",children:A.organizationPoints.toLocaleString("tr-TR")})]})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Seviye İlerlemesi"}),(0,i.jsx)(p.A,{className:"h-6 w-6 text-gray-400"})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Mevcut Seviye"}),(0,i.jsx)("span",{className:"font-semibold text-gray-900",children:A.currentLevel})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,i.jsx)("div",{className:"bg-gradient-to-r from-yellow-400 to-yellow-600 h-3 rounded-full transition-all duration-300",style:{width:`${M}%`}})}),(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:[A.nextLevel," seviyesi i\xe7in ",A.nextLevelPoints," puan kaldı"]})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Aylık Kazan\xe7 Trendi"}),(0,i.jsx)(g.A,{className:"h-6 w-6 text-gray-400"})]}),(0,i.jsx)("div",{className:"space-y-4",children:A.monthlyEarnings.map((e,t)=>(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("span",{className:"text-sm font-medium text-gray-700 w-16",children:e.month}),(0,i.jsx)("div",{className:"flex-1 bg-gray-200 rounded-full h-2 w-32",children:(0,i.jsx)("div",{className:"bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full",style:{width:`${e.earnings/1300*100}%`}})})]}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsxs)("div",{className:"text-sm font-semibold text-gray-900",children:["₺",e.earnings.toFixed(2)]}),(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:["%",e.activity," aktiflik"]})]})]},t))})]}),(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Ekibim"}),(0,i.jsx)(c.A,{className:"h-6 w-6 text-gray-400"})]}),(0,i.jsxs)("div",{className:"overflow-hidden",children:[(0,i.jsx)("div",{className:"space-y-3",children:n.x1.slice(0,5).map(e=>(0,i.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center",children:(0,i.jsxs)("span",{className:"text-white font-semibold text-sm",children:[e.firstName.charAt(0),e.lastName.charAt(0)]})}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[e.firstName," ",e.lastName]}),(0,i.jsxs)("p",{className:"text-xs text-gray-500",children:["Seviye ",e.level," • ",e.points," puan"]})]})]}),(0,i.jsx)("div",{className:"text-right",children:(0,i.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isActive?"Aktif":"Pasif"})})]},e.id))}),n.x1.length>5&&(0,i.jsx)("div",{className:"mt-4 text-center",children:(0,i.jsxs)("a",{href:"/my-team",className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:["T\xfcm\xfcn\xfc G\xf6r\xfcnt\xfcle (",n.x1.length-5," daha)"]})})]})]})]}),(0,i.jsxs)("div",{className:"mt-8 bg-white rounded-xl shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Aylık Puan Trendi"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Son 6 aylık puan performansınız"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),(0,i.jsx)("span",{className:"text-sm text-gray-600",children:"Kazanılan Puan"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-3 h-3 bg-gray-300 rounded-full"}),(0,i.jsx)("span",{className:"text-sm text-gray-600",children:"Hedef"})]})]})]}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("div",{className:"h-64 flex items-end justify-between space-x-2 mb-4",children:A.monthlyPointsHistory.map((e,t)=>{let s=Math.max(...A.monthlyPointsHistory.map(e=>Math.max(e.points,e.target))),a=e.points/s*200,r=e.target/s*200;return(0,i.jsxs)("div",{className:"flex-1 flex flex-col items-center space-y-2",children:[(0,i.jsxs)("div",{className:"relative w-full h-48 flex items-end justify-center space-x-1",children:[(0,i.jsx)("div",{className:"w-6 bg-gray-200 rounded-t-md relative group",style:{height:`${r}px`},children:(0,i.jsxs)("div",{className:"absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap",children:["Hedef: ",e.target]})}),(0,i.jsxs)("div",{className:"w-6 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-md relative group",style:{height:`${a}px`},children:[(0,i.jsxs)("div",{className:"absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap",children:["Puan: ",e.points]}),e.points>=e.target&&(0,i.jsx)("div",{className:"absolute -top-2 -right-2 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center",children:(0,i.jsx)("svg",{className:"w-2 h-2 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.month}),(0,i.jsx)("div",{className:"text-xs text-gray-500",children:e.points>=e.target?(0,i.jsx)("span",{className:"text-green-600 font-medium",children:"✓ Hedefe ulaşıldı"}):(0,i.jsxs)("span",{className:"text-gray-500",children:[e.target-e.points," puan kaldı"]})})]})]},t)})}),(0,i.jsxs)("div",{className:"grid grid-cols-3 gap-4 pt-4 border-t border-gray-100",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-lg font-bold text-blue-600",children:A.monthlyPointsHistory[A.monthlyPointsHistory.length-1]?.points||0}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Bu Ay"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-lg font-bold text-green-600",children:A.monthlyPointsHistory.filter(e=>e.points>=e.target).length}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Hedefe Ulaşılan Ay"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-lg font-bold text-gray-900",children:Math.round(A.monthlyPointsHistory.reduce((e,t)=>e+t.points,0)/A.monthlyPointsHistory.length)}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Ortalama"})]})]})]})]}),(0,i.jsxs)("div",{className:"mt-8 bg-white rounded-xl shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Aylık Performans Trendi"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Aktiflik y\xfczdesi ve ekip b\xfcy\xfcmesi"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-3 h-0.5 bg-purple-500"}),(0,i.jsx)("span",{className:"text-sm text-gray-600",children:"Aktiflik %"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-3 h-0.5 bg-green-500"}),(0,i.jsx)("span",{className:"text-sm text-gray-600",children:"Ekip Sayısı"})]})]})]}),(0,i.jsx)("div",{className:"h-80",children:(0,i.jsx)(u.u,{width:"100%",height:"100%",children:(0,i.jsxs)(f.Q,{data:A.monthlyActivityTrend,margin:{top:5,right:30,left:20,bottom:5},children:[(0,i.jsxs)("defs",{children:[(0,i.jsxs)("linearGradient",{id:"colorActivity",x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,i.jsx)("stop",{offset:"5%",stopColor:"#8b5cf6",stopOpacity:.8}),(0,i.jsx)("stop",{offset:"95%",stopColor:"#8b5cf6",stopOpacity:.1})]}),(0,i.jsxs)("linearGradient",{id:"colorTeam",x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,i.jsx)("stop",{offset:"5%",stopColor:"#10b981",stopOpacity:.8}),(0,i.jsx)("stop",{offset:"95%",stopColor:"#10b981",stopOpacity:.1})]})]}),(0,i.jsx)(v.d,{strokeDasharray:"3 3",stroke:"#f0f0f0",horizontal:!0,vertical:!1}),(0,i.jsx)(j.W,{dataKey:"month",stroke:"#6b7280",fontSize:12,axisLine:!1,tickLine:!1}),(0,i.jsx)(b.h,{yAxisId:"left",stroke:"#6b7280",fontSize:12,domain:[0,100],label:{value:"Aktiflik %",angle:-90,position:"insideLeft"},axisLine:!1,tickLine:!1}),(0,i.jsx)(b.h,{yAxisId:"right",orientation:"right",stroke:"#6b7280",fontSize:12,domain:[0,15],label:{value:"Ekip Sayısı",angle:90,position:"insideRight"},axisLine:!1,tickLine:!1}),(0,i.jsx)(N.m,{contentStyle:{backgroundColor:"rgba(31, 41, 55, 0.95)",border:"none",borderRadius:"12px",color:"white",fontSize:"12px",boxShadow:"0 10px 25px rgba(0, 0, 0, 0.1)",backdropFilter:"blur(10px)"},formatter:(e,t)=>["Aktiflik %"===t?`%${e}`:e,"Aktiflik %"===t?"Aktiflik":"Ekip Sayısı"],labelStyle:{fontWeight:"bold",marginBottom:"4px"}}),(0,i.jsx)(k.s,{wrapperStyle:{paddingTop:"20px",fontSize:"12px"}}),(0,i.jsx)(w.G,{yAxisId:"left",type:"monotone",dataKey:"activityPercentage",stroke:"#8b5cf6",strokeWidth:3,fill:"url(#colorActivity)",fillOpacity:1,name:"Aktiflik %",dot:{fill:"#8b5cf6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#8b5cf6",strokeWidth:2,fill:"#fff"},animationBegin:0,animationDuration:2e3,animationEasing:"ease-out"}),(0,i.jsx)(w.G,{yAxisId:"right",type:"monotone",dataKey:"teamSize",stroke:"#10b981",strokeWidth:3,fill:"url(#colorTeam)",fillOpacity:1,name:"Ekip Sayısı",dot:{fill:"#059669",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#059669",strokeWidth:2,fill:"#fff"},animationBegin:500,animationDuration:2e3,animationEasing:"ease-out"})]})})}),(0,i.jsx)("div",{className:"flex justify-between mt-4 px-8",children:A.monthlyActivityTrend.map((e,t)=>(0,i.jsx)("div",{className:"text-center",children:e.newMembers>0&&(0,i.jsxs)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:["+",e.newMembers," yeni \xfcye"]})},t))}),(0,i.jsxs)("div",{className:"grid grid-cols-4 gap-4 pt-6 border-t border-gray-100 mt-4",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsxs)("div",{className:"text-lg font-bold text-purple-600",children:["%",A.monthlyActivityTrend[A.monthlyActivityTrend.length-1]?.activityPercentage||0]}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Bu Ay Aktiflik"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-lg font-bold text-green-600",children:A.monthlyActivityTrend[A.monthlyActivityTrend.length-1]?.teamSize||0}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Mevcut Ekip"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-lg font-bold text-blue-600",children:A.monthlyActivityTrend.reduce((e,t)=>e+t.newMembers,0)}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Toplam Yeni \xdcye"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsxs)("div",{className:"text-lg font-bold text-gray-900",children:["%",Math.round(A.monthlyActivityTrend.reduce((e,t)=>e+t.activityPercentage,0)/A.monthlyActivityTrend.length)]}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Ortalama Aktiflik"})]})]})]}),(0,i.jsxs)("div",{className:"mt-8 bg-white rounded-xl shadow-lg p-6",children:[(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"Hızlı İşlemler"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,i.jsxs)("a",{href:"/earnings",className:"flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors",children:[(0,i.jsx)(m.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium text-gray-900",children:"Kazan\xe7 Detayları"}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"T\xfcm kazan\xe7 ge\xe7mişinizi g\xf6r\xfcnt\xfcleyin"})]})]}),(0,i.jsxs)("a",{href:"/my-team",className:"flex items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors",children:[(0,i.jsx)(c.A,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium text-gray-900",children:"Ekip Y\xf6netimi"}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"Ekibinizi detaylı olarak inceleyin"})]})]}),(0,i.jsxs)("a",{href:"/team-tree",className:"flex items-center p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors",children:[(0,i.jsx)(g.A,{className:"h-6 w-6 text-orange-600 mr-3"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium text-gray-900",children:"Ekip Ağacı Y\xf6netimi"}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"Ekip hiyerarşinizi g\xf6rselleştirin"})]})]}),P?(0,i.jsxs)("a",{href:"/pending-products",className:"flex items-center p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors",children:[(0,i.jsx)(y.A,{className:"h-6 w-6 text-green-600 mr-3"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium text-gray-900",children:"\xdcr\xfcn Y\xf6netimi"}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"\xdcr\xfcn ekle ve onay bekleyenleri g\xf6r\xfcnt\xfcle"})]})]}):(0,i.jsxs)("div",{className:"flex items-center p-4 border border-gray-200 rounded-lg bg-gray-50 opacity-50",children:[(0,i.jsx)(y.A,{className:"h-6 w-6 text-gray-400 mr-3"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium text-gray-500",children:"\xdcr\xfcn Y\xf6netimi"}),(0,i.jsx)("p",{className:"text-sm text-gray-400",children:"Sadece satıcılar i\xe7in"})]})]})]})]})]})})}},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28947:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41312:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},42579:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var i=s(65239),a=s(48088),r=s(88170),l=s.n(r),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let o={children:["",{children:["panel",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,79833)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\panel\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\panel\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new i.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/panel/page",pathname:"/panel",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},53411:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var i=s(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),l=e=>{let t=r(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:r="",children:l,iconNode:c,...m},x)=>(0,i.createElement)("svg",{ref:x,...o,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:n("lucide",r),...!l&&!d(m)&&{"aria-hidden":"true"},...m},[...c.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(l)?l:[l]])),m=(e,t)=>{let s=(0,i.forwardRef)(({className:s,...r},d)=>(0,i.createElement)(c,{ref:d,iconNode:t,className:n(`lucide-${a(l(e))}`,`lucide-${e}`,s),...r}));return s.displayName=l(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63138:(e,t,s)=>{Promise.resolve().then(s.bind(s,79833))},64398:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},67268:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var i=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},79833:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});let i=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\panel\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\panel\\page.tsx","default")},81290:(e,t,s)=>{Promise.resolve().then(s.bind(s,22635))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84299:(e,t,s)=>{"use strict";s.d(t,{At:()=>l,Zq:()=>i,dt:()=>n,wI:()=>r,x1:()=>a});let i=[{id:1,distributorId:2,date:"2023-04-15",reference:"Aylin Şahin",points:120,amount:240.5,level:1,percentage:8},{id:2,distributorId:2,date:"2023-04-20",reference:"Emre Kılı\xe7",points:85,amount:170,level:2,percentage:5},{id:3,distributorId:2,date:"2023-04-28",reference:"Arda Altun",points:150,amount:300.75,level:1,percentage:8},{id:4,distributorId:2,date:"2023-05-05",reference:"Burak \xd6zt\xfcrk",points:60,amount:120.25,level:3,percentage:3},{id:5,distributorId:2,date:"2023-05-12",reference:"Selin Kara",points:200,amount:400,level:1,percentage:8}],a=[{id:101,firstName:"Aylin",lastName:"Şahin",level:1,joinDate:"2023-02-10",points:450,isActive:!0},{id:102,firstName:"Emre",lastName:"Kılı\xe7",level:2,joinDate:"2023-02-15",points:320,isActive:!0},{id:103,firstName:"Arda",lastName:"Altun",level:1,joinDate:"2023-03-01",points:580,isActive:!0},{id:104,firstName:"Burak",lastName:"\xd6zt\xfcrk",level:3,joinDate:"2023-03-12",points:150,isActive:!1},{id:105,firstName:"Selin",lastName:"Kara",level:1,joinDate:"2023-03-25",points:650,isActive:!0},{id:106,firstName:"Murat",lastName:"Aydın",level:2,joinDate:"2023-04-05",points:280,isActive:!0},{id:107,firstName:"Elif",lastName:"\xc7elik",level:3,joinDate:"2023-04-18",points:120,isActive:!1}],r={totalEarnings:4250.75,monthlyPoints:350,monthlyActivityPercentage:75,teamSize:a.length,monthlyBalance:1230.5,totalBalance:4250.75,totalPoints:2150,organizationPoints:5680,monthlyEarnings:[{month:"Ocak",earnings:850.25,activity:65},{month:"Şubat",earnings:920.5,activity:70},{month:"Mart",earnings:1050.75,activity:80},{month:"Nisan",earnings:1230.5,activity:75},{month:"Mayıs",earnings:980.25,activity:72}],monthlyPointsHistory:[{month:"Ocak",points:280,target:300},{month:"Şubat",points:320,target:300},{month:"Mart",points:390,target:350},{month:"Nisan",points:420,target:350},{month:"Mayıs",points:350,target:400},{month:"Haziran",points:310,target:400}],monthlyActivityTrend:[{month:"Ocak",activityPercentage:65,teamSize:4,newMembers:1},{month:"Şubat",activityPercentage:70,teamSize:5,newMembers:1},{month:"Mart",activityPercentage:80,teamSize:6,newMembers:1},{month:"Nisan",activityPercentage:75,teamSize:6,newMembers:0},{month:"Mayıs",activityPercentage:72,teamSize:7,newMembers:1},{month:"Haziran",activityPercentage:78,teamSize:7,newMembers:0}],nextLevelPoints:500,currentLevel:"G\xfcm\xfcş",nextLevel:"Altın"},l={id:1,name:"Distrib\xfct\xf6r (Sen)",level:3,points:2150,joinDate:"2023-01-01",isActive:!0,totalEarnings:4250.75,monthlyPoints:350,children:{left:{id:101,name:"Aylin Şahin",level:1,points:450,joinDate:"2023-02-10",isActive:!0,parentId:1,position:"left",totalEarnings:1200.5,monthlyPoints:120,children:{left:{id:103,name:"Arda Altun",level:1,points:580,joinDate:"2023-03-01",isActive:!0,parentId:101,position:"left",totalEarnings:850.25,monthlyPoints:95,children:{left:{id:107,name:"Elif \xc7elik",level:3,points:120,joinDate:"2023-04-18",isActive:!1,parentId:103,position:"left",totalEarnings:240,monthlyPoints:25}}},right:{id:105,name:"Selin Kara",level:1,points:650,joinDate:"2023-03-25",isActive:!0,parentId:101,position:"right",totalEarnings:980.75,monthlyPoints:135}}},right:{id:102,name:"Emre Kılı\xe7",level:2,points:320,joinDate:"2023-02-15",isActive:!0,parentId:1,position:"right",totalEarnings:750.25,monthlyPoints:85,children:{left:{id:104,name:"Burak \xd6zt\xfcrk",level:3,points:150,joinDate:"2023-03-12",isActive:!1,parentId:102,position:"left",totalEarnings:320,monthlyPoints:40},right:{id:106,name:"Murat Aydın",level:2,points:280,joinDate:"2023-04-05",isActive:!0,parentId:102,position:"right",totalEarnings:480.5,monthlyPoints:65}}}}},n={totalMembers:7,activeMembers:5,totalLevels:3,totalPoints:2550,totalEarnings:8950,monthlyGrowth:12.5}},86561:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},93508:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[447,181,658,450,85],()=>s(42579));module.exports=i})();