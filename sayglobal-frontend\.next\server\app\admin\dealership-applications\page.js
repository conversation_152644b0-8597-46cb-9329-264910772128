(()=>{var e={};e.id=784,e.ids=[784],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6278:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\admin\\\\dealership-applications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\dealership-applications\\page.tsx","default")},10022:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16316:(e,t,a)=>{Promise.resolve().then(a.bind(a,6278))},17313:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},19080:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25334:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35071:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40228:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48730:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62688:(e,t,a)=>{"use strict";a.d(t,{A:()=>m});var s=a(43210);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:a=2,absoluteStrokeWidth:r,className:i="",children:l,iconNode:o,...m},x)=>(0,s.createElement)("svg",{ref:x,...c,width:t,height:t,stroke:e,strokeWidth:r?24*Number(a)/Number(t):a,className:n("lucide",i),...!l&&!d(m)&&{"aria-hidden":"true"},...m},[...o.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(l)?l:[l]])),m=(e,t)=>{let a=(0,s.forwardRef)(({className:a,...i},d)=>(0,s.createElement)(o,{ref:d,iconNode:t,className:n(`lucide-${r(l(e))}`,`lucide-${e}`,a),...i}));return a.displayName=l(e),a}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68899:(e,t,a)=>{"use strict";a.d(t,{CM:()=>r,nA:()=>i,vM:()=>s});let s=[{id:1,userId:6,userName:"Mehmet Yılmaz",userEmail:"<EMAIL>",applicationData:{firstName:"Mehmet",lastName:"Yılmaz",email:"<EMAIL>",phone:"+90 ************",mainProductCategory:"Elektronik",subProductCategories:["Telefon","Bilgisayar","Aksesuar"],estimatedProductCount:"50-100 adet",sampleProductListUrl:"https://example.com/products",companyName:"Yılmaz Elektronik Ltd. Şti.",taxNumber:"1234567890",taxOffice:"İstanbul Vergi Dairesi",companyAddress:"Atat\xfcrk Cad. No:123 Kadık\xf6y/İstanbul",authorizedPersonName:"Mehmet Yılmaz",authorizedPersonTcId:"12345678901",alternativeContactNumber:"+90 ************",userAgreementAccepted:!0,dealershipAgreementAccepted:!0,privacyPolicyAccepted:!0},status:"pending",submittedAt:"2024-01-15T10:30:00Z"},{id:2,userId:7,userName:"Ayşe Kaya",userEmail:"<EMAIL>",applicationData:{firstName:"Ayşe",lastName:"Kaya",email:"<EMAIL>",phone:"+90 ************",mainProductCategory:"Ev & Yaşam",subProductCategories:["Mutfak","Dekorasyon"],estimatedProductCount:"20-50 adet",companyName:"Kaya Ev Tekstili",taxNumber:"0987654321",taxOffice:"Ankara Vergi Dairesi",companyAddress:"Kızılay Cad. No:456 \xc7ankaya/Ankara",authorizedPersonName:"Ayşe Kaya",authorizedPersonTcId:"10987654321",alternativeContactNumber:"+90 ************",userAgreementAccepted:!0,dealershipAgreementAccepted:!0,privacyPolicyAccepted:!0},status:"approved",submittedAt:"2024-01-10T14:20:00Z",reviewedAt:"2024-01-12T09:15:00Z",reviewedBy:1,adminNotes:"Başvuru uygun bulunmuştur."}],r=["Elektronik","Ev & Yaşam","Giyim & Aksesuar","Spor & Outdoor","Kozmetik & Kişisel Bakım","Kitap & Kırtasiye","Oyuncak & Hobi","Otomotiv","Anne & Bebek","Diğer"],i=["1-10 adet","11-25 adet","26-50 adet","51-100 adet","101-250 adet","250+ adet"]},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},74460:(e,t,a)=>{Promise.resolve().then(a.bind(a,78830))},78830:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>D});var s=a(60687),r=a(43210),i=a.n(r),l=a(15908),n=a(16189),d=a(68899),c=a(48730),o=a(5336),m=a(35071),x=a(58869),p=a(17313),u=a(19080),h=a(25334);let y=(0,a(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),g=({application:e,isOpen:t,onClose:a,onApprove:i,onReject:l,adminNotes:n,setAdminNotes:d,isProcessing:g,onShowWarning:f})=>((0,r.useEffect)(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),t&&e)?(0,s.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-sm overflow-y-auto h-full w-full z-50",children:(0,s.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white",children:(0,s.jsxs)("div",{className:"mt-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Başvuru Detayları"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(e=>{switch(e){case"pending":return(0,s.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[(0,s.jsx)(c.A,{className:"w-3 h-3 mr-1"}),"Beklemede"]});case"approved":return(0,s.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,s.jsx)(o.A,{className:"w-3 h-3 mr-1"}),"Onaylandı"]});case"rejected":return(0,s.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[(0,s.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"Reddedildi"]})}})(e.status),(0,s.jsx)("button",{onClick:a,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}),(0,s.jsxs)("div",{className:"space-y-6 max-h-96 overflow-y-auto",children:[(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Başvuran Bilgileri"]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Ad Soyad:"}),(0,s.jsxs)("span",{className:"ml-2 font-medium text-gray-700",children:[e.applicationData.firstName," ",e.applicationData.lastName]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"E-posta:"}),(0,s.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:e.applicationData.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Telefon:"}),(0,s.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:e.applicationData.phone})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Alternatif Tel:"}),(0,s.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:e.applicationData.alternativeContactNumber})]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Şirket Bilgileri"]}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Şirket Adı:"}),(0,s.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:e.applicationData.companyName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Vergi No:"}),(0,s.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:e.applicationData.taxNumber})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Vergi Dairesi:"}),(0,s.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:e.applicationData.taxOffice})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Adres:"}),(0,s.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:e.applicationData.companyAddress})]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"\xdcr\xfcn Bilgileri"]}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Ana Kategori:"}),(0,s.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:e.applicationData.mainProductCategory})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Tahmini \xdcr\xfcn Sayısı:"}),(0,s.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:e.applicationData.estimatedProductCount})]}),e.applicationData.sampleProductListUrl&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"\xd6rnek URL:"}),(0,s.jsxs)("a",{href:e.applicationData.sampleProductListUrl,target:"_blank",rel:"noopener noreferrer",className:"ml-2 text-blue-600 hover:text-blue-800 flex items-center",children:["Link ",(0,s.jsx)(h.A,{className:"h-3 w-3 ml-1"})]})]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"Yetkili Kişi"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Ad Soyad:"}),(0,s.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:e.applicationData.authorizedPersonName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"T.C. No:"}),(0,s.jsx)("span",{className:"ml-2 font-medium text-gray-700",children:e.applicationData.authorizedPersonTcId})]})]})]}),e.adminNotes&&(0,s.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,s.jsxs)("h4",{className:"font-medium text-gray-900 mb-2 flex items-center",children:[(0,s.jsx)(y,{className:"h-4 w-4 mr-2"}),"Admin Notları"]}),(0,s.jsx)("p",{className:"text-sm text-gray-700",children:e.adminNotes}),e.reviewedAt&&(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["İncelenme: ",new Date(e.reviewedAt).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})]})]})]}),"pending"===e.status&&(0,s.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Admin Notları"}),(0,s.jsx)("textarea",{value:n,onChange:e=>d(e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 text-black",placeholder:"Başvuru hakkında notlarınızı yazın..."})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,s.jsxs)("button",{onClick:()=>{if(!n.trim()){f&&f();return}l(e.id,n)},disabled:g,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 flex items-center",children:[g?(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Reddet"]}),(0,s.jsxs)("button",{onClick:()=>{i(e.id,n)},disabled:g,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center",children:[g?(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,s.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Onayla"]})]})]})]})})}):null;var f=a(88920),j=a(26001),v=a(93613),N=a(11860);let b=({isOpen:e,onClose:t,type:a,title:r,message:l,autoClose:n=!0,autoCloseDelay:d=3e3})=>(i().useEffect(()=>{if(e&&n){let e=setTimeout(()=>{t()},d);return()=>clearTimeout(e)}},[e,n,d,t]),(0,s.jsx)(f.N,{children:e&&(0,s.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,s.jsxs)("div",{className:"flex min-h-full items-center justify-center p-4",children:[(0,s.jsx)(j.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:t,className:"fixed inset-0 bg-black/20 backdrop-blur-sm transition-opacity"}),(0,s.jsxs)(j.P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{type:"spring",duration:.5},className:`relative transform overflow-hidden rounded-xl bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6 border-2 ${(()=>{switch(a){case"success":default:return"bg-green-50 border-green-200";case"error":return"bg-red-50 border-red-200";case"warning":return"bg-yellow-50 border-yellow-200"}})()}`,children:[(0,s.jsx)("button",{onClick:t,className:"absolute right-4 top-4 text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)(N.A,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(j.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring",stiffness:200},className:"mx-auto flex h-12 w-12 items-center justify-center",children:(()=>{switch(a){case"success":default:return(0,s.jsx)(o.A,{className:"h-12 w-12 text-green-600"});case"error":return(0,s.jsx)(m.A,{className:"h-12 w-12 text-red-600"});case"warning":return(0,s.jsx)(v.A,{className:"h-12 w-12 text-yellow-600"})}})()}),(0,s.jsxs)(j.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},className:"mt-3 text-center sm:mt-5",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold leading-6 text-gray-900",children:r}),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)("p",{className:"text-sm text-gray-600",children:l})})]})]}),(0,s.jsx)(j.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},className:"mt-5 sm:mt-6",children:(0,s.jsx)("button",{type:"button",onClick:t,className:`inline-flex w-full justify-center rounded-lg px-3 py-2 text-sm font-semibold text-white shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${(()=>{switch(a){case"success":default:return"bg-green-600 hover:bg-green-700 focus:ring-green-500";case"error":return"bg-red-600 hover:bg-red-700 focus:ring-red-500";case"warning":return"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500"}})()}`,children:"Tamam"})}),n&&(0,s.jsx)(j.P.div,{initial:{width:"100%"},animate:{width:"0%"},transition:{duration:d/1e3,ease:"linear"},className:`absolute bottom-0 left-0 h-1 rounded-b-xl ${"success"===a?"bg-green-400":"error"===a?"bg-red-400":"bg-yellow-400"}`})]})]})})}));var w=a(99891),k=a(10022),A=a(99270),C=a(40228),P=a(13861);let D=()=>{let{user:e,isLoading:t,updateUserRole:a}=(0,l.A)(),i=(0,n.useRouter)(),[p,u]=(0,r.useState)(d.vM),[h,y]=(0,r.useState)(null),[f,j]=(0,r.useState)("all"),[v,N]=(0,r.useState)(""),[D,M]=(0,r.useState)(!1),[z,S]=(0,r.useState)(""),[q,B]=(0,r.useState)({isOpen:!1,type:"success",title:"",message:""});if((0,r.useEffect)(()=>{t||e&&"admin"===e.role||i.push("/login")},[e,t,i]),t)return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})});if(!e||"admin"!==e.role)return null;let E=p.filter(e=>{let t="all"===f||e.status===f,a=""===v||e.userName.toLowerCase().includes(v.toLowerCase())||e.userEmail.toLowerCase().includes(v.toLowerCase())||e.applicationData.companyName.toLowerCase().includes(v.toLowerCase());return t&&a}),L=(e,t,a)=>{B({isOpen:!0,type:e,title:t,message:a})},T=async(t,s,r)=>{let i=r||z;M(!0);try{let r=p.find(e=>e.id===t);if(!r)throw Error("Başvuru bulunamadı");await new Promise(e=>setTimeout(e,1500)),u(a=>a.map(a=>a.id===t?{...a,status:"approve"===s?"approved":"rejected",reviewedAt:new Date().toISOString(),reviewedBy:e.id,adminNotes:i.trim()||("approve"===s?"Başvuru onaylandı.":"Başvuru reddedildi.")}:a)),"approve"===s?a(r.userId,"dealership",!0,"approved"):"reject"===s&&a(r.userId,"customer",!1,"rejected"),y(null),S(""),"approve"===s?L("success","Başvuru Onaylandı!","Satıcı başvurusu başarıyla onaylandı ve kullanıcı hesabı aktif edildi."):L("error","Başvuru Reddedildi!","Satıcı başvurusu reddedildi ve kullanıcıya bilgilendirme e-postası g\xf6nderildi.")}catch(e){L("error","İşlem Başarısız!","İşlem sırasında bir hata oluştu. L\xfctfen tekrar deneyin.")}finally{M(!1)}},O=e=>{switch(e){case"pending":return(0,s.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[(0,s.jsx)(c.A,{className:"w-3 h-3 mr-1"}),"Beklemede"]});case"approved":return(0,s.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,s.jsx)(o.A,{className:"w-3 h-3 mr-1"}),"Onaylandı"]});case"rejected":return(0,s.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[(0,s.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"Reddedildi"]})}},R=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Satıcı Başvuruları"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Platforma katılmak isteyen satıcı başvurularını y\xf6netin"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 bg-red-100 px-4 py-2 rounded-lg",children:[(0,s.jsx)(w.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)("span",{className:"text-red-800 font-medium",children:"Admin Paneli"})]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-yellow-500",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Bekleyen"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.filter(e=>"pending"===e.status).length})]}),(0,s.jsx)(c.A,{className:"h-8 w-8 text-yellow-600"})]})}),(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Onaylanan"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.filter(e=>"approved"===e.status).length})]}),(0,s.jsx)(o.A,{className:"h-8 w-8 text-green-600"})]})}),(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Reddedilen"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.filter(e=>"rejected"===e.status).length})]}),(0,s.jsx)(m.A,{className:"h-8 w-8 text-red-600"})]})}),(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.length})]}),(0,s.jsx)(k.A,{className:"h-8 w-8 text-blue-600"})]})})]}),(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 mb-8",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(A.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsx)("input",{type:"text",placeholder:"Ad, email veya şirket ara...",value:v,onChange:e=>N(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 w-64 text-black"})]}),(0,s.jsxs)("select",{value:f,onChange:e=>j(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-600",children:[(0,s.jsx)("option",{value:"all",children:"T\xfcm Durumlar"}),(0,s.jsx)("option",{value:"pending",children:"Bekleyen"}),(0,s.jsx)("option",{value:"approved",children:"Onaylanan"}),(0,s.jsx)("option",{value:"rejected",children:"Reddedilen"})]})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[E.length," sonu\xe7 g\xf6steriliyor"]})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Başvuran"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Şirket"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kategori"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Başvuru Tarihi"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Durum"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"İşlemler"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:E.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,s.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,s.jsx)(x.A,{className:"h-5 w-5 text-gray-600"})})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.userEmail})]})]})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.applicationData.companyName}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.applicationData.taxNumber})]}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.applicationData.mainProductCategory}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.applicationData.estimatedProductCount})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-900",children:[(0,s.jsx)(C.A,{className:"h-4 w-4 mr-1 text-gray-400"}),R(e.submittedAt)]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:O(e.status)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,s.jsxs)("button",{onClick:()=>y(e),className:"text-red-600 hover:text-red-900 flex items-center",children:[(0,s.jsx)(P.A,{className:"h-4 w-4 mr-1"}),"İncele"]})})]},e.id))})]})}),0===E.length&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(k.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,s.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Başvuru bulunamadı"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Arama kriterlerinize uygun başvuru bulunmuyor."})]})]}),(0,s.jsx)(g,{application:h,isOpen:!!h,onClose:()=>y(null),onApprove:(e,t)=>T(e,"approve",t),onReject:(e,t)=>T(e,"reject",t),adminNotes:z,setAdminNotes:S,isProcessing:D,onShowWarning:()=>L("warning","A\xe7ıklama Gerekli","Red işlemi i\xe7in a\xe7ıklama yazmanız gerekmektedir.")}),(0,s.jsx)(b,{isOpen:q.isOpen,onClose:()=>{B(e=>({...e,isOpen:!1}))},type:q.type,title:q.title,message:q.message})]})})}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93613:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},97753:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var s=a(65239),r=a(48088),i=a(88170),l=a.n(i),n=a(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(t,d);let c={children:["",{children:["admin",{children:["dealership-applications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,6278)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\dealership-applications\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\dealership-applications\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/dealership-applications/page",pathname:"/admin/dealership-applications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},99270:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},99891:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[447,181,658,85],()=>a(97753));module.exports=s})();