(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7323],{5041:(t,e,r)=>{"use strict";r.d(e,{n:()=>c});var n=r(12115),i=r(34560),s=r(7165),o=r(25910),a=r(52020),u=class extends o.Q{#t;#e=void 0;#r;#n;constructor(t,e){super(),this.#t=t,this.setOptions(e),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){let e=this.options;this.options=this.#t.defaultMutationOptions(t),(0,a.f8)(this.options,e)||this.#t.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),e?.mutationKey&&this.options.mutationKey&&(0,a.EN)(e.mutationKey)!==(0,a.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(t){this.#i(),this.#s(t)}getCurrentResult(){return this.#e}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#i(),this.#s()}mutate(t,e){return this.#n=e,this.#r?.removeObserver(this),this.#r=this.#t.getMutationCache().build(this.#t,this.options),this.#r.addObserver(this),this.#r.execute(t)}#i(){let t=this.#r?.state??(0,i.$)();this.#e={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#s(t){s.jG.batch(()=>{if(this.#n&&this.hasListeners()){let e=this.#e.variables,r=this.#e.context;t?.type==="success"?(this.#n.onSuccess?.(t.data,e,r),this.#n.onSettled?.(t.data,null,e,r)):t?.type==="error"&&(this.#n.onError?.(t.error,e,r),this.#n.onSettled?.(void 0,t.error,e,r))}this.listeners.forEach(t=>{t(this.#e)})})}},l=r(26715);function c(t,e){let r=(0,l.jE)(e),[i]=n.useState(()=>new u(r,t));n.useEffect(()=>{i.setOptions(t)},[i,t]);let o=n.useSyncExternalStore(n.useCallback(t=>i.subscribe(s.jG.batchCalls(t)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),c=n.useCallback((t,e)=>{i.mutate(t,e).catch(a.lQ)},[i]);if(o.error&&(0,a.GU)(i.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:c,mutateAsync:o.mutate}}},6784:(t,e,r)=>{"use strict";r.d(e,{II:()=>h,v_:()=>u,wm:()=>c});var n=r(50920),i=r(21239),s=r(73504),o=r(52020);function a(t){return Math.min(1e3*2**t,3e4)}function u(t){return(t??"online")!=="online"||i.t.isOnline()}var l=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function c(t){return t instanceof l}function h(t){let e,r=!1,c=0,h=!1,f=(0,s.T)(),d=()=>n.m.isFocused()&&("always"===t.networkMode||i.t.isOnline())&&t.canRun(),p=()=>u(t.networkMode)&&t.canRun(),y=r=>{h||(h=!0,t.onSuccess?.(r),e?.(),f.resolve(r))},m=r=>{h||(h=!0,t.onError?.(r),e?.(),f.reject(r))},g=()=>new Promise(r=>{e=t=>{(h||d())&&r(t)},t.onPause?.()}).then(()=>{e=void 0,h||t.onContinue?.()}),b=()=>{let e;if(h)return;let n=0===c?t.initialPromise:void 0;try{e=n??t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(y).catch(e=>{if(h)return;let n=t.retry??3*!o.S$,i=t.retryDelay??a,s="function"==typeof i?i(c,e):i,u=!0===n||"number"==typeof n&&c<n||"function"==typeof n&&n(c,e);if(r||!u)return void m(e);c++,t.onFail?.(c,e),(0,o.yy)(s).then(()=>d()?void 0:g()).then(()=>{r?m(e):b()})})};return{promise:f,cancel:e=>{h||(m(new l(e)),t.abort?.())},continue:()=>(e?.(),f),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:p,start:()=>(p()?b():g().then(b),f)}}},7165:(t,e,r)=>{"use strict";r.d(e,{jG:()=>i});var n=t=>setTimeout(t,0),i=function(){let t=[],e=0,r=t=>{t()},i=t=>{t()},s=n,o=n=>{e?t.push(n):s(()=>{r(n)})},a=()=>{let e=t;t=[],e.length&&s(()=>{i(()=>{e.forEach(t=>{r(t)})})})};return{batch:t=>{let r;e++;try{r=t()}finally{--e||a()}return r},batchCalls:t=>(...e)=>{o(()=>{t(...e)})},schedule:o,setNotifyFunction:t=>{r=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{s=t}}}()},21239:(t,e,r)=>{"use strict";r.d(e,{t:()=>s});var n=r(25910),i=r(52020),s=new class extends n.Q{#o=!0;#a;#u;constructor(){super(),this.#u=t=>{if(!i.S$&&window.addEventListener){let e=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#a||this.setEventListener(this.#u)}onUnsubscribe(){this.hasListeners()||(this.#a?.(),this.#a=void 0)}setEventListener(t){this.#u=t,this.#a?.(),this.#a=t(this.setOnline.bind(this))}setOnline(t){this.#o!==t&&(this.#o=t,this.listeners.forEach(e=>{e(t)}))}isOnline(){return this.#o}}},23464:(t,e,r)=>{"use strict";let n;r.d(e,{A:()=>el});var i,s,o,a={};function u(t,e){return function(){return t.apply(e,arguments)}}r.r(a),r.d(a,{hasBrowserEnv:()=>tf,hasStandardBrowserEnv:()=>tp,hasStandardBrowserWebWorkerEnv:()=>ty,navigator:()=>td,origin:()=>tm});var l=r(49509);let{toString:c}=Object.prototype,{getPrototypeOf:h}=Object,{iterator:f,toStringTag:d}=Symbol,p=(t=>e=>{let r=c.call(e);return t[r]||(t[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),y=t=>(t=t.toLowerCase(),e=>p(e)===t),m=t=>e=>typeof e===t,{isArray:g}=Array,b=m("undefined"),v=y("ArrayBuffer"),E=m("string"),w=m("function"),R=m("number"),S=t=>null!==t&&"object"==typeof t,O=t=>{if("object"!==p(t))return!1;let e=h(t);return(null===e||e===Object.prototype||null===Object.getPrototypeOf(e))&&!(d in t)&&!(f in t)},T=y("Date"),C=y("File"),A=y("Blob"),_=y("FileList"),x=y("URLSearchParams"),[U,I,L,P]=["ReadableStream","Request","Response","Headers"].map(y);function N(t,e,{allOwnKeys:r=!1}={}){let n,i;if(null!=t)if("object"!=typeof t&&(t=[t]),g(t))for(n=0,i=t.length;n<i;n++)e.call(null,t[n],n,t);else{let i,s=r?Object.getOwnPropertyNames(t):Object.keys(t),o=s.length;for(n=0;n<o;n++)i=s[n],e.call(null,t[i],i,t)}}function j(t,e){let r;e=e.toLowerCase();let n=Object.keys(t),i=n.length;for(;i-- >0;)if(e===(r=n[i]).toLowerCase())return r;return null}let B="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,F=t=>!b(t)&&t!==B,D=(t=>e=>t&&e instanceof t)("undefined"!=typeof Uint8Array&&h(Uint8Array)),k=y("HTMLFormElement"),M=(({hasOwnProperty:t})=>(e,r)=>t.call(e,r))(Object.prototype),q=y("RegExp"),Q=(t,e)=>{let r=Object.getOwnPropertyDescriptors(t),n={};N(r,(r,i)=>{let s;!1!==(s=e(r,i,t))&&(n[i]=s||r)}),Object.defineProperties(t,n)},z=y("AsyncFunction"),G=(i="function"==typeof setImmediate,s=w(B.postMessage),i?setImmediate:s?((t,e)=>(B.addEventListener("message",({source:r,data:n})=>{r===B&&n===t&&e.length&&e.shift()()},!1),r=>{e.push(r),B.postMessage(t,"*")}))(`axios@${Math.random()}`,[]):t=>setTimeout(t)),H="undefined"!=typeof queueMicrotask?queueMicrotask.bind(B):void 0!==l&&l.nextTick||G,J={isArray:g,isArrayBuffer:v,isBuffer:function(t){return null!==t&&!b(t)&&null!==t.constructor&&!b(t.constructor)&&w(t.constructor.isBuffer)&&t.constructor.isBuffer(t)},isFormData:t=>{let e;return t&&("function"==typeof FormData&&t instanceof FormData||w(t.append)&&("formdata"===(e=p(t))||"object"===e&&w(t.toString)&&"[object FormData]"===t.toString()))},isArrayBufferView:function(t){let e;return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&v(t.buffer)},isString:E,isNumber:R,isBoolean:t=>!0===t||!1===t,isObject:S,isPlainObject:O,isReadableStream:U,isRequest:I,isResponse:L,isHeaders:P,isUndefined:b,isDate:T,isFile:C,isBlob:A,isRegExp:q,isFunction:w,isStream:t=>S(t)&&w(t.pipe),isURLSearchParams:x,isTypedArray:D,isFileList:_,forEach:N,merge:function t(){let{caseless:e}=F(this)&&this||{},r={},n=(n,i)=>{let s=e&&j(r,i)||i;O(r[s])&&O(n)?r[s]=t(r[s],n):O(n)?r[s]=t({},n):g(n)?r[s]=n.slice():r[s]=n};for(let t=0,e=arguments.length;t<e;t++)arguments[t]&&N(arguments[t],n);return r},extend:(t,e,r,{allOwnKeys:n}={})=>(N(e,(e,n)=>{r&&w(e)?t[n]=u(e,r):t[n]=e},{allOwnKeys:n}),t),trim:t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:t=>(65279===t.charCodeAt(0)&&(t=t.slice(1)),t),inherits:(t,e,r,n)=>{t.prototype=Object.create(e.prototype,n),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),r&&Object.assign(t.prototype,r)},toFlatObject:(t,e,r,n)=>{let i,s,o,a={};if(e=e||{},null==t)return e;do{for(s=(i=Object.getOwnPropertyNames(t)).length;s-- >0;)o=i[s],(!n||n(o,t,e))&&!a[o]&&(e[o]=t[o],a[o]=!0);t=!1!==r&&h(t)}while(t&&(!r||r(t,e))&&t!==Object.prototype);return e},kindOf:p,kindOfTest:y,endsWith:(t,e,r)=>{t=String(t),(void 0===r||r>t.length)&&(r=t.length),r-=e.length;let n=t.indexOf(e,r);return -1!==n&&n===r},toArray:t=>{if(!t)return null;if(g(t))return t;let e=t.length;if(!R(e))return null;let r=Array(e);for(;e-- >0;)r[e]=t[e];return r},forEachEntry:(t,e)=>{let r,n=(t&&t[f]).call(t);for(;(r=n.next())&&!r.done;){let n=r.value;e.call(t,n[0],n[1])}},matchAll:(t,e)=>{let r,n=[];for(;null!==(r=t.exec(e));)n.push(r);return n},isHTMLForm:k,hasOwnProperty:M,hasOwnProp:M,reduceDescriptors:Q,freezeMethods:t=>{Q(t,(e,r)=>{if(w(t)&&-1!==["arguments","caller","callee"].indexOf(r))return!1;if(w(t[r])){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},toObjectSet:(t,e)=>{let r={};return(g(t)?t:String(t).split(e)).forEach(t=>{r[t]=!0}),r},toCamelCase:t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,e,r){return e.toUpperCase()+r}),noop:()=>{},toFiniteNumber:(t,e)=>null!=t&&Number.isFinite(t*=1)?t:e,findKey:j,global:B,isContextDefined:F,isSpecCompliantForm:function(t){return!!(t&&w(t.append)&&"FormData"===t[d]&&t[f])},toJSONObject:t=>{let e=Array(10),r=(t,n)=>{if(S(t)){if(e.indexOf(t)>=0)return;if(!("toJSON"in t)){e[n]=t;let i=g(t)?[]:{};return N(t,(t,e)=>{let s=r(t,n+1);b(s)||(i[e]=s)}),e[n]=void 0,i}}return t};return r(t,0)},isAsyncFn:z,isThenable:t=>t&&(S(t)||w(t))&&w(t.then)&&w(t.catch),setImmediate:G,asap:H,isIterable:t=>null!=t&&w(t[f])};function K(t,e,r,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=t,this.name="AxiosError",e&&(this.code=e),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}J.inherits(K,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:J.toJSONObject(this.config),code:this.code,status:this.status}}});let W=K.prototype,$={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{$[t]={value:t}}),Object.defineProperties(K,$),Object.defineProperty(W,"isAxiosError",{value:!0}),K.from=(t,e,r,n,i,s)=>{let o=Object.create(W);return J.toFlatObject(t,o,function(t){return t!==Error.prototype},t=>"isAxiosError"!==t),K.call(o,t.message,e,r,n,i),o.cause=t,o.name=t.name,s&&Object.assign(o,s),o};var V=r(49641).Buffer;function X(t){return J.isPlainObject(t)||J.isArray(t)}function Y(t){return J.endsWith(t,"[]")?t.slice(0,-2):t}function Z(t,e,r){return t?t.concat(e).map(function(t,e){return t=Y(t),!r&&e?"["+t+"]":t}).join(r?".":""):e}let tt=J.toFlatObject(J,{},null,function(t){return/^is[A-Z]/.test(t)}),te=function(t,e,r){if(!J.isObject(t))throw TypeError("target must be an object");e=e||new FormData;let n=(r=J.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(t,e){return!J.isUndefined(e[t])})).metaTokens,i=r.visitor||l,s=r.dots,o=r.indexes,a=(r.Blob||"undefined"!=typeof Blob&&Blob)&&J.isSpecCompliantForm(e);if(!J.isFunction(i))throw TypeError("visitor must be a function");function u(t){if(null===t)return"";if(J.isDate(t))return t.toISOString();if(J.isBoolean(t))return t.toString();if(!a&&J.isBlob(t))throw new K("Blob is not supported. Use a Buffer instead.");return J.isArrayBuffer(t)||J.isTypedArray(t)?a&&"function"==typeof Blob?new Blob([t]):V.from(t):t}function l(t,r,i){let a=t;if(t&&!i&&"object"==typeof t)if(J.endsWith(r,"{}"))r=n?r:r.slice(0,-2),t=JSON.stringify(t);else{var l;if(J.isArray(t)&&(l=t,J.isArray(l)&&!l.some(X))||(J.isFileList(t)||J.endsWith(r,"[]"))&&(a=J.toArray(t)))return r=Y(r),a.forEach(function(t,n){J.isUndefined(t)||null===t||e.append(!0===o?Z([r],n,s):null===o?r:r+"[]",u(t))}),!1}return!!X(t)||(e.append(Z(i,r,s),u(t)),!1)}let c=[],h=Object.assign(tt,{defaultVisitor:l,convertValue:u,isVisitable:X});if(!J.isObject(t))throw TypeError("data must be an object");return!function t(r,n){if(!J.isUndefined(r)){if(-1!==c.indexOf(r))throw Error("Circular reference detected in "+n.join("."));c.push(r),J.forEach(r,function(r,s){!0===(!(J.isUndefined(r)||null===r)&&i.call(e,r,J.isString(s)?s.trim():s,n,h))&&t(r,n?n.concat(s):[s])}),c.pop()}}(t),e};function tr(t){let e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(t){return e[t]})}function tn(t,e){this._pairs=[],t&&te(t,this,e)}let ti=tn.prototype;function ts(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function to(t,e,r){let n;if(!e)return t;let i=r&&r.encode||ts;J.isFunction(r)&&(r={serialize:r});let s=r&&r.serialize;if(n=s?s(e,r):J.isURLSearchParams(e)?e.toString():new tn(e,r).toString(i)){let e=t.indexOf("#");-1!==e&&(t=t.slice(0,e)),t+=(-1===t.indexOf("?")?"?":"&")+n}return t}ti.append=function(t,e){this._pairs.push([t,e])},ti.toString=function(t){let e=t?function(e){return t.call(this,e,tr)}:tr;return this._pairs.map(function(t){return e(t[0])+"="+e(t[1])},"").join("&")};class ta{constructor(){this.handlers=[]}use(t,e,r){return this.handlers.push({fulfilled:t,rejected:e,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){J.forEach(this.handlers,function(e){null!==e&&t(e)})}}let tu={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},tl="undefined"!=typeof URLSearchParams?URLSearchParams:tn,tc="undefined"!=typeof FormData?FormData:null,th="undefined"!=typeof Blob?Blob:null,tf="undefined"!=typeof window&&"undefined"!=typeof document,td="object"==typeof navigator&&navigator||void 0,tp=tf&&(!td||0>["ReactNative","NativeScript","NS"].indexOf(td.product)),ty="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,tm=tf&&window.location.href||"http://localhost",tg={...a,isBrowser:!0,classes:{URLSearchParams:tl,FormData:tc,Blob:th},protocols:["http","https","file","blob","url","data"]},tb=function(t){if(J.isFormData(t)&&J.isFunction(t.entries)){let e={};return J.forEachEntry(t,(t,r)=>{!function t(e,r,n,i){let s=e[i++];if("__proto__"===s)return!0;let o=Number.isFinite(+s),a=i>=e.length;return(s=!s&&J.isArray(n)?n.length:s,a)?J.hasOwnProp(n,s)?n[s]=[n[s],r]:n[s]=r:(n[s]&&J.isObject(n[s])||(n[s]=[]),t(e,r,n[s],i)&&J.isArray(n[s])&&(n[s]=function(t){let e,r,n={},i=Object.keys(t),s=i.length;for(e=0;e<s;e++)n[r=i[e]]=t[r];return n}(n[s]))),!o}(J.matchAll(/\w+|\[(\w*)]/g,t).map(t=>"[]"===t[0]?"":t[1]||t[0]),r,e,0)}),e}return null},tv={transitional:tu,adapter:["xhr","http","fetch"],transformRequest:[function(t,e){let r,n=e.getContentType()||"",i=n.indexOf("application/json")>-1,s=J.isObject(t);if(s&&J.isHTMLForm(t)&&(t=new FormData(t)),J.isFormData(t))return i?JSON.stringify(tb(t)):t;if(J.isArrayBuffer(t)||J.isBuffer(t)||J.isStream(t)||J.isFile(t)||J.isBlob(t)||J.isReadableStream(t))return t;if(J.isArrayBufferView(t))return t.buffer;if(J.isURLSearchParams(t))return e.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();if(s){if(n.indexOf("application/x-www-form-urlencoded")>-1){var o,a;return(o=t,a=this.formSerializer,te(o,new tg.classes.URLSearchParams,Object.assign({visitor:function(t,e,r,n){return tg.isNode&&J.isBuffer(t)?(this.append(e,t.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},a))).toString()}if((r=J.isFileList(t))||n.indexOf("multipart/form-data")>-1){let e=this.env&&this.env.FormData;return te(r?{"files[]":t}:t,e&&new e,this.formSerializer)}}if(s||i){e.setContentType("application/json",!1);var u=t;if(J.isString(u))try{return(0,JSON.parse)(u),J.trim(u)}catch(t){if("SyntaxError"!==t.name)throw t}return(0,JSON.stringify)(u)}return t}],transformResponse:[function(t){let e=this.transitional||tv.transitional,r=e&&e.forcedJSONParsing,n="json"===this.responseType;if(J.isResponse(t)||J.isReadableStream(t))return t;if(t&&J.isString(t)&&(r&&!this.responseType||n)){let r=e&&e.silentJSONParsing;try{return JSON.parse(t)}catch(t){if(!r&&n){if("SyntaxError"===t.name)throw K.from(t,K.ERR_BAD_RESPONSE,this,null,this.response);throw t}}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:tg.classes.FormData,Blob:tg.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};J.forEach(["delete","get","head","post","put","patch"],t=>{tv.headers[t]={}});let tE=J.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),tw=t=>{let e,r,n,i={};return t&&t.split("\n").forEach(function(t){n=t.indexOf(":"),e=t.substring(0,n).trim().toLowerCase(),r=t.substring(n+1).trim(),!e||i[e]&&tE[e]||("set-cookie"===e?i[e]?i[e].push(r):i[e]=[r]:i[e]=i[e]?i[e]+", "+r:r)}),i},tR=Symbol("internals");function tS(t){return t&&String(t).trim().toLowerCase()}function tO(t){return!1===t||null==t?t:J.isArray(t)?t.map(tO):String(t)}let tT=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function tC(t,e,r,n,i){if(J.isFunction(n))return n.call(this,e,r);if(i&&(e=r),J.isString(e)){if(J.isString(n))return -1!==e.indexOf(n);if(J.isRegExp(n))return n.test(e)}}class tA{constructor(t){t&&this.set(t)}set(t,e,r){let n=this;function i(t,e,r){let i=tS(e);if(!i)throw Error("header name must be a non-empty string");let s=J.findKey(n,i);s&&void 0!==n[s]&&!0!==r&&(void 0!==r||!1===n[s])||(n[s||e]=tO(t))}let s=(t,e)=>J.forEach(t,(t,r)=>i(t,r,e));if(J.isPlainObject(t)||t instanceof this.constructor)s(t,e);else if(J.isString(t)&&(t=t.trim())&&!tT(t))s(tw(t),e);else if(J.isObject(t)&&J.isIterable(t)){let r={},n,i;for(let e of t){if(!J.isArray(e))throw TypeError("Object iterator must return a key-value pair");r[i=e[0]]=(n=r[i])?J.isArray(n)?[...n,e[1]]:[n,e[1]]:e[1]}s(r,e)}else null!=t&&i(e,t,r);return this}get(t,e){if(t=tS(t)){let r=J.findKey(this,t);if(r){let t=this[r];if(!e)return t;if(!0===e){let e,r=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;e=n.exec(t);)r[e[1]]=e[2];return r}if(J.isFunction(e))return e.call(this,t,r);if(J.isRegExp(e))return e.exec(t);throw TypeError("parser must be boolean|regexp|function")}}}has(t,e){if(t=tS(t)){let r=J.findKey(this,t);return!!(r&&void 0!==this[r]&&(!e||tC(this,this[r],r,e)))}return!1}delete(t,e){let r=this,n=!1;function i(t){if(t=tS(t)){let i=J.findKey(r,t);i&&(!e||tC(r,r[i],i,e))&&(delete r[i],n=!0)}}return J.isArray(t)?t.forEach(i):i(t),n}clear(t){let e=Object.keys(this),r=e.length,n=!1;for(;r--;){let i=e[r];(!t||tC(this,this[i],i,t,!0))&&(delete this[i],n=!0)}return n}normalize(t){let e=this,r={};return J.forEach(this,(n,i)=>{let s=J.findKey(r,i);if(s){e[s]=tO(n),delete e[i];return}let o=t?i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,e,r)=>e.toUpperCase()+r):String(i).trim();o!==i&&delete e[i],e[o]=tO(n),r[o]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){let e=Object.create(null);return J.forEach(this,(r,n)=>{null!=r&&!1!==r&&(e[n]=t&&J.isArray(r)?r.join(", "):r)}),e}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,e])=>t+": "+e).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...e){let r=new this(t);return e.forEach(t=>r.set(t)),r}static accessor(t){let e=(this[tR]=this[tR]={accessors:{}}).accessors,r=this.prototype;function n(t){let n=tS(t);if(!e[n]){let i=J.toCamelCase(" "+t);["get","set","has"].forEach(e=>{Object.defineProperty(r,e+i,{value:function(r,n,i){return this[e].call(this,t,r,n,i)},configurable:!0})}),e[n]=!0}}return J.isArray(t)?t.forEach(n):n(t),this}}function t_(t,e){let r=this||tv,n=e||r,i=tA.from(n.headers),s=n.data;return J.forEach(t,function(t){s=t.call(r,s,i.normalize(),e?e.status:void 0)}),i.normalize(),s}function tx(t){return!!(t&&t.__CANCEL__)}function tU(t,e,r){K.call(this,null==t?"canceled":t,K.ERR_CANCELED,e,r),this.name="CanceledError"}function tI(t,e,r){let n=r.config.validateStatus;!r.status||!n||n(r.status)?t(r):e(new K("Request failed with status code "+r.status,[K.ERR_BAD_REQUEST,K.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}tA.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),J.reduceDescriptors(tA.prototype,({value:t},e)=>{let r=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(t){this[r]=t}}}),J.freezeMethods(tA),J.inherits(tU,K,{__CANCEL__:!0});let tL=function(t,e){let r,n=Array(t=t||10),i=Array(t),s=0,o=0;return e=void 0!==e?e:1e3,function(a){let u=Date.now(),l=i[o];r||(r=u),n[s]=a,i[s]=u;let c=o,h=0;for(;c!==s;)h+=n[c++],c%=t;if((s=(s+1)%t)===o&&(o=(o+1)%t),u-r<e)return;let f=l&&u-l;return f?Math.round(1e3*h/f):void 0}},tP=function(t,e){let r,n,i=0,s=1e3/e,o=(e,s=Date.now())=>{i=s,r=null,n&&(clearTimeout(n),n=null),t.apply(null,e)};return[(...t)=>{let e=Date.now(),a=e-i;a>=s?o(t,e):(r=t,n||(n=setTimeout(()=>{n=null,o(r)},s-a)))},()=>r&&o(r)]},tN=(t,e,r=3)=>{let n=0,i=tL(50,250);return tP(r=>{let s=r.loaded,o=r.lengthComputable?r.total:void 0,a=s-n,u=i(a);n=s,t({loaded:s,total:o,progress:o?s/o:void 0,bytes:a,rate:u||void 0,estimated:u&&o&&s<=o?(o-s)/u:void 0,event:r,lengthComputable:null!=o,[e?"download":"upload"]:!0})},r)},tj=(t,e)=>{let r=null!=t;return[n=>e[0]({lengthComputable:r,total:t,loaded:n}),e[1]]},tB=t=>(...e)=>J.asap(()=>t(...e)),tF=tg.hasStandardBrowserEnv?((t,e)=>r=>(r=new URL(r,tg.origin),t.protocol===r.protocol&&t.host===r.host&&(e||t.port===r.port)))(new URL(tg.origin),tg.navigator&&/(msie|trident)/i.test(tg.navigator.userAgent)):()=>!0,tD=tg.hasStandardBrowserEnv?{write(t,e,r,n,i,s){let o=[t+"="+encodeURIComponent(e)];J.isNumber(r)&&o.push("expires="+new Date(r).toGMTString()),J.isString(n)&&o.push("path="+n),J.isString(i)&&o.push("domain="+i),!0===s&&o.push("secure"),document.cookie=o.join("; ")},read(t){let e=document.cookie.match(RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function tk(t,e,r){let n=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e);return t&&(n||!1==r)?e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t:e}let tM=t=>t instanceof tA?{...t}:t;function tq(t,e){e=e||{};let r={};function n(t,e,r,n){return J.isPlainObject(t)&&J.isPlainObject(e)?J.merge.call({caseless:n},t,e):J.isPlainObject(e)?J.merge({},e):J.isArray(e)?e.slice():e}function i(t,e,r,i){return J.isUndefined(e)?J.isUndefined(t)?void 0:n(void 0,t,r,i):n(t,e,r,i)}function s(t,e){if(!J.isUndefined(e))return n(void 0,e)}function o(t,e){return J.isUndefined(e)?J.isUndefined(t)?void 0:n(void 0,t):n(void 0,e)}function a(r,i,s){return s in e?n(r,i):s in t?n(void 0,r):void 0}let u={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(t,e,r)=>i(tM(t),tM(e),r,!0)};return J.forEach(Object.keys(Object.assign({},t,e)),function(n){let s=u[n]||i,o=s(t[n],e[n],n);J.isUndefined(o)&&s!==a||(r[n]=o)}),r}let tQ=t=>{let e,r=tq({},t),{data:n,withXSRFToken:i,xsrfHeaderName:s,xsrfCookieName:o,headers:a,auth:u}=r;if(r.headers=a=tA.from(a),r.url=to(tk(r.baseURL,r.url,r.allowAbsoluteUrls),t.params,t.paramsSerializer),u&&a.set("Authorization","Basic "+btoa((u.username||"")+":"+(u.password?unescape(encodeURIComponent(u.password)):""))),J.isFormData(n)){if(tg.hasStandardBrowserEnv||tg.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(e=a.getContentType())){let[t,...r]=e?e.split(";").map(t=>t.trim()).filter(Boolean):[];a.setContentType([t||"multipart/form-data",...r].join("; "))}}if(tg.hasStandardBrowserEnv&&(i&&J.isFunction(i)&&(i=i(r)),i||!1!==i&&tF(r.url))){let t=s&&o&&tD.read(o);t&&a.set(s,t)}return r},tz="undefined"!=typeof XMLHttpRequest&&function(t){return new Promise(function(e,r){let n,i,s,o,a,u=tQ(t),l=u.data,c=tA.from(u.headers).normalize(),{responseType:h,onUploadProgress:f,onDownloadProgress:d}=u;function p(){o&&o(),a&&a(),u.cancelToken&&u.cancelToken.unsubscribe(n),u.signal&&u.signal.removeEventListener("abort",n)}let y=new XMLHttpRequest;function m(){if(!y)return;let n=tA.from("getAllResponseHeaders"in y&&y.getAllResponseHeaders());tI(function(t){e(t),p()},function(t){r(t),p()},{data:h&&"text"!==h&&"json"!==h?y.response:y.responseText,status:y.status,statusText:y.statusText,headers:n,config:t,request:y}),y=null}y.open(u.method.toUpperCase(),u.url,!0),y.timeout=u.timeout,"onloadend"in y?y.onloadend=m:y.onreadystatechange=function(){y&&4===y.readyState&&(0!==y.status||y.responseURL&&0===y.responseURL.indexOf("file:"))&&setTimeout(m)},y.onabort=function(){y&&(r(new K("Request aborted",K.ECONNABORTED,t,y)),y=null)},y.onerror=function(){r(new K("Network Error",K.ERR_NETWORK,t,y)),y=null},y.ontimeout=function(){let e=u.timeout?"timeout of "+u.timeout+"ms exceeded":"timeout exceeded",n=u.transitional||tu;u.timeoutErrorMessage&&(e=u.timeoutErrorMessage),r(new K(e,n.clarifyTimeoutError?K.ETIMEDOUT:K.ECONNABORTED,t,y)),y=null},void 0===l&&c.setContentType(null),"setRequestHeader"in y&&J.forEach(c.toJSON(),function(t,e){y.setRequestHeader(e,t)}),J.isUndefined(u.withCredentials)||(y.withCredentials=!!u.withCredentials),h&&"json"!==h&&(y.responseType=u.responseType),d&&([s,a]=tN(d,!0),y.addEventListener("progress",s)),f&&y.upload&&([i,o]=tN(f),y.upload.addEventListener("progress",i),y.upload.addEventListener("loadend",o)),(u.cancelToken||u.signal)&&(n=e=>{y&&(r(!e||e.type?new tU(null,t,y):e),y.abort(),y=null)},u.cancelToken&&u.cancelToken.subscribe(n),u.signal&&(u.signal.aborted?n():u.signal.addEventListener("abort",n)));let g=function(t){let e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}(u.url);if(g&&-1===tg.protocols.indexOf(g))return void r(new K("Unsupported protocol "+g+":",K.ERR_BAD_REQUEST,t));y.send(l||null)})},tG=(t,e)=>{let{length:r}=t=t?t.filter(Boolean):[];if(e||r){let r,n=new AbortController,i=function(t){if(!r){r=!0,o();let e=t instanceof Error?t:this.reason;n.abort(e instanceof K?e:new tU(e instanceof Error?e.message:e))}},s=e&&setTimeout(()=>{s=null,i(new K(`timeout ${e} of ms exceeded`,K.ETIMEDOUT))},e),o=()=>{t&&(s&&clearTimeout(s),s=null,t.forEach(t=>{t.unsubscribe?t.unsubscribe(i):t.removeEventListener("abort",i)}),t=null)};t.forEach(t=>t.addEventListener("abort",i));let{signal:a}=n;return a.unsubscribe=()=>J.asap(o),a}},tH=function*(t,e){let r,n=t.byteLength;if(!e||n<e)return void(yield t);let i=0;for(;i<n;)r=i+e,yield t.slice(i,r),i=r},tJ=async function*(t,e){for await(let r of tK(t))yield*tH(r,e)},tK=async function*(t){if(t[Symbol.asyncIterator])return void(yield*t);let e=t.getReader();try{for(;;){let{done:t,value:r}=await e.read();if(t)break;yield r}}finally{await e.cancel()}},tW=(t,e,r,n)=>{let i,s=tJ(t,e),o=0,a=t=>{!i&&(i=!0,n&&n(t))};return new ReadableStream({async pull(t){try{let{done:e,value:n}=await s.next();if(e){a(),t.close();return}let i=n.byteLength;if(r){let t=o+=i;r(t)}t.enqueue(new Uint8Array(n))}catch(t){throw a(t),t}},cancel:t=>(a(t),s.return())},{highWaterMark:2})},t$="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,tV=t$&&"function"==typeof ReadableStream,tX=t$&&("function"==typeof TextEncoder?(n=new TextEncoder,t=>n.encode(t)):async t=>new Uint8Array(await new Response(t).arrayBuffer())),tY=(t,...e)=>{try{return!!t(...e)}catch(t){return!1}},tZ=tV&&tY(()=>{let t=!1,e=new Request(tg.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e}),t0=tV&&tY(()=>J.isReadableStream(new Response("").body)),t1={stream:t0&&(t=>t.body)};t$&&(o=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(t=>{t1[t]||(t1[t]=J.isFunction(o[t])?e=>e[t]():(e,r)=>{throw new K(`Response type '${t}' is not supported`,K.ERR_NOT_SUPPORT,r)})}));let t2=async t=>{if(null==t)return 0;if(J.isBlob(t))return t.size;if(J.isSpecCompliantForm(t)){let e=new Request(tg.origin,{method:"POST",body:t});return(await e.arrayBuffer()).byteLength}return J.isArrayBufferView(t)||J.isArrayBuffer(t)?t.byteLength:(J.isURLSearchParams(t)&&(t+=""),J.isString(t))?(await tX(t)).byteLength:void 0},t5=async(t,e)=>{let r=J.toFiniteNumber(t.getContentLength());return null==r?t2(e):r},t3={http:null,xhr:tz,fetch:t$&&(async t=>{let e,r,{url:n,method:i,data:s,signal:o,cancelToken:a,timeout:u,onDownloadProgress:l,onUploadProgress:c,responseType:h,headers:f,withCredentials:d="same-origin",fetchOptions:p}=tQ(t);h=h?(h+"").toLowerCase():"text";let y=tG([o,a&&a.toAbortSignal()],u),m=y&&y.unsubscribe&&(()=>{y.unsubscribe()});try{if(c&&tZ&&"get"!==i&&"head"!==i&&0!==(r=await t5(f,s))){let t,e=new Request(n,{method:"POST",body:s,duplex:"half"});if(J.isFormData(s)&&(t=e.headers.get("content-type"))&&f.setContentType(t),e.body){let[t,n]=tj(r,tN(tB(c)));s=tW(e.body,65536,t,n)}}J.isString(d)||(d=d?"include":"omit");let o="credentials"in Request.prototype;e=new Request(n,{...p,signal:y,method:i.toUpperCase(),headers:f.normalize().toJSON(),body:s,duplex:"half",credentials:o?d:void 0});let a=await fetch(e,p),u=t0&&("stream"===h||"response"===h);if(t0&&(l||u&&m)){let t={};["status","statusText","headers"].forEach(e=>{t[e]=a[e]});let e=J.toFiniteNumber(a.headers.get("content-length")),[r,n]=l&&tj(e,tN(tB(l),!0))||[];a=new Response(tW(a.body,65536,r,()=>{n&&n(),m&&m()}),t)}h=h||"text";let g=await t1[J.findKey(t1,h)||"text"](a,t);return!u&&m&&m(),await new Promise((r,n)=>{tI(r,n,{data:g,headers:tA.from(a.headers),status:a.status,statusText:a.statusText,config:t,request:e})})}catch(r){if(m&&m(),r&&"TypeError"===r.name&&/Load failed|fetch/i.test(r.message))throw Object.assign(new K("Network Error",K.ERR_NETWORK,t,e),{cause:r.cause||r});throw K.from(r,r&&r.code,t,e)}})};J.forEach(t3,(t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch(t){}Object.defineProperty(t,"adapterName",{value:e})}});let t6=t=>`- ${t}`,t8=t=>J.isFunction(t)||null===t||!1===t,t4={getAdapter:t=>{let e,r,{length:n}=t=J.isArray(t)?t:[t],i={};for(let s=0;s<n;s++){let n;if(r=e=t[s],!t8(e)&&void 0===(r=t3[(n=String(e)).toLowerCase()]))throw new K(`Unknown adapter '${n}'`);if(r)break;i[n||"#"+s]=r}if(!r){let t=Object.entries(i).map(([t,e])=>`adapter ${t} `+(!1===e?"is not supported by the environment":"is not available in the build"));throw new K("There is no suitable adapter to dispatch the request "+(n?t.length>1?"since :\n"+t.map(t6).join("\n"):" "+t6(t[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r}};function t9(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new tU(null,t)}function t7(t){return t9(t),t.headers=tA.from(t.headers),t.data=t_.call(t,t.transformRequest),-1!==["post","put","patch"].indexOf(t.method)&&t.headers.setContentType("application/x-www-form-urlencoded",!1),t4.getAdapter(t.adapter||tv.adapter)(t).then(function(e){return t9(t),e.data=t_.call(t,t.transformResponse,e),e.headers=tA.from(e.headers),e},function(e){return!tx(e)&&(t9(t),e&&e.response&&(e.response.data=t_.call(t,t.transformResponse,e.response),e.response.headers=tA.from(e.response.headers))),Promise.reject(e)})}let et="1.10.0",ee={};["object","boolean","number","function","string","symbol"].forEach((t,e)=>{ee[t]=function(r){return typeof r===t||"a"+(e<1?"n ":" ")+t}});let er={};ee.transitional=function(t,e,r){function n(t,e){return"[Axios v"+et+"] Transitional option '"+t+"'"+e+(r?". "+r:"")}return(r,i,s)=>{if(!1===t)throw new K(n(i," has been removed"+(e?" in "+e:"")),K.ERR_DEPRECATED);return e&&!er[i]&&(er[i]=!0,console.warn(n(i," has been deprecated since v"+e+" and will be removed in the near future"))),!t||t(r,i,s)}},ee.spelling=function(t){return(e,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};let en={assertOptions:function(t,e,r){if("object"!=typeof t)throw new K("options must be an object",K.ERR_BAD_OPTION_VALUE);let n=Object.keys(t),i=n.length;for(;i-- >0;){let s=n[i],o=e[s];if(o){let e=t[s],r=void 0===e||o(e,s,t);if(!0!==r)throw new K("option "+s+" must be "+r,K.ERR_BAD_OPTION_VALUE);continue}if(!0!==r)throw new K("Unknown option "+s,K.ERR_BAD_OPTION)}},validators:ee},ei=en.validators;class es{constructor(t){this.defaults=t||{},this.interceptors={request:new ta,response:new ta}}async request(t,e){try{return await this._request(t,e)}catch(t){if(t instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=Error();let r=e.stack?e.stack.replace(/^.+\n/,""):"";try{t.stack?r&&!String(t.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(t.stack+="\n"+r):t.stack=r}catch(t){}}throw t}}_request(t,e){let r,n;"string"==typeof t?(e=e||{}).url=t:e=t||{};let{transitional:i,paramsSerializer:s,headers:o}=e=tq(this.defaults,e);void 0!==i&&en.assertOptions(i,{silentJSONParsing:ei.transitional(ei.boolean),forcedJSONParsing:ei.transitional(ei.boolean),clarifyTimeoutError:ei.transitional(ei.boolean)},!1),null!=s&&(J.isFunction(s)?e.paramsSerializer={serialize:s}:en.assertOptions(s,{encode:ei.function,serialize:ei.function},!0)),void 0!==e.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?e.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:e.allowAbsoluteUrls=!0),en.assertOptions(e,{baseUrl:ei.spelling("baseURL"),withXsrfToken:ei.spelling("withXSRFToken")},!0),e.method=(e.method||this.defaults.method||"get").toLowerCase();let a=o&&J.merge(o.common,o[e.method]);o&&J.forEach(["delete","get","head","post","put","patch","common"],t=>{delete o[t]}),e.headers=tA.concat(a,o);let u=[],l=!0;this.interceptors.request.forEach(function(t){("function"!=typeof t.runWhen||!1!==t.runWhen(e))&&(l=l&&t.synchronous,u.unshift(t.fulfilled,t.rejected))});let c=[];this.interceptors.response.forEach(function(t){c.push(t.fulfilled,t.rejected)});let h=0;if(!l){let t=[t7.bind(this),void 0];for(t.unshift.apply(t,u),t.push.apply(t,c),n=t.length,r=Promise.resolve(e);h<n;)r=r.then(t[h++],t[h++]);return r}n=u.length;let f=e;for(h=0;h<n;){let t=u[h++],e=u[h++];try{f=t(f)}catch(t){e.call(this,t);break}}try{r=t7.call(this,f)}catch(t){return Promise.reject(t)}for(h=0,n=c.length;h<n;)r=r.then(c[h++],c[h++]);return r}getUri(t){return to(tk((t=tq(this.defaults,t)).baseURL,t.url,t.allowAbsoluteUrls),t.params,t.paramsSerializer)}}J.forEach(["delete","get","head","options"],function(t){es.prototype[t]=function(e,r){return this.request(tq(r||{},{method:t,url:e,data:(r||{}).data}))}}),J.forEach(["post","put","patch"],function(t){function e(e){return function(r,n,i){return this.request(tq(i||{},{method:t,headers:e?{"Content-Type":"multipart/form-data"}:{},url:r,data:n}))}}es.prototype[t]=e(),es.prototype[t+"Form"]=e(!0)});class eo{constructor(t){let e;if("function"!=typeof t)throw TypeError("executor must be a function.");this.promise=new Promise(function(t){e=t});let r=this;this.promise.then(t=>{if(!r._listeners)return;let e=r._listeners.length;for(;e-- >0;)r._listeners[e](t);r._listeners=null}),this.promise.then=t=>{let e,n=new Promise(t=>{r.subscribe(t),e=t}).then(t);return n.cancel=function(){r.unsubscribe(e)},n},t(function(t,n,i){r.reason||(r.reason=new tU(t,n,i),e(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason)return void t(this.reason);this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;let e=this._listeners.indexOf(t);-1!==e&&this._listeners.splice(e,1)}toAbortSignal(){let t=new AbortController,e=e=>{t.abort(e)};return this.subscribe(e),t.signal.unsubscribe=()=>this.unsubscribe(e),t.signal}static source(){let t;return{token:new eo(function(e){t=e}),cancel:t}}}let ea={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ea).forEach(([t,e])=>{ea[e]=t});let eu=function t(e){let r=new es(e),n=u(es.prototype.request,r);return J.extend(n,es.prototype,r,{allOwnKeys:!0}),J.extend(n,r,null,{allOwnKeys:!0}),n.create=function(r){return t(tq(e,r))},n}(tv);eu.Axios=es,eu.CanceledError=tU,eu.CancelToken=eo,eu.isCancel=tx,eu.VERSION=et,eu.toFormData=te,eu.AxiosError=K,eu.Cancel=eu.CanceledError,eu.all=function(t){return Promise.all(t)},eu.spread=function(t){return function(e){return t.apply(null,e)}},eu.isAxiosError=function(t){return J.isObject(t)&&!0===t.isAxiosError},eu.mergeConfig=tq,eu.AxiosHeaders=tA,eu.formToJSON=t=>tb(J.isHTMLForm(t)?new FormData(t):t),eu.getAdapter=t4.getAdapter,eu.HttpStatusCode=ea,eu.default=eu;let el=eu},25910:(t,e,r)=>{"use strict";r.d(e,{Q:()=>n});var n=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},26715:(t,e,r)=>{"use strict";r.d(e,{Ht:()=>a,jE:()=>o});var n=r(12115),i=r(95155),s=n.createContext(void 0),o=t=>{let e=n.useContext(s);if(t)return t;if(!e)throw Error("No QueryClient set, use QueryClientProvider to set one");return e},a=t=>{let{client:e,children:r}=t;return n.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,i.jsx)(s.Provider,{value:e,children:r})}},31425:t=>{"use strict";let e=new Set(["ENOTFOUND","ENETUNREACH","UNABLE_TO_GET_ISSUER_CERT","UNABLE_TO_GET_CRL","UNABLE_TO_DECRYPT_CERT_SIGNATURE","UNABLE_TO_DECRYPT_CRL_SIGNATURE","UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY","CERT_SIGNATURE_FAILURE","CRL_SIGNATURE_FAILURE","CERT_NOT_YET_VALID","CERT_HAS_EXPIRED","CRL_NOT_YET_VALID","CRL_HAS_EXPIRED","ERROR_IN_CERT_NOT_BEFORE_FIELD","ERROR_IN_CERT_NOT_AFTER_FIELD","ERROR_IN_CRL_LAST_UPDATE_FIELD","ERROR_IN_CRL_NEXT_UPDATE_FIELD","OUT_OF_MEM","DEPTH_ZERO_SELF_SIGNED_CERT","SELF_SIGNED_CERT_IN_CHAIN","UNABLE_TO_GET_ISSUER_CERT_LOCALLY","UNABLE_TO_VERIFY_LEAF_SIGNATURE","CERT_CHAIN_TOO_LONG","CERT_REVOKED","INVALID_CA","PATH_LENGTH_EXCEEDED","INVALID_PURPOSE","CERT_UNTRUSTED","CERT_REJECTED","HOSTNAME_MISMATCH"]);t.exports=t=>!e.has(t&&t.code)},32784:(t,e,r)=>{"use strict";r.d(e,{Ay:()=>b});var n=r(31425);let i="axios-retry";function s(t){return!(t.response||!t.code||["ERR_CANCELED","ECONNABORTED"].includes(t.code))&&n(t)}let o=["get","head","options"],a=o.concat(["put","delete"]);function u(t){return"ECONNABORTED"!==t.code&&(!t.response||429===t.response.status||t.response.status>=500&&t.response.status<=599)}function l(t){return!!t.config?.method&&u(t)&&-1!==a.indexOf(t.config.method)}function c(t){return s(t)||l(t)}function h(t){let e=t?.response?.headers["retry-after"];if(!e)return 0;let r=1e3*(Number(e)||0);return 0===r&&(r=(new Date(e).valueOf()||0)-Date.now()),Math.max(0,r)}let f={retries:3,retryCondition:c,retryDelay:function(t=0,e){return Math.max(0,h(e))},shouldResetTimeout:!1,onRetry:()=>{},onMaxRetryTimesExceeded:()=>{},validateResponse:null};function d(t,e,r=!1){var n;let s=(n=e||{},{...f,...n,...t[i]});return s.retryCount=s.retryCount||0,(!s.lastRequestTime||r)&&(s.lastRequestTime=Date.now()),t[i]=s,s}async function p(t,e){let{retries:r,retryCondition:n}=t,i=(t.retryCount||0)<r&&n(e);if("object"==typeof i)try{let t=await i;return!1!==t}catch(t){return!1}return i}async function y(t,e,r,n){e.retryCount+=1;let{retryDelay:i,shouldResetTimeout:s,onRetry:o}=e,a=i(e.retryCount,r);if(t.defaults.agent===n.agent&&delete n.agent,t.defaults.httpAgent===n.httpAgent&&delete n.httpAgent,t.defaults.httpsAgent===n.httpsAgent&&delete n.httpsAgent,!s&&n.timeout&&e.lastRequestTime){let t=Date.now()-e.lastRequestTime,i=n.timeout-t-a;if(i<=0)return Promise.reject(r);n.timeout=i}return(n.transformRequest=[t=>t],await o(e.retryCount,r,n),n.signal?.aborted)?Promise.resolve(t(n)):new Promise(e=>{let r=()=>{clearTimeout(i),e(t(n))},i=setTimeout(()=>{e(t(n)),n.signal?.removeEventListener&&n.signal.removeEventListener("abort",r)},a);n.signal?.addEventListener&&n.signal.addEventListener("abort",r,{once:!0})})}async function m(t,e){t.retryCount>=t.retries&&await t.onMaxRetryTimesExceeded(e,t.retryCount)}let g=(t,e)=>({requestInterceptorId:t.interceptors.request.use(t=>(d(t,e,!0),t[i]?.validateResponse&&(t.validateStatus=()=>!1),t)),responseInterceptorId:t.interceptors.response.use(null,async r=>{let{config:n}=r;if(!n)return Promise.reject(r);let i=d(n,e);return r.response&&i.validateResponse?.(r.response)?r.response:await p(i,r)?y(t,i,r,n):(await m(i,r),Promise.reject(r))})});g.isNetworkError=s,g.isSafeRequestError=function(t){return!!t.config?.method&&u(t)&&-1!==o.indexOf(t.config.method)},g.isIdempotentRequestError=l,g.isNetworkOrIdempotentRequestError=c,g.exponentialDelay=function(t=0,e,r=100){let n=Math.max(2**t*r,h(e)),i=.2*n*Math.random();return n+i},g.linearDelay=function(t=100){return(e=0,r)=>Math.max(e*t,h(r))},g.isRetryableError=u;let b=g},32960:(t,e,r)=>{"use strict";r.d(e,{I:()=>A});var n=r(50920),i=r(7165),s=r(39853),o=r(25910),a=r(73504),u=r(52020),l=class extends o.Q{constructor(t,e){super(),this.options=e,this.#t=t,this.#l=null,this.#c=(0,a.T)(),this.options.experimental_prefetchInRender||this.#c.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}#t;#h=void 0;#f=void 0;#e=void 0;#d;#p;#c;#l;#y;#m;#g;#b;#v;#E;#w=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#h.addObserver(this),c(this.#h,this.options)?this.#R():this.updateResult(),this.#S())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return h(this.#h,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return h(this.#h,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#O(),this.#T(),this.#h.removeObserver(this)}setOptions(t){let e=this.options,r=this.#h;if(this.options=this.#t.defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,u.Eh)(this.options.enabled,this.#h))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#C(),this.#h.setOptions(this.options),e._defaulted&&!(0,u.f8)(this.options,e)&&this.#t.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#h,observer:this});let n=this.hasListeners();n&&f(this.#h,r,this.options,e)&&this.#R(),this.updateResult(),n&&(this.#h!==r||(0,u.Eh)(this.options.enabled,this.#h)!==(0,u.Eh)(e.enabled,this.#h)||(0,u.d2)(this.options.staleTime,this.#h)!==(0,u.d2)(e.staleTime,this.#h))&&this.#A();let i=this.#_();n&&(this.#h!==r||(0,u.Eh)(this.options.enabled,this.#h)!==(0,u.Eh)(e.enabled,this.#h)||i!==this.#E)&&this.#x(i)}getOptimisticResult(t){var e,r;let n=this.#t.getQueryCache().build(this.#t,t),i=this.createResult(n,t);return e=this,r=i,(0,u.f8)(e.getCurrentResult(),r)||(this.#e=i,this.#p=this.options,this.#d=this.#h.state),i}getCurrentResult(){return this.#e}trackResult(t,e){return new Proxy(t,{get:(t,r)=>(this.trackProp(r),e?.(r),Reflect.get(t,r))})}trackProp(t){this.#w.add(t)}getCurrentQuery(){return this.#h}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){let e=this.#t.defaultQueryOptions(t),r=this.#t.getQueryCache().build(this.#t,e);return r.fetch().then(()=>this.createResult(r,e))}fetch(t){return this.#R({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#e))}#R(t){this.#C();let e=this.#h.fetch(this.options,t);return t?.throwOnError||(e=e.catch(u.lQ)),e}#A(){this.#O();let t=(0,u.d2)(this.options.staleTime,this.#h);if(u.S$||this.#e.isStale||!(0,u.gn)(t))return;let e=(0,u.j3)(this.#e.dataUpdatedAt,t);this.#b=setTimeout(()=>{this.#e.isStale||this.updateResult()},e+1)}#_(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#h):this.options.refetchInterval)??!1}#x(t){this.#T(),this.#E=t,!u.S$&&!1!==(0,u.Eh)(this.options.enabled,this.#h)&&(0,u.gn)(this.#E)&&0!==this.#E&&(this.#v=setInterval(()=>{(this.options.refetchIntervalInBackground||n.m.isFocused())&&this.#R()},this.#E))}#S(){this.#A(),this.#x(this.#_())}#O(){this.#b&&(clearTimeout(this.#b),this.#b=void 0)}#T(){this.#v&&(clearInterval(this.#v),this.#v=void 0)}createResult(t,e){let r,n=this.#h,i=this.options,o=this.#e,l=this.#d,h=this.#p,p=t!==n?t.state:this.#f,{state:y}=t,m={...y},g=!1;if(e._optimisticResults){let r=this.hasListeners(),o=!r&&c(t,e),a=r&&f(t,n,e,i);(o||a)&&(m={...m,...(0,s.k)(y.data,t.options)}),"isRestoring"===e._optimisticResults&&(m.fetchStatus="idle")}let{error:b,errorUpdatedAt:v,status:E}=m;r=m.data;let w=!1;if(void 0!==e.placeholderData&&void 0===r&&"pending"===E){let t;o?.isPlaceholderData&&e.placeholderData===h?.placeholderData?(t=o.data,w=!0):t="function"==typeof e.placeholderData?e.placeholderData(this.#g?.state.data,this.#g):e.placeholderData,void 0!==t&&(E="success",r=(0,u.pl)(o?.data,t,e),g=!0)}if(e.select&&void 0!==r&&!w)if(o&&r===l?.data&&e.select===this.#y)r=this.#m;else try{this.#y=e.select,r=e.select(r),r=(0,u.pl)(o?.data,r,e),this.#m=r,this.#l=null}catch(t){this.#l=t}this.#l&&(b=this.#l,r=this.#m,v=Date.now(),E="error");let R="fetching"===m.fetchStatus,S="pending"===E,O="error"===E,T=S&&R,C=void 0!==r,A={status:E,fetchStatus:m.fetchStatus,isPending:S,isSuccess:"success"===E,isError:O,isInitialLoading:T,isLoading:T,data:r,dataUpdatedAt:m.dataUpdatedAt,error:b,errorUpdatedAt:v,failureCount:m.fetchFailureCount,failureReason:m.fetchFailureReason,errorUpdateCount:m.errorUpdateCount,isFetched:m.dataUpdateCount>0||m.errorUpdateCount>0,isFetchedAfterMount:m.dataUpdateCount>p.dataUpdateCount||m.errorUpdateCount>p.errorUpdateCount,isFetching:R,isRefetching:R&&!S,isLoadingError:O&&!C,isPaused:"paused"===m.fetchStatus,isPlaceholderData:g,isRefetchError:O&&C,isStale:d(t,e),refetch:this.refetch,promise:this.#c};if(this.options.experimental_prefetchInRender){let e=t=>{"error"===A.status?t.reject(A.error):void 0!==A.data&&t.resolve(A.data)},r=()=>{e(this.#c=A.promise=(0,a.T)())},i=this.#c;switch(i.status){case"pending":t.queryHash===n.queryHash&&e(i);break;case"fulfilled":("error"===A.status||A.data!==i.value)&&r();break;case"rejected":("error"!==A.status||A.error!==i.reason)&&r()}}return A}updateResult(){let t=this.#e,e=this.createResult(this.#h,this.options);this.#d=this.#h.state,this.#p=this.options,void 0!==this.#d.data&&(this.#g=this.#h),(0,u.f8)(e,t)||(this.#e=e,this.#s({listeners:(()=>{if(!t)return!0;let{notifyOnChangeProps:e}=this.options,r="function"==typeof e?e():e;if("all"===r||!r&&!this.#w.size)return!0;let n=new Set(r??this.#w);return this.options.throwOnError&&n.add("error"),Object.keys(this.#e).some(e=>this.#e[e]!==t[e]&&n.has(e))})()}))}#C(){let t=this.#t.getQueryCache().build(this.#t,this.options);if(t===this.#h)return;let e=this.#h;this.#h=t,this.#f=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#S()}#s(t){i.jG.batch(()=>{t.listeners&&this.listeners.forEach(t=>{t(this.#e)}),this.#t.getQueryCache().notify({query:this.#h,type:"observerResultsUpdated"})})}};function c(t,e){return!1!==(0,u.Eh)(e.enabled,t)&&void 0===t.state.data&&("error"!==t.state.status||!1!==e.retryOnMount)||void 0!==t.state.data&&h(t,e,e.refetchOnMount)}function h(t,e,r){if(!1!==(0,u.Eh)(e.enabled,t)&&"static"!==(0,u.d2)(e.staleTime,t)){let n="function"==typeof r?r(t):r;return"always"===n||!1!==n&&d(t,e)}return!1}function f(t,e,r,n){return(t!==e||!1===(0,u.Eh)(n.enabled,t))&&(!r.suspense||"error"!==t.state.status)&&d(t,r)}function d(t,e){return!1!==(0,u.Eh)(e.enabled,t)&&t.isStaleByTime((0,u.d2)(e.staleTime,t))}var p=r(12115),y=r(26715);r(95155);var m=p.createContext(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}()),g=()=>p.useContext(m),b=(t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&!e.isReset()&&(t.retryOnMount=!1)},v=t=>{p.useEffect(()=>{t.clearReset()},[t])},E=t=>{let{result:e,errorResetBoundary:r,throwOnError:n,query:i,suspense:s}=t;return e.isError&&!r.isReset()&&!e.isFetching&&i&&(s&&void 0===e.data||(0,u.GU)(n,[e.error,i]))},w=p.createContext(!1),R=()=>p.useContext(w);w.Provider;var S=t=>{if(t.suspense){let e=t=>"static"===t?t:Math.max(t??1e3,1e3),r=t.staleTime;t.staleTime="function"==typeof r?(...t)=>e(r(...t)):e(r),"number"==typeof t.gcTime&&(t.gcTime=Math.max(t.gcTime,1e3))}},O=(t,e)=>t.isLoading&&t.isFetching&&!e,T=(t,e)=>t?.suspense&&e.isPending,C=(t,e,r)=>e.fetchOptimistic(t).catch(()=>{r.clearReset()});function A(t,e){return function(t,e,r){var n,s,o,a,l;let c=R(),h=g(),f=(0,y.jE)(r),d=f.defaultQueryOptions(t);null==(s=f.getDefaultOptions().queries)||null==(n=s._experimental_beforeQuery)||n.call(s,d),d._optimisticResults=c?"isRestoring":"optimistic",S(d),b(d,h),v(h);let m=!f.getQueryCache().get(d.queryHash),[w]=p.useState(()=>new e(f,d)),A=w.getOptimisticResult(d),_=!c&&!1!==t.subscribed;if(p.useSyncExternalStore(p.useCallback(t=>{let e=_?w.subscribe(i.jG.batchCalls(t)):u.lQ;return w.updateResult(),e},[w,_]),()=>w.getCurrentResult(),()=>w.getCurrentResult()),p.useEffect(()=>{w.setOptions(d)},[d,w]),T(d,A))throw C(d,w,h);if(E({result:A,errorResetBoundary:h,throwOnError:d.throwOnError,query:f.getQueryCache().get(d.queryHash),suspense:d.suspense}))throw A.error;if(null==(a=f.getDefaultOptions().queries)||null==(o=a._experimental_afterQuery)||o.call(a,d,A),d.experimental_prefetchInRender&&!u.S$&&O(A,c)){let t=m?C(d,w,h):null==(l=f.getQueryCache().get(d.queryHash))?void 0:l.promise;null==t||t.catch(u.lQ).finally(()=>{w.updateResult()})}return d.notifyOnChangeProps?A:w.trackResult(A)}(t,l,e)}},34560:(t,e,r)=>{"use strict";r.d(e,{$:()=>a,s:()=>o});var n=r(7165),i=r(57948),s=r(6784),o=class extends i.k{#U;#I;#L;constructor(t){super(),this.mutationId=t.mutationId,this.#I=t.mutationCache,this.#U=[],this.state=t.state||a(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#U.includes(t)||(this.#U.push(t),this.clearGcTimeout(),this.#I.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#U=this.#U.filter(e=>e!==t),this.scheduleGc(),this.#I.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#U.length||("pending"===this.state.status?this.scheduleGc():this.#I.remove(this))}continue(){return this.#L?.continue()??this.execute(this.state.variables)}async execute(t){let e=()=>{this.#P({type:"continue"})};this.#L=(0,s.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#P({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#P({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#I.canRun(this)});let r="pending"===this.state.status,n=!this.#L.canStart();try{if(r)e();else{this.#P({type:"pending",variables:t,isPaused:n}),await this.#I.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#P({type:"pending",context:e,variables:t,isPaused:n})}let i=await this.#L.start();return await this.#I.config.onSuccess?.(i,t,this.state.context,this),await this.options.onSuccess?.(i,t,this.state.context),await this.#I.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,t,this.state.context),this.#P({type:"success",data:i}),i}catch(e){try{throw await this.#I.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#I.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#P({type:"error",error:e})}}finally{this.#I.runNext(this)}}#P(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),n.jG.batch(()=>{this.#U.forEach(e=>{e.onMutationUpdate(t)}),this.#I.notify({mutation:this,type:"updated",action:t})})}};function a(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},35695:(t,e,r)=>{"use strict";var n=r(18999);r.o(n,"useParams")&&r.d(e,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(e,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(e,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(e,{useSearchParams:function(){return n.useSearchParams}})},39853:(t,e,r)=>{"use strict";r.d(e,{X:()=>a,k:()=>u});var n=r(52020),i=r(7165),s=r(6784),o=r(57948),a=class extends o.k{#N;#j;#B;#t;#L;#F;#D;constructor(t){super(),this.#D=!1,this.#F=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#t=t.client,this.#B=this.#t.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#N=function(t){let e="function"==typeof t.initialData?t.initialData():t.initialData,r=void 0!==e,n=r?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:r?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=t.state??this.#N,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#L?.promise}setOptions(t){this.options={...this.#F,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#B.remove(this)}setData(t,e){let r=(0,n.pl)(this.state.data,t,this.options);return this.#P({data:r,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),r}setState(t,e){this.#P({type:"setState",state:t,setStateOptions:e})}cancel(t){let e=this.#L?.promise;return this.#L?.cancel(t),e?e.then(n.lQ).catch(n.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#N)}isActive(){return this.observers.some(t=>!1!==(0,n.Eh)(t.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===n.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(t=>"static"===(0,n.d2)(t.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(t=0){return void 0===this.state.data||"static"!==t&&(!!this.state.isInvalidated||!(0,n.j3)(this.state.dataUpdatedAt,t))}onFocus(){let t=this.observers.find(t=>t.shouldFetchOnWindowFocus());t?.refetch({cancelRefetch:!1}),this.#L?.continue()}onOnline(){let t=this.observers.find(t=>t.shouldFetchOnReconnect());t?.refetch({cancelRefetch:!1}),this.#L?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#B.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.#L&&(this.#D?this.#L.cancel({revert:!0}):this.#L.cancelRetry()),this.scheduleGc()),this.#B.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#P({type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#L)return this.#L.continueRetry(),this.#L.promise}if(t&&this.setOptions(t),!this.options.queryFn){let t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}let r=new AbortController,i=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(this.#D=!0,r.signal)})},o=()=>{let t=(0,n.ZM)(this.options,e),r=(()=>{let t={client:this.#t,queryKey:this.queryKey,meta:this.meta};return i(t),t})();return(this.#D=!1,this.options.persister)?this.options.persister(t,r,this):t(r)},a=(()=>{let t={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:this.#t,state:this.state,fetchFn:o};return i(t),t})();this.options.behavior?.onFetch(a,this),this.#j=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==a.fetchOptions?.meta)&&this.#P({type:"fetch",meta:a.fetchOptions?.meta});let u=t=>{(0,s.wm)(t)&&t.silent||this.#P({type:"error",error:t}),(0,s.wm)(t)||(this.#B.config.onError?.(t,this),this.#B.config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return this.#L=(0,s.II)({initialPromise:e?.initialPromise,fn:a.fetchFn,abort:r.abort.bind(r),onSuccess:t=>{if(void 0===t)return void u(Error(`${this.queryHash} data is undefined`));try{this.setData(t)}catch(t){u(t);return}this.#B.config.onSuccess?.(t,this),this.#B.config.onSettled?.(t,this.state.error,this),this.scheduleGc()},onError:u,onFail:(t,e)=>{this.#P({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#P({type:"pause"})},onContinue:()=>{this.#P({type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode,canRun:()=>!0}),this.#L.start()}#P(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...u(e.data,this.options),fetchMeta:t.meta??null};case"success":return this.#j=void 0,{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let r=t.error;if((0,s.wm)(r)&&r.revert&&this.#j)return{...this.#j,fetchStatus:"idle"};return{...e,error:r,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),i.jG.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate()}),this.#B.notify({query:this,type:"updated",action:t})})}};function u(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,s.v_)(e.networkMode)?"fetching":"paused",...void 0===t&&{error:null,status:"pending"}}}},46786:(t,e,r)=>{"use strict";r.d(e,{Zr:()=>h,lt:()=>u});let n=new Map,i=t=>{let e=n.get(t);return e?Object.fromEntries(Object.entries(e.stores).map(([t,e])=>[t,e.getState()])):{}},s=(t,e,r)=>{if(void 0===t)return{type:"untracked",connection:e.connect(r)};let i=n.get(r.name);if(i)return{type:"tracked",store:t,...i};let s={connection:e.connect(r),stores:{}};return n.set(r.name,s),{type:"tracked",store:t,...s}},o=(t,e)=>{if(void 0===e)return;let r=n.get(t);r&&(delete r.stores[e],0===Object.keys(r.stores).length&&n.delete(t))},a=t=>{var e,r;if(!t)return;let n=t.split("\n"),i=n.findIndex(t=>t.includes("api.setState"));if(i<0)return;let s=(null==(e=n[i+1])?void 0:e.trim())||"";return null==(r=/.+ (.+) .+/.exec(s))?void 0:r[1]},u=(t,e={})=>(r,n,u)=>{let c,{enabled:h,anonymousActionType:f,store:d,...p}=e;try{c=(null==h||h)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(t){}if(!c)return t(r,n,u);let{connection:y,...m}=s(d,c,p),g=!0;u.setState=(t,e,s)=>{let o=r(t,e);if(!g)return o;let l=void 0===s?{type:f||a(Error().stack)||"anonymous"}:"string"==typeof s?{type:s}:s;return void 0===d?null==y||y.send(l,n()):null==y||y.send({...l,type:`${d}/${l.type}`},{...i(p.name),[d]:u.getState()}),o},u.devtools={cleanup:()=>{y&&"function"==typeof y.unsubscribe&&y.unsubscribe(),o(p.name,d)}};let b=(...t)=>{let e=g;g=!1,r(...t),g=e},v=t(u.setState,n,u);if("untracked"===m.type?null==y||y.init(v):(m.stores[m.store]=u,null==y||y.init(Object.fromEntries(Object.entries(m.stores).map(([t,e])=>[t,t===m.store?v:e.getState()])))),u.dispatchFromDevtools&&"function"==typeof u.dispatch){let t=!1,e=u.dispatch;u.dispatch=(...r)=>{"__setState"!==r[0].type||t||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),t=!0),e(...r)}}return y.subscribe(t=>{var e;switch(t.type){case"ACTION":if("string"!=typeof t.payload)return void console.error("[zustand devtools middleware] Unsupported action format");return l(t.payload,t=>{if("__setState"===t.type){if(void 0===d)return void b(t.state);1!==Object.keys(t.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let e=t.state[d];return void(null==e||JSON.stringify(u.getState())!==JSON.stringify(e)&&b(e))}u.dispatchFromDevtools&&"function"==typeof u.dispatch&&u.dispatch(t)});case"DISPATCH":switch(t.payload.type){case"RESET":if(b(v),void 0===d)return null==y?void 0:y.init(u.getState());return null==y?void 0:y.init(i(p.name));case"COMMIT":if(void 0===d){null==y||y.init(u.getState());break}return null==y?void 0:y.init(i(p.name));case"ROLLBACK":return l(t.state,t=>{if(void 0===d){b(t),null==y||y.init(u.getState());return}b(t[d]),null==y||y.init(i(p.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return l(t.state,t=>{if(void 0===d)return void b(t);JSON.stringify(u.getState())!==JSON.stringify(t[d])&&b(t[d])});case"IMPORT_STATE":{let{nextLiftedState:r}=t.payload,n=null==(e=r.computedStates.slice(-1)[0])?void 0:e.state;if(!n)return;void 0===d?b(n):b(n[d]),null==y||y.send(null,r);break}case"PAUSE_RECORDING":return g=!g}return}}),v},l=(t,e)=>{let r;try{r=JSON.parse(t)}catch(t){console.error("[zustand devtools middleware] Could not parse the received json",t)}void 0!==r&&e(r)},c=t=>e=>{try{let r=t(e);if(r instanceof Promise)return r;return{then:t=>c(t)(r),catch(t){return this}}}catch(t){return{then(t){return this},catch:e=>c(e)(t)}}},h=(t,e)=>(r,n,i)=>{let s,o={storage:function(t,e){let r;try{r=t()}catch(t){return}return{getItem:t=>{var e;let n=t=>null===t?null:JSON.parse(t,void 0),i=null!=(e=r.getItem(t))?e:null;return i instanceof Promise?i.then(n):n(i)},setItem:(t,e)=>r.setItem(t,JSON.stringify(e,void 0)),removeItem:t=>r.removeItem(t)}}(()=>localStorage),partialize:t=>t,version:0,merge:(t,e)=>({...e,...t}),...e},a=!1,u=new Set,l=new Set,h=o.storage;if(!h)return t((...t)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),r(...t)},n,i);let f=()=>{let t=o.partialize({...n()});return h.setItem(o.name,{state:t,version:o.version})},d=i.setState;i.setState=(t,e)=>{d(t,e),f()};let p=t((...t)=>{r(...t),f()},n,i);i.getInitialState=()=>p;let y=()=>{var t,e;if(!h)return;a=!1,u.forEach(t=>{var e;return t(null!=(e=n())?e:p)});let i=(null==(e=o.onRehydrateStorage)?void 0:e.call(o,null!=(t=n())?t:p))||void 0;return c(h.getItem.bind(h))(o.name).then(t=>{if(t)if("number"!=typeof t.version||t.version===o.version)return[!1,t.state];else{if(o.migrate){let e=o.migrate(t.state,t.version);return e instanceof Promise?e.then(t=>[!0,t]):[!0,e]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(t=>{var e;let[i,a]=t;if(r(s=o.merge(a,null!=(e=n())?e:p),!0),i)return f()}).then(()=>{null==i||i(s,void 0),s=n(),a=!0,l.forEach(t=>t(s))}).catch(t=>{null==i||i(void 0,t)})};return i.persist={setOptions:t=>{o={...o,...t},t.storage&&(h=t.storage)},clearStorage:()=>{null==h||h.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>y(),hasHydrated:()=>a,onHydrate:t=>(u.add(t),()=>{u.delete(t)}),onFinishHydration:t=>(l.add(t),()=>{l.delete(t)})},o.skipHydration||y(),s||p}},49641:t=>{!function(){var e={675:function(t,e){"use strict";e.byteLength=function(t){var e=u(t),r=e[0],n=e[1];return(r+n)*3/4-n},e.toByteArray=function(t){var e,r,s=u(t),o=s[0],a=s[1],l=new i((o+a)*3/4-a),c=0,h=a>0?o-4:o;for(r=0;r<h;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],l[c++]=e>>16&255,l[c++]=e>>8&255,l[c++]=255&e;return 2===a&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,l[c++]=255&e),1===a&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,l[c++]=e>>8&255,l[c++]=255&e),l},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,s=[],o=0,a=n-i;o<a;o+=16383)s.push(function(t,e,n){for(var i,s=[],o=e;o<n;o+=3)i=(t[o]<<16&0xff0000)+(t[o+1]<<8&65280)+(255&t[o+2]),s.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return s.join("")}(t,o,o+16383>a?a:o+16383));return 1===i?s.push(r[(e=t[n-1])>>2]+r[e<<4&63]+"=="):2===i&&s.push(r[(e=(t[n-2]<<8)+t[n-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),s.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,a=s.length;o<a;++o)r[o]=s[o],n[s.charCodeAt(o)]=o;function u(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},72:function(t,e,r){"use strict";var n=r(675),i=r(783),s="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,a.prototype),e}function a(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return c(t)}return u(t,e,r)}function u(t,e,r){if("string"==typeof t){var n=t,i=e;if(("string"!=typeof i||""===i)&&(i="utf8"),!a.isEncoding(i))throw TypeError("Unknown encoding: "+i);var s=0|d(n,i),u=o(s),l=u.write(n,i);return l!==s&&(u=u.slice(0,l)),u}if(ArrayBuffer.isView(t))return h(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(x(t,ArrayBuffer)||t&&x(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(x(t,SharedArrayBuffer)||t&&x(t.buffer,SharedArrayBuffer)))return function(t,e,r){var n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),a.prototype),n}(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var c=t.valueOf&&t.valueOf();if(null!=c&&c!==t)return a.from(c,e,r);var p=function(t){if(a.isBuffer(t)){var e=0|f(t.length),r=o(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?o(0):h(t):"Buffer"===t.type&&Array.isArray(t.data)?h(t.data):void 0}(t);if(p)return p;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return a.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function l(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function c(t){return l(t),o(t<0?0:0|f(t))}function h(t){for(var e=t.length<0?0:0|f(t.length),r=o(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}e.Buffer=a,e.SlowBuffer=function(t){return+t!=t&&(t=0),a.alloc(+t)},e.INSPECT_MAX_BYTES=50,e.kMaxLength=0x7fffffff,a.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(t,e,r){return u(t,e,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(t,e,r){return(l(t),t<=0)?o(t):void 0!==e?"string"==typeof r?o(t).fill(e,r):o(t).fill(e):o(t)},a.allocUnsafe=function(t){return c(t)},a.allocUnsafeSlow=function(t){return c(t)};function f(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function d(t,e){if(a.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||x(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return T(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return A(t).length;default:if(i)return n?-1:T(t).length;e=(""+e).toLowerCase(),i=!0}}function p(t,e,r){var i,s,o,a=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",s=e;s<r;++s)i+=U[t[s]];return i}(this,e,r);case"utf8":case"utf-8":return b(this,e,r);case"ascii":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}(this,e,r);case"base64":return i=this,s=e,o=r,0===s&&o===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(s,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),i="",s=0;s<n.length;s+=2)i+=String.fromCharCode(n[s]+256*n[s+1]);return i}(this,e,r);default:if(a)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),a=!0}}function y(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function m(t,e,r,n,i){var s;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(s=r*=1)!=s&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length)if(i)return -1;else r=t.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof e&&(e=a.from(e,n)),a.isBuffer(e))return 0===e.length?-1:g(t,e,r,n,i);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(t,e,r);else return Uint8Array.prototype.lastIndexOf.call(t,e,r);return g(t,[e],r,n,i)}throw TypeError("val must be string, number or Buffer")}function g(t,e,r,n,i){var s,o=1,a=t.length,u=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;o=2,a/=2,u/=2,r/=2}function l(t,e){return 1===o?t[e]:t.readUInt16BE(e*o)}if(i){var c=-1;for(s=r;s<a;s++)if(l(t,s)===l(e,-1===c?0:s-c)){if(-1===c&&(c=s),s-c+1===u)return c*o}else -1!==c&&(s-=s-c),c=-1}else for(r+u>a&&(r=a-u),s=r;s>=0;s--){for(var h=!0,f=0;f<u;f++)if(l(t,s+f)!==l(e,f)){h=!1;break}if(h)return s}return -1}a.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==a.prototype},a.compare=function(t,e){if(x(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),x(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(t)||!a.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,s=Math.min(r,n);i<s;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:+(n<r)},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,n=a.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var s=t[r];if(x(s,Uint8Array)&&(s=a.from(s)),!a.isBuffer(s))throw TypeError('"list" argument must be an Array of Buffers');s.copy(n,i),i+=s.length}return n},a.byteLength=d,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)y(this,e,e+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)y(this,e,e+3),y(this,e+1,e+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)y(this,e,e+7),y(this,e+1,e+6),y(this,e+2,e+5),y(this,e+3,e+4);return this},a.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?b(this,0,t):p.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(t){if(!a.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},s&&(a.prototype[s]=a.prototype.inspect),a.prototype.compare=function(t,e,r,n,i){if(x(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,i>>>=0,this===t)return 0;for(var s=i-n,o=r-e,u=Math.min(s,o),l=this.slice(n,i),c=t.slice(e,r),h=0;h<u;++h)if(l[h]!==c[h]){s=l[h],o=c[h];break}return s<o?-1:+(o<s)},a.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},a.prototype.indexOf=function(t,e,r){return m(this,t,e,r,!0)},a.prototype.lastIndexOf=function(t,e,r){return m(this,t,e,r,!1)};function b(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var s,o,a,u,l=t[i],c=null,h=l>239?4:l>223?3:l>191?2:1;if(i+h<=r)switch(h){case 1:l<128&&(c=l);break;case 2:(192&(s=t[i+1]))==128&&(u=(31&l)<<6|63&s)>127&&(c=u);break;case 3:s=t[i+1],o=t[i+2],(192&s)==128&&(192&o)==128&&(u=(15&l)<<12|(63&s)<<6|63&o)>2047&&(u<55296||u>57343)&&(c=u);break;case 4:s=t[i+1],o=t[i+2],a=t[i+3],(192&s)==128&&(192&o)==128&&(192&a)==128&&(u=(15&l)<<18|(63&s)<<12|(63&o)<<6|63&a)>65535&&u<1114112&&(c=u)}null===c?(c=65533,h=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=h}var f=n,d=f.length;if(d<=4096)return String.fromCharCode.apply(String,f);for(var p="",y=0;y<d;)p+=String.fromCharCode.apply(String,f.slice(y,y+=4096));return p}function v(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function E(t,e,r,n,i,s){if(!a.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<s)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function w(t,e,r,n,i,s){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function R(t,e,r,n,s){return e*=1,r>>>=0,s||w(t,e,r,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,r,n,23,4),r+4}function S(t,e,r,n,s){return e*=1,r>>>=0,s||w(t,e,r,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,r,n,52,8),r+8}a.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,s,o,a,u,l,c,h,f=this.length-e;if((void 0===r||r>f)&&(r=f),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var d=!1;;)switch(n){case"hex":return function(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var s=e.length;n>s/2&&(n=s/2);for(var o=0;o<n;++o){var a,u=parseInt(e.substr(2*o,2),16);if((a=u)!=a)break;t[r+o]=u}return o}(this,t,e,r);case"utf8":case"utf-8":return i=e,s=r,_(T(t,this.length-i),this,i,s);case"ascii":return o=e,a=r,_(C(t),this,o,a);case"latin1":case"binary":return function(t,e,r,n){return _(C(e),t,r,n)}(this,t,e,r);case"base64":return u=e,l=r,_(A(t),this,u,l);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return c=e,h=r,_(function(t,e){for(var r,n,i=[],s=0;s<t.length&&!((e-=2)<0);++s)n=(r=t.charCodeAt(s))>>8,i.push(r%256),i.push(n);return i}(t,this.length-c),this,c,h);default:if(d)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),d=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||v(t,e,this.length);for(var n=this[t],i=1,s=0;++s<e&&(i*=256);)n+=this[t+s]*i;return n},a.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||v(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},a.prototype.readUInt8=function(t,e){return t>>>=0,e||v(t,1,this.length),this[t]},a.prototype.readUInt16LE=function(t,e){return t>>>=0,e||v(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUInt16BE=function(t,e){return t>>>=0,e||v(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUInt32LE=function(t,e){return t>>>=0,e||v(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},a.prototype.readUInt32BE=function(t,e){return t>>>=0,e||v(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||v(t,e,this.length);for(var n=this[t],i=1,s=0;++s<e&&(i*=256);)n+=this[t+s]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},a.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||v(t,e,this.length);for(var n=e,i=1,s=this[t+--n];n>0&&(i*=256);)s+=this[t+--n]*i;return s>=(i*=128)&&(s-=Math.pow(2,8*e)),s},a.prototype.readInt8=function(t,e){return(t>>>=0,e||v(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},a.prototype.readInt16LE=function(t,e){t>>>=0,e||v(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt16BE=function(t,e){t>>>=0,e||v(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt32LE=function(t,e){return t>>>=0,e||v(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return t>>>=0,e||v(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,e){return t>>>=0,e||v(t,4,this.length),i.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return t>>>=0,e||v(t,4,this.length),i.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return t>>>=0,e||v(t,8,this.length),i.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return t>>>=0,e||v(t,8,this.length),i.read(this,t,!1,52,8)},a.prototype.writeUIntLE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;E(this,t,e,r,i,0)}var s=1,o=0;for(this[e]=255&t;++o<r&&(s*=256);)this[e+o]=t/s&255;return e+r},a.prototype.writeUIntBE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;E(this,t,e,r,i,0)}var s=r-1,o=1;for(this[e+s]=255&t;--s>=0&&(o*=256);)this[e+s]=t/o&255;return e+r},a.prototype.writeUInt8=function(t,e,r){return t*=1,e>>>=0,r||E(this,t,e,1,255,0),this[e]=255&t,e+1},a.prototype.writeUInt16LE=function(t,e,r){return t*=1,e>>>=0,r||E(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeUInt16BE=function(t,e,r){return t*=1,e>>>=0,r||E(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeUInt32LE=function(t,e,r){return t*=1,e>>>=0,r||E(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},a.prototype.writeUInt32BE=function(t,e,r){return t*=1,e>>>=0,r||E(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeIntLE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);E(this,t,e,r,i-1,-i)}var s=0,o=1,a=0;for(this[e]=255&t;++s<r&&(o*=256);)t<0&&0===a&&0!==this[e+s-1]&&(a=1),this[e+s]=(t/o|0)-a&255;return e+r},a.prototype.writeIntBE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);E(this,t,e,r,i-1,-i)}var s=r-1,o=1,a=0;for(this[e+s]=255&t;--s>=0&&(o*=256);)t<0&&0===a&&0!==this[e+s+1]&&(a=1),this[e+s]=(t/o|0)-a&255;return e+r},a.prototype.writeInt8=function(t,e,r){return t*=1,e>>>=0,r||E(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,r){return t*=1,e>>>=0,r||E(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeInt16BE=function(t,e,r){return t*=1,e>>>=0,r||E(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeInt32LE=function(t,e,r){return t*=1,e>>>=0,r||E(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},a.prototype.writeInt32BE=function(t,e,r){return t*=1,e>>>=0,r||E(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeFloatLE=function(t,e,r){return R(this,t,e,!0,r)},a.prototype.writeFloatBE=function(t,e,r){return R(this,t,e,!1,r)},a.prototype.writeDoubleLE=function(t,e,r){return S(this,t,e,!0,r)},a.prototype.writeDoubleBE=function(t,e,r){return S(this,t,e,!1,r)},a.prototype.copy=function(t,e,r,n){if(!a.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,r,n);else if(this===t&&r<e&&e<n)for(var s=i-1;s>=0;--s)t[s+e]=this[s+r];else Uint8Array.prototype.set.call(t,this.subarray(r,n),e);return i},a.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){var i,s=t.charCodeAt(0);("utf8"===n&&s<128||"latin1"===n)&&(t=s)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{var o=a.isBuffer(t)?t:a.from(t,n),u=o.length;if(0===u)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=o[i%u]}return this};var O=/[^+/0-9A-Za-z-_]/g;function T(t,e){e=e||1/0;for(var r,n=t.length,i=null,s=[],o=0;o<n;++o){if((r=t.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319||o+1===n){(e-=3)>-1&&s.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&s.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&s.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;s.push(r)}else if(r<2048){if((e-=2)<0)break;s.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;s.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;s.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return s}function C(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}function A(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(O,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function _(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length)&&!(i>=t.length);++i)e[i+r]=t[i];return i}function x(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var U=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}()},783:function(t,e){e.read=function(t,e,r,n,i){var s,o,a=8*i-n-1,u=(1<<a)-1,l=u>>1,c=-7,h=r?i-1:0,f=r?-1:1,d=t[e+h];for(h+=f,s=d&(1<<-c)-1,d>>=-c,c+=a;c>0;s=256*s+t[e+h],h+=f,c-=8);for(o=s&(1<<-c)-1,s>>=-c,c+=n;c>0;o=256*o+t[e+h],h+=f,c-=8);if(0===s)s=1-l;else{if(s===u)return o?NaN:1/0*(d?-1:1);o+=Math.pow(2,n),s-=l}return(d?-1:1)*o*Math.pow(2,s-n)},e.write=function(t,e,r,n,i,s){var o,a,u,l=8*s-i-1,c=(1<<l)-1,h=c>>1,f=5960464477539062e-23*(23===i),d=n?0:s-1,p=n?1:-1,y=+(e<0||0===e&&1/e<0);for(isNaN(e=Math.abs(e))||e===1/0?(a=+!!isNaN(e),o=c):(o=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-o))<1&&(o--,u*=2),o+h>=1?e+=f/u:e+=f*Math.pow(2,1-h),e*u>=2&&(o++,u/=2),o+h>=c?(a=0,o=c):o+h>=1?(a=(e*u-1)*Math.pow(2,i),o+=h):(a=e*Math.pow(2,h-1)*Math.pow(2,i),o=0));i>=8;t[r+d]=255&a,d+=p,a/=256,i-=8);for(o=o<<i|a,l+=i;l>0;t[r+d]=255&o,d+=p,o/=256,l-=8);t[r+d-p]|=128*y}}},r={};function n(t){var i=r[t];if(void 0!==i)return i.exports;var s=r[t]={exports:{}},o=!0;try{e[t](s,s.exports,n),o=!1}finally{o&&delete r[t]}return s.exports}n.ab="//",t.exports=n(72)}()},50920:(t,e,r)=>{"use strict";r.d(e,{m:()=>s});var n=r(25910),i=r(52020),s=new class extends n.Q{#k;#a;#u;constructor(){super(),this.#u=t=>{if(!i.S$&&window.addEventListener){let e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#a||this.setEventListener(this.#u)}onUnsubscribe(){this.hasListeners()||(this.#a?.(),this.#a=void 0)}setEventListener(t){this.#u=t,this.#a?.(),this.#a=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.#k!==t&&(this.#k=t,this.onFocus())}onFocus(){let t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return"boolean"==typeof this.#k?this.#k:globalThis.document?.visibilityState!=="hidden"}}},52020:(t,e,r)=>{"use strict";r.d(e,{Cp:()=>p,EN:()=>d,Eh:()=>l,F$:()=>f,GU:()=>C,MK:()=>c,S$:()=>n,ZM:()=>T,ZZ:()=>S,Zw:()=>s,d2:()=>u,f8:()=>y,gn:()=>o,hT:()=>O,j3:()=>a,lQ:()=>i,nJ:()=>h,pl:()=>E,rX:()=>w,y9:()=>R,yy:()=>v});var n="undefined"==typeof window||"Deno"in globalThis;function i(){}function s(t,e){return"function"==typeof t?t(e):t}function o(t){return"number"==typeof t&&t>=0&&t!==1/0}function a(t,e){return Math.max(t+(e||0)-Date.now(),0)}function u(t,e){return"function"==typeof t?t(e):t}function l(t,e){return"function"==typeof t?t(e):t}function c(t,e){let{type:r="all",exact:n,fetchStatus:i,predicate:s,queryKey:o,stale:a}=t;if(o){if(n){if(e.queryHash!==f(o,e.options))return!1}else if(!p(e.queryKey,o))return!1}if("all"!==r){let t=e.isActive();if("active"===r&&!t||"inactive"===r&&t)return!1}return("boolean"!=typeof a||e.isStale()===a)&&(!i||i===e.state.fetchStatus)&&(!s||!!s(e))}function h(t,e){let{exact:r,status:n,predicate:i,mutationKey:s}=t;if(s){if(!e.options.mutationKey)return!1;if(r){if(d(e.options.mutationKey)!==d(s))return!1}else if(!p(e.options.mutationKey,s))return!1}return(!n||e.state.status===n)&&(!i||!!i(e))}function f(t,e){return(e?.queryKeyHashFn||d)(t)}function d(t){return JSON.stringify(t,(t,e)=>g(e)?Object.keys(e).sort().reduce((t,r)=>(t[r]=e[r],t),{}):e)}function p(t,e){return t===e||typeof t==typeof e&&!!t&&!!e&&"object"==typeof t&&"object"==typeof e&&Object.keys(e).every(r=>p(t[r],e[r]))}function y(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(let r in t)if(t[r]!==e[r])return!1;return!0}function m(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function g(t){if(!b(t))return!1;let e=t.constructor;if(void 0===e)return!0;let r=e.prototype;return!!b(r)&&!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype}function b(t){return"[object Object]"===Object.prototype.toString.call(t)}function v(t){return new Promise(e=>{setTimeout(e,t)})}function E(t,e,r){return"function"==typeof r.structuralSharing?r.structuralSharing(t,e):!1!==r.structuralSharing?function t(e,r){if(e===r)return e;let n=m(e)&&m(r);if(n||g(e)&&g(r)){let i=n?e:Object.keys(e),s=i.length,o=n?r:Object.keys(r),a=o.length,u=n?[]:{},l=new Set(i),c=0;for(let i=0;i<a;i++){let s=n?i:o[i];(!n&&l.has(s)||n)&&void 0===e[s]&&void 0===r[s]?(u[s]=void 0,c++):(u[s]=t(e[s],r[s]),u[s]===e[s]&&void 0!==e[s]&&c++)}return s===a&&c===s?e:u}return r}(t,e):e}function w(t){return t}function R(t,e,r=0){let n=[...t,e];return r&&n.length>r?n.slice(1):n}function S(t,e,r=0){let n=[e,...t];return r&&n.length>r?n.slice(0,-1):n}var O=Symbol();function T(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==O?t.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${t.queryHash}'`))}function C(t,e){return"function"==typeof t?t(...e):!!t}},57948:(t,e,r)=>{"use strict";r.d(e,{k:()=>i});var n=r(52020),i=class{#M;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,n.gn)(this.gcTime)&&(this.#M=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(n.S$?1/0:3e5))}clearGcTimeout(){this.#M&&(clearTimeout(this.#M),this.#M=void 0)}}},65453:(t,e,r)=>{"use strict";r.d(e,{v:()=>u});var n=r(12115);let i=t=>{let e,r=new Set,n=(t,n)=>{let i="function"==typeof t?t(e):t;if(!Object.is(i,e)){let t=e;e=(null!=n?n:"object"!=typeof i||null===i)?i:Object.assign({},e,i),r.forEach(r=>r(e,t))}},i=()=>e,s={setState:n,getState:i,getInitialState:()=>o,subscribe:t=>(r.add(t),()=>r.delete(t))},o=e=t(n,i,s);return s},s=t=>t?i(t):i,o=t=>t,a=t=>{let e=s(t),r=t=>(function(t,e=o){let r=n.useSyncExternalStore(t.subscribe,()=>e(t.getState()),()=>e(t.getInitialState()));return n.useDebugValue(r),r})(e,t);return Object.assign(r,e),r},u=t=>t?a(t):a},73504:(t,e,r)=>{"use strict";function n(){let t,e,r=new Promise((r,n)=>{t=r,e=n});function n(t){Object.assign(r,t),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=e=>{n({status:"fulfilled",value:e}),t(e)},r.reject=t=>{n({status:"rejected",reason:t}),e(t)},r}r.d(e,{T:()=>n})}}]);