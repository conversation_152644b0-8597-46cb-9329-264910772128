(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8947],{1894:(e,t,r)=>{"use strict";r.d(t,{d:()=>l});var a=r(31802);class i{static async convertToWebP(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.DEFAULT_WEBP_OPTIONS,r=performance.now();console.log("\uD83D\uDDBC️ WebP d\xf6n\xfcşt\xfcrme başlıyor...",{originalName:e.name,originalSize:"".concat((e.size/1024/1024).toFixed(2),"MB"),originalType:e.type});try{let i={maxSizeMB:t.maxSizeMB||1,maxWidthOrHeight:t.maxWidthOrHeight||800,useWebWorker:!1!==t.useWebWorker,fileType:t.fileType||"image/webp",quality:t.quality||.8,alwaysKeepResolution:!1,initialQuality:t.quality||.8},l=await (0,a.A)(e,i),s=performance.now()-r,n={file:l,originalSize:e.size,compressedSize:l.size,compressionRatio:(1-l.size/e.size)*100,processingTime:s};return console.log("✅ WebP d\xf6n\xfcşt\xfcrme tamamlandı!",{compressedName:l.name,compressedSize:"".concat((l.size/1024/1024).toFixed(2),"MB"),compressedType:l.type,compressionRatio:"".concat(n.compressionRatio.toFixed(1),"%"),processingTime:"".concat(s.toFixed(1),"ms")}),n}catch(e){throw console.error("❌ WebP d\xf6n\xfcşt\xfcrme hatası:",e),Error("Resim d\xf6n\xfcşt\xfcr\xfcl\xfcrken hata oluştu")}}static async convertProfilePictureToWebP(e){return this.convertToWebP(e,this.PROFILE_WEBP_OPTIONS)}static async convertProductImageToWebP(e){return this.convertToWebP(e,this.PRODUCT_WEBP_OPTIONS)}static validateImageFile(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return e.type.startsWith("image/")?e.size>1024*t*1024?{isValid:!1,error:"Dosya boyutu ".concat(t,"MB'dan k\xfc\xe7\xfck olmalıdır")}:["image/jpeg","image/jpg","image/png","image/webp","image/gif"].includes(e.type.toLowerCase())?{isValid:!0}:{isValid:!1,error:"Desteklenen formatlar: JPEG, PNG, WebP, GIF"}:{isValid:!1,error:"L\xfctfen bir resim dosyası se\xe7in"}}static createPreviewUrl(e){return new Promise((t,r)=>{let a=new FileReader;a.onloadend=()=>t(a.result),a.onerror=r,a.readAsDataURL(e)})}static async convertMultipleToWebP(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.DEFAULT_WEBP_OPTIONS;console.log("\uD83D\uDDBC️ ".concat(e.length," resim d\xf6n\xfcşt\xfcr\xfcl\xfcyor..."));let r=await Promise.allSettled(e.map(e=>this.convertToWebP(e,t))),a=[],i=[];return r.forEach((t,r)=>{"fulfilled"===t.status?a.push(t.value):i.push("".concat(e[r].name,": ").concat(t.reason.message))}),i.length>0&&console.warn("⚠️ Bazı resimler işlenirken hata oluştu:",i),console.log("✅ ".concat(a.length,"/").concat(e.length," resim başarıyla d\xf6n\xfcşt\xfcr\xfcld\xfc")),a}static formatFileSize(e){if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]}static formatCompressionRatio(e){return"".concat(e.toFixed(1),"%")}}i.DEFAULT_WEBP_OPTIONS={maxSizeMB:1,maxWidthOrHeight:800,quality:.8,fileType:"image/webp",useWebWorker:!0},i.PROFILE_WEBP_OPTIONS={maxSizeMB:.5,maxWidthOrHeight:400,quality:.85,fileType:"image/webp",useWebWorker:!0},i.PRODUCT_WEBP_OPTIONS={maxSizeMB:1.5,maxWidthOrHeight:1200,quality:.9,fileType:"image/webp",useWebWorker:!0};let l=i},24618:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(95155),i=r(12115),l=r(60760),s=r(76408),n=r(59959),o=r(26715),c=r(66681);let d=e=>{let{isOpen:t,onClose:r,onFileSelect:d,hasProfilePicture:u,onDeleteSuccess:m,onDeleteError:h,isDeleting:g=!1,mode:p="immediate"}=e,x=(0,i.useRef)(null),f=(0,i.useRef)(null),b=(0,o.jE)(),[y,v]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=e=>{f.current&&!f.current.contains(e.target)&&r()};return t&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[t,r]),(0,i.useEffect)(()=>{let e=e=>{"Escape"===e.key&&r()};return t&&document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}},[t,r]);let N=async()=>{if(u){if("edit"===p){null==m||m(),r();return}v(!0);try{await n.y.deleteProfilePicture(),b.invalidateQueries({queryKey:c.ZF.user()}),b.invalidateQueries({queryKey:c.ZF.profileInfo()}),await Promise.all([b.refetchQueries({queryKey:c.ZF.user()}),b.refetchQueries({queryKey:c.ZF.profileInfo()})]),null==m||m(),r()}catch(r){var e,t;null==h||h((null==(t=r.response)||null==(e=t.data)?void 0:e.message)||"Profil fotoğrafı silinirken bir hata oluştu")}finally{v(!1)}}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.N,{children:t&&(0,a.jsx)(s.P.div,{ref:f,initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},transition:{duration:.1},className:"absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50",children:(0,a.jsxs)("div",{className:"p-3 space-y-2",children:[(0,a.jsxs)(s.P.button,{type:"button",onClick:()=>{var e;null==(e=x.current)||e.click(),r()},className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-2 px-3 rounded-lg text-sm font-medium flex items-center justify-center hover:shadow-lg transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,a.jsxs)("svg",{className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 13a3 3 0 11-6 0 3 3 0 016 0z"})]}),"Fotoğraf Y\xfckle"]}),(0,a.jsx)(s.P.button,{type:"button",onClick:N,disabled:!u||y||g,className:"w-full py-2 px-3 rounded-lg text-sm flex items-center justify-center transition-all duration-300 ".concat(!u||y||g?"text-gray-400 cursor-not-allowed bg-gray-50":"text-red-600 hover:bg-red-50 hover:text-red-700"),whileHover:!u||y||g?{}:{scale:1.02},whileTap:!u||y||g?{}:{scale:.98},children:y||g?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"h-4 w-4 mr-2 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Siliniyor..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Profil Fotoğrafını Kaldır"]})})]})})}),(0,a.jsx)("input",{ref:x,type:"file",accept:"image/*",onChange:e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];r&&d(r),x.current&&(x.current.value="")},className:"hidden"})]})}},75921:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(95155),i=r(12115),l=r(76408),s=r(6874),n=r.n(s),o=r(35695),c=r(87220),d=r(59959),u=r(1894),m=r(24618),h=r(83717),g=r(26715),p=r(66681);function x(){let e,t,r=(0,o.useRouter)(),{user:s,isLoading:x}=(0,c.A)(),{data:f,isLoading:b}=(0,p.dS)(),y=(0,g.jE)(),[v,N]=(0,i.useState)({firstName:"",lastName:"",phone:"",location:"",birthDate:"",gender:""}),[j,w]=(0,i.useState)({firstName:"",lastName:"",phone:"",location:"",birthDate:"",gender:""}),[k,P]=(0,i.useState)(!1),[W,z]=(0,i.useState)(null),[T,D]=(0,i.useState)(null),[S,F]=(0,i.useState)(!1),[B,O]=(0,i.useState)({}),[E,C]=(0,i.useState)(null),[M,L]=(0,i.useState)(!1),[A,R]=(0,i.useState)(!1);(0,i.useEffect)(()=>{if(!x&&!s)return void r.push("/login");if(f){let e={firstName:f.firstName||"",lastName:f.lastName||"",phone:(null==s?void 0:s.phoneNumber)||"",location:f.location||"",birthDate:f.dateOfBirth?f.dateOfBirth.split("T")[0]:"",gender:void 0!==f.gender&&null!==f.gender?1===f.gender?"erkek":2===f.gender?"kadın":3===f.gender?"diğer":"":""};N(e),w({...e})}},[s,x,f,r]);let I=e=>{let{name:t,value:r}=e.target;N(e=>({...e,[t]:r})),B[t]&&O(e=>({...e,[t]:""}))},_=async e=>{try{let t=u.d.validateImageFile(e,10);if(!t.isValid)return void O(e=>({...e,image:t.error}));B.image&&O(e=>({...e,image:""})),F(!0);let r=await u.d.convertProfilePictureToWebP(e);z(r.file),setImageProcessingResult(r);let a=await u.d.createPreviewUrl(r.file);D(a)}catch(e){O(t=>({...t,image:e instanceof Error?e.message:"Resim işlenirken hata oluştu"}))}finally{F(!1)}},H=()=>{let e={};return v.firstName.trim()||(e.firstName="Ad alanı zorunludur"),v.lastName.trim()||(e.lastName="Soyad alanı zorunludur"),v.phone&&!/^\+?\d{10,15}$/.test(v.phone.replace(/\s+/g,""))&&(e.phone="Ge\xe7erli bir telefon numarası girin"),O(e),0===Object.keys(e).length},q=()=>v.firstName!==j.firstName||v.lastName!==j.lastName||v.phone!==j.phone||v.location!==j.location||v.birthDate!==j.birthDate||v.gender!==j.gender||null!==W||A,K=async e=>{if(e.preventDefault(),H()){if(!q())return void r.push("/account");P(!0);try{O({}),C(null);let e=[],t=v.firstName!==j.firstName||v.lastName!==j.lastName||v.phone!==j.phone||v.location!==j.location||v.birthDate!==j.birthDate||v.gender!==j.gender;if(t){let t={};if(v.firstName&&v.firstName.trim()&&(t.firstName=v.firstName.trim()),v.lastName&&v.lastName.trim()&&(t.lastName=v.lastName.trim()),v.phone&&v.phone.trim()&&(t.phoneNumber=v.phone.trim()),v.location&&v.location.trim()&&(t.location=v.location.trim()),v.birthDate&&(t.dateOfBirth=new Date(v.birthDate).toISOString()),v.gender){let e={erkek:h.w7.Male,kadın:h.w7.Female,diğer:h.w7.Other};e[v.gender]&&(t.gender=e[v.gender])}Object.keys(t).length>0&&e.push(d.y.updateProfile(t))}W&&e.push(d.y.updateProfilePicture(W)),A&&e.push(d.y.deleteProfilePicture()),await Promise.all(e),y.invalidateQueries({queryKey:p.ZF.user()}),y.invalidateQueries({queryKey:p.ZF.profileInfo()}),await Promise.all([y.refetchQueries({queryKey:p.ZF.user()}),y.refetchQueries({queryKey:p.ZF.profileInfo()})]),W||A?t?C("Profil bilgileriniz ve fotoğrafınız başarıyla g\xfcncellendi!"):C("Profil fotoğrafınız başarıyla g\xfcncellendi!"):C("Profil bilgileriniz başarıyla g\xfcncellendi!"),z(null),D(null),setImageProcessingResult(null),R(!1),setTimeout(()=>{C(null)},3e3),setTimeout(()=>{r.push("/account")},1500)}catch(e){O({submit:e instanceof Error&&"response"in e&&e.response&&"object"==typeof e.response&&"data"in e.response&&e.response.data&&"object"==typeof e.response.data&&"message"in e.response.data?String(e.response.data.message):"Profil g\xfcncellenirken bir hata oluştu"})}finally{P(!1)}}};if(x||b)return(0,a.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsx)("div",{className:"bg-white rounded-2xl shadow-lg overflow-hidden p-8",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-300 rounded mb-6 w-64"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"h-10 bg-gray-300 rounded"}),(0,a.jsx)("div",{className:"h-10 bg-gray-300 rounded"}),(0,a.jsx)("div",{className:"h-10 bg-gray-300 rounded"}),(0,a.jsx)("div",{className:"h-10 bg-gray-300 rounded"})]})]})})})});if(!s)return null;let U=T||(A?null:null==f?void 0:f.profilePictureUrl);return(0,a.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsx)("div",{className:"bg-white rounded-2xl shadow-lg overflow-hidden",children:(0,a.jsxs)("div",{className:"p-6 md:p-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8 pb-6 border-b border-gray-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl md:text-3xl font-bold text-gray-800 mb-2",children:"Profili D\xfczenle"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Profil bilgilerinizi g\xfcncelleyebilirsiniz"})]}),(0,a.jsx)(n(),{href:"/account",children:(0,a.jsxs)(l.P.button,{className:"text-gray-500 hover:text-gray-700 transition-colors flex items-center",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Geri D\xf6n"]})})]}),B.submit&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{className:"h-5 w-5 text-red-500 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("span",{className:"text-red-700",children:B.submit})]})}),E&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{className:"h-5 w-5 text-green-500 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("span",{className:"text-green-700",children:E})]})}),(0,a.jsxs)("form",{onSubmit:K,className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"w-32 h-32 rounded-full bg-gradient-to-br from-purple-600 to-indigo-600 flex items-center justify-center overflow-hidden",children:[U?(0,a.jsx)("img",{src:U,alt:"Profile",className:"w-full h-full object-cover",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling.style.display="flex"}}):null,(0,a.jsx)("span",{className:"text-white text-3xl font-bold ".concat(U?"hidden":"flex"," items-center justify-center w-full h-full"),children:(e=(null==f?void 0:f.firstName)||s.firstName,t=(null==f?void 0:f.lastName)||s.lastName,"".concat(e.charAt(0).toUpperCase()).concat(t.charAt(0).toUpperCase()))})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(l.P.button,{type:"button",onClick:()=>{L(!M)},disabled:S,className:"absolute bottom-0 right-0 rounded-full p-2 shadow-lg transition-all ".concat(S?"bg-gray-400 cursor-not-allowed":"bg-purple-600 hover:bg-purple-700"," text-white"),whileHover:S?{}:{scale:1.1},whileTap:S?{}:{scale:.9},children:S?(0,a.jsxs)("svg",{className:"h-4 w-4 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 13a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,a.jsx)(m.A,{isOpen:M,onClose:()=>{L(!1)},onFileSelect:_,hasProfilePicture:!!U&&!W&&!A,onDeleteSuccess:()=>{R(!0),z(null),D(null),setImageProcessingResult(null),C("Profil fotoğrafınız silmek \xfczere işaretlendi. Değişiklikleri kaydetmeyi unutmayın!"),setTimeout(()=>{C(null)},3e3)},onDeleteError:e=>{O(t=>({...t,image:e}))},isDeleting:S,mode:"edit"})]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:S?"Resim WebP formatına d\xf6n\xfcşt\xfcr\xfcl\xfcyor...":"Profil fotoğrafını değiştirmek i\xe7in tıklayın"}),B.image&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-1",children:B.image})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Temel Bilgiler"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Ad *"}),(0,a.jsx)("input",{type:"text",id:"firstName",name:"firstName",value:v.firstName,onChange:I,className:"w-full px-4 py-2 rounded-lg border transition text-black ".concat(B.firstName?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 focus:ring-purple-500 focus:border-transparent"," focus:outline-none focus:ring-2"),required:!0}),B.firstName&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-1",children:B.firstName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Soyad *"}),(0,a.jsx)("input",{type:"text",id:"lastName",name:"lastName",value:v.lastName,onChange:I,className:"w-full px-4 py-2 rounded-lg border transition text-black ".concat(B.lastName?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 focus:ring-purple-500 focus:border-transparent"," focus:outline-none focus:ring-2"),required:!0}),B.lastName&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-1",children:B.lastName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:"Telefon"}),(0,a.jsx)("input",{type:"tel",id:"phone",name:"phone",value:v.phone,onChange:I,className:"w-full px-4 py-2 rounded-lg border transition text-black ".concat(B.phone?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 focus:ring-purple-500 focus:border-transparent"," focus:outline-none focus:ring-2"),placeholder:"+90 ************"}),B.phone&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-1",children:B.phone})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Kişisel Bilgiler"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"birthDate",className:"block text-sm font-medium text-gray-700 mb-1",children:"Doğum Tarihi"}),(0,a.jsx)("input",{type:"date",id:"birthDate",name:"birthDate",value:v.birthDate,onChange:I,className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"gender",className:"block text-sm font-medium text-gray-700 mb-1",children:"Cinsiyet"}),(0,a.jsxs)("select",{id:"gender",name:"gender",value:v.gender,onChange:I,className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",children:[(0,a.jsx)("option",{value:"",children:"Se\xe7iniz"}),(0,a.jsx)("option",{value:"erkek",children:"Erkek"}),(0,a.jsx)("option",{value:"kadın",children:"Kadın"}),(0,a.jsx)("option",{value:"diğer",children:"Diğer"})]})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{htmlFor:"location",className:"block text-sm font-medium text-gray-700 mb-1",children:"Konum"}),(0,a.jsx)("input",{type:"text",id:"location",name:"location",value:v.location,onChange:I,className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",placeholder:"Şehir, \xdclke"})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200",children:[(0,a.jsx)(l.P.button,{type:"submit",disabled:k||!q(),className:"flex-1 py-3 px-6 rounded-lg font-medium transition-all duration-300 ".concat(k||!q()?"bg-gray-400 text-gray-600 cursor-not-allowed":"bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:shadow-lg"),whileHover:!k&&q()?{scale:1.02}:{},whileTap:!k&&q()?{scale:.98}:{},children:k?"Kaydediliyor...":"Değişiklikleri Kaydet"}),(0,a.jsx)(n(),{href:"/account",className:"flex-1",children:(0,a.jsx)(l.P.button,{type:"button",disabled:k,className:"w-full py-3 px-6 rounded-lg font-medium transition-all duration-300 ".concat(k?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-gray-100 text-gray-700 hover:bg-gray-200"),whileHover:k?{}:{scale:1.02},whileTap:k?{}:{scale:.98},children:"İptal"})})]})]})]})})})})}},83717:(e,t,r)=>{"use strict";r.d(t,{F:()=>i,Sz:()=>s,vn:()=>a,w7:()=>l});var a=function(e){return e[e.Default=0]="Default",e[e.PriceAsc=1]="PriceAsc",e[e.PriceDesc=2]="PriceDesc",e[e.RatingDesc=3]="RatingDesc",e}({}),i=function(e){return e[e.None=0]="None",e[e.Baslangic=1]="Baslangic",e[e.Girisimci=2]="Girisimci",e[e.Bronz=3]="Bronz",e[e.Gumus=4]="Gumus",e[e.Altin=5]="Altin",e[e.Platin=6]="Platin",e[e.PlatinMax=7]="PlatinMax",e}({}),l=function(e){return e[e.Unspecified=0]="Unspecified",e[e.Male=1]="Male",e[e.Female=2]="Female",e[e.Other=3]="Other",e}({}),s=function(e){return e[e.Pending=0]="Pending",e[e.Accepted=1]="Accepted",e[e.Rejected=2]="Rejected",e}({})},83769:(e,t,r)=>{Promise.resolve().then(r.bind(r,75921))}},e=>{var t=t=>e(e.s=t);e.O(0,[6408,6874,7323,8713,6681,8441,1684,7358],()=>t(83769)),_N_E=e.O()}]);