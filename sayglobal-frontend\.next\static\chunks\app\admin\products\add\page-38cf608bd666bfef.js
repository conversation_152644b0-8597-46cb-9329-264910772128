(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1295],{5061:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>D});var r=t(95155),s=t(12115),i=t(87220),n=t(35695),l=t(76408),d=t(35169),c=t(75525),o=t(81284),m=t(43332),x=t(62525),u=t(71539),g=t(27213),p=t(4229),h=t(6874),y=t.n(h),b=t(80722),f=t(22934),N=t(81087),v=t(87923),j=t(45106),k=t(26715),w=t(5041);let D=()=>{let{user:e,isLoading:a}=(0,i.A)(),t=(0,n.useRouter)(),h=(0,k.jE)(),{formData:D,selectedNames:I,variants:V,error:C,availableFeatures:F}=(0,v.t3)(),{handleInputChange:S,setCategorySelection:A,clearAllSelections:E,deleteVariant:M,removeImage:P,setMainImage:R,setError:O,reset:z,setVariants:K,saveVariant:q}=(0,v.t3)(e=>e),{openProductCategorySelector:_,closeProductCategorySelector:T,openProductVariant:Q,closeProductVariant:U}=(0,j.QR)(),Y=(0,j.fW)(),H=(0,j._f)(),L=(0,j.HX)(),B=(0,j.qA)(),G=e=>{let a={};return e.forEach(e=>{e.featureDetails.forEach(e=>{a[e.featureDefinitionId]||(a[e.featureDefinitionId]=[]),a[e.featureDefinitionId].includes(e.featureValueId)||a[e.featureDefinitionId].push(e.featureValueId)})}),a},{mutate:W,isPending:X}=(0,w.n)({mutationFn:e=>b.jU.createFullProduct(e),onSuccess:()=>{console.log("✅ \xdcr\xfcn başarıyla eklendi. Cache temizleniyor..."),h.invalidateQueries({queryKey:["products"]}),h.invalidateQueries({queryKey:["adminProducts"]}),h.invalidateQueries({queryKey:["adminProductStatistics"]}),t.push("/admin/products"),z()},onError:e=>{O(e.message||"\xdcr\xfcn eklenirken bir hata oluştu")}});(0,s.useEffect)(()=>()=>{z()},[z]),(0,s.useEffect)(()=>{e&&"admin"!==e.role&&t.push("/"),a||e||t.push("/login")},[e,a,t]);let J=e=>{Q({editingVariant:e,availableFeatures:F,existingVariants:V})},Z=async e=>{e.preventDefault(),O(null);try{if(!D.name.trim())throw Error("\xdcr\xfcn adı gereklidir");if(!D.description.trim())throw Error("\xdcr\xfcn a\xe7ıklaması gereklidir");if(D.brandId<=0)throw Error("Marka ve kategori se\xe7imi gereklidir");if(0===V.length)throw Error("En az bir varyant oluşturmalısınız.");for(let e of V){if(e.pricing.price<=0)throw Error("".concat(e.name," varyantı i\xe7in fiyat 0'dan b\xfcy\xfck olmalıdır"));if(e.pricing.stock<0)throw Error("".concat(e.name," varyantı i\xe7in stok miktarı negatif olamaz"))}let e=new FormData;e.append("Product.Name",D.name),e.append("Product.Description",D.description),e.append("Product.BrandId",D.brandId.toString()),e.append("Product.SubCategoryId",D.subCategoryId.toString()),e.append("Product.IsActive",D.isActive.toString()),V.forEach((a,t)=>{e.append("Variant[".concat(t,"].stock"),a.pricing.stock.toString()),e.append("Variant[".concat(t,"].price"),a.pricing.price.toString()),e.append("Variant[".concat(t,"].extraDiscount"),(a.pricing.extraDiscount||0).toString()),e.append("Variant[".concat(t,"].pv"),(a.pricing.ratios.pvRatio||0).toString()),e.append("Variant[".concat(t,"].cv"),(a.pricing.ratios.cvRatio||0).toString()),e.append("Variant[".concat(t,"].sp"),(a.pricing.ratios.spRatio||0).toString()),Object.values(a.selectedFeatures).flat().forEach((a,r)=>{e.append("Variant[".concat(t,"].featureValueIds[").concat(r,"]"),a.toString())}),a.images.forEach((a,r)=>{a.file&&(e.append("Variant[".concat(t,"].images[").concat(r,"].file"),a.file),e.append("Variant[".concat(t,"].images[").concat(r,"].isMain"),a.isMain.toString()),e.append("Variant[".concat(t,"].images[").concat(r,"].sortOrder"),a.sortOrder.toString()))})}),W(e)}catch(e){O(e.message||"\xdcr\xfcn eklenirken bir hata oluştu")}};return a?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):e&&"admin"===e.role?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:[(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(y(),{href:"/admin/products",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 mr-2"}),"\xdcr\xfcn Listesi"]}),(0,r.jsx)("span",{className:"text-gray-300",children:"/"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Yeni \xdcr\xfcn Ekle"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 bg-red-100 px-4 py-2 rounded-lg",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-red-600"}),(0,r.jsx)("span",{className:"text-red-800 font-medium",children:"Admin Erişimi"})]})]})}),C&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsx)("p",{className:"text-red-800",children:C}),(0,r.jsx)("button",{onClick:()=>O(null),className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Kapat"})]}),(0,r.jsxs)("form",{onSubmit:Z,className:"space-y-8",children:[(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(o.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Temel Bilgiler"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xdcr\xfcn Adı *"}),(0,r.jsx)("input",{type:"text",value:D.name,onChange:e=>S("name",e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-black",placeholder:"\xdcr\xfcn adını girin...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"A\xe7ıklama *"}),(0,r.jsx)("textarea",{value:D.description,onChange:e=>S("description",e.target.value),rows:4,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-black",placeholder:"\xdcr\xfcn a\xe7ıklamasını girin...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Marka, Kategori ve Detaylar"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>_({initialData:D?{brandId:D.brandId,categoryId:D.categoryId,subCategoryId:D.subCategoryId,selectedFeatures:G(V)}:{brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{}}}),className:"flex-grow px-4 py-3 border-2 border-dashed border-gray-300 rounded-lg hover:border-red-500 transition-colors flex items-center justify-center text-gray-600 hover:text-red-600",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2"}),D.brandId>0?"Kategori Se\xe7imini D\xfczenle":"Marka, Kategori ve Detaylar Se\xe7in"]}),D.brandId>0&&(0,r.jsx)("button",{type:"button",onClick:E,title:"T\xfcm se\xe7imleri temizle",className:"flex-shrink-0 p-3 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors",children:(0,r.jsx)(x.A,{className:"h-5 w-5"})})]}),D.brandId>0&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-gray-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"Se\xe7ili:"})," Marka: ",I.brandName,D.categoryId>0&&", Kategori: ".concat(I.categoryName),D.subCategoryId>0&&", Alt Kategori: ".concat(I.subCategoryName)]})})]})]})]}),(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"h-6 w-6 text-red-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Varyant Y\xf6netimi"})]})}),D.subCategoryId<=0&&(0,r.jsx)("div",{className:"p-4 text-center bg-gray-50 rounded-lg",children:(0,r.jsx)("p",{className:"text-gray-500 font-medium",children:"Tekli \xfcr\xfcn eklemek veya varyant oluşturmak i\xe7in l\xfctfen \xf6nce marka, kategori ve alt kategori se\xe7in."})}),D.subCategoryId>0&&0===V.length&&(0,r.jsxs)("div",{className:"p-8 text-center bg-gray-50 rounded-lg",children:[(0,r.jsx)(u.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-3"}),(0,r.jsx)("p",{className:"text-gray-500 font-medium",children:"Hen\xfcz varyant oluşturulmadı."}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1 mb-4",children:'Yukarıdaki "Marka, Kategori ve Detaylar Se\xe7in" kısmı ile d\xfczenleyebilirsiniz.'})]}),V.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 text-blue-600 mr-2 mt-0.5"}),(0,r.jsx)("p",{className:"text-sm text-blue-800",children:"Oluşturulan her bir varyant kombinasyonu i\xe7in Fiyat, Stok ve Puan bilgilerini girin. Tek bir \xfcr\xfcn ekliyorsanız bile, bu \xfcr\xfcn bir varyant olarak kabul edilir."})]})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Varyant"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fiyat (₺)"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stok"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"PV Oranı (%)"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"CV Oranı (%)"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"SP Oranı (%)"}),(0,r.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,r.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:V.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.featureDetails.map(e=>e.featureValue).join(", ")})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.price})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.stock})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.ratios.pvRatio})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.ratios.cvRatio})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.ratios.spRatio})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,r.jsx)("button",{type:"button",onClick:()=>J(e),className:"text-red-600 hover:text-red-900 mr-3",children:"Detay"}),(0,r.jsx)("button",{type:"button",onClick:()=>M(e.id),className:"text-gray-500 hover:text-gray-800",children:"Sil"})]})]},e.id))})]})})]})]}),(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(g.A,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"\xdcr\xfcn Fotoğrafları"})]}),(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(o.A,{className:"h-5 w-5 text-blue-400"})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-blue-700",children:'Her bir varyantın fotoğraflarını, varyant tablosundaki "Detay" butonuna tıklayarak a\xe7ılan pencereden y\xf6netebilirsiniz.'})})]})}),V.some(e=>e.images.length>0)&&(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"T\xfcm Varyant G\xf6rselleri (\xd6nizleme)"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:V.flatMap(e=>e.images.map(a=>({...a,variantName:e.name}))).map((e,a)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg overflow-hidden",children:(0,r.jsx)("img",{src:e.url,alt:"".concat(e.variantName," g\xf6rseli"),className:"w-full h-full object-cover"})}),(0,r.jsx)("div",{className:"absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded",children:e.variantName}),e.isMain&&(0,r.jsx)("div",{className:"absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded",children:"Ana"})]},a))})]})]}),(0,r.jsxs)(l.P.div,{className:"flex justify-end space-x-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,r.jsx)(y(),{href:"/admin/products",className:"px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"İptal"}),(0,r.jsx)("button",{type:"submit",disabled:X,className:"px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:X?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Kaydediliyor..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"\xdcr\xfcn\xfc Kaydet"]})})]})]})]}),(0,r.jsx)(f.A,{isOpen:Y,onClose:T,onSelect:e=>{A({brandId:e.brandId,categoryId:e.categoryId,subCategoryId:e.subCategoryId,brandName:e.brandName,categoryName:e.categoryName,subCategoryName:e.subCategoryName,selectedFeatures:e.selectedFeatures,selectedFeatureDetails:e.selectedFeatureDetails}),K(e.generatedVariants),T(),O(null)},initialData:(null==H?void 0:H.initialData)&&"object"==typeof H.initialData&&"selectedFeatures"in H.initialData?H.initialData:{brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{}}}),(0,r.jsx)(N.A,{isOpen:L,onClose:U,onSave:e=>{q(e,e.id),U()},editingVariant:null==B?void 0:B.editingVariant,availableFeatures:F,existingVariants:V})]}):null}},87923:(e,a,t)=>{"use strict";t.d(a,{t3:()=>d});var r=t(65453),s=t(46786);let i={formData:{name:"",description:"",brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{},price:0,stock:0,extraDiscount:0,ratios:{pvRatio:0,cvRatio:0,spRatio:0},points:{pv:0,cv:0,sp:0},hasVariants:!1,variants:[],images:[],isActive:!0},selectedNames:{brandName:"",categoryName:"",subCategoryName:""},selectedFeatureDetails:[],variants:[],availableFeatures:[],error:null},n=(e,a)=>({pv:Math.round(e*(a.pvRatio/100)),cv:Math.round(e*(a.cvRatio/100)),sp:Math.round(e*(a.spRatio/100))}),l=e=>{let a={},t=[];return e.forEach(e=>{e.featureDetails.forEach(e=>{a[e.featureDefinitionId]||(a[e.featureDefinitionId]=[]),a[e.featureDefinitionId].includes(e.featureValueId)||a[e.featureDefinitionId].push(e.featureValueId),t.some(a=>a.featureName===e.featureName&&a.featureValue===e.featureValue)||t.push({featureName:e.featureName,featureValue:e.featureValue})})}),{newSelectedFeatures:a,newSelectedFeatureDetails:t}},d=(0,r.v)()((0,s.lt)((e,a)=>({...i,setFormData:a=>e(e=>({formData:{...e.formData,...a}})),handleInputChange:(t,r)=>{let s={...a().formData,[t]:r};if(("price"===t||"ratios"===t)&&!s.hasVariants){let e=n(s.price,s.ratios);s={...s,points:e}}e({formData:s})},handleRatioChange:(t,r)=>{let s=a().formData,i={...s.ratios,[t]:r},l={...s,ratios:i};if(!l.hasVariants){let e=n(l.price,i);l={...l,points:e}}e({formData:l})},setCategorySelection:t=>{e({formData:{...a().formData,brandId:t.brandId,categoryId:t.categoryId,subCategoryId:t.subCategoryId,selectedFeatures:t.selectedFeatures,hasVariants:!1},selectedNames:{brandName:t.brandName,categoryName:t.categoryName,subCategoryName:t.subCategoryName},selectedFeatureDetails:t.selectedFeatureDetails,error:null})},clearAllSelections:()=>{e({formData:{...a().formData,brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{},hasVariants:!1},selectedNames:{brandName:"",categoryName:"",subCategoryName:""},selectedFeatureDetails:[],availableFeatures:[],variants:[],error:null})},setAvailableFeatures:a=>e({availableFeatures:a}),setVariants:a=>e({variants:a}),saveVariant:(t,r)=>{let s,{variants:i}=a(),{newSelectedFeatures:n,newSelectedFeatureDetails:d}=l(s=r?i.map(e=>e.id===r?t:e):[...i,{...t,id:Date.now()}]);e(e=>({variants:s,formData:{...e.formData,selectedFeatures:n,hasVariants:s.length>1},selectedFeatureDetails:d}))},deleteVariant:t=>{let{variants:r}=a(),s=r.filter(e=>e.id!==t),{newSelectedFeatures:i,newSelectedFeatureDetails:n}=l(s);e(e=>({variants:s,formData:{...e.formData,selectedFeatures:i,hasVariants:s.length>1},selectedFeatureDetails:n}))},generateVariants:a=>{let{newSelectedFeatures:t,newSelectedFeatureDetails:r}=l(a);e(e=>({formData:{...e.formData,hasVariants:!0,selectedFeatures:t},selectedFeatureDetails:r,variants:a,error:null}))},handleImageUpload:t=>{let{formData:r}=a(),s=Array.from(t).map((e,a)=>({url:URL.createObjectURL(e),isMain:0===r.images.length&&0===a,sortOrder:r.images.length+a,file:e}));e({formData:{...r,images:[...r.images,...s]}})},removeImage:t=>{let{formData:r}=a(),s=r.images.filter((e,a)=>a!==t);r.images[t].isMain&&s.length>0&&(s[0].isMain=!0),e({formData:{...r,images:s}})},setMainImage:t=>{let{formData:r}=a(),s=r.images.map((e,a)=>({...e,isMain:a===t}));e({formData:{...r,images:s}})},setError:a=>e({error:a}),reset:()=>e({...i})}),{name:"add-product-store",enabled:!1}))},99212:(e,a,t)=>{Promise.resolve().then(t.bind(t,5061))}},e=>{var a=a=>e(e.s=a);e.O(0,[6408,6874,7323,1531,6681,3651,8441,1684,7358],()=>a(99212)),_N_E=e.O()}]);