(()=>{var e={};e.id=957,e.ids=[957],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},13943:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23026:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},25541:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26098:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>U});var i=r(60687),s=r(43210),a=r(84299),n=r(88920),l=r(26001),o=r(11860),d=r(58869),c=r(40228),h=r(97992),m=r(28559),x=r(70334);let u=({isOpen:e,onClose:t,pendingMembers:r,onPlaceLeft:s,onPlaceRight:a})=>(0,i.jsx)(n.N,{children:e&&(0,i.jsx)(l.P.div,{className:"fixed inset-0 bg-white/20 backdrop-blur-lg flex items-center justify-center z-50",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:t,children:(0,i.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden",initial:{scale:.9,opacity:0,y:20},animate:{scale:1,opacity:1,y:0},exit:{scale:.9,opacity:0,y:20},transition:{duration:.3,type:"spring",damping:25,stiffness:300},onClick:e=>e.stopPropagation(),children:[(0,i.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4 text-white",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold",children:"Yerleşim Bekleyen \xdcyeler"}),(0,i.jsx)("p",{className:"text-blue-100 mt-1",children:"Ekibe yeni katılan \xfcyeleri ağaca yerleştirin"})]}),(0,i.jsx)("button",{onClick:t,className:"p-2 hover:bg-white/30 hover:bg-opacity-20 rounded-lg transition-colors",children:(0,i.jsx)(o.A,{className:"h-6 w-6"})})]})}),(0,i.jsx)("div",{className:"p-6",children:0===r.length?(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)(d.A,{className:"h-8 w-8 text-gray-400"})}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Yerleşim Bekleyen \xdcye Yok"}),(0,i.jsx)("p",{className:"text-gray-500",children:"Şu anda ağaca yerleştirilmeyi bekleyen yeni \xfcye bulunmuyor."})]}):(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"mb-6",children:(0,i.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,i.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"Yerleştirme Kuralları"}),(0,i.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,i.jsxs)("li",{children:["• ",(0,i.jsx)("strong",{children:"Sola Ekle:"})," \xdcyeyi ağacın en sol boş pozisyonuna yerleştirir"]}),(0,i.jsxs)("li",{children:["• ",(0,i.jsx)("strong",{children:"Sağa Ekle:"})," \xdcyeyi ağacın en sağ boş pozisyonuna yerleştirir"]}),(0,i.jsx)("li",{children:"• Yerleştirme \xf6nceliği: \xdcst seviyelerden alt seviyelere doğru"})]})]})}),(0,i.jsx)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:r.map(e=>(0,i.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-gray-300 transition-colors",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full flex items-center justify-center",children:(0,i.jsx)(d.A,{className:"h-6 w-6 text-white"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900",children:e.name}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:e.email}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:e.phone})]})]}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsxs)("div",{className:"flex items-center text-sm text-gray-500 mb-2",children:[(0,i.jsx)(c.A,{className:"h-4 w-4 mr-1"}),new Date(e.joinDate).toLocaleDateString("tr-TR")]}),(0,i.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 mr-1"}),"Sponsor: ",e.sponsorName]})]})]}),(0,i.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,i.jsxs)("div",{className:"flex space-x-3",children:[(0,i.jsxs)("button",{onClick:()=>s(e.id),className:"flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[(0,i.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Sola Ekle"]}),(0,i.jsxs)("button",{onClick:()=>a(e.id),className:"flex-1 inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:[(0,i.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Sağa Ekle"]})]})})]},e.id))})]})})]})})});var p=r(5336);let y=({isOpen:e,onClose:t,memberName:r,position:s})=>(0,i.jsx)(n.N,{children:e&&(0,i.jsx)(l.P.div,{className:"fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:t,children:(0,i.jsxs)(l.P.div,{className:"bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4",initial:{scale:.8,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.8,opacity:0,y:50},transition:{duration:.3,type:"spring",damping:20,stiffness:300},onClick:e=>e.stopPropagation(),children:[(0,i.jsx)(l.P.div,{className:"flex justify-center mb-6",initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring",damping:15},children:(0,i.jsx)("div",{className:"w-20 h-20 bg-green-100 rounded-full flex items-center justify-center",children:(0,i.jsx)(l.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",damping:12},children:(0,i.jsx)(p.A,{className:"h-12 w-12 text-green-600"})})})}),(0,i.jsxs)(l.P.div,{className:"text-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-3",children:"Başarıyla Yerleştirildi! \uD83C\uDF89"}),(0,i.jsxs)("p",{className:"text-gray-600 mb-6",children:[(0,i.jsx)("span",{className:"font-semibold text-green-600",children:r})," ","ekip ağacının ",(0,i.jsx)("span",{className:"font-semibold",children:s})," tarafına başarıyla yerleştirildi."]}),(0,i.jsx)("div",{className:"flex space-x-3",children:(0,i.jsx)(l.P.button,{onClick:t,className:"flex-1 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Harika!"})})]}),(0,i.jsx)(l.P.div,{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-400 to-emerald-500 rounded-t-2xl",initial:{scaleX:0},animate:{scaleX:1},transition:{delay:.4,duration:.6}})]})})});function f(e){var t=0,r=e.children,i=r&&r.length;if(i)for(;--i>=0;)t+=r[i].value;else t=1;e.value=t}function g(e,t){e instanceof Map?(e=[void 0,e],void 0===t&&(t=b)):void 0===t&&(t=v);for(var r,i,s,a,n,l=new w(e),o=[l];r=o.pop();)if((s=t(r.data))&&(n=(s=Array.from(s)).length))for(r.children=s,a=n-1;a>=0;--a)o.push(i=s[a]=new w(s[a])),i.parent=r,i.depth=r.depth+1;return l.eachBefore(N)}function v(e){return e.children}function b(e){return Array.isArray(e)?e[1]:null}function j(e){void 0!==e.data.value&&(e.value=e.data.value),e.data=e.data.data}function N(e){var t=0;do e.height=t;while((e=e.parent)&&e.height<++t)}function w(e){this.data=e,this.depth=this.height=0,this.parent=null}function k(e,t){return e.parent===t.parent?1:2}function A(e){var t=e.children;return t?t[0]:e.t}function M(e){var t=e.children;return t?t[t.length-1]:e.t}function S(e,t){this._=e,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=t}w.prototype=g.prototype={constructor:w,count:function(){return this.eachAfter(f)},each:function(e,t){let r=-1;for(let i of this)e.call(t,i,++r,this);return this},eachAfter:function(e,t){for(var r,i,s,a=this,n=[a],l=[],o=-1;a=n.pop();)if(l.push(a),r=a.children)for(i=0,s=r.length;i<s;++i)n.push(r[i]);for(;a=l.pop();)e.call(t,a,++o,this);return this},eachBefore:function(e,t){for(var r,i,s=this,a=[s],n=-1;s=a.pop();)if(e.call(t,s,++n,this),r=s.children)for(i=r.length-1;i>=0;--i)a.push(r[i]);return this},find:function(e,t){let r=-1;for(let i of this)if(e.call(t,i,++r,this))return i},sum:function(e){return this.eachAfter(function(t){for(var r=+e(t.data)||0,i=t.children,s=i&&i.length;--s>=0;)r+=i[s].value;t.value=r})},sort:function(e){return this.eachBefore(function(t){t.children&&t.children.sort(e)})},path:function(e){for(var t=this,r=function(e,t){if(e===t)return e;var r=e.ancestors(),i=t.ancestors(),s=null;for(e=r.pop(),t=i.pop();e===t;)s=e,e=r.pop(),t=i.pop();return s}(t,e),i=[t];t!==r;)i.push(t=t.parent);for(var s=i.length;e!==r;)i.splice(s,0,e),e=e.parent;return i},ancestors:function(){for(var e=this,t=[e];e=e.parent;)t.push(e);return t},descendants:function(){return Array.from(this)},leaves:function(){var e=[];return this.eachBefore(function(t){t.children||e.push(t)}),e},links:function(){var e=this,t=[];return e.each(function(r){r!==e&&t.push({source:r.parent,target:r})}),t},copy:function(){return g(this).eachBefore(j)},[Symbol.iterator]:function*(){var e,t,r,i,s=this,a=[s];do for(e=a.reverse(),a=[];s=e.pop();)if(yield s,t=s.children)for(r=0,i=t.length;r<i;++r)a.push(t[r]);while(a.length)}},S.prototype=Object.create(w.prototype);var z=r(62688);let P=(0,z.A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]),D=(0,z.A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var E=r(78272),C=r(41312),q=r(58559),L=r(86561),$=r(64398),I=r(25541),Y=r(23026);let _=(0,z.A)("zoom-out",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),B=(0,z.A)("zoom-in",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);var K=r(13943);let T=[{id:201,name:"Ayşe Demir",email:"<EMAIL>",phone:"+90 ************",joinDate:"2024-01-15",sponsorId:1,sponsorName:"Sen (Distrib\xfct\xf6r)"},{id:202,name:"Mehmet Kaya",email:"<EMAIL>",phone:"+90 ************",joinDate:"2024-01-16",sponsorId:2,sponsorName:"Ahmet Yılmaz"},{id:203,name:"Fatma \xd6z",email:"<EMAIL>",phone:"+90 ************",joinDate:"2024-01-17",sponsorId:1,sponsorName:"Sen (Distrib\xfct\xf6r)"}],H=({d3Node:e,onToggleExpand:t,isExpanded:r})=>{let s=e.data,a=s.children&&(s.children.left||s.children.right);return(0,i.jsx)("div",{className:"absolute transition-all duration-500",style:{left:`${e.x}px`,top:`${e.y}px`,transform:"translate(-50%, -50%)"},children:(0,i.jsxs)("div",{className:`relative bg-white rounded-xl shadow-lg p-4 w-64 border-2 transition-all duration-300 hover:shadow-xl ${s.isActive?"border-green-400 hover:border-green-500":"border-red-300 hover:border-red-400"} ${1===s.id?"ring-2 ring-blue-400":""}`,children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,i.jsx)("div",{className:`w-12 h-12 rounded-full flex items-center justify-center ${1===s.id?"bg-gradient-to-r from-blue-500 to-purple-600":s.isActive?"bg-gradient-to-r from-green-400 to-green-600":"bg-gradient-to-r from-gray-400 to-gray-600"}`,children:1===s.id?(0,i.jsx)(P,{className:"h-6 w-6 text-white"}):(0,i.jsx)(d.A,{className:"h-6 w-6 text-white"})}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-900 text-sm",children:s.name}),(0,i.jsxs)("p",{className:"text-xs text-gray-500",children:["Seviye ",s.level]}),1!==s.id&&s.position&&(0,i.jsx)("div",{className:`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium mt-1 ${"left"===s.position?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"}`,children:"left"===s.position?"Sol Kol":"Sağ Kol"})]}),(0,i.jsx)("div",{className:`px-2 py-1 rounded-full text-xs font-medium ${s.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:s.isActive?"Aktif":"Pasif"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,i.jsxs)("div",{className:"bg-blue-50 p-2 rounded",children:[(0,i.jsx)("div",{className:"font-semibold text-blue-700",children:s.points}),(0,i.jsx)("div",{className:"text-blue-600",children:"Puan"})]}),(0,i.jsxs)("div",{className:"bg-green-50 p-2 rounded",children:[(0,i.jsxs)("div",{className:"font-semibold text-green-700",children:["₺",s.totalEarnings.toLocaleString("tr-TR")]}),(0,i.jsx)("div",{className:"text-green-600",children:"Kazan\xe7"})]})]}),(0,i.jsx)("div",{className:"mt-2 pt-2 border-t border-gray-100",children:(0,i.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,i.jsx)(c.A,{className:"h-3 w-3 mr-1"}),new Date(s.joinDate).toLocaleDateString("tr-TR")]})}),a&&(0,i.jsx)("button",{onClick:()=>{t(s.id)},className:"absolute -bottom-4 left-1/2 transform -translate-x-1/2 bg-white border-2 border-gray-300 rounded-full p-1 hover:border-blue-400 transition-colors",children:r?(0,i.jsx)(D,{className:"h-4 w-4 text-gray-600"}):(0,i.jsx)(E.A,{className:"h-4 w-4 text-gray-600"})})]})})},R=({source:e,destination:t})=>{let r=e.x,s=e.y,a=t.x,n=t.y,l=`M ${r} ${s} C ${r} ${(s+n)/2}, ${a} ${(s+n)/2}, ${a} ${n}`;return(0,i.jsx)("path",{d:l,fill:"none",stroke:"#d1d5db",strokeWidth:"2",className:"transition-all duration-500"})},U=()=>{let e=a.dt,[t,r]=(0,s.useState)(a.At),[n,l]=(0,s.useState)(!1),[o,d]=(0,s.useState)(T),[c,h]=(0,s.useState)(!1),[m,x]=(0,s.useState)({memberName:"",position:"sol"}),[p,f]=(0,s.useState)(.52),[v,b]=(0,s.useState)(0),[j,N]=(0,s.useState)(0),w=(0,s.useRef)(null),[z,P]=(0,s.useState)(!1),[U,X]=(0,s.useState)({x:0,y:0}),[O,G]=(0,s.useState)({x:0,y:0}),[W,Z]=(0,s.useState)(!0),[F,J]=(0,s.useState)(()=>new Set),Q=(0,s.useCallback)(e=>{J(t=>{let r=new Set(t);return r.has(e)?r.delete(e):r.add(e),r})},[]),V=(0,s.useMemo)(()=>{let e=(function(){var e=k,t=1,r=1,i=null;function s(s){var o=function(e){for(var t,r,i,s,a,n=new S(e,0),l=[n];t=l.pop();)if(i=t._.children)for(t.children=Array(a=i.length),s=a-1;s>=0;--s)l.push(r=t.children[s]=new S(i[s],s)),r.parent=t;return(n.parent=new S(null,0)).children=[n],n}(s);if(o.eachAfter(a),o.parent.m=-o.z,o.eachBefore(n),i)s.eachBefore(l);else{var d=s,c=s,h=s;s.eachBefore(function(e){e.x<d.x&&(d=e),e.x>c.x&&(c=e),e.depth>h.depth&&(h=e)});var m=d===c?1:e(d,c)/2,x=m-d.x,u=t/(c.x+m+x),p=r/(h.depth||1);s.eachBefore(function(e){e.x=(e.x+x)*u,e.y=e.depth*p})}return s}function a(t){var r=t.children,i=t.parent.children,s=t.i?i[t.i-1]:null;if(r){!function(e){for(var t,r=0,i=0,s=e.children,a=s.length;--a>=0;)t=s[a],t.z+=r,t.m+=r,r+=t.s+(i+=t.c)}(t);var a=(r[0].z+r[r.length-1].z)/2;s?(t.z=s.z+e(t._,s._),t.m=t.z-a):t.z=a}else s&&(t.z=s.z+e(t._,s._));t.parent.A=function(t,r,i){if(r){for(var s,a,n,l=t,o=t,d=r,c=l.parent.children[0],h=l.m,m=o.m,x=d.m,u=c.m;d=M(d),l=A(l),d&&l;)c=A(c),(o=M(o)).a=t,(n=d.z+x-l.z-h+e(d._,l._))>0&&(!function(e,t,r){var i=r/(t.i-e.i);t.c-=i,t.s+=r,e.c+=i,t.z+=r,t.m+=r}((s=d,a=i,s.a.parent===t.parent?s.a:a),t,n),h+=n,m+=n),x+=d.m,h+=l.m,u+=c.m,m+=o.m;d&&!M(o)&&(o.t=d,o.m+=x-m),l&&!A(c)&&(c.t=l,c.m+=h-u,i=t)}return i}(t,s,t.parent.A||i[0])}function n(e){e._.x=e.z+e.parent.m,e.m+=e.parent.m}function l(e){e.x*=t,e.y=e.depth*r}return s.separation=function(t){return arguments.length?(e=t,s):e},s.size=function(e){return arguments.length?(i=!1,t=+e[0],r=+e[1],s):i?null:[t,r]},s.nodeSize=function(e){return arguments.length?(i=!0,t=+e[0],r=+e[1],s):i?[t,r]:null},s})().nodeSize([340,240]).separation((e,t)=>e.parent===t.parent?1.5:2)(g(t,e=>{if(F.has(e.id))return;let t=[];return e.children?.left&&t.push(e.children.left),e.children?.right&&t.push(e.children.right),t.length>0?t:void 0}));return console.log("D3 Layout sonu\xe7ları:",{nodeCount:e.descendants().length,nodePositions:e.descendants().map(e=>({id:e.data.id,name:e.data.name,x:Math.round(e.x),y:Math.round(e.y),depth:e.depth}))}),e},[t,F]),{allNodes:ee,allLinks:et}=(0,s.useMemo)(()=>{let e=[],t=[],r=i=>{e.push(i),!F.has(i.data.id)&&i.children&&i.children.forEach(e=>{t.push({source:i,target:e}),r(e)})};return r(V),{allNodes:e,allLinks:t}},[V,F]),{width:er,height:ei,offsetX:es,offsetY:ea}=(0,s.useMemo)(()=>{if(0===ee.length)return{width:800,height:600,offsetX:0,offsetY:0};let e=Math.min(...ee.map(e=>e.x)),t=Math.max(...ee.map(e=>e.x)),r=Math.min(...ee.map(e=>e.y)),i=Math.max(...ee.map(e=>e.y)),s=t-e+340+300,a=i-r+240+300,n=-e+170+150,l=-r+120+150;return console.log("Container boyutları hesaplandı:",{nodeCount:ee.length,bounds:{minX:e,maxX:t,minY:r,maxY:i},totalWidth:s,totalHeight:a,offsetX:n,offsetY:l,nodePositions:ee.map(e=>({id:e.data.id,x:Math.round(e.x),y:Math.round(e.y)}))}),{width:s,height:a,offsetX:n,offsetY:l}},[ee]);(0,s.useEffect)(()=>{if(W&&w.current&&ee.length>0&&er>0&&ei>0&&void 0!==es&&void 0!==ea){let e=w.current.clientWidth,t=w.current.clientHeight;if(0===e||0===t)return;let r=Math.min(...ee.map(e=>e.x)),i=Math.max(...ee.map(e=>e.x)),s=Math.min(...ee.map(e=>e.y)),a=Math.max(...ee.map(e=>e.y)),n=i-r,l=a-s,o=r+es,d=s+ea,c=(e/2-o-n/2)/p,h=(t/2-d-l/2)/p;console.log("Direkt merkeze alma:",{containerSize:{width:e,height:t},treeBounds:{minX:r,maxX:i,minY:s,maxY:a},treeSize:{width:n,height:l},treePosition:{left:o,top:d},offset:{x:es,y:ea},zoom:p,calculatedPan:{x:c,y:h}}),b(c),N(h),G({x:c,y:h}),Z(!1)}},[W,ee.length,er,ei,es,ea,p]),(0,s.useEffect)(()=>{let e=w.current;if(!e)return;let t=e=>(e.preventDefault(),e.stopPropagation(),f(Math.max(.3,Math.min(3,p*(e.deltaY>0?.9:1.1)))),!1);return e.addEventListener("wheel",t,{passive:!1}),()=>{e.removeEventListener("wheel",t)}},[p]);let en=(0,s.useCallback)(()=>{f(Math.min(1.2*p,3))},[p]),el=(0,s.useCallback)(()=>{f(Math.max(p/1.2,.3))},[p]),eo=(0,s.useCallback)(e=>{0===e.button&&(P(!0),X({x:e.clientX,y:e.clientY}),G({x:v,y:j}),e.preventDefault())},[v,j]),ed=(0,s.useCallback)(e=>{if(z){let t=(e.clientX-U.x)/p,r=(e.clientY-U.y)/p;b(O.x+t),N(O.y+r)}},[z,U,O,p]),ec=(0,s.useCallback)(()=>{P(!1)},[]),[eh,em]=(0,s.useState)(null),[ex,eu]=(0,s.useState)(null),ep=(0,s.useCallback)(e=>{if(2===e.touches.length){let t=e.touches[0],r=e.touches[1];em(Math.sqrt(Math.pow(r.clientX-t.clientX,2)+Math.pow(r.clientY-t.clientY,2)))}else if(1===e.touches.length){let t=e.touches[0];eu({x:t.clientX,y:t.clientY}),G({x:v,y:j})}},[v,j]),ey=(0,s.useCallback)(e=>{if(2===e.touches.length&&eh){e.preventDefault();let t=e.touches[0],r=e.touches[1],i=Math.sqrt(Math.pow(r.clientX-t.clientX,2)+Math.pow(r.clientY-t.clientY,2));f(Math.max(.3,Math.min(3,i/eh*p))),em(i)}else if(1===e.touches.length&&ex){e.preventDefault();let t=e.touches[0],r=(t.clientX-ex.x)/p,i=(t.clientY-ex.y)/p;b(O.x+r),N(O.y+i)}},[eh,ex,O,p]),ef=(0,s.useCallback)(()=>{em(null),eu(null)},[]),eg=e=>{if(null===e||"object"!=typeof e)return e;if(e instanceof Date)return new Date(e.getTime());if(e instanceof Array)return e.map(e=>eg(e));if("object"==typeof e){let t={};return Object.keys(e).forEach(r=>{t[r]=eg(e[r])}),t}},ev=(e,t)=>{let r=[],i=(e,t)=>{if(e.id===t)return!0;if(e.children){for(let s of[e.children.left,e.children.right].filter(Boolean))if(i(s,t))return r.push(e.id),!0}return!1};return i(e,t),r.reverse()},eb=e=>{let t=e.children?.left;if(!t)return{parentId:e.id,position:"left"};let r=t;for(;r.children?.left;)r=r.children.left;return{parentId:r.id,position:"left"}},ej=e=>{let t=e.children?.right;if(!t)return{parentId:e.id,position:"right"};let r=t;for(;r.children?.right;)r=r.children.right;return{parentId:r.id,position:"right"}},eN=(e,t)=>{if(e.id===t)return e;if(e.children?.left){let r=eN(e.children.left,t);if(r)return r}if(e.children?.right){let r=eN(e.children.right,t);if(r)return r}return null},ew=(e,i)=>{let s=o.find(t=>t.id===e);if(!s)return;let a="left"===i?eb(t):ej(t);if(!a)return void alert("Ağa\xe7ta uygun boş pozisyon bulunamadı!");let n=eN(t,a.parentId),c=n?n.level+1:1,m={id:s.id,name:s.name,level:c,points:0,monthlyPoints:0,totalEarnings:0,isActive:!0,joinDate:s.joinDate,parentId:a.parentId,position:a.position};console.log("Yeni \xfcye ekleniyor:",{id:m.id,name:m.name,parentId:a.parentId,position:a.position});let u=eg(t);ek(u,a.parentId,m,a.position),r(u);let p=ev(u,m.id);J(e=>{let t=new Set(e);return p.forEach(e=>t.delete(e)),t.delete(a.parentId),t}),d(t=>t.filter(t=>t.id!==e)),x({memberName:s.name,position:"left"===i?"sol":"sağ"}),h(!0),l(!1)},ek=(e,t,r,i)=>e.id===t?(e.children||(e.children={}),e.children[i]=r,!0):!!(e.children?.left&&ek(e.children.left,t,r,i)||e.children?.right&&ek(e.children.right,t,r,i));return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Ekip Ağacı Y\xf6netimi"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Ekibinizdeki t\xfcm bağlantıları ve hiyerarşiyi g\xf6rselleştirin"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-8",children:[(0,i.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-4 border-l-4 border-blue-500",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam \xdcye"}),(0,i.jsx)("p",{className:"text-xl font-bold text-gray-900",children:e.totalMembers})]}),(0,i.jsx)(C.A,{className:"h-6 w-6 text-blue-600"})]})}),(0,i.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-4 border-l-4 border-green-500",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Aktif \xdcye"}),(0,i.jsx)("p",{className:"text-xl font-bold text-gray-900",children:e.activeMembers})]}),(0,i.jsx)(q.A,{className:"h-6 w-6 text-green-600"})]})}),(0,i.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-4 border-l-4 border-purple-500",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Seviye"}),(0,i.jsx)("p",{className:"text-xl font-bold text-gray-900",children:e.totalLevels})]}),(0,i.jsx)(L.A,{className:"h-6 w-6 text-purple-600"})]})}),(0,i.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-4 border-l-4 border-yellow-500",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Puan"}),(0,i.jsx)("p",{className:"text-xl font-bold text-gray-900",children:e.totalPoints.toLocaleString("tr-TR")})]}),(0,i.jsx)($.A,{className:"h-6 w-6 text-yellow-600"})]})}),(0,i.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-4 border-l-4 border-indigo-500",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Kazan\xe7"}),(0,i.jsxs)("p",{className:"text-lg font-bold text-gray-900",children:["₺",e.totalEarnings.toLocaleString("tr-TR")]})]}),(0,i.jsx)(I.A,{className:"h-6 w-6 text-indigo-600"})]})}),(0,i.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-4 border-l-4 border-orange-500",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Aylık B\xfcy\xfcme"}),(0,i.jsxs)("p",{className:"text-xl font-bold text-gray-900",children:["%",e.monthlyGrowth]})]}),(0,i.jsx)(I.A,{className:"h-6 w-6 text-orange-600"})]})})]}),(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 mb-8",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Yerleştirme Kontrolleri"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Yeni katılan \xfcyeleri ağaca yerleştirin"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[o.length>0&&(0,i.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:[o.length," \xfcye yerleşim bekliyor"]}),(0,i.jsxs)("button",{onClick:()=>l(!0),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[(0,i.jsx)(Y.A,{className:"h-4 w-4 mr-2"}),"Yerleşim Bekleyenler (",o.length,")"]})]})]}),(0,i.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,i.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,i.jsx)(Y.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium text-blue-900",children:"Yerleştirme Sistemi"}),(0,i.jsxs)("p",{className:"text-sm text-blue-700 mt-1",children:["• Yeni \xfcyeler otomatik olarak sisteme eklenir ve yerleşim bekler",(0,i.jsx)("br",{}),'• "Sola Ekle" \xfcyeyi ağacın en sol boş pozisyonuna yerleştirir',(0,i.jsx)("br",{}),'• "Sağa Ekle" \xfcyeyi ağacın en sağ boş pozisyonuna yerleştirir',(0,i.jsx)("br",{}),"• Yerleştirme işlemi \xfcst seviyelerden alt seviyelere doğru yapılır"]})]})]})})]}),(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8 mb-8",style:{overflow:"hidden"},children:[(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-2",children:"Ekip Hiyerarşisi"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Ekibinizdeki t\xfcm \xfcyeler ve aralarındaki bağlantılar. D\xfcğ\xfcmlere tıklayarak alt seviyeleri daraltıp genişletebilirsiniz."})]}),(0,i.jsx)("div",{className:"flex items-center space-x-2",children:(0,i.jsxs)("div",{className:"bg-gray-100 rounded-lg p-1 flex items-center space-x-1",children:[(0,i.jsx)("button",{onClick:el,className:"p-2 rounded-md hover:bg-gray-200 transition-colors",title:"K\xfc\xe7\xfclt",children:(0,i.jsx)(_,{className:"h-4 w-4 text-gray-600"})}),(0,i.jsxs)("span",{className:"text-sm font-medium text-gray-700 min-w-[60px] text-center",children:[Math.round(100*p),"%"]}),(0,i.jsx)("button",{onClick:en,className:"p-2 rounded-md hover:bg-gray-200 transition-colors",title:"B\xfcy\xfclt",children:(0,i.jsx)(B,{className:"h-4 w-4 text-gray-600"})}),(0,i.jsx)("div",{className:"w-px h-6 bg-gray-300 mx-1"}),(0,i.jsx)("button",{onClick:()=>{f(.52),b(0),N(0),G({x:0,y:0}),Z(!0)},className:"p-2 rounded-md hover:bg-gray-200 transition-colors",title:"Sıfırla",children:(0,i.jsx)(K.A,{className:"h-4 w-4 text-gray-600"})})]})})]}),(0,i.jsxs)("div",{className:"mt-4 text-sm text-gray-500",children:["\uD83D\uDCA1 ",(0,i.jsx)("strong",{children:"İpucu:"})," Mouse tekerleği ile zoom yapabilir, mobilde iki parmakla b\xfcy\xfct\xfcp k\xfc\xe7\xfcltebilirsiniz."]})]}),(0,i.jsxs)("div",{ref:w,className:"relative border border-gray-200 rounded-lg",style:{width:"100%",height:"600px",overflow:"hidden",touchAction:"none",cursor:z?"grabbing":"grab"},onMouseDown:eo,onMouseMove:ed,onMouseUp:ec,onMouseLeave:ec,onTouchStart:ep,onTouchMove:ey,onTouchEnd:ef,children:[(0,i.jsxs)("div",{className:"absolute inset-0",style:{transform:`scale(${p}) translate(${v}px, ${j}px)`,transformOrigin:"center center",transition:z?"none":"transform 0.1s ease-out",width:er,height:ei,pointerEvents:"none"},children:[(0,i.jsx)("svg",{className:"absolute top-0 left-0",width:er,height:ei,children:(0,i.jsx)("g",{transform:`translate(${es}, ${ea})`,children:et.map((e,t)=>(0,i.jsx)(R,{source:e.source,destination:e.target},`link-${e.source.data.id}-to-${e.target.data.id}`))})}),(0,i.jsx)("div",{className:"relative",style:{transform:`translate(${es}px, ${ea}px)`},children:ee.map(e=>(0,i.jsx)("div",{style:{pointerEvents:"auto"},children:(0,i.jsx)(H,{d3Node:e,onToggleExpand:Q,isExpanded:!F.has(e.data.id)})},`node-${e.data.id}`))})]}),(0,i.jsxs)("div",{className:"absolute bottom-4 left-4 bg-black bg-opacity-75 text-white px-3 py-1 rounded-full text-sm",children:[Math.round(100*p),"%"]}),(0,i.jsx)("div",{className:"absolute top-4 left-4 bg-blue-500 bg-opacity-75 text-white px-3 py-1 rounded-full text-sm",children:"\uD83D\uDDB1️ S\xfcr\xfckle & Zoom"})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Renk Kodları ve A\xe7ıklamalar"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded border-2 border-blue-400"}),(0,i.jsx)("span",{className:"text-sm text-gray-700",children:"Distrib\xfct\xf6r (Sen)"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-4 h-4 bg-gradient-to-r from-green-400 to-green-600 rounded border-2 border-green-400"}),(0,i.jsx)("span",{className:"text-sm text-gray-700",children:"Aktif \xdcye"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-4 h-4 bg-gradient-to-r from-gray-400 to-gray-600 rounded border-2 border-red-300"}),(0,i.jsx)("span",{className:"text-sm text-gray-700",children:"Pasif \xdcye"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(E.A,{className:"h-4 w-4 text-gray-600"}),(0,i.jsx)("span",{className:"text-sm text-gray-700",children:"Alt seviyeyi genişlet"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(D,{className:"h-4 w-4 text-gray-600"}),(0,i.jsx)("span",{className:"text-sm text-gray-700",children:"Alt seviyeyi daralt"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(Y.A,{className:"h-4 w-4 text-blue-600"}),(0,i.jsx)("span",{className:"text-sm text-gray-700",children:"Yerleşim bekleyen \xfcye"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs font-medium",children:"Sol Kol"}),(0,i.jsx)("span",{className:"text-sm text-gray-700",children:"Sol kolda yer alan \xfcye"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"px-2 py-0.5 bg-purple-100 text-purple-800 rounded-full text-xs font-medium",children:"Sağ Kol"}),(0,i.jsx)("span",{className:"text-sm text-gray-700",children:"Sağ kolda yer alan \xfcye"})]})]})]}),(0,i.jsx)(u,{isOpen:n,onClose:()=>l(!1),pendingMembers:o,onPlaceLeft:e=>ew(e,"left"),onPlaceRight:e=>ew(e,"right")}),(0,i.jsx)(y,{isOpen:c,onClose:()=>h(!1),memberName:m.memberName,position:m.position})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38824:(e,t,r)=>{Promise.resolve().then(r.bind(r,26098))},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},40963:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\team-tree\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\team-tree\\page.tsx","default")},41312:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58559:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var i=r(43210);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:a="",children:n,iconNode:c,...h},m)=>(0,i.createElement)("svg",{ref:m,...d,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:l("lucide",a),...!n&&!o(h)&&{"aria-hidden":"true"},...h},[...c.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(n)?n:[n]])),h=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...a},o)=>(0,i.createElement)(c,{ref:o,iconNode:t,className:l(`lucide-${s(n(e))}`,`lucide-${e}`,r),...a}));return r.displayName=n(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var i=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75272:(e,t,r)=>{Promise.resolve().then(r.bind(r,40963))},78272:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83923:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>m,tree:()=>d});var i=r(65239),s=r(48088),a=r(88170),n=r.n(a),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["team-tree",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,40963)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\team-tree\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\team-tree\\page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/team-tree/page",pathname:"/team-tree",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},83997:e=>{"use strict";e.exports=require("tty")},84299:(e,t,r)=>{"use strict";r.d(t,{At:()=>n,Zq:()=>i,dt:()=>l,wI:()=>a,x1:()=>s});let i=[{id:1,distributorId:2,date:"2023-04-15",reference:"Aylin Şahin",points:120,amount:240.5,level:1,percentage:8},{id:2,distributorId:2,date:"2023-04-20",reference:"Emre Kılı\xe7",points:85,amount:170,level:2,percentage:5},{id:3,distributorId:2,date:"2023-04-28",reference:"Arda Altun",points:150,amount:300.75,level:1,percentage:8},{id:4,distributorId:2,date:"2023-05-05",reference:"Burak \xd6zt\xfcrk",points:60,amount:120.25,level:3,percentage:3},{id:5,distributorId:2,date:"2023-05-12",reference:"Selin Kara",points:200,amount:400,level:1,percentage:8}],s=[{id:101,firstName:"Aylin",lastName:"Şahin",level:1,joinDate:"2023-02-10",points:450,isActive:!0},{id:102,firstName:"Emre",lastName:"Kılı\xe7",level:2,joinDate:"2023-02-15",points:320,isActive:!0},{id:103,firstName:"Arda",lastName:"Altun",level:1,joinDate:"2023-03-01",points:580,isActive:!0},{id:104,firstName:"Burak",lastName:"\xd6zt\xfcrk",level:3,joinDate:"2023-03-12",points:150,isActive:!1},{id:105,firstName:"Selin",lastName:"Kara",level:1,joinDate:"2023-03-25",points:650,isActive:!0},{id:106,firstName:"Murat",lastName:"Aydın",level:2,joinDate:"2023-04-05",points:280,isActive:!0},{id:107,firstName:"Elif",lastName:"\xc7elik",level:3,joinDate:"2023-04-18",points:120,isActive:!1}],a={totalEarnings:4250.75,monthlyPoints:350,monthlyActivityPercentage:75,teamSize:s.length,monthlyBalance:1230.5,totalBalance:4250.75,totalPoints:2150,organizationPoints:5680,monthlyEarnings:[{month:"Ocak",earnings:850.25,activity:65},{month:"Şubat",earnings:920.5,activity:70},{month:"Mart",earnings:1050.75,activity:80},{month:"Nisan",earnings:1230.5,activity:75},{month:"Mayıs",earnings:980.25,activity:72}],monthlyPointsHistory:[{month:"Ocak",points:280,target:300},{month:"Şubat",points:320,target:300},{month:"Mart",points:390,target:350},{month:"Nisan",points:420,target:350},{month:"Mayıs",points:350,target:400},{month:"Haziran",points:310,target:400}],monthlyActivityTrend:[{month:"Ocak",activityPercentage:65,teamSize:4,newMembers:1},{month:"Şubat",activityPercentage:70,teamSize:5,newMembers:1},{month:"Mart",activityPercentage:80,teamSize:6,newMembers:1},{month:"Nisan",activityPercentage:75,teamSize:6,newMembers:0},{month:"Mayıs",activityPercentage:72,teamSize:7,newMembers:1},{month:"Haziran",activityPercentage:78,teamSize:7,newMembers:0}],nextLevelPoints:500,currentLevel:"G\xfcm\xfcş",nextLevel:"Altın"},n={id:1,name:"Distrib\xfct\xf6r (Sen)",level:3,points:2150,joinDate:"2023-01-01",isActive:!0,totalEarnings:4250.75,monthlyPoints:350,children:{left:{id:101,name:"Aylin Şahin",level:1,points:450,joinDate:"2023-02-10",isActive:!0,parentId:1,position:"left",totalEarnings:1200.5,monthlyPoints:120,children:{left:{id:103,name:"Arda Altun",level:1,points:580,joinDate:"2023-03-01",isActive:!0,parentId:101,position:"left",totalEarnings:850.25,monthlyPoints:95,children:{left:{id:107,name:"Elif \xc7elik",level:3,points:120,joinDate:"2023-04-18",isActive:!1,parentId:103,position:"left",totalEarnings:240,monthlyPoints:25}}},right:{id:105,name:"Selin Kara",level:1,points:650,joinDate:"2023-03-25",isActive:!0,parentId:101,position:"right",totalEarnings:980.75,monthlyPoints:135}}},right:{id:102,name:"Emre Kılı\xe7",level:2,points:320,joinDate:"2023-02-15",isActive:!0,parentId:1,position:"right",totalEarnings:750.25,monthlyPoints:85,children:{left:{id:104,name:"Burak \xd6zt\xfcrk",level:3,points:150,joinDate:"2023-03-12",isActive:!1,parentId:102,position:"left",totalEarnings:320,monthlyPoints:40},right:{id:106,name:"Murat Aydın",level:2,points:280,joinDate:"2023-04-05",isActive:!0,parentId:102,position:"right",totalEarnings:480.5,monthlyPoints:65}}}}},l={totalMembers:7,activeMembers:5,totalLevels:3,totalPoints:2550,totalEarnings:8950,monthlyGrowth:12.5}},86561:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},94735:e=>{"use strict";e.exports=require("events")},97992:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,181,658,85],()=>r(83923));module.exports=i})();