(()=>{var e={};e.id=700,e.ids=[700],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8487:(e,r,t)=>{Promise.resolve().then(t.bind(t,90322))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48655:(e,r,t)=>{Promise.resolve().then(t.bind(t,85316))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73113:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),l=t.n(i),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(r,o);let d={children:["",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,85316)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\reset-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\reset-password\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/reset-password/page",pathname:"/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85316:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\reset-password\\page.tsx","default")},90322:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),a=t(85814),i=t.n(a),l=t(26001),n=t(43210),o=t(16189);function d(){let e=(0,o.useSearchParams)().get("token"),[r,t]=(0,n.useState)(""),[a,d]=(0,n.useState)(""),[c,p]=(0,n.useState)(!1),[x,u]=(0,n.useState)(!1),[m,h]=(0,n.useState)({}),f=()=>{let t={};return e||(t.token="Ge\xe7ersiz sıfırlama bağlantısı"),r?r.length<8&&(t.password="Şifre en az 8 karakter olmalıdır"):t.password="Şifre gereklidir",r!==a&&(t.confirmPassword="Şifreler eşleşmiyor"),h(t),0===Object.keys(t).length},g=async e=>{if(e.preventDefault(),f()){p(!0);try{await new Promise(e=>setTimeout(e,1500)),u(!0)}catch(e){h(e=>({...e,general:"Bir hata oluştu. L\xfctfen daha sonra tekrar deneyin."}))}finally{p(!1)}}},b=(()=>{if(!r)return{strength:0,label:"",color:""};let e=0;r.length>=8&&(e+=1),r.length>=12&&(e+=1),/[A-Z]/.test(r)&&(e+=1),/[a-z]/.test(r)&&(e+=1),/[0-9]/.test(r)&&(e+=1),/[^A-Za-z0-9]/.test(r)&&(e+=1);let t=[{label:"\xc7ok zayıf",color:"bg-red-500"},{label:"Zayıf",color:"bg-orange-500"},{label:"Orta",color:"bg-yellow-500"},{label:"İyi",color:"bg-lime-500"},{label:"G\xfc\xe7l\xfc",color:"bg-green-500"},{label:"\xc7ok g\xfc\xe7l\xfc",color:"bg-emerald-500"}];return{strength:Math.min(e,5),label:t[e].label,color:t[e].color}})();return x?(0,s.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,s.jsx)("div",{className:"max-w-md mx-auto",children:(0,s.jsx)(l.P.div,{className:"bg-white rounded-2xl shadow-lg overflow-hidden",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,s.jsxs)("div",{className:"p-8 text-center",children:[(0,s.jsx)("div",{className:"w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-10 w-10 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,s.jsx)("h1",{className:"text-3xl font-bold mb-4 text-gray-800",children:"Şifreniz Sıfırlandı!"}),(0,s.jsx)("p",{className:"text-gray-700 mb-8",children:"Şifreniz başarıyla değiştirildi. Artık yeni şifrenizle giriş yapabilirsiniz."}),(0,s.jsx)(l.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,s.jsx)(i(),{href:"/login",className:"inline-block bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300",children:"Giriş Yap"})})]})})})}):(0,s.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,s.jsx)("div",{className:"max-w-md mx-auto",children:(0,s.jsx)(l.P.div,{className:"bg-white rounded-2xl shadow-lg overflow-hidden",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,s.jsxs)("div",{className:"p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-2 text-gray-800",children:"Şifrenizi Sıfırlayın"}),(0,s.jsx)("p",{className:"text-gray-700",children:"L\xfctfen hesabınız i\xe7in yeni bir şifre belirleyin"})]}),m.token?(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6",children:[(0,s.jsx)("p",{children:m.token}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)(i(),{href:"/forgot-password",className:"text-purple-600 font-medium hover:text-purple-800 transition",children:"Yeni bir şifre sıfırlama bağlantısı iste"})})]}):(0,s.jsxs)("form",{onSubmit:g,children:[m.general&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6",children:(0,s.jsx)("p",{children:m.general})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Yeni Şifre"}),(0,s.jsx)("input",{id:"password",type:"password",value:r,onChange:e=>{t(e.target.value),m.password&&h(e=>({...e,password:void 0}))},className:`w-full px-4 py-3 rounded-lg border ${m.password?"border-red-500":"border-gray-300"} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500`,placeholder:"En az 8 karakter"}),m.password&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:m.password}),r&&(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsx)("div",{className:"h-2 flex-1 bg-gray-200 rounded-full overflow-hidden",children:(0,s.jsx)("div",{className:`h-full ${b.color}`,style:{width:`${(b.strength+1)*16.67}%`}})}),(0,s.jsx)("span",{className:"text-xs text-gray-500 ml-2 min-w-[70px]",children:b.label})]}),(0,s.jsxs)("ul",{className:"text-xs text-gray-600 mt-2 space-y-1",children:[(0,s.jsx)("li",{className:`${r.length>=8?"text-green-500":""}`,children:"• En az 8 karakter"}),(0,s.jsx)("li",{className:`${/[A-Z]/.test(r)?"text-green-500":""}`,children:"• En az bir b\xfcy\xfck harf"}),(0,s.jsx)("li",{className:`${/[0-9]/.test(r)?"text-green-500":""}`,children:"• En az bir rakam"}),(0,s.jsx)("li",{className:`${/[^A-Za-z0-9]/.test(r)?"text-green-500":""}`,children:"• En az bir \xf6zel karakter"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Şifre Tekrar"}),(0,s.jsx)("input",{id:"confirmPassword",type:"password",value:a,onChange:e=>{d(e.target.value),m.confirmPassword&&h(e=>({...e,confirmPassword:void 0}))},className:`w-full px-4 py-3 rounded-lg border ${m.confirmPassword?"border-red-500":"border-gray-300"} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500`,placeholder:"Şifrenizi tekrar girin"}),m.confirmPassword&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:m.confirmPassword})]}),(0,s.jsx)(l.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:c,className:`w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 ${c?"opacity-70 cursor-not-allowed":""}`,children:c?(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"İşleniyor..."]}):"Şifreyi Sıfırla"})]})]}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsx)("p",{className:"text-gray-700",children:(0,s.jsx)(i(),{href:"/login",className:"text-purple-600 font-medium hover:text-purple-800 transition",children:"Giriş sayfasına d\xf6n"})})})]})})})})}},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,181,658,85],()=>t(73113));module.exports=s})();