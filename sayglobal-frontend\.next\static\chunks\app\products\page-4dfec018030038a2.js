(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3571],{5278:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var s=a(95155),r=a(76408),l=a(5196);function n(e){let{checked:t,onChange:a,label:n,disabled:i=!1,size:o="md",className:d=""}=e,c={sm:{checkbox:"w-4 h-4",text:"text-xs",icon:"w-2.5 h-2.5"},md:{checkbox:"w-5 h-5",text:"text-sm",icon:"w-3 h-3"},lg:{checkbox:"w-6 h-6",text:"text-base",icon:"w-4 h-4"}}[o];return(0,s.jsxs)(r.P.label,{className:"flex items-center space-x-3 cursor-pointer select-none ".concat(i?"opacity-50 cursor-not-allowed":""," ").concat(d),whileHover:i?{}:{scale:1.01},whileTap:i?{}:{scale:.99},children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"checkbox",checked:t,onChange:e=>!i&&a(e.target.checked),disabled:i,className:"sr-only"}),(0,s.jsx)(r.P.div,{className:"\n                        ".concat(c.checkbox,"\n                        rounded-md\n                        border-2\n                        flex\n                        items-center\n                        justify-center\n                        transition-all\n                        duration-200\n                        ").concat(t?"bg-gradient-to-br from-purple-500 to-purple-700 border-purple-600 shadow-lg shadow-purple-500/25":"bg-white border-gray-300 hover:border-purple-400","\n                        ").concat(!i&&"hover:shadow-md","\n                    "),initial:!1,animate:{scale:t?1.05:1,boxShadow:t?"0 10px 25px -5px rgba(147, 51, 234, 0.25), 0 4px 6px -2px rgba(147, 51, 234, 0.05)":"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"},transition:{duration:.2,ease:"easeInOut"},children:(0,s.jsx)(r.P.div,{initial:!1,animate:{scale:+!!t,opacity:+!!t},transition:{duration:.15,ease:"easeInOut"},children:(0,s.jsx)(l.A,{className:"".concat(c.icon," text-white stroke-[3]")})})})]}),(0,s.jsx)("span",{className:"\n                    ".concat(c.text," \n                    text-gray-700 \n                    font-medium \n                    truncate \n                    flex-1\n                    ").concat(!i&&"group-hover:text-gray-900","\n                "),children:n})]})}},9655:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>V});var s=a(95155),r=a(12115),l=a(76408),n=a(35695),i=a(54416),o=a(32960),d=a(80722);let c=()=>(0,o.I)({queryKey:["categories"],queryFn:async()=>{let e=await d.jU.getCategories();if(!e.success)throw Error(e.error||"Kategoriler alınamadı");return e.data},staleTime:6e5,refetchOnWindowFocus:!1,refetchOnMount:!1}),u=e=>(0,o.I)({queryKey:["subcategories",e],queryFn:async()=>{if(!e)return[];let t=await d.jU.getSubCategoriesByCategory(e);if(!t.success)throw Error(t.error||"Alt kategoriler alınamadı");return t.data},enabled:!!e,staleTime:6e5,refetchOnWindowFocus:!1}),x=e=>(0,o.I)({queryKey:["products",e],queryFn:async()=>{let t=await d.jU.filterProducts(e);if(!t.success)throw Error(t.error||"\xdcr\xfcnler alınamadı");return t.data},staleTime:12e4,refetchOnWindowFocus:!0,refetchOnMount:!0,enabled:!0}),m=()=>(0,o.I)({queryKey:["referenceData"],queryFn:async()=>{let e=await d.jU.getReferenceData();if(!e.success)throw Error(e.error||"Reference data alınamadı");return e.data},staleTime:6e5,refetchOnWindowFocus:!1,refetchOnMount:!1});var g=a(65453),h=a(46786),p=a(83717);let f={categories:[],subCategories:{},products:[],isLoadingProducts:!1,brands:[],featureDefinitions:[],featureValues:[],isLoadingReferenceData:!1,selectedCategoryId:null,selectedSubCategoryId:null,selectedBrandIds:[],selectedFeatureValueIds:[],priceRange:[0,1e4],sortBy:p.vn.Default,isFilterOpen:!1,hoveredCategoryId:null,error:null},y=(0,g.v)()((0,h.lt)((e,t)=>({...f,setCategories:t=>{console.log("\uD83C\uDFEA Zustand: Setting categories:",t),e({categories:t},!1,"productsPage/setCategories")},setSubCategories:(t,a)=>{console.log("\uD83C\uDFEA Zustand: Setting subcategories for category",t,":",a),e(e=>({subCategories:{...e.subCategories,[t]:a}}),!1,"productsPage/setSubCategories")},setProducts:t=>{console.log("\uD83C\uDFEA Zustand: Setting products:",t.length,"items"),e({products:t},!1,"productsPage/setProducts")},setLoadingProducts:t=>{e({isLoadingProducts:t},!1,"productsPage/setLoadingProducts")},setBrands:t=>{console.log("\uD83C\uDFEA Zustand: Setting brands:",t),e({brands:t},!1,"productsPage/setBrands")},setFeatureDefinitions:t=>{console.log("\uD83C\uDFEA Zustand: Setting feature definitions:",t),e({featureDefinitions:t},!1,"productsPage/setFeatureDefinitions")},setFeatureValues:t=>{console.log("\uD83C\uDFEA Zustand: Setting feature values:",t),e({featureValues:t},!1,"productsPage/setFeatureValues")},setLoadingReferenceData:t=>{e({isLoadingReferenceData:t},!1,"productsPage/setLoadingReferenceData")},setSelectedCategory:t=>{console.log("\uD83C\uDFEA Zustand: Setting selected category:",t),e({selectedCategoryId:t,selectedSubCategoryId:null},!1,"productsPage/setSelectedCategory")},setSelectedSubCategory:t=>{console.log("\uD83C\uDFEA Zustand: Setting selected subcategory:",t),e({selectedSubCategoryId:t},!1,"productsPage/setSelectedSubCategory")},setSelectedBrandIds:t=>{console.log("\uD83C\uDFEA Zustand: Setting selected brand IDs:",t),e({selectedBrandIds:t},!1,"productsPage/setSelectedBrandIds")},setSelectedFeatureValueIds:t=>{console.log("\uD83C\uDFEA Zustand: Setting selected feature value IDs:",t),e({selectedFeatureValueIds:t},!1,"productsPage/setSelectedFeatureValueIds")},setPriceRange:t=>{console.log("\uD83C\uDFEA Zustand: Setting price range:",t),e({priceRange:t},!1,"productsPage/setPriceRange")},setSortBy:t=>{console.log("\uD83C\uDFEA Zustand: Setting sort by:",t),e({sortBy:t},!1,"productsPage/setSortBy")},resetFilters:()=>{console.log("\uD83C\uDFEA Zustand: Resetting all filters"),e({selectedCategoryId:null,selectedSubCategoryId:null,selectedBrandIds:[],selectedFeatureValueIds:[],priceRange:[0,1e4],sortBy:p.vn.Default},!1,"productsPage/resetFilters")},setFilterOpen:t=>{e({isFilterOpen:t},!1,"productsPage/setFilterOpen")},setHoveredCategory:t=>{e({hoveredCategoryId:t},!1,"productsPage/setHoveredCategory")},setError:t=>{console.log("\uD83C\uDFEA Zustand: Setting error:",t),e({error:t},!1,"productsPage/setError")},getCurrentFilterRequest:()=>{let e=t(),a={sortBy:e.sortBy};return e.selectedCategoryId&&(a.categoryIds=[e.selectedCategoryId]),e.selectedSubCategoryId&&(a.subCategoryIds=[e.selectedSubCategoryId]),e.selectedBrandIds.length>0&&(a.brandIds=e.selectedBrandIds),e.selectedFeatureValueIds.length>0&&(a.featureValueIds=e.selectedFeatureValueIds),e.priceRange[0]>0&&(a.minPrice=e.priceRange[0]),e.priceRange[1]<1e4&&(a.maxPrice=e.priceRange[1]),console.log("\uD83C\uDFEA Zustand: Generated filter request:",a),a}}),{name:"products-page-store",enabled:!1}));var b=a(60760),v=a(46767),j=a(42148),w=a(57340),N=a(6752),k=a(99245),P=a(40288),C=a(57930),S=a(51976),I=a(54653),F=a(74783),L=a(66474),A=a(40133);let M={Elektronik:v.A,Bilgisayar:j.A,"Ev & Yaşam":w.A,Giyim:N.A,Kitap:k.A,Oyun:P.A,Otomotiv:C.A,Sağlık:S.A,default:I.A};function R(){var e,t;let[a,n]=(0,r.useState)(!1),[i,o]=(0,r.useState)(null),[d,x]=(0,r.useState)(null),[m,g]=(0,r.useState)(null),[h,p]=(0,r.useState)(!1),[f,v]=(0,r.useState)(null),j=(0,r.useRef)(null),w=(0,r.useRef)(null),N=(0,r.useRef)(null),{selectedCategoryId:k,selectedSubCategoryId:P}=y(),{setSelectedCategory:C,setSelectedSubCategory:S,resetFilters:R}=y(),{data:T=[],isLoading:B}=c(),{data:z=[]}=u(i),{data:D=[]}=u(k),{data:O=[]}=u(f),E=e=>M[e]||M.default,H=e=>{N.current&&clearTimeout(N.current),N.current=setTimeout(()=>{o(e)},500)},Z=()=>{N.current&&clearTimeout(N.current),N.current=setTimeout(()=>{o(null)},800)},V=()=>{N.current&&clearTimeout(N.current)},W=e=>{k===e?C(null):C(e),S(null),n(!1),o(null)},K=e=>{let t=h?f:i;t&&C(t),S(e),n(!1),o(null),g(null),v(null)};(0,r.useEffect)(()=>{let e=()=>{let e=window.innerWidth<768;console.log("Mobile detection:",e,"Width:",window.innerWidth),p(e)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let q=e=>{console.log("Mobile category toggle:",e,"Current expanded:",m),m===e?(C(e),S(null),g(null),v(null),n(!1)):(g(e),v(e))};return((0,r.useEffect)(()=>{let e=e=>{j.current&&!j.current.contains(e.target)&&w.current&&!w.current.contains(e.target)&&(n(!1),o(null),g(null),v(null))};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e),N.current&&clearTimeout(N.current)}},[]),B)?(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-4",children:(0,s.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"})})}):(0,s.jsxs)("div",{className:"mb-8 relative",children:[(0,s.jsx)(l.P.div,{className:"bg-white rounded-xl shadow-lg border border-gray-100 relative z-10",initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.5,delay:.1}},whileHover:{scale:1.02,boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"},transition:{duration:.2,ease:"easeOut"},children:(0,s.jsxs)(l.P.button,{ref:w,onClick:()=>{n(!a),o(null)},whileTap:{scale:.98},transition:{duration:.2,ease:"easeOut"},className:"w-full flex items-center justify-between px-6 py-4 text-left rounded-xl",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,s.jsx)(F.A,{className:"h-5 w-5 text-purple-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm text-gray-500 block",children:"Kategori Se\xe7in"}),(0,s.jsx)("span",{className:"font-semibold text-gray-900 text-lg",children:(()=>{if(P){let e=D.find(e=>e.id===P);return(null==e?void 0:e.name)||"Alt Kategori"}if(k){let e=T.find(e=>e.id===k);return(null==e?void 0:e.name)||"Kategori"}return"T\xfcm Kategoriler"})()})]})]}),(0,s.jsx)(l.P.div,{animate:{rotate:180*!!a},transition:{duration:.2},children:(0,s.jsx)(L.A,{className:"h-5 w-5 text-gray-400"})})]})}),(0,s.jsx)(b.N,{children:a&&(0,s.jsx)(l.P.div,{ref:j,initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-2xl border border-gray-100 z-50 overflow-hidden",children:(0,s.jsxs)("div",{className:"max-h-96 overflow-y-auto",children:[(0,s.jsx)(l.P.button,{onClick:()=>{C(null),S(null),n(!1),o(null)},whileHover:{backgroundColor:"#f8fafc"},className:"w-full flex items-center px-6 py-4 text-left transition-all duration-200 border-b border-gray-100 ".concat(k||P?"text-gray-700 hover:bg-gray-50":"bg-purple-50 text-purple-700"),children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg ".concat(k||P?"bg-gray-100":"bg-purple-100"),children:(0,s.jsx)(I.A,{className:"h-5 w-5 ".concat(k||P?"text-gray-600":"text-purple-600")})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"T\xfcm Kategoriler"}),(0,s.jsx)("span",{className:"text-sm text-gray-500 block",children:"T\xfcm \xfcr\xfcnleri g\xf6r\xfcnt\xfcle"})]})]})}),(0,s.jsx)("div",{className:"py-2",children:T.map(e=>{let t=E(e.name),a=k===e.id;return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)(l.P.button,{onClick:()=>{console.log("Category clicked:",e.id,"isMobile:",h),h?q(e.id):W(e.id)},onMouseEnter:()=>{h||(H(e.id),x(e.id))},onMouseLeave:()=>{h||(Z(),x(null))},initial:{backgroundColor:a?"#f3f4f6":"transparent"},animate:{backgroundColor:a?"#f3f4f6":"transparent"},whileHover:{backgroundColor:"#e9d5ff",color:"#7c3aed",scale:1.01},whileTap:{scale:.99},transition:{duration:.15,ease:"easeOut"},className:"w-full flex items-center justify-between px-6 py-3 text-left ".concat(a?"text-purple-700":"text-gray-700"),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg ".concat(a||d===e.id?"bg-purple-100":"bg-gray-100"),children:(0,s.jsx)(t,{className:"h-5 w-5 ".concat(a||d===e.id?"text-purple-600":"text-gray-600")})}),(0,s.jsx)("span",{className:"font-medium",children:e.name})]}),(0,s.jsx)(L.A,{className:"h-4 w-4 transition-transform duration-200 ".concat(h?m===e.id?"rotate-180":"":i===e.id?"rotate-180":"")})]}),(0,s.jsx)(b.N,{children:(h&&m===e.id||!h&&i===e.id)&&(h?O.length>0:z.length>0)&&(0,s.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.2},onMouseEnter:h?void 0:V,onMouseLeave:h?void 0:Z,className:"bg-gray-50 border-t border-gray-100",children:(0,s.jsx)("div",{className:"py-2",children:(h?O:z).map(e=>(0,s.jsx)(l.P.button,{onClick:()=>K(e.id),initial:{backgroundColor:P===e.id?"#ffffff":"transparent"},animate:{backgroundColor:P===e.id?"#ffffff":"transparent"},whileHover:{x:4,backgroundColor:"#ffffff",color:"#7c3aed",scale:1.01},whileTap:{scale:.99},transition:{duration:.15,ease:"easeOut"},onMouseEnter:h?void 0:V,className:"w-full flex items-center px-12 py-2 text-left ".concat(P===e.id?"text-purple-700 font-medium shadow-sm":"text-gray-600"),children:(0,s.jsx)("span",{className:"text-sm",children:e.name})},e.id))})})})]},e.id)})})]})})}),(k||P)&&(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 flex items-center gap-2 flex-wrap",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Aktif filtre:"}),(0,s.jsxs)(l.P.button,{whileHover:{scale:1.02,transition:{duration:.15,ease:"easeOut"}},whileTap:{scale:.98,transition:{duration:.1,ease:"easeInOut"}},transition:{duration:.15,ease:"easeInOut"},onClick:R,className:"flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-xs font-medium",title:"T\xfcm filtreleri temizle",children:[(0,s.jsx)(A.A,{className:"w-3 h-3"}),(0,s.jsx)("span",{children:"Temizle"})]})]}),k&&(0,s.jsxs)("span",{className:"bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2",children:[null==(e=T.find(e=>e.id===k))?void 0:e.name,!P&&(0,s.jsx)("button",{onClick:()=>C(null),className:"text-purple-500 hover:text-purple-700 transition-colors",children:(0,s.jsx)("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),P&&(0,s.jsxs)("span",{className:"bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2",children:[null==(t=D.find(e=>e.id===P))?void 0:t.name,(0,s.jsx)("button",{onClick:()=>S(null),className:"text-blue-500 hover:text-blue-700 transition-colors",children:(0,s.jsx)("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]})]})}var T=a(66766),B=a(6874),z=a.n(B),D=a(53234),O=a(57038);function E(e){let{product:t}=e,{data:a,isLoading:r}=(0,D.P)(),{isCustomerPrice:n}=(0,O.w)(),i=(null==a?void 0:a.discountRate)||null,o=(e,t)=>t?Math.round(t/100*e):0,d=(()=>{let e=t.price;!n&&i&&i>0&&(e*=1-i/100);let a=Number(t.extraDiscount);return a>0&&(e*=1-a/100),e})(),c=!n&&i&&i>0||Number(t.extraDiscount)>0,u=o(t.price,t.pv),x=o(t.price,t.cv);return(0,s.jsx)(l.P.div,{whileHover:{y:-8},transition:{duration:.3},className:"group",children:(0,s.jsx)("div",{className:"bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 h-full flex flex-col",children:(0,s.jsxs)(z(),{href:"/product/".concat(t.id),className:"flex-1 flex flex-col",children:[(0,s.jsxs)("div",{className:"relative h-64 overflow-hidden group",children:[(0,s.jsx)(T.default,{src:t.mainImageUrl,alt:t.name,fill:!0,className:"object-cover transition-transform duration-700 group-hover:scale-110"}),(0,s.jsxs)("div",{className:"absolute top-3 right-3 flex flex-col gap-2",children:[!n&&i&&i>0&&(0,s.jsxs)(l.P.div,{className:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg",initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring"},children:["%",i," \xdcye İndirimi"]}),(()=>{let e=Number(t.extraDiscount);return e>0&&(0,s.jsxs)(l.P.div,{className:"bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg",initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring"},children:["%",e," İndirim"]})})()]}),(0,s.jsxs)("div",{className:"absolute top-3 left-3 flex flex-col gap-1",children:[u>0&&(0,s.jsxs)(l.P.div,{className:"bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-2 py-1 rounded-full text-xs font-medium shadow-lg flex items-center gap-1",initial:{scale:0},animate:{scale:1},transition:{delay:.3,type:"spring"},children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,s.jsx)("span",{className:"font-bold",children:"PV"}),(0,s.jsx)("span",{children:u})]}),x>0&&(0,s.jsxs)(l.P.div,{className:"bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-2 py-1 rounded-full text-xs font-medium shadow-lg flex items-center gap-1",initial:{scale:0},animate:{scale:1},transition:{delay:.4,type:"spring"},children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,s.jsx)("span",{className:"font-bold",children:"CV"}),(0,s.jsx)("span",{children:x})]})]})]}),(0,s.jsxs)("div",{className:"p-5 flex-1 flex flex-col",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-1 text-gray-800",children:t.name}),(0,s.jsx)("p",{className:"text-gray-600 mb-2 text-sm",children:t.brandName}),(0,s.jsx)("p",{className:"text-gray-500 text-sm mb-4 line-clamp-2 flex-1",children:t.description}),(0,s.jsx)("div",{className:"mt-auto",children:(0,s.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("span",{className:"text-lg font-bold text-purple-700",children:[d.toFixed(2)," ₺"]}),c&&(0,s.jsxs)("span",{className:"text-sm text-gray-500 line-through ml-2",children:[t.price.toFixed(2)," ₺"]})]}),t.averageRating&&t.averageRating>0&&(0,s.jsxs)("div",{className:"flex items-center bg-yellow-50 px-2 py-1 rounded",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),(0,s.jsx)("span",{className:"ml-1 text-sm font-medium text-yellow-700",children:t.averageRating.toFixed(1)}),(0,s.jsxs)("span",{className:"ml-1 text-xs text-gray-500",children:["(",t.totalReviewCount,")"]})]})]})})]})]})})})}var H=a(5278),Z=a(87220);function V(){let e=(0,n.useSearchParams)().get("category"),{user:t,isAuthenticated:a}=(0,Z.A)(),{data:o,isLoading:d}=(0,D.P)(),{isCustomerPrice:c,setIsCustomerPrice:u,resetCustomerPrice:g,_hasHydrated:h}=(0,O.w)(),{priceRange:f,sortBy:b,isFilterOpen:v,selectedBrandIds:j,selectedFeatureValueIds:w,brands:N,featureDefinitions:k,featureValues:P,getCurrentFilterRequest:C}=y(),{setFilterOpen:S,setPriceRange:I,setSortBy:F,setSelectedBrandIds:L,setSelectedFeatureValueIds:A,setBrands:M,setFeatureDefinitions:T,setFeatureValues:B}=y(),{data:z,isLoading:V,error:W}=x(C()),{data:K,isLoading:q,error:G}=m(),U=(null==z?void 0:z.data)||[];(0,r.useEffect)(()=>{e&&console.log("Category slug from URL:",e)},[e]),(0,r.useEffect)(()=>{(null==K?void 0:K.data)&&(M(K.data.brands),T(K.data.featureDefinitions),B(K.data.featureValues))},[K,M,T,B]);let _={hidden:{opacity:0,y:20},show:{opacity:1,y:0}};return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsx)(l.P.div,{className:"mb-6 text-center",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,s.jsx)("h1",{className:"text-4xl font-bold",children:"\xdcr\xfcnlerimiz"})}),(0,s.jsx)(R,{}),(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,s.jsx)("div",{className:"lg:hidden mb-4",children:(0,s.jsxs)(l.P.button,{whileTap:{scale:.95},onClick:()=>S(!v),className:"w-full bg-white rounded-lg border border-gray-200 px-4 py-3 flex items-center justify-between shadow-sm",children:[(0,s.jsx)("span",{className:"font-medium text-gray-700",children:"Filtreler"}),(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 transition-transform  text-gray-700 ".concat(v?"rotate-180":""),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]})}),(0,s.jsx)(l.P.aside,{className:"lg:block lg:w-1/4 ".concat(v?"block":"hidden"),initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.2},children:(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-md p-6 sticky top-24",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6 pb-4 border-b border-gray-100",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-black",children:"Filtreler"}),(j.length>0||w.length>0)&&(0,s.jsxs)(l.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{L([]),A([])},className:"flex items-center space-x-1 px-2 py-1 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors",title:"Filtreleri Temizle",children:[(0,s.jsx)(i.A,{className:"w-3 h-3"}),(0,s.jsx)("span",{children:"Temizle"})]})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h3",{className:"font-semibold mb-4 text-gray-800",children:"Fiyat Aralığı"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm text-gray-700",children:[(0,s.jsxs)("span",{children:[f[0]," ₺"]}),(0,s.jsxs)("span",{children:[f[1]," ₺"]})]}),(0,s.jsx)("input",{type:"range",min:"0",max:"10000",step:"100",value:f[1],onChange:e=>I([f[0],parseInt(e.target.value)]),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-purple-600"}),(0,s.jsxs)("div",{className:"flex justify-between gap-4",children:[(0,s.jsxs)(l.P.div,{className:"relative flex-1",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,s.jsx)("input",{type:"number",min:"0",max:f[1]||1e4,value:f[0],onChange:e=>{I([parseInt(e.target.value)||0,f[1]])},className:"w-full p-2 pr-4 border border-gray-200 rounded-md text-sm text-gray-500 focus:outline-none focus:ring-1 focus:ring-purple-500"}),(0,s.jsx)("span",{className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-700 text-sm",children:"₺"})]}),(0,s.jsxs)(l.P.div,{className:"relative flex-1",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,s.jsx)("input",{type:"number",min:f[0]||0,max:"10000",value:f[1],onChange:e=>{I([f[0],parseInt(e.target.value)||1e4])},className:"w-full p-2 pr-4 border border-gray-200 rounded-md text-sm text-gray-500 focus:outline-none focus:ring-1 focus:ring-purple-500"}),(0,s.jsx)("span",{className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-700 text-sm",children:"₺"})]})]})]})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-800",children:"Marka"}),j.length>0&&(0,s.jsx)(l.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>L([]),className:"flex items-center space-x-1 px-1.5 py-0.5 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors",title:"Marka Filtrelerini Temizle",children:(0,s.jsx)(i.A,{className:"w-3 h-3"})})]}),(0,s.jsx)("div",{className:"space-y-3 max-h-48 overflow-y-auto overflow-x-hidden",children:N.map(e=>(0,s.jsx)(H.A,{checked:j.includes(e.id),onChange:t=>{t?L([...j,e.id]):L(j.filter(t=>t!==e.id))},label:e.name,size:"sm",className:"hover:bg-gray-50 p-2 rounded-lg transition-colors"},e.id))})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-800",children:"\xd6zellikler"}),w.length>0&&(0,s.jsx)(l.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>A([]),className:"flex items-center space-x-1 px-1.5 py-0.5 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors",title:"\xd6zellik Filtrelerini Temizle",children:(0,s.jsx)(i.A,{className:"w-3 h-3"})})]}),(0,s.jsx)("div",{className:"space-y-4",children:k.map(e=>{let t=P.filter(t=>t.featureDefinitionId===e.id);return 0===t.length?null:(0,s.jsxs)("div",{className:"border-b border-gray-100 pb-4",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-700 mb-3 truncate",children:e.name}),(0,s.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto overflow-x-hidden",children:t.map(e=>(0,s.jsx)(H.A,{checked:w.includes(e.id),onChange:t=>{t?A([...w,e.id]):A(w.filter(t=>t!==e.id))},label:e.name,size:"sm",className:"hover:bg-gray-50 p-1.5 rounded-lg transition-colors"},e.id))})]},e.id)})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold mb-3 text-gray-800",children:"Sıralama"}),(0,s.jsxs)(l.P.select,{whileHover:{scale:1.02},whileTap:{scale:.98},value:b,onChange:e=>F(parseInt(e.target.value)),className:"w-full p-2 border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500 text-gray-700",children:[(0,s.jsx)("option",{value:p.vn.Default,children:"\xd6ne \xc7ıkanlar"}),(0,s.jsx)("option",{value:p.vn.PriceAsc,children:"Fiyat: D\xfcş\xfckten Y\xfckseğe"}),(0,s.jsx)("option",{value:p.vn.PriceDesc,children:"Fiyat: Y\xfcksekten D\xfcş\xfcğe"}),(0,s.jsx)("option",{value:p.vn.RatingDesc,children:"Puana G\xf6re"})]})]})]})}),(0,s.jsxs)(l.P.div,{className:"lg:w-3/4",variants:{hidden:{opacity:0},show:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"show",children:[h&&!d&&(null==o?void 0:o.discountRate)&&o.discountRate>0&&(0,s.jsxs)(l.P.div,{className:"mb-6 bg-white rounded-xl shadow-md p-4",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,s.jsx)(H.A,{checked:c,onChange:u,label:"M\xfcşteri Fiyatlarını G\xf6ster (\xdcye indirimi uygulanmaz)",size:"md",className:"flex items-center gap-3"}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 mt-2 ml-8",children:["Bu se\xe7enek aktif olduğunda \xfcye indiriminiz (%",o.discountRate,") uygulanmaz ve \xfcr\xfcnler m\xfcşteri fiyatları ile g\xf6r\xfcnt\xfclenir."]})]}),V||q?(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-md overflow-hidden animate-pulse",children:[(0,s.jsx)("div",{className:"h-64 bg-gray-200"}),(0,s.jsxs)("div",{className:"p-5",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-2"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded mb-4 w-2/3"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded mb-4"}),(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded"})]})]},t))}):W||G?(0,s.jsxs)(l.P.div,{className:"bg-white rounded-xl p-8 text-center shadow-md",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 mx-auto text-red-400 mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("h3",{className:"text-xl font-bold mb-2",children:"Hata Oluştu"}),(0,s.jsx)("p",{className:"text-gray-700 mb-4",children:"\xdcr\xfcnler y\xfcklenirken bir hata oluştu. L\xfctfen sayfayı yenileyin."}),(0,s.jsx)("p",{className:"text-sm text-red-600",children:(null==W?void 0:W.message)||(null==G?void 0:G.message)})]}):0===U.length?(0,s.jsxs)(l.P.div,{className:"bg-white rounded-xl p-8 text-center shadow-md",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 mx-auto text-gray-400 mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("h3",{className:"text-xl font-bold mb-2",children:"\xdcr\xfcn Bulunamadı"}),(0,s.jsx)("p",{className:"text-gray-700 mb-4",children:"Se\xe7tiğiniz filtrelere uygun \xfcr\xfcn bulunmamaktadır. L\xfctfen filtrelerinizi değiştirerek tekrar deneyin."}),(0,s.jsx)(l.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{let{resetFilters:e}=y.getState();e()},className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors",children:"Filtreleri Temizle"})]}):(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:U.map(e=>(0,s.jsx)(l.P.div,{variants:_,className:"group",children:(0,s.jsx)(E,{product:e})},e.id))})]})]})]})}},56630:(e,t,a)=>{Promise.resolve().then(a.bind(a,9655))},83717:(e,t,a)=>{"use strict";a.d(t,{F:()=>r,Sz:()=>n,vn:()=>s,w7:()=>l});var s=function(e){return e[e.Default=0]="Default",e[e.PriceAsc=1]="PriceAsc",e[e.PriceDesc=2]="PriceDesc",e[e.RatingDesc=3]="RatingDesc",e}({}),r=function(e){return e[e.None=0]="None",e[e.Baslangic=1]="Baslangic",e[e.Girisimci=2]="Girisimci",e[e.Bronz=3]="Bronz",e[e.Gumus=4]="Gumus",e[e.Altin=5]="Altin",e[e.Platin=6]="Platin",e[e.PlatinMax=7]="PlatinMax",e}({}),l=function(e){return e[e.Unspecified=0]="Unspecified",e[e.Male=1]="Male",e[e.Female=2]="Female",e[e.Other=3]="Other",e}({}),n=function(e){return e[e.Pending=0]="Pending",e[e.Accepted=1]="Accepted",e[e.Rejected=2]="Rejected",e}({})}},e=>{var t=t=>e(e.s=t);e.O(0,[6408,6874,7323,6766,3579,6681,8441,1684,7358],()=>t(56630)),_N_E=e.O()}]);