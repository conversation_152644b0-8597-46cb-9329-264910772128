(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[305],{4516:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},13052:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},14408:(e,t,s)=>{Promise.resolve().then(s.bind(s,26998))},16785:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},17580:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},18186:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},19420:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},19946:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var a=s(12115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=r(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:l=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:o="",children:m,iconNode:x,...h}=e;return(0,a.createElement)("svg",{ref:t,...d,width:l,height:l,stroke:s,strokeWidth:i?24*Number(r)/Number(l):r,className:n("lucide",o),...!m&&!c(h)&&{"aria-hidden":"true"},...h},[...x.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let s=(0,a.forwardRef)((s,r)=>{let{className:c,...d}=s;return(0,a.createElement)(o,{ref:r,iconNode:t,className:n("lucide-".concat(l(i(e))),"lucide-".concat(e),c),...d})});return s.displayName=i(e),s}},21492:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},26998:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var a=s(95155),l=s(12115),r=s(31395),i=s(76408),n=s(18186),c=s(38564),d=s(17580),o=s(54416),m=s(69037),x=s(69074),h=s(79397),p=s(28883),g=s(19420),u=s(33109),y=s(71539),j=s(55868),f=s(13052),v=s(4516),b=s(71007),N=s(16785);let w=e=>{let{member:t,isOpen:s,onClose:r}=e,[w,A]=(0,l.useState)("overview");if((0,l.useEffect)(()=>(s?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[s]),!s||!t)return null;let k={monthlyPoints:Math.floor(200*Math.random())+50,weeklyActivity:Math.floor(40*Math.random())+60,totalSales:Math.floor(5e3*Math.random())+1e3,teamMembers:Math.floor(10*Math.random())+1,monthlyGrowth:Math.floor(30*Math.random())+5,achievements:[{title:"İlk Satış",date:"2023-02-15",icon:n.A},{title:"Y\xfcksek Performans",date:"2023-03-10",icon:c.A},{title:"Ekip Lideri",date:"2023-04-05",icon:d.A}],recentActivity:[{action:"Yeni \xfcr\xfcn satışı",date:"2024-01-15",points:25},{action:"Ekip \xfcyesi ekledi",date:"2024-01-12",points:50},{action:"Hedef tamamlandı",date:"2024-01-10",points:100}]},M=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric"}),D=Math.floor((new Date().getTime()-new Date(t.joinDate).getTime())/864e5),S=(e=>{switch(e){case 1:return{name:"Bronz \xdcye",color:"from-amber-400 to-yellow-600",bgColor:"bg-amber-100",textColor:"text-amber-800"};case 2:return{name:"G\xfcm\xfcş \xdcye",color:"from-gray-400 to-gray-600",bgColor:"bg-gray-100",textColor:"text-gray-800"};case 3:return{name:"Altın \xdcye",color:"from-yellow-400 to-yellow-600",bgColor:"bg-yellow-100",textColor:"text-yellow-800"};default:return{name:"\xdcye",color:"from-blue-400 to-blue-600",bgColor:"bg-blue-100",textColor:"text-blue-800"}}})(t.level);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-lg flex items-center justify-center p-4 z-50",children:(0,a.jsxs)(i.P.div,{className:"bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.2},children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-indigo-600 px-6 py-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-white font-bold text-xl",children:[t.firstName.charAt(0),t.lastName.charAt(0)]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-white",children:[t.firstName," ",t.lastName]}),(0,a.jsx)("p",{className:"text-purple-100",children:S.name}),(0,a.jsx)("div",{className:"flex items-center mt-1",children:(0,a.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ".concat(t.isActive?"bg-green-500 bg-opacity-90 text-white border-green-400":"bg-red-500 bg-opacity-90 text-white border-red-400"),children:t.isActive?"Aktif":"Pasif"})})]})]}),(0,a.jsx)("button",{onClick:r,className:"p-2 hover:bg-white/20 hover:bg-opacity-20 rounded-lg transition-colors text-white",children:(0,a.jsx)(o.A,{className:"w-6 h-6"})})]})}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsxs)("nav",{className:"flex px-6",children:[(0,a.jsx)("button",{onClick:()=>A("overview"),className:"py-4 px-4 border-b-2 font-medium text-sm transition-colors ".concat("overview"===w?"border-purple-500 text-purple-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"Genel Bakış"}),(0,a.jsx)("button",{onClick:()=>A("performance"),className:"py-4 px-4 border-b-2 font-medium text-sm transition-colors ".concat("performance"===w?"border-purple-500 text-purple-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"Performans"}),(0,a.jsx)("button",{onClick:()=>A("contact"),className:"py-4 px-4 border-b-2 font-medium text-sm transition-colors ".concat("contact"===w?"border-purple-500 text-purple-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"İletişim"})]})}),(0,a.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["overview"===w&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-purple-600",children:"Toplam Puan"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-900",children:t.points.toLocaleString("tr-TR")})]}),(0,a.jsx)(c.A,{className:"h-8 w-8 text-purple-500"})]})}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-blue-600",children:"Seviye"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-900",children:t.level})]}),(0,a.jsx)(m.A,{className:"h-8 w-8 text-blue-500"})]})}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-green-600",children:"\xdcyelik S\xfcresi"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-900",children:D}),(0,a.jsx)("p",{className:"text-xs text-green-600",children:"g\xfcn"})]}),(0,a.jsx)(x.A,{className:"h-8 w-8 text-green-500"})]})}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-orange-600",children:"Ekip Boyutu"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-900",children:k.teamMembers})]}),(0,a.jsx)(d.A,{className:"h-8 w-8 text-orange-500"})]})})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"\xdcye Bilgileri"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Katılım Tarihi"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:M(t.joinDate)})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Distrib\xfct\xf6r Seviyesi"}),(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(S.bgColor," ").concat(S.textColor," mt-1"),children:S.name})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Durum"}),(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-1 ".concat(t.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:t.isActive?"Aktif \xdcye":"Pasif \xdcye"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Toplam Puan"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[t.points.toLocaleString("tr-TR")," puan"]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Hızlı İşlemler"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 mr-2"}),"Mesaj G\xf6nder"]}),(0,a.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Telefon Et"]}),(0,a.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"Rapor G\xf6r\xfcnt\xfcle"]})]})]})]}),"performance"===w&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-blue-600",children:"Aylık Puan"}),(0,a.jsx)("p",{className:"text-xl font-bold text-blue-900",children:k.monthlyPoints})]}),(0,a.jsx)(y.A,{className:"h-6 w-6 text-blue-500"})]})}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-green-600",children:"Haftalık Aktivite"}),(0,a.jsxs)("p",{className:"text-xl font-bold text-green-900",children:[k.weeklyActivity,"%"]})]}),(0,a.jsx)(h.A,{className:"h-6 w-6 text-green-500"})]})}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-purple-600",children:"Toplam Satış"}),(0,a.jsxs)("p",{className:"text-xl font-bold text-purple-900",children:["₺",k.totalSales.toLocaleString("tr-TR")]})]}),(0,a.jsx)(j.A,{className:"h-6 w-6 text-purple-500"})]})}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-orange-600",children:"Aylık B\xfcy\xfcme"}),(0,a.jsxs)("p",{className:"text-xl font-bold text-orange-900",children:["%",k.monthlyGrowth]})]}),(0,a.jsx)(u.A,{className:"h-6 w-6 text-orange-500"})]})})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Başarılar"}),(0,a.jsx)("div",{className:"space-y-3",children:k.achievements.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)(e.icon,{className:"h-5 w-5 text-yellow-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:M(e.date)})]})]}),(0,a.jsx)(n.A,{className:"h-5 w-5 text-yellow-500"})]},t))})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Son Aktiviteler"}),(0,a.jsx)("div",{className:"space-y-3",children:k.recentActivity.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.action}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:M(e.date)})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-green-600",children:["+",e.points," puan"]}),(0,a.jsx)(f.A,{className:"h-4 w-4 text-gray-400 ml-1"})]})]},t))})]})]}),"contact"===w&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"İletişim Bilgileri"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 text-gray-400 mr-3 mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"E-posta"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[t.firstName.toLowerCase(),".",t.lastName.toLowerCase(),"@sayglobal.com"]})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-gray-400 mr-3 mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Telefon"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"+90 5XX XXX XX XX"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(v.A,{className:"h-5 w-5 text-gray-400 mr-3 mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Adres"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"İstanbul, T\xfcrkiye"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"İletişim Se\xe7enekleri"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 mr-2"}),"E-posta G\xf6nder"]}),(0,a.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Telefon Et"]}),(0,a.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 mr-2"}),"Profil Detayları"]}),(0,a.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 mr-2"}),"Hedef Belirle"]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Son İletişim Ge\xe7mişi"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-between p-3 bg-white rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)(p.A,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"E-posta g\xf6nderildi"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"2 g\xfcn \xf6nce"})]})]})}),(0,a.jsx)("div",{className:"flex items-center justify-between p-3 bg-white rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)(g.A,{className:"h-4 w-4 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Telefon g\xf6r\xfcşmesi"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"1 hafta \xf6nce"})]})]})})]})]})]})]})]})})};var A=s(33786),k=s(55670),M=s(47924),D=s(21492);let S=()=>{let[e,t]=(0,l.useState)(r.x1),[s,i]=(0,l.useState)(""),[n,o]=(0,l.useState)("all"),[h,g]=(0,l.useState)("all"),[y,j]=(0,l.useState)("joinDate"),[f,v]=(0,l.useState)("desc"),[N,S]=(0,l.useState)(null),[P,C]=(0,l.useState)(!1),E=e=>{S(e),C(!0)},T=()=>{let e=r.x1;s&&(e=e.filter(e=>"".concat(e.firstName," ").concat(e.lastName).toLowerCase().includes(s.toLowerCase()))),"all"!==n&&(e=e.filter(e=>e.level===parseInt(n))),"all"!==h&&(e=e.filter(e=>"active"===h?e.isActive:!e.isActive)),t(e)},L=s=>{let a=y===s&&"desc"===f?"asc":"desc";j(s),v(a),t([...e].sort((e,t)=>{let l,r;switch(s){case"name":l="".concat(e.firstName," ").concat(e.lastName),r="".concat(t.firstName," ").concat(t.lastName);break;case"level":l=e.level,r=t.level;break;case"points":l=e.points,r=t.points;break;case"joinDate":l=new Date(e.joinDate),r=new Date(t.joinDate);break;default:return 0}return"asc"===a?l>r?1:-1:l<r?1:-1}))};l.useEffect(()=>{T()},[s,n,h]);let z=r.x1.filter(e=>e.isActive).length,H=r.x1.reduce((e,t)=>e+t.points,0),q=Math.round(H/r.x1.length);return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100 py-8",children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Ekip Y\xf6netimi"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Ekibinizdeki \xfcyeleri y\xf6netin ve performanslarını takip edin"})]}),(0,a.jsx)("div",{className:"mt-4 sm:mt-0",children:(0,a.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Yeni \xdcye Davet Et"]})})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Ekip"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:r.x1.length})]}),(0,a.jsx)("div",{className:"bg-purple-100 p-3 rounded-full",children:(0,a.jsx)(d.A,{className:"h-6 w-6 text-purple-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Aktif \xdcyeler"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:z})]}),(0,a.jsx)("div",{className:"bg-green-100 p-3 rounded-full",children:(0,a.jsx)(k.A,{className:"h-6 w-6 text-green-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-yellow-500",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Puan"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:H.toLocaleString("tr-TR")})]}),(0,a.jsx)("div",{className:"bg-yellow-100 p-3 rounded-full",children:(0,a.jsx)(c.A,{className:"h-6 w-6 text-yellow-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Ortalama Puan"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:q})]}),(0,a.jsx)("div",{className:"bg-blue-100 p-3 rounded-full",children:(0,a.jsx)(u.A,{className:"h-6 w-6 text-blue-600"})})]})})]}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 mb-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(M.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"\xdcye ara...",value:s,onChange:e=>i(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 placeholder:text-gray-600 text-black"})]}),(0,a.jsxs)("select",{value:n,onChange:e=>o(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-600",children:[(0,a.jsx)("option",{value:"all",children:"T\xfcm Seviyeler"}),(0,a.jsx)("option",{value:"1",children:"Seviye 1"}),(0,a.jsx)("option",{value:"2",children:"Seviye 2"}),(0,a.jsx)("option",{value:"3",children:"Seviye 3"})]}),(0,a.jsxs)("select",{value:h,onChange:e=>g(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-600",children:[(0,a.jsx)("option",{value:"all",children:"T\xfcm Durumlar"}),(0,a.jsx)("option",{value:"active",children:"Aktif"}),(0,a.jsx)("option",{value:"inactive",children:"Pasif"})]}),(0,a.jsx)("button",{onClick:()=>{i(""),o("all"),g("all"),t(r.x1)},className:"px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Filtreleri Temizle"})]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:e.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-indigo-600 px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-white font-bold text-lg",children:[e.firstName.charAt(0),e.lastName.charAt(0)]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white",children:[e.firstName," ",e.lastName]}),(0,a.jsxs)("p",{className:"text-purple-100 text-sm",children:["Seviye ",e.level]})]})]}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full ".concat(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"Aktif":"Pasif"})]})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-yellow-500 mr-1"}),(0,a.jsx)("span",{className:"text-lg font-bold text-gray-900",children:e.points})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Puan"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-blue-500 mr-1"}),(0,a.jsx)("span",{className:"text-lg font-bold text-gray-900",children:Math.floor((new Date().getTime()-new Date(e.joinDate).getTime())/864e5)})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"G\xfcn"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Katılım Tarihi:"}),(0,a.jsx)("span",{className:"font-medium text-gray-900",children:new Date(e.joinDate).toLocaleDateString("tr-TR")})]}),(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ".concat(1===e.level?"bg-green-100 text-green-800":2===e.level?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:[(0,a.jsx)(m.A,{className:"h-3 w-3 mr-1"}),"Seviye ",e.level," Distrib\xfct\xf6r"]})}),(0,a.jsxs)("div",{className:"flex space-x-2 pt-4",children:[(0,a.jsxs)("button",{onClick:()=>E(e),className:"flex-1 bg-purple-100 text-purple-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-purple-200 transition-colors",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 inline mr-1"}),"Detaylar"]}),(0,a.jsxs)("button",{className:"flex-1 bg-blue-100 text-blue-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-blue-200 transition-colors",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 inline mr-1"}),"Mesaj"]})]})]})})]},e.id))}),0===e.length&&(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsxs)("div",{className:"text-gray-500",children:[(0,a.jsx)(d.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"\xdcye bulunamadı"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Se\xe7ilen kriterlere uygun \xfcye bulunmuyor."})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Detaylı Ekip Listesi"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>L("name"),className:"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50",children:["İsim ",(0,a.jsx)(D.A,{className:"h-4 w-4 ml-1"})]}),(0,a.jsxs)("button",{onClick:()=>L("points"),className:"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50",children:["Puan ",(0,a.jsx)(D.A,{className:"h-4 w-4 ml-1"})]}),(0,a.jsxs)("button",{onClick:()=>L("joinDate"),className:"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50",children:["Tarih ",(0,a.jsx)(D.A,{className:"h-4 w-4 ml-1"})]})]})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"\xdcye"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Seviye"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Puan"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Katılım Tarihi"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Durum"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"İşlemler"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-purple-400 to-indigo-500 rounded-full flex items-center justify-center mr-3",children:(0,a.jsxs)("span",{className:"text-white font-semibold text-sm",children:[e.firstName.charAt(0),e.lastName.charAt(0)]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:[e.firstName," ",e.lastName]})})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(1===e.level?"bg-green-100 text-green-800":2===e.level?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:["Seviye ",e.level]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-yellow-500 mr-1"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.points.toLocaleString("tr-TR")})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:new Date(e.joinDate).toLocaleDateString("tr-TR")}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"Aktif":"Pasif"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>E(e),className:"text-purple-600 hover:text-purple-900 transition-colors",children:"Detaylar"}),(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-900 transition-colors",children:"Mesaj"})]})})]},e.id))})]})})]}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsx)("a",{href:"/panel",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors",children:"Distrib\xfct\xf6r Paneline D\xf6n"})})]}),(0,a.jsx)(w,{member:N,isOpen:P,onClose:()=>{C(!1),S(null)}})]})}},28883:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},31395:(e,t,s)=>{"use strict";s.d(t,{At:()=>i,Zq:()=>a,dt:()=>n,wI:()=>r,x1:()=>l});let a=[{id:1,distributorId:2,date:"2023-04-15",reference:"Aylin Şahin",points:120,amount:240.5,level:1,percentage:8},{id:2,distributorId:2,date:"2023-04-20",reference:"Emre Kılı\xe7",points:85,amount:170,level:2,percentage:5},{id:3,distributorId:2,date:"2023-04-28",reference:"Arda Altun",points:150,amount:300.75,level:1,percentage:8},{id:4,distributorId:2,date:"2023-05-05",reference:"Burak \xd6zt\xfcrk",points:60,amount:120.25,level:3,percentage:3},{id:5,distributorId:2,date:"2023-05-12",reference:"Selin Kara",points:200,amount:400,level:1,percentage:8}],l=[{id:101,firstName:"Aylin",lastName:"Şahin",level:1,joinDate:"2023-02-10",points:450,isActive:!0},{id:102,firstName:"Emre",lastName:"Kılı\xe7",level:2,joinDate:"2023-02-15",points:320,isActive:!0},{id:103,firstName:"Arda",lastName:"Altun",level:1,joinDate:"2023-03-01",points:580,isActive:!0},{id:104,firstName:"Burak",lastName:"\xd6zt\xfcrk",level:3,joinDate:"2023-03-12",points:150,isActive:!1},{id:105,firstName:"Selin",lastName:"Kara",level:1,joinDate:"2023-03-25",points:650,isActive:!0},{id:106,firstName:"Murat",lastName:"Aydın",level:2,joinDate:"2023-04-05",points:280,isActive:!0},{id:107,firstName:"Elif",lastName:"\xc7elik",level:3,joinDate:"2023-04-18",points:120,isActive:!1}],r={totalEarnings:4250.75,monthlyPoints:350,monthlyActivityPercentage:75,teamSize:l.length,monthlyBalance:1230.5,totalBalance:4250.75,totalPoints:2150,organizationPoints:5680,monthlyEarnings:[{month:"Ocak",earnings:850.25,activity:65},{month:"Şubat",earnings:920.5,activity:70},{month:"Mart",earnings:1050.75,activity:80},{month:"Nisan",earnings:1230.5,activity:75},{month:"Mayıs",earnings:980.25,activity:72}],monthlyPointsHistory:[{month:"Ocak",points:280,target:300},{month:"Şubat",points:320,target:300},{month:"Mart",points:390,target:350},{month:"Nisan",points:420,target:350},{month:"Mayıs",points:350,target:400},{month:"Haziran",points:310,target:400}],monthlyActivityTrend:[{month:"Ocak",activityPercentage:65,teamSize:4,newMembers:1},{month:"Şubat",activityPercentage:70,teamSize:5,newMembers:1},{month:"Mart",activityPercentage:80,teamSize:6,newMembers:1},{month:"Nisan",activityPercentage:75,teamSize:6,newMembers:0},{month:"Mayıs",activityPercentage:72,teamSize:7,newMembers:1},{month:"Haziran",activityPercentage:78,teamSize:7,newMembers:0}],nextLevelPoints:500,currentLevel:"G\xfcm\xfcş",nextLevel:"Altın"},i={id:1,name:"Distrib\xfct\xf6r (Sen)",level:3,points:2150,joinDate:"2023-01-01",isActive:!0,totalEarnings:4250.75,monthlyPoints:350,children:{left:{id:101,name:"Aylin Şahin",level:1,points:450,joinDate:"2023-02-10",isActive:!0,parentId:1,position:"left",totalEarnings:1200.5,monthlyPoints:120,children:{left:{id:103,name:"Arda Altun",level:1,points:580,joinDate:"2023-03-01",isActive:!0,parentId:101,position:"left",totalEarnings:850.25,monthlyPoints:95,children:{left:{id:107,name:"Elif \xc7elik",level:3,points:120,joinDate:"2023-04-18",isActive:!1,parentId:103,position:"left",totalEarnings:240,monthlyPoints:25}}},right:{id:105,name:"Selin Kara",level:1,points:650,joinDate:"2023-03-25",isActive:!0,parentId:101,position:"right",totalEarnings:980.75,monthlyPoints:135}}},right:{id:102,name:"Emre Kılı\xe7",level:2,points:320,joinDate:"2023-02-15",isActive:!0,parentId:1,position:"right",totalEarnings:750.25,monthlyPoints:85,children:{left:{id:104,name:"Burak \xd6zt\xfcrk",level:3,points:150,joinDate:"2023-03-12",isActive:!1,parentId:102,position:"left",totalEarnings:320,monthlyPoints:40},right:{id:106,name:"Murat Aydın",level:2,points:280,joinDate:"2023-04-05",isActive:!0,parentId:102,position:"right",totalEarnings:480.5,monthlyPoints:65}}}}},n={totalMembers:7,activeMembers:5,totalLevels:3,totalPoints:2550,totalEarnings:8950,monthlyGrowth:12.5}},33109:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},33786:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},38564:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},47924:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},54416:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},55670:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},55868:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},69037:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71007:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},71539:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},79397:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[6408,8441,1684,7358],()=>t(14408)),_N_E=e.O()}]);