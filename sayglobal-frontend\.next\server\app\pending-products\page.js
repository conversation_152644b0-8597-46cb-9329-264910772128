(()=>{var e={};e.id=489,e.ids=[489],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14952:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19080:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35071:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41862:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},47033:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62688:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var r=t(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=(...e)=>e.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim(),d=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:o,...m},x)=>(0,r.createElement)("svg",{ref:x,...c,width:s,height:s,stroke:e,strokeWidth:a?24*Number(t)/Number(s):t,className:n("lucide",l),...!i&&!d(m)&&{"aria-hidden":"true"},...m},[...o.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(i)?i:[i]])),m=(e,s)=>{let t=(0,r.forwardRef)(({className:t,...l},d)=>(0,r.createElement)(o,{ref:d,iconNode:s,className:n(`lucide-${a(i(e))}`,`lucide-${e}`,t),...l}));return t.displayName=i(e),t}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},68914:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\pending-products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\pending-products\\page.tsx","default")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72835:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["pending-products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,68914)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\pending-products\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\pending-products\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/pending-products/page",pathname:"/pending-products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},73560:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>M});var r=t(60687),a=t(43210),l=t(15908),i=t(16189),n=t(85814),d=t.n(n),c=t(26001),o=t(48730),m=t(5336),x=t(35071),h=t(19080),u=t(28559),p=t(96474),g=t(99270),y=t(41862),j=t(40228),f=t(58869),b=t(13861),N=t(63143),v=t(47033),w=t(14952),k=t(55245);let A=(0,t(26787).v)(e=>({searchTerm:"",statusFilter:-1,setSearchTerm:s=>e({searchTerm:s}),setStatusFilter:s=>e({statusFilter:s}),resetFilters:()=>e({searchTerm:"",statusFilter:-1})}));var C=t(8693),S=t(93613),q=t(11860),P=t(40237);let D=({productId:e,isOpen:s,onClose:t})=>{let l=(0,i.useRouter)(),n=(0,C.jE)(),d=(0,P.Q6)(),o=(0,P.e$)(),u=(0,P.Z9)(e=>e.setCurrentImage),p=(0,P.Z9)(e=>e.setSelectedVariant),{data:g,isLoading:b,error:N}=(0,k.N5)(e),{data:A,isLoading:D}=(0,k.qr)(e,s);if((0,a.useEffect)(()=>{g&&console.log("\uD83D\uDD0D Dealership Modal ProductDetail Data:",{id:g.id,name:g.name,variantsCount:g.variants?.length||0,variants:g.variants,hasVariants:g.variants&&g.variants.length>0,firstVariant:g.variants?.[0],firstVariantPrice:g.variants?.[0]?.price,firstVariantPriceType:typeof g.variants?.[0]?.price})},[g]),(0,a.useEffect)(()=>(s?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[s]),(0,a.useEffect)(()=>{u(0)},[d,u]),(0,a.useEffect)(()=>{s&&e&&(console.log("\uD83D\uDD04 Dealership Modal opened, forcing fresh data fetch for product:",e),n.invalidateQueries({queryKey:["dealershipProductDetail",e]}))},[s,e,n]),!s)return null;let M=e=>new Intl.NumberFormat("tr-TR",{style:"currency",currency:"TRY"}).format(e),z=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),U=g?.variants&&g.variants.length>0,E=U?Math.min(d,g.variants.length-1):0,R=U?g.variants[E]:null,B=R?.images?.map(e=>e.url)||[],T=R?(e=>0===e?{text:"Stokta Yok",color:"bg-red-100 text-red-800",icon:(0,r.jsx)(x.A,{className:"h-4 w-4"})}:e<20?{text:"Az Stok",color:"bg-yellow-100 text-yellow-800",icon:(0,r.jsx)(S.A,{className:"h-4 w-4"})}:{text:"Stokta",color:"bg-green-100 text-green-800",icon:(0,r.jsx)(m.A,{className:"h-4 w-4"})})(R.stock):null;return(0,r.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-lg flex items-center justify-center p-4 z-50",onClick:t,children:(0,r.jsxs)(c.P.div,{className:"bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.2},onClick:e=>e.stopPropagation(),children:[(0,r.jsx)("div",{className:"sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-xl",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center space-x-3",children:(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"\xdcr\xfcn Detayları"})}),(0,r.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(q.A,{className:"w-6 h-6"})})]})}),(0,r.jsxs)("div",{className:"p-6",children:[b&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,r.jsx)(y.A,{className:"h-8 w-8 animate-spin text-blue-600"}),(0,r.jsx)("span",{className:"ml-2 text-gray-600",children:"\xdcr\xfcn detayları y\xfckleniyor..."})]}),N&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(S.A,{className:"h-6 w-6 text-red-600 mt-1 mr-3 flex-shrink-0"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-red-800 mb-2",children:"\xdcr\xfcn Detayları Y\xfcklenemedi"}),(0,r.jsx)("p",{className:"text-red-700 mb-4",children:"\xdcr\xfcn detayları y\xfcklenirken bir hata oluştu. L\xfctfen tekrar deneyin."}),(0,r.jsxs)("div",{className:"text-sm text-red-600 bg-red-100 rounded p-2 font-mono",children:["Hata: ",N?.message||"Bilinmeyen hata"]}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Sayfayı Yenile"})]})]})}),g&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[U&&g.variants.length>1&&(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsx)("nav",{className:"-mb-px flex space-x-8",children:g.variants.map((e,s)=>(0,r.jsxs)("button",{onClick:()=>p(s),className:`py-2 px-1 border-b-2 font-medium text-sm ${d===s?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Varyant ",s+1,e.features.length>0&&(0,r.jsxs)("span",{className:"ml-1 text-xs",children:["(",e.features.map(e=>e.featureValue).join(", "),")"]})]},e.id))})}),(0,r.jsx)("div",{className:"relative aspect-square bg-gray-100 rounded-lg overflow-hidden",children:B.length>0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("img",{src:B[o],alt:g.name,className:"w-full h-full object-cover"}),B.length>1&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>{if(!R||!R.images?.length)return;let e=R.images.length;u((o-1+e)%e)},className:"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors",children:(0,r.jsx)(v.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>{if(R&&R.images?.length)u((o+1)%R.images.length)},className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors",children:(0,r.jsx)(w.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:[o+1," / ",B.length]})]})]}):(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,r.jsx)(h.A,{className:"h-16 w-16 text-gray-400"})})}),B.length>1&&(0,r.jsx)("div",{className:"grid grid-cols-4 gap-2",children:B.map((e,s)=>(0,r.jsx)("button",{onClick:()=>u(s),className:`aspect-square rounded-lg overflow-hidden border-2 ${o===s?"border-blue-500":"border-gray-200 hover:border-gray-300"}`,children:(0,r.jsx)("img",{src:e,alt:`${g.name} ${s+1}`,className:"w-full h-full object-cover"})},s))})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${g.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:g.isActive?"Aktif":"Pasif"}),(0,r.jsxs)("span",{className:`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full border ${(e=>{switch(e){case 0:return"bg-yellow-100 text-yellow-800 border-yellow-200";case 1:return"bg-green-100 text-green-800 border-green-200";case 2:return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(g.status)}`,children:[(e=>{switch(e){case 0:default:return(0,r.jsx)(S.A,{className:"h-4 w-4"});case 1:return(0,r.jsx)(m.A,{className:"h-4 w-4"});case 2:return(0,r.jsx)(x.A,{className:"h-4 w-4"})}})(g.status),(0,r.jsx)("span",{className:"ml-1",children:(e=>{switch(e){case 0:return"Onay Bekliyor";case 1:return"Onaylandı";case 2:return"Reddedildi";default:return"Bilinmiyor"}})(g.status)})]})]}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:g.categoryName})]}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:g.name}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:g.brandName})]}),R?(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:"Varyant Bilgileri"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Fiyat"}),(0,r.jsx)("p",{className:"text-xl font-bold text-gray-900",children:(()=>{let e=R.price;return null==e||void 0===e?"Fiyat Belirtilmemiş":isNaN(e)?"Ge\xe7ersiz Fiyat":M(e)})()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Stok"}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:T&&(0,r.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${T.color}`,children:[T.icon,(0,r.jsxs)("span",{className:"ml-1",children:[R.stock," - ",T.text]})]})})]})]}),R.features.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"\xd6zellikler"}),(0,r.jsx)("div",{className:"space-y-2",children:R.features.map((e,s)=>(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[e.featureName,":"]}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.featureValue})]},s))})]})]}):(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"h-5 w-5 text-yellow-600 mr-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-yellow-800",children:"Varyant Bilgileri Eksik"}),(0,r.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"Bu \xfcr\xfcn i\xe7in varyant bilgileri bulunamadı. \xdcr\xfcn\xfc d\xfczenleyerek varyant ekleyebilirsiniz."})]})]})}),g.description&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"A\xe7ıklama"}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:g.description})]}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-2",children:[(0,r.jsxs)("h3",{className:"font-semibold text-gray-900 flex items-center",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 mr-2 text-gray-600"}),"Tarihler"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Oluşturulma:"}),(0,r.jsx)("span",{className:"text-gray-900",children:z(g.createdAt)})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Son G\xfcncelleme:"}),(0,r.jsx)("span",{className:"text-gray-900",children:z(g.updatedAt)})]})]})]}),g.createdByUserId&&(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg border",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[(0,r.jsx)(f.A,{className:"h-5 w-5 mr-2 text-gray-600"}),"Oluşturan"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Kullanıcı Adı:"}),(0,r.jsx)("p",{className:"text-gray-900 font-medium",children:g.createdByUserName})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Rol:"}),(0,r.jsx)("p",{className:"text-gray-900 font-medium",children:g.createdByUserRole?.toLowerCase()==="admin"?"Admin":g.createdByUserRole?.toLowerCase()==="dealership"?"Satıcı":g.createdByUserRole?.toLowerCase()==="customer"?"M\xfcşteri":g.createdByUserRole})]})]})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg border",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 mr-2 text-gray-600"}),"Admin \xdcr\xfcn Notu"]}),D?(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,r.jsx)(y.A,{className:"h-5 w-5 animate-spin text-gray-400 mr-2"}),(0,r.jsx)("span",{className:"text-gray-500",children:"Not y\xfckleniyor..."})]}):A?(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"mb-3",children:A.message?(0,r.jsx)("p",{className:"text-gray-800 font-medium",children:A.message}):(0,r.jsx)("p",{className:"text-gray-500 italic",children:"Admin notu girilmemiş"})}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Değiştiren:"})," ",A.approvedByUser.fullName," (",A.approvedByUser.role,")"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Tarih:"})," ",new Date(A.changedAtUtc).toLocaleString("tr-TR")]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Durum:"}),(0,r.jsx)("span",{className:`ml-1 ${1===A.newStatus?"text-green-600":2===A.newStatus?"text-red-600":"text-yellow-600"}`,children:1===A.newStatus?"Onaylandı":2===A.newStatus?"Reddedildi":"Beklemede"})]})]})]}),(0,r.jsx)("div",{className:"ml-4",children:1===A.newStatus?(0,r.jsx)(m.A,{className:"h-5 w-5 text-green-500"}):2===A.newStatus?(0,r.jsx)(x.A,{className:"h-5 w-5 text-red-500"}):(0,r.jsx)(S.A,{className:"h-5 w-5 text-yellow-500"})})]})}):(0,r.jsx)("div",{className:"text-center py-4",children:g?.status===0?(0,r.jsxs)("div",{className:"flex items-center justify-center text-yellow-600",children:[(0,r.jsx)(S.A,{className:"h-5 w-5 mr-2"}),(0,r.jsx)("span",{children:"Onay bekleniyor"})]}):(0,r.jsxs)("div",{className:"flex items-center justify-center text-gray-500",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 mr-2"}),(0,r.jsx)("span",{children:"Admin notu girilmemiş"})]})})]}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsx)("button",{onClick:t,className:"flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors",children:"Kapat"}),0===g.status&&(0,r.jsx)("button",{className:"flex-1 px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors",onClick:()=>{t(),l.push(`/edit-product/${g.id}`)},children:"D\xfczenle"})]})]})]})]})]})})},M=()=>{let{user:e,isLoading:s}=(0,l.A)(),t=(0,i.useRouter)(),n=(0,C.jE)(),[S,q]=(0,a.useState)(1),[P,M]=(0,a.useState)(null),[z,U]=(0,a.useState)(!1),{searchTerm:E,statusFilter:R,setSearchTerm:B,setStatusFilter:T}=A(),[O,$]=(0,a.useState)("");(0,a.useEffect)(()=>{let e=setTimeout(()=>{$(E),q(1)},300);return()=>{clearTimeout(e)}},[E]),(0,a.useEffect)(()=>{if(!s&&!e||!s&&e&&"dealership"!==e.role&&"admin"!==e.role)return void t.push("/login")},[e,s,t]),(0,a.useEffect)(()=>{e&&("dealership"===e.role||"admin"===e.role)&&(n.invalidateQueries({queryKey:["myProductStatistics"]}),n.invalidateQueries({queryKey:["myProducts"]}),console.log("\uD83D\uDCCA \xdcr\xfcn y\xf6netimi sayfası y\xfcklendi, istatistikler ve \xfcr\xfcn listesi yenileniyor..."))},[e,n]);let{data:_,isLoading:L}=(0,k.Ee)(),{data:F,isLoading:V,isFetching:I}=(0,k.i5)(S,O,R),G=e=>{switch(e){case 0:return"bg-yellow-100 text-yellow-800";case 1:return"bg-green-100 text-green-800";case 2:return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},Y=e=>{switch(e){case 0:return(0,r.jsx)(o.A,{className:"h-4 w-4"});case 1:return(0,r.jsx)(m.A,{className:"h-4 w-4"});case 2:return(0,r.jsx)(x.A,{className:"h-4 w-4"});default:return(0,r.jsx)(h.A,{className:"h-4 w-4"})}},K=e=>{switch(e){case 0:return"Onay Bekliyor";case 1:return"Onaylandı";case 2:return"Reddedildi";default:return"Bilinmiyor"}},H=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),Q=e=>{M(e),U(!0)},Z=F?.data?.items||[],W=F?.data?.totalCount||0,X=_?.totalProductCount||0,J=_?.pendingCount||0,ee=_?.approvedCount||0,es=_?.rejectedCount||0;return s||L?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):e&&("dealership"===e.role||"admin"===e.role)?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:[(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(d(),{href:"/panel",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"Panele D\xf6n"]})}),(0,r.jsxs)(d(),{href:"/add-product",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2",children:[(0,r.jsx)(p.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Yeni \xdcr\xfcn Ekle"})]})]}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"\xdcr\xfcn Y\xf6netimi"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Eklediğiniz \xfcr\xfcnlerin onay durumunu takip edin ve yeni \xfcr\xfcnler ekleyin"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,r.jsx)(o.A,{className:"h-6 w-6 text-yellow-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Onay Bekleyen"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:J})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,r.jsx)(m.A,{className:"h-6 w-6 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Onaylanan"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:ee})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,r.jsx)(x.A,{className:"h-6 w-6 text-red-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Reddedilen"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:es})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,r.jsx)(h.A,{className:"h-6 w-6 text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:X})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,r.jsx)("input",{type:"text",placeholder:"\xdcr\xfcn ara...",value:E,onChange:e=>B(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"}),I&&(0,r.jsx)(y.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 animate-spin"})]}),(0,r.jsxs)("select",{value:R,onChange:e=>T(Number(e.target.value)),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-600",children:[(0,r.jsx)("option",{value:-1,children:"T\xfcm Durumlar"}),(0,r.jsx)("option",{value:0,children:"Onay Bekleyen"}),(0,r.jsx)("option",{value:1,children:"Onaylanan"}),(0,r.jsx)("option",{value:2,children:"Reddedilen"})]})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[W," \xfcr\xfcn g\xf6steriliyor"]})]})}),V?(0,r.jsxs)("div",{className:"flex justify-center items-center py-12",children:[(0,r.jsx)(y.A,{className:"h-8 w-8 animate-spin text-blue-600"}),(0,r.jsx)("span",{className:"ml-2 text-gray-600",children:"\xdcr\xfcnler y\xfckleniyor..."})]}):0===Z.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(h.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Hen\xfcz \xfcr\xfcn yok"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"İlk \xfcr\xfcn\xfcn\xfcz\xfc ekleyerek başlayın"}),(0,r.jsxs)(d(),{href:"/add-product",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center space-x-2",children:[(0,r.jsx)(p.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"\xdcr\xfcn Ekle"})]})]}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Z.map(e=>(0,r.jsxs)(c.P.div,{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,r.jsx)("div",{className:"aspect-w-16 aspect-h-12 bg-gray-200",children:e.imageUrl?(0,r.jsx)("img",{src:e.imageUrl,alt:e.name,className:"w-full h-48 object-cover"}):(0,r.jsx)("div",{className:"w-full h-48 bg-gray-200 flex items-center justify-center",children:(0,r.jsx)(h.A,{className:"h-12 w-12 text-gray-400"})})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${G(e.statusId)}`,children:[Y(e.statusId),(0,r.jsx)("span",{className:"ml-1",children:K(e.statusId)})]}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:e.categoryName})]}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 line-clamp-2",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-3 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:["₺",e.price.toFixed(2)]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 text-blue-500"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-blue-600",children:[e.totalStock," adet"]})]})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(j.A,{className:"h-3 w-3 mr-1"}),"Oluşturuldu: ",H(e.createdAt)]}),(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[(0,r.jsx)(f.A,{className:"h-3 w-3 mr-1"}),"Marka: ",e.brandName]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>Q(e.id),className:"flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-3 rounded-lg text-sm font-medium transition-colors flex items-center justify-center",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-1"}),"Detay"]}),0===e.statusId&&(0,r.jsxs)(d(),{href:`/edit-product/${e.id}`,className:"flex-1 bg-blue-100 hover:bg-blue-200 text-blue-800 py-2 px-3 rounded-lg text-sm font-medium transition-colors flex items-center justify-center",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 mr-1"}),"D\xfczenle"]})]})]})]},e.id))}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm mt-8",children:(0,r.jsxs)("div",{className:"px-6 py-4 border-t border-gray-200 flex items-center justify-between",children:[(0,r.jsxs)("button",{onClick:()=>q(e=>Math.max(e-1,1)),disabled:1===S,className:"flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"\xd6nceki"]}),(0,r.jsxs)("span",{className:"text-sm text-gray-700",children:["Sayfa ",(0,r.jsx)("span",{className:"font-bold",children:S})]}),(0,r.jsxs)("button",{onClick:()=>q(e=>10===Z.length?e+1:e),disabled:Z.length<10,className:"flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:["Sonraki",(0,r.jsx)(w.A,{className:"h-4 w-4 ml-2"})]})]})})]}),P&&(0,r.jsx)(D,{productId:P,isOpen:z,onClose:()=>{M(null),U(!1)}})]}):null}},74075:e=>{"use strict";e.exports=require("zlib")},75064:(e,s,t)=>{Promise.resolve().then(t.bind(t,68914))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84792:(e,s,t)=>{Promise.resolve().then(t.bind(t,73560))},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99270:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,181,658,85,32],()=>t(72835));module.exports=r})();