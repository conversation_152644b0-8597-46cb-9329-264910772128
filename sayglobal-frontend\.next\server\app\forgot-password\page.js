(()=>{var e={};e.id=162,e.ids=[162],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8717:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36200)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\forgot-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\forgot-password\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10038:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(60687),a=r(85814),i=r.n(a),n=r(26001),o=r(43210);function l(){let[e,t]=(0,o.useState)(""),[r,a]=(0,o.useState)(!1),[l,d]=(0,o.useState)(""),[c,p]=(0,o.useState)(!1),u=async t=>{if(t.preventDefault(),!e)return void d("L\xfctfen e-posta adresinizi girin");if(!/\S+@\S+\.\S+/.test(e))return void d("Ge\xe7erli bir e-posta adresi giriniz");d(""),p(!0);try{await new Promise(e=>setTimeout(e,1500)),a(!0)}catch(e){d("Bir hata oluştu. L\xfctfen daha sonra tekrar deneyin.")}finally{p(!1)}};return(0,s.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,s.jsx)("div",{className:"max-w-md mx-auto",children:(0,s.jsx)(n.P.div,{className:"bg-white rounded-2xl shadow-lg overflow-hidden",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,s.jsxs)("div",{className:"p-8",children:[r?(0,s.jsxs)(n.P.div,{className:"text-center py-6",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"E-posta G\xf6nderildi!"}),(0,s.jsxs)("p",{className:"text-gray-700 mb-6",children:[(0,s.jsx)("span",{className:"font-medium",children:e})," adresine şifre sıfırlama bağlantısı g\xf6nderdik. L\xfctfen gelen kutunuzu kontrol edin."]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"E-postayı bulamıyor musunuz? Spam klas\xf6r\xfcn\xfcz\xfc kontrol edin veya tekrar deneyin."}),(0,s.jsx)(n.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>a(!1),className:"text-purple-600 font-medium hover:text-purple-800 transition",children:"Farklı bir e-posta dene"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-2 text-gray-800",children:"Şifrenizi mi Unuttunuz?"}),(0,s.jsx)("p",{className:"text-gray-700",children:"Endişelenmeyin, size şifrenizi sıfırlamanız i\xe7in bir bağlantı g\xf6ndereceğiz."})]}),(0,s.jsx)("form",{onSubmit:u,children:(0,s.jsxs)("div",{className:"space-y-5",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"E-posta Adresi"}),(0,s.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>{t(e.target.value),l&&d("")},className:`w-full px-4 py-3 rounded-lg border ${l?"border-red-500":"border-gray-300"} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500`,placeholder:"<EMAIL>"}),l&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l})]}),(0,s.jsx)(n.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:c,className:`w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 ${c?"opacity-70 cursor-not-allowed":""}`,children:c?(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"İşleniyor..."]}):"Şifre Sıfırlama Bağlantısı G\xf6nder"})]})})]}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsx)("p",{className:"text-gray-700",children:(0,s.jsx)(i(),{href:"/login",className:"text-purple-600 font-medium hover:text-purple-800 transition",children:"Giriş sayfasına d\xf6n"})})})]})})})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36200:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\forgot-password\\page.tsx","default")},54145:(e,t,r)=>{Promise.resolve().then(r.bind(r,36200))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90593:(e,t,r)=>{Promise.resolve().then(r.bind(r,10038))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,181,658,85],()=>r(8717));module.exports=s})();