(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2162],{37663:(e,t,a)=>{Promise.resolve().then(a.bind(a,69028))},69028:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var s=a(95155),r=a(6874),i=a.n(r),n=a(76408),l=a(12115);function o(){let[e,t]=(0,l.useState)(""),[a,r]=(0,l.useState)(!1),[o,c]=(0,l.useState)(""),[d,m]=(0,l.useState)(!1),u=async t=>{if(t.preventDefault(),!e)return void c("L\xfctfen e-posta adresinizi girin");if(!/\S+@\S+\.\S+/.test(e))return void c("Ge\xe7erli bir e-posta adresi giriniz");c(""),m(!0);try{await new Promise(e=>setTimeout(e,1500)),r(!0)}catch(e){c("Bir hata oluştu. L\xfctfen daha sonra tekrar deneyin.")}finally{m(!1)}};return(0,s.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,s.jsx)("div",{className:"max-w-md mx-auto",children:(0,s.jsx)(n.P.div,{className:"bg-white rounded-2xl shadow-lg overflow-hidden",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,s.jsxs)("div",{className:"p-8",children:[a?(0,s.jsxs)(n.P.div,{className:"text-center py-6",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"E-posta G\xf6nderildi!"}),(0,s.jsxs)("p",{className:"text-gray-700 mb-6",children:[(0,s.jsx)("span",{className:"font-medium",children:e})," adresine şifre sıfırlama bağlantısı g\xf6nderdik. L\xfctfen gelen kutunuzu kontrol edin."]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"E-postayı bulamıyor musunuz? Spam klas\xf6r\xfcn\xfcz\xfc kontrol edin veya tekrar deneyin."}),(0,s.jsx)(n.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>r(!1),className:"text-purple-600 font-medium hover:text-purple-800 transition",children:"Farklı bir e-posta dene"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-2 text-gray-800",children:"Şifrenizi mi Unuttunuz?"}),(0,s.jsx)("p",{className:"text-gray-700",children:"Endişelenmeyin, size şifrenizi sıfırlamanız i\xe7in bir bağlantı g\xf6ndereceğiz."})]}),(0,s.jsx)("form",{onSubmit:u,children:(0,s.jsxs)("div",{className:"space-y-5",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"E-posta Adresi"}),(0,s.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>{t(e.target.value),o&&c("")},className:"w-full px-4 py-3 rounded-lg border ".concat(o?"border-red-500":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500"),placeholder:"<EMAIL>"}),o&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o})]}),(0,s.jsx)(n.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:d,className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 ".concat(d?"opacity-70 cursor-not-allowed":""),children:d?(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"İşleniyor..."]}):"Şifre Sıfırlama Bağlantısı G\xf6nder"})]})})]}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsx)("p",{className:"text-gray-700",children:(0,s.jsx)(i(),{href:"/login",className:"text-purple-600 font-medium hover:text-purple-800 transition",children:"Giriş sayfasına d\xf6n"})})})]})})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6408,6874,8441,1684,7358],()=>t(37663)),_N_E=e.O()}]);