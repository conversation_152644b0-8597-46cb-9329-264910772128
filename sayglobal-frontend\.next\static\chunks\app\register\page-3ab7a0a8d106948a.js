(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2454],{45106:(e,a,r)=>{"use strict";r.d(a,{$N:()=>A,EI:()=>M,F0:()=>c,Gc:()=>i,HX:()=>P,JZ:()=>x,OG:()=>k,Ph:()=>f,QK:()=>m,QR:()=>R,S:()=>u,VS:()=>d,Zm:()=>S,_f:()=>j,c6:()=>h,fW:()=>w,gA:()=>b,hg:()=>y,ig:()=>C,lA:()=>p,nb:()=>D,qA:()=>v,u6:()=>g,vQ:()=>N});var t=r(65453),o=r(46786);let s={editPersonalInfo:!1,referenceRegistration:!1,addAddress:!1,setDefaultConfirmation:!1,banking:!1,addCard:!1,setDefaultCard:!1,registerSuccess:!1,successNotification:!1,productCategorySelector:!1,productVariantSetup:!1,productVariant:!1,productDeleteConfirmation:!1},l={editPersonalInfoUser:null,referenceRegistrationData:null,addAddressData:null,setDefaultConfirmationData:null,bankingData:null,addCardData:null,setDefaultCardData:null,successNotificationData:null,productCategorySelectorData:null,productVariantSetupData:null,productVariantData:null,productDeleteConfirmationData:null},n=(0,t.v)()((0,o.lt)(e=>({...s,...l,openModal:(a,r)=>{e(e=>({...e,[a]:!0,...r&&{["".concat(a,"User")]:r}}),!1,"modal/open/".concat(a))},closeModal:a=>{e(e=>({...e,[a]:!1,["".concat(a,"User")]:null}),!1,"modal/close/".concat(a))},closeAllModals:()=>{e({...s,...l},!1,"modal/closeAll")},openEditPersonalInfoModal:a=>{e({editPersonalInfo:!0,editPersonalInfoUser:a},!1,"modal/openEditPersonalInfo")},closeEditPersonalInfoModal:()=>{e({editPersonalInfo:!1,editPersonalInfoUser:null},!1,"modal/closeEditPersonalInfo")},openReferenceRegistrationModal:a=>{e({referenceRegistration:!0,referenceRegistrationData:a},!1,"modal/openReferenceRegistration")},closeReferenceRegistrationModal:()=>{e({referenceRegistration:!1,referenceRegistrationData:null},!1,"modal/closeReferenceRegistration")},openAddAddressModal:a=>{e({addAddress:!0,addAddressData:a},!1,"modal/openAddAddress")},closeAddAddressModal:()=>{e({addAddress:!1,addAddressData:null},!1,"modal/closeAddAddress")},openSetDefaultConfirmationModal:a=>{e({setDefaultConfirmation:!0,setDefaultConfirmationData:a},!1,"modal/openSetDefaultConfirmation")},closeSetDefaultConfirmationModal:()=>{e({setDefaultConfirmation:!1,setDefaultConfirmationData:null},!1,"modal/closeSetDefaultConfirmation")},openBankingModal:a=>{e({banking:!0,bankingData:a},!1,"modal/openBanking")},closeBankingModal:()=>{e({banking:!1,bankingData:null},!1,"modal/closeBanking")},openAddCardModal:a=>{e({addCard:!0,addCardData:a},!1,"modal/openAddCard")},closeAddCardModal:()=>{e({addCard:!1,addCardData:null},!1,"modal/closeAddCard")},openSetDefaultCardModal:a=>{e({setDefaultCard:!0,setDefaultCardData:a},!1,"modal/openSetDefaultCard")},closeSetDefaultCardModal:()=>{e({setDefaultCard:!1,setDefaultCardData:null},!1,"modal/closeSetDefaultCard")},openRegisterSuccessModal:()=>{e({registerSuccess:!0},!1,"modal/openRegisterSuccess")},closeRegisterSuccessModal:()=>{e({registerSuccess:!1},!1,"modal/closeRegisterSuccess")},openSuccessNotificationModal:a=>{e({successNotification:!0,successNotificationData:a},!1,"modal/openSuccessNotification")},closeSuccessNotificationModal:()=>{e({successNotification:!1,successNotificationData:null},!1,"modal/closeSuccessNotification")},openProductCategorySelector:a=>{e({productCategorySelector:!0,productCategorySelectorData:a},!1,"modal/openProductCategorySelector")},closeProductCategorySelector:()=>{e({productCategorySelector:!1,productCategorySelectorData:null},!1,"modal/closeProductCategorySelector")},openProductVariantSetup:a=>{e({productVariantSetup:!0,productVariantSetupData:a},!1,"modal/openProductVariantSetup")},closeProductVariantSetup:()=>{e({productVariantSetup:!1,productVariantSetupData:null},!1,"modal/closeProductVariantSetup")},openProductVariant:a=>{e({productVariant:!0,productVariantData:a},!1,"modal/openProductVariant")},closeProductVariant:()=>{e({productVariant:!1,productVariantData:null},!1,"modal/closeProductVariant")},openProductDeleteConfirmation:a=>{e({productDeleteConfirmation:!0,productDeleteConfirmationData:a},!1,"modal/openProductDeleteConfirmation")},closeProductDeleteConfirmation:()=>{e({productDeleteConfirmation:!1,productDeleteConfirmationData:null},!1,"modal/closeProductDeleteConfirmation")}}),{name:"modal-store",enabled:!1})),d=()=>n(e=>e.editPersonalInfo),i=()=>n(e=>e.editPersonalInfoUser),c=()=>n(e=>e.referenceRegistration),u=()=>n(e=>e.referenceRegistrationData),m=()=>n(e=>e.addAddress),p=()=>n(e=>e.addAddressData),f=()=>n(e=>e.setDefaultConfirmation),g=()=>n(e=>e.setDefaultConfirmationData),x=()=>n(e=>e.banking),b=()=>n(e=>e.bankingData),h=()=>n(e=>e.addCard),y=()=>n(e=>e.addCardData),C=()=>n(e=>e.setDefaultCard),N=()=>n(e=>e.setDefaultCardData),D=()=>n(e=>e.registerSuccess),S=()=>n(e=>e.successNotification),k=()=>n(e=>e.successNotificationData),w=()=>n(e=>e.productCategorySelector),j=()=>n(e=>e.productCategorySelectorData),P=()=>n(e=>e.productVariant),v=()=>n(e=>e.productVariantData),M=()=>n(e=>e.productDeleteConfirmation),A=()=>n(e=>e.productDeleteConfirmationData),R=()=>{let e=n(e=>e.openModal),a=n(e=>e.closeModal),r=n(e=>e.closeAllModals),t=n(e=>e.openEditPersonalInfoModal),o=n(e=>e.closeEditPersonalInfoModal),s=n(e=>e.openReferenceRegistrationModal),l=n(e=>e.closeReferenceRegistrationModal),d=n(e=>e.openAddAddressModal),i=n(e=>e.closeAddAddressModal),c=n(e=>e.openSetDefaultConfirmationModal),u=n(e=>e.closeSetDefaultConfirmationModal),m=n(e=>e.openBankingModal),p=n(e=>e.closeBankingModal),f=n(e=>e.openAddCardModal),g=n(e=>e.closeAddCardModal),x=n(e=>e.openSetDefaultCardModal),b=n(e=>e.closeSetDefaultCardModal),h=n(e=>e.openRegisterSuccessModal),y=n(e=>e.closeRegisterSuccessModal),C=n(e=>e.openSuccessNotificationModal),N=n(e=>e.closeSuccessNotificationModal),D=n(e=>e.openProductCategorySelector),S=n(e=>e.closeProductCategorySelector),k=n(e=>e.openProductVariantSetup),w=n(e=>e.closeProductVariantSetup),j=n(e=>e.openProductVariant),P=n(e=>e.closeProductVariant);return{openModal:e,closeModal:a,closeAllModals:r,openEditPersonalInfoModal:t,closeEditPersonalInfoModal:o,openReferenceRegistrationModal:s,closeReferenceRegistrationModal:l,openAddAddressModal:d,closeAddAddressModal:i,openSetDefaultConfirmationModal:c,closeSetDefaultConfirmationModal:u,openBankingModal:m,closeBankingModal:p,openAddCardModal:f,closeAddCardModal:g,openSetDefaultCardModal:x,closeSetDefaultCardModal:b,openRegisterSuccessModal:h,closeRegisterSuccessModal:y,openSuccessNotificationModal:C,closeSuccessNotificationModal:N,openProductCategorySelector:D,closeProductCategorySelector:S,openProductVariantSetup:k,closeProductVariantSetup:w,openProductVariant:j,closeProductVariant:P,openProductDeleteConfirmation:n(e=>e.openProductDeleteConfirmation),closeProductDeleteConfirmation:n(e=>e.closeProductDeleteConfirmation)}}},71783:(e,a,r)=>{Promise.resolve().then(r.bind(r,86616))},86616:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>u});var t=r(95155),o=r(6874),s=r.n(o),l=r(76408),n=r(12115),d=r(87220),i=r(35695),c=r(45106);function u(){let[e,a]=(0,n.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",phoneNumber:"",referansCode:"",role:"customer",terms:!1}),[r,o]=(0,n.useState)({}),[u,m]=(0,n.useState)(""),{register:p,isLoading:f}=(0,d.A)();(0,i.useRouter)();let{openRegisterSuccessModal:g}=(0,c.QR)(),x=e=>{let{name:t,value:s,type:l}=e.target,n=e.target.checked;a(e=>({...e,[t]:"checkbox"===l?n:s})),r[t]&&o(e=>({...e,[t]:""})),m("")},b=()=>{let a={};if(e.firstName||(a.firstName="İsim gereklidir"),e.lastName||(a.lastName="Soyisim gereklidir"),e.email?/\S+@\S+\.\S+/.test(e.email)||(a.email="Ge\xe7erli bir e-posta adresi giriniz"):a.email="E-posta gereklidir",e.password){let r=[];e.password.length<6&&r.push("en az 6 karakter"),/[A-Z]/.test(e.password)||r.push("en az 1 b\xfcy\xfck harf"),/[a-z]/.test(e.password)||r.push("en az 1 k\xfc\xe7\xfck harf"),/[0-9]/.test(e.password)||r.push("en az 1 rakam"),/[^a-zA-Z0-9]/.test(e.password)||r.push("en az 1 \xf6zel karakter (!@#$%^&*)"),r.length>0&&(a.password="Şifre ".concat(r.join(", ")," i\xe7ermelidir"))}else a.password="Şifre gereklidir";return e.password!==e.confirmPassword&&(a.confirmPassword="Şifreler eşleşmiyor"),e.terms||(a.terms="Şartları ve koşulları kabul etmelisiniz"),o(a),0===Object.keys(a).length},h=async a=>{a.preventDefault(),m(""),b()&&(await p({firstName:e.firstName,lastName:e.lastName,email:e.email,password:e.password,confirmPassword:e.confirmPassword,phoneNumber:e.phoneNumber||void 0,referansCode:e.referansCode||void 0,role:e.role})?g():m("Kayıt işlemi başarısız! L\xfctfen bilgilerinizi kontrol edin ve tekrar deneyin."))};return(0,t.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,t.jsx)("div",{className:"max-w-lg mx-auto",children:(0,t.jsx)(l.P.div,{className:"bg-white rounded-2xl shadow-lg overflow-hidden",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,t.jsxs)("div",{className:"p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-2 text-gray-800",children:"Hesap Oluştur"}),(0,t.jsx)("p",{className:"text-gray-700",children:"Say Global ailesine katılarak sağlıklı yaşama adım atın"})]}),u&&(0,t.jsx)(l.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg",children:u}),(0,t.jsxs)("form",{onSubmit:h,children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-5 mb-5",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:"İsim"}),(0,t.jsx)("input",{id:"firstName",name:"firstName",type:"text",value:e.firstName,onChange:x,disabled:f,className:"w-full px-4 py-3 rounded-lg border ".concat(r.firstName?"border-red-500":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100"),placeholder:"İsminizi girin"}),r.firstName&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:r.firstName})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Soyisim"}),(0,t.jsx)("input",{id:"lastName",name:"lastName",type:"text",value:e.lastName,onChange:x,disabled:f,className:"w-full px-4 py-3 rounded-lg border ".concat(r.lastName?"border-red-500":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100"),placeholder:"Soyisminizi girin"}),r.lastName&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:r.lastName})]})]}),(0,t.jsxs)("div",{className:"space-y-5",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"E-posta Adresi"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",value:e.email,onChange:x,disabled:f,className:"w-full px-4 py-3 rounded-lg border ".concat(r.email?"border-red-500":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100"),placeholder:"<EMAIL>"}),r.email&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:r.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"referansCode",className:"block text-sm font-medium text-gray-700 mb-1",children:"Referans Kodu (Opsiyonel)"}),(0,t.jsx)("input",{id:"referansCode",name:"referansCode",type:"text",value:e.referansCode,onChange:x,disabled:f,className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100",placeholder:"Referans kodunu girin"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:"Telefon"}),(0,t.jsx)("input",{id:"phone",name:"phoneNumber",type:"tel",value:e.phoneNumber,onChange:x,disabled:f,className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100",placeholder:"+90 ************"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700 mb-1",children:"Hesap T\xfcr\xfc"}),(0,t.jsx)("select",{id:"role",name:"role",value:e.role,onChange:x,disabled:f,className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 disabled:bg-gray-100",children:(0,t.jsx)("option",{value:"customer",children:"M\xfcşteri"})}),(0,t.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Distrib\xfct\xf6r olmak i\xe7in \xfcyelik seviyenizi y\xfckseltmeniz, satıcı olmak i\xe7in başvuru yapmanız gerekir."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Şifre"}),(0,t.jsx)("input",{id:"password",name:"password",type:"password",value:e.password,onChange:x,disabled:f,autoComplete:"new-password",className:"w-full px-4 py-3 rounded-lg border ".concat(r.password?"border-red-500":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100"),placeholder:"En az 6 karakter"}),r.password&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:r.password})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Şifre Tekrar"}),(0,t.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",value:e.confirmPassword,onChange:x,disabled:f,autoComplete:"new-password",className:"w-full px-4 py-3 rounded-lg border ".concat(r.confirmPassword?"border-red-500":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100"),placeholder:"Şifrenizi tekrar girin"}),r.confirmPassword&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:r.confirmPassword})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex items-center h-5",children:(0,t.jsx)("input",{id:"terms",name:"terms",type:"checkbox",checked:e.terms,onChange:x,disabled:f,className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded ".concat(r.terms?"border-red-500":""," disabled:bg-gray-100")})}),(0,t.jsxs)("div",{className:"ml-3 text-sm",children:[(0,t.jsx)("label",{htmlFor:"terms",className:"font-medium text-gray-700",children:(0,t.jsxs)("span",{className:"text-gray-700",children:[(0,t.jsx)(s(),{href:"/terms",className:"text-purple-600 hover:text-purple-800",children:"Şartlar ve koşulları"})," ","okudum ve kabul ediyorum"]})}),r.terms&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:r.terms})]})]}),(0,t.jsx)(l.P.button,{whileHover:{scale:f?1:1.02},whileTap:{scale:f?1:.98},type:"submit",disabled:f,className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center",children:f?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Kayıt Oluşturuluyor..."]}):"Kayıt Ol"})]})]}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["Zaten bir hesabınız var mı?"," ",(0,t.jsx)(s(),{href:"/login",className:"text-purple-600 font-medium hover:text-purple-800 transition",children:"Giriş Yap"})]})})]})})})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[6408,6874,7323,6681,8441,1684,7358],()=>a(71783)),_N_E=e.O()}]);