(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7904],{634:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>I});var r=t(95155),s=t(12115),i=t(87220),l=t(35695),n=t(76408),d=t(35169),c=t(75525),o=t(81284),m=t(43332),u=t(62525),x=t(71539),g=t(27213),h=t(4229),p=t(6874),b=t.n(p),y=t(80722),f=t(22934),N=t(81087),v=t(87923),j=t(45106),k=t(26715),D=t(5041);let I=()=>{let{user:e,isLoading:a}=(0,i.A)(),t=(0,l.useRouter)(),p=(0,k.jE)(),{formData:I,selectedNames:w,variants:C,error:V,availableFeatures:F}=(0,v.t3)(),{handleInputChange:S,setCategorySelection:A,clearAllSelections:E,deleteVariant:M,setError:P,reset:z,setVariants:K,saveVariant:R}=(0,v.t3)(e=>e),{openProductCategorySelector:O,closeProductCategorySelector:_,openProductVariant:q,closeProductVariant:T}=(0,j.QR)(),U=(0,j.fW)(),Y=(0,j._f)(),H=(0,j.HX)(),L=(0,j.qA)(),Q=e=>{let a={};return e.forEach(e=>{e.featureDetails.forEach(e=>{a[e.featureDefinitionId]||(a[e.featureDefinitionId]=[]),a[e.featureDefinitionId].includes(e.featureValueId)||a[e.featureDefinitionId].push(e.featureValueId)})}),a},{mutate:B,isPending:G}=(0,D.n)({mutationFn:e=>y.jU.createDealershipProduct(e),onSuccess:()=>{console.log("✅ \xdcr\xfcn başarıyla eklendi. Cache temizleniyor..."),p.invalidateQueries({queryKey:["myProducts"]}),p.invalidateQueries({queryKey:["products"]}),t.push("/pending-products"),z()},onError:e=>{P(e.message||"\xdcr\xfcn eklenirken bir hata oluştu")}});(0,s.useEffect)(()=>()=>{z()},[z]),(0,s.useEffect)(()=>{e&&"dealership"!==e.role&&"admin"!==e.role&&t.push("/"),a||e||t.push("/login")},[e,a,t]);let W=e=>{q({editingVariant:e,availableFeatures:F,existingVariants:C})},X=async e=>{e.preventDefault(),P(null);try{if(!I.name.trim())throw Error("\xdcr\xfcn adı gereklidir");if(!I.description.trim())throw Error("\xdcr\xfcn a\xe7ıklaması gereklidir");if(I.brandId<=0)throw Error("Marka ve kategori se\xe7imi gereklidir");if(0===C.length)throw Error("En az bir varyant oluşturmalısınız.");for(let e of C){if(e.pricing.price<=0)throw Error("".concat(e.name," varyantı i\xe7in fiyat 0'dan b\xfcy\xfck olmalıdır"));if(e.pricing.stock<0)throw Error("".concat(e.name," varyantı i\xe7in stok miktarı negatif olamaz"))}let e=new FormData;e.append("Product.Name",I.name),e.append("Product.Description",I.description),e.append("Product.BrandId",I.brandId.toString()),e.append("Product.SubCategoryId",I.subCategoryId.toString()),e.append("Product.Stock",I.stock.toString()),C.forEach((a,t)=>{e.append("Variant[".concat(t,"].stock"),a.pricing.stock.toString()),e.append("Variant[".concat(t,"].price"),a.pricing.price.toString()),Object.values(a.selectedFeatures).flat().forEach((a,r)=>{e.append("Variant[".concat(t,"].featureValueIds[").concat(r,"]"),a.toString())}),a.images.forEach((a,r)=>{a.file&&(e.append("Variant[".concat(t,"].images[").concat(r,"].file"),a.file),e.append("Variant[".concat(t,"].images[").concat(r,"].isMain"),a.isMain.toString()),e.append("Variant[".concat(t,"].images[").concat(r,"].sortOrder"),a.sortOrder.toString()))})}),B(e)}catch(e){P(e.message||"\xdcr\xfcn eklenirken bir hata oluştu")}};return a?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):e&&("dealership"===e.role||"admin"===e.role)?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:[(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(b(),{href:"/pending-products",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 mr-2"}),"\xdcr\xfcn Listesi"]}),(0,r.jsx)("span",{className:"text-gray-300",children:"/"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Yeni \xdcr\xfcn Ekle"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 bg-blue-100 px-4 py-2 rounded-lg",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-blue-600"}),(0,r.jsx)("span",{className:"text-blue-800 font-medium",children:"Satıcı Erişimi"})]})]})}),V&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsx)("p",{className:"text-red-800",children:V}),(0,r.jsx)("button",{onClick:()=>P(null),className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Kapat"})]}),(0,r.jsxs)("form",{onSubmit:X,className:"space-y-8",children:[(0,r.jsxs)(n.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(o.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Temel Bilgiler"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xdcr\xfcn Adı *"}),(0,r.jsx)("input",{type:"text",value:I.name,onChange:e=>S("name",e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black",placeholder:"\xdcr\xfcn adını girin...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"A\xe7ıklama *"}),(0,r.jsx)("textarea",{value:I.description,onChange:e=>S("description",e.target.value),rows:4,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black",placeholder:"\xdcr\xfcn a\xe7ıklamasını girin...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Marka, Kategori ve Detaylar"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>O({initialData:I?{brandId:I.brandId,categoryId:I.categoryId,subCategoryId:I.subCategoryId,selectedFeatures:Q(C)}:{brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{}}}),className:"flex-grow px-4 py-3 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 transition-colors flex items-center justify-center text-gray-600 hover:text-blue-600",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2"}),I.brandId>0?"Kategori Se\xe7imini D\xfczenle":"Marka, Kategori ve Detaylar Se\xe7in"]}),I.brandId>0&&(0,r.jsx)("button",{type:"button",onClick:E,title:"T\xfcm se\xe7imleri temizle",className:"flex-shrink-0 p-3 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors",children:(0,r.jsx)(u.A,{className:"h-5 w-5"})})]}),I.brandId>0&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-gray-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"Se\xe7ili:"})," Marka: ",w.brandName,I.categoryId>0&&", Kategori: ".concat(w.categoryName),I.subCategoryId>0&&", Alt Kategori: ".concat(w.subCategoryName)]})})]})]})]}),(0,r.jsxs)(n.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Varyant Y\xf6netimi"})]})}),I.subCategoryId<=0&&(0,r.jsx)("div",{className:"p-4 text-center bg-gray-50 rounded-lg",children:(0,r.jsx)("p",{className:"text-gray-500 font-medium",children:"Tekli \xfcr\xfcn eklemek veya varyant oluşturmak i\xe7in l\xfctfen \xf6nce marka, kategori ve alt kategori se\xe7in."})}),I.subCategoryId>0&&0===C.length&&(0,r.jsxs)("div",{className:"p-8 text-center bg-gray-50 rounded-lg",children:[(0,r.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-3"}),(0,r.jsx)("p",{className:"text-gray-500 font-medium",children:"Hen\xfcz varyant oluşturulmadı."}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1 mb-4",children:'Yukarıdaki "Marka, Kategori ve Detaylar Se\xe7in" kısmı ile d\xfczenleyebilirsiniz.'})]}),C.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 text-blue-600 mr-2 mt-0.5"}),(0,r.jsxs)("p",{className:"text-sm text-blue-800",children:["Oluşturulan her bir varyant kombinasyonu i\xe7in Fiyat ve Stok bilgilerini girin. Tek bir \xfcr\xfcn ekliyorsanız bile, bu \xfcr\xfcn bir varyant olarak kabul edilir.",(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Not:"})," PV, CV, SP ve indirim oranları admin tarafından belirlenecektir."]})]})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Varyant"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fiyat (₺)"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stok"}),(0,r.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,r.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:C.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.featureDetails.map(e=>e.featureValue).join(", ")})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.price})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.stock})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,r.jsx)("button",{type:"button",onClick:()=>W(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:"Detay"}),(0,r.jsx)("button",{type:"button",onClick:()=>M("number"==typeof e.id?e.id:0),className:"text-gray-500 hover:text-gray-800",children:"Sil"})]})]},e.id))})]})})]})]}),(0,r.jsxs)(n.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(g.A,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"\xdcr\xfcn Fotoğrafları"})]}),(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(o.A,{className:"h-5 w-5 text-blue-400"})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-blue-700",children:'Her bir varyantın fotoğraflarını, varyant tablosundaki "Detay" butonuna tıklayarak a\xe7ılan pencereden y\xf6netebilirsiniz.'})})]})}),C.some(e=>e.images.length>0)&&(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"T\xfcm Varyant G\xf6rselleri (\xd6nizleme)"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:C.flatMap(e=>e.images.map(a=>({...a,variantName:e.name}))).map((e,a)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg overflow-hidden",children:(0,r.jsx)("img",{src:e.url,alt:"".concat(e.variantName," g\xf6rseli"),className:"w-full h-full object-cover"})}),(0,r.jsx)("div",{className:"absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded",children:e.variantName}),e.isMain&&(0,r.jsx)("div",{className:"absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded",children:"Ana"})]},a))})]})]}),(0,r.jsxs)(n.P.div,{className:"flex justify-end space-x-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,r.jsx)(b(),{href:"/pending-products",className:"px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"İptal"}),(0,r.jsx)("button",{type:"submit",disabled:G,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:G?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Kaydediliyor..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"\xdcr\xfcn\xfc Kaydet"]})})]})]})]}),(0,r.jsx)(f.A,{isOpen:U,onClose:_,onSelect:e=>{A({brandId:e.brandId,categoryId:e.categoryId,subCategoryId:e.subCategoryId,brandName:e.brandName,categoryName:e.categoryName,subCategoryName:e.subCategoryName,selectedFeatures:e.selectedFeatures,selectedFeatureDetails:e.selectedFeatureDetails}),K(e.generatedVariants),_(),P(null)},initialData:(null==Y?void 0:Y.initialData)&&"object"==typeof Y.initialData&&"selectedFeatures"in Y.initialData?Y.initialData:{brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{}},colorScheme:"blue"}),(0,r.jsx)(N.A,{isOpen:H,onClose:T,onSave:e=>{R(e,"number"==typeof e.id?e.id:void 0),T()},editingVariant:null==L?void 0:L.editingVariant,availableFeatures:F,existingVariants:C,hidePvCvSp:!0,colorScheme:"blue"})]}):null}},48217:(e,a,t)=>{Promise.resolve().then(t.bind(t,634))},87923:(e,a,t)=>{"use strict";t.d(a,{t3:()=>d});var r=t(65453),s=t(46786);let i={formData:{name:"",description:"",brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{},price:0,stock:0,extraDiscount:0,ratios:{pvRatio:0,cvRatio:0,spRatio:0},points:{pv:0,cv:0,sp:0},hasVariants:!1,variants:[],images:[],isActive:!0},selectedNames:{brandName:"",categoryName:"",subCategoryName:""},selectedFeatureDetails:[],variants:[],availableFeatures:[],error:null},l=(e,a)=>({pv:Math.round(e*(a.pvRatio/100)),cv:Math.round(e*(a.cvRatio/100)),sp:Math.round(e*(a.spRatio/100))}),n=e=>{let a={},t=[];return e.forEach(e=>{e.featureDetails.forEach(e=>{a[e.featureDefinitionId]||(a[e.featureDefinitionId]=[]),a[e.featureDefinitionId].includes(e.featureValueId)||a[e.featureDefinitionId].push(e.featureValueId),t.some(a=>a.featureName===e.featureName&&a.featureValue===e.featureValue)||t.push({featureName:e.featureName,featureValue:e.featureValue})})}),{newSelectedFeatures:a,newSelectedFeatureDetails:t}},d=(0,r.v)()((0,s.lt)((e,a)=>({...i,setFormData:a=>e(e=>({formData:{...e.formData,...a}})),handleInputChange:(t,r)=>{let s={...a().formData,[t]:r};if(("price"===t||"ratios"===t)&&!s.hasVariants){let e=l(s.price,s.ratios);s={...s,points:e}}e({formData:s})},handleRatioChange:(t,r)=>{let s=a().formData,i={...s.ratios,[t]:r},n={...s,ratios:i};if(!n.hasVariants){let e=l(n.price,i);n={...n,points:e}}e({formData:n})},setCategorySelection:t=>{e({formData:{...a().formData,brandId:t.brandId,categoryId:t.categoryId,subCategoryId:t.subCategoryId,selectedFeatures:t.selectedFeatures,hasVariants:!1},selectedNames:{brandName:t.brandName,categoryName:t.categoryName,subCategoryName:t.subCategoryName},selectedFeatureDetails:t.selectedFeatureDetails,error:null})},clearAllSelections:()=>{e({formData:{...a().formData,brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{},hasVariants:!1},selectedNames:{brandName:"",categoryName:"",subCategoryName:""},selectedFeatureDetails:[],availableFeatures:[],variants:[],error:null})},setAvailableFeatures:a=>e({availableFeatures:a}),setVariants:a=>e({variants:a}),saveVariant:(t,r)=>{let s,{variants:i}=a(),{newSelectedFeatures:l,newSelectedFeatureDetails:d}=n(s=r?i.map(e=>e.id===r?t:e):[...i,{...t,id:Date.now()}]);e(e=>({variants:s,formData:{...e.formData,selectedFeatures:l,hasVariants:s.length>1},selectedFeatureDetails:d}))},deleteVariant:t=>{let{variants:r}=a(),s=r.filter(e=>e.id!==t),{newSelectedFeatures:i,newSelectedFeatureDetails:l}=n(s);e(e=>({variants:s,formData:{...e.formData,selectedFeatures:i,hasVariants:s.length>1},selectedFeatureDetails:l}))},generateVariants:a=>{let{newSelectedFeatures:t,newSelectedFeatureDetails:r}=n(a);e(e=>({formData:{...e.formData,hasVariants:!0,selectedFeatures:t},selectedFeatureDetails:r,variants:a,error:null}))},handleImageUpload:t=>{let{formData:r}=a(),s=Array.from(t).map((e,a)=>({url:URL.createObjectURL(e),isMain:0===r.images.length&&0===a,sortOrder:r.images.length+a,file:e}));e({formData:{...r,images:[...r.images,...s]}})},removeImage:t=>{let{formData:r}=a(),s=r.images.filter((e,a)=>a!==t);r.images[t].isMain&&s.length>0&&(s[0].isMain=!0),e({formData:{...r,images:s}})},setMainImage:t=>{let{formData:r}=a(),s=r.images.map((e,a)=>({...e,isMain:a===t}));e({formData:{...r,images:s}})},setError:a=>e({error:a}),reset:()=>e({...i})}),{name:"add-product-store",enabled:!1}))}},e=>{var a=a=>e(e.s=a);e.O(0,[6408,6874,7323,1531,6681,3651,8441,1684,7358],()=>a(48217)),_N_E=e.O()}]);