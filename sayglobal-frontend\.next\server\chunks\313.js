"use strict";exports.id=313,exports.ids=[313],exports.modules={11860:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},13861:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14952:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19080:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},21254:(e,s,t)=>{t.d(s,{A:()=>v});var a=t(60687),r=t(43210),l=t(26001),i=t(16189),n=t(8693),c=t(93613),d=t(5336),o=t(35071),x=t(11860),m=t(41862),h=t(47033),y=t(14952),g=t(19080),u=t(40228),p=t(58869),j=t(55245),f=t(40237),N=t(78259);let v=({productId:e,isOpen:s,onClose:t,showApprovalStatus:v=!1,refreshOnClose:b=!1})=>{let w=(0,i.useRouter)(),k=(0,n.jE)(),A=(0,f.Q6)(),z=(0,f.e$)(),S=(0,f.Z9)(e=>e.setCurrentImage),M=(0,f.Z9)(e=>e.setSelectedVariant),P=(0,f.Z9)(e=>e.clearProductCache),{data:C,isLoading:D,error:B}=(0,j.LJ)(e),{data:R,isLoading:q}=(0,j.qr)(e,v&&s);(0,r.useEffect)(()=>{C&&console.log("\uD83D\uDD0D Modal ProductDetail Data:",{id:C.id,name:C.name,variantsCount:C.variants?.length||0,variants:C.variants,hasVariants:C.variants&&C.variants.length>0,firstVariant:C.variants?.[0],firstVariantPrice:C.variants?.[0]?.price,firstVariantPriceType:typeof C.variants?.[0]?.price})},[C]),(0,r.useEffect)(()=>(s?(document.body.style.overflow="hidden",v&&e&&(console.log("\uD83D\uDD04 Onay y\xf6netimi sayfasından modal a\xe7ıldı, cache refresh ediliyor..."),k.invalidateQueries({queryKey:["productDetail",e]}),k.invalidateQueries({queryKey:["productMessage",e]}))):document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[s,v,e,k]),(0,r.useEffect)(()=>{S(0)},[A,S]),(0,r.useEffect)(()=>{s&&e&&(console.log("\uD83D\uDD04 Modal opened, forcing fresh data fetch for product:",e),P(e),k.invalidateQueries({queryKey:["productDetail",e]}))},[s,e,P,k]);let $=()=>{b&&e&&(console.log("\uD83D\uDD04 Modal kapatılıyor, cache refresh ediliyor..."),k.invalidateQueries({queryKey:["productDetail",e]}),k.invalidateQueries({queryKey:["productMessage",e]}),k.invalidateQueries({queryKey:["adminProducts"]})),t()};if(!s)return null;let L=e=>new Intl.NumberFormat("tr-TR",{style:"currency",currency:"TRY"}).format(e),U=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),V=C?.variants&&C.variants.length>0,E=V?Math.min(A,C.variants.length-1):0,F=V?C.variants[E]:null,O=F?.images?.map(e=>e.url)||[],K=F?(e=>0===e?{text:"Stokta Yok",color:"bg-red-100 text-red-800",icon:(0,a.jsx)(o.A,{className:"h-4 w-4"})}:e<20?{text:"Az Stok",color:"bg-yellow-100 text-yellow-800",icon:(0,a.jsx)(c.A,{className:"h-4 w-4"})}:{text:"Stokta",color:"bg-green-100 text-green-800",icon:(0,a.jsx)(d.A,{className:"h-4 w-4"})})(F.stock):null;return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-lg flex items-center justify-center p-4 z-50",onClick:$,children:(0,a.jsxs)(l.P.div,{className:"bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.2},onClick:e=>e.stopPropagation(),children:[(0,a.jsx)("div",{className:"sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-xl",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-3",children:(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"\xdcr\xfcn Detayları (Admin)"})}),(0,a.jsx)("button",{onClick:$,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(x.A,{className:"w-6 h-6"})})]})}),(0,a.jsxs)("div",{className:"p-6",children:[D&&(0,a.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 animate-spin text-blue-600"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:"\xdcr\xfcn detayları y\xfckleniyor..."})]}),B&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(c.A,{className:"h-6 w-6 text-red-600 mt-1 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-red-800 mb-2",children:"\xdcr\xfcn Detayları Y\xfcklenemedi"}),(0,a.jsx)("p",{className:"text-red-700 mb-4",children:"\xdcr\xfcn detayları y\xfcklenirken bir hata oluştu. L\xfctfen tekrar deneyin."}),(0,a.jsxs)("div",{className:"text-sm text-red-600 bg-red-100 rounded p-2 font-mono",children:["Hata: ",B?.message||"Bilinmeyen hata"]}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Sayfayı Yenile"})]})]})}),C&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[V&&C.variants.length>1&&(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:C.variants.map((e,s)=>(0,a.jsxs)("button",{onClick:()=>M(s),className:`py-2 px-1 border-b-2 font-medium text-sm ${A===s?"border-red-500 text-red-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Varyant ",s+1,e.features.length>0&&(0,a.jsxs)("span",{className:"ml-1 text-xs",children:["(",e.features.map(e=>e.featureValue).join(", "),")"]})]},e.id))})}),(0,a.jsx)("div",{className:"relative aspect-square bg-gray-100 rounded-lg overflow-hidden",children:O.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("img",{src:O[z],alt:C.name,className:"w-full h-full object-cover"}),O.length>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>{if(!F||!F.images?.length)return;let e=F.images.length;S((z-1+e)%e)},className:"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>{if(F&&F.images?.length)S((z+1)%F.images.length)},className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:[z+1," / ",O.length]})]})]}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)(g.A,{className:"h-16 w-16 text-gray-400"})})}),O.length>1&&(0,a.jsx)("div",{className:"grid grid-cols-4 gap-2",children:O.map((e,s)=>(0,a.jsx)("button",{onClick:()=>S(s),className:`aspect-square rounded-lg overflow-hidden border-2 ${z===s?"border-red-500":"border-gray-200 hover:border-gray-300"}`,children:(0,a.jsx)("img",{src:e,alt:`${C.name} ${s+1}`,className:"w-full h-full object-cover"})},s))})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${C.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:C.isActive?"Aktif":"Pasif"}),v&&void 0!==C.status&&(0,a.jsxs)("span",{className:`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full border ${(e=>{switch(e){case N.Sz.Pending:return"bg-yellow-100 text-yellow-800 border-yellow-200";case N.Sz.Accepted:return"bg-green-100 text-green-800 border-green-200";case N.Sz.Rejected:return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(C.status)}`,children:[(e=>{switch(e){case N.Sz.Pending:return(0,a.jsx)(c.A,{className:"h-4 w-4"});case N.Sz.Accepted:return(0,a.jsx)(d.A,{className:"h-4 w-4"});case N.Sz.Rejected:return(0,a.jsx)(o.A,{className:"h-4 w-4"});default:return(0,a.jsx)(c.A,{className:"h-4 w-4"})}})(C.status),(0,a.jsx)("span",{className:"ml-1",children:(e=>{switch(e){case N.Sz.Pending:return"Onay Bekliyor";case N.Sz.Accepted:return"Onaylandı";case N.Sz.Rejected:return"Reddedildi";default:return"Bilinmiyor"}})(C.status)})]})]}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:C.categoryName})]}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:C.name}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:C.brandName})]}),F?(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Varyant Bilgileri"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Fiyat"}),(0,a.jsx)("p",{className:"text-xl font-bold text-gray-900",children:(()=>{let e=F.price;return(console.log("\uD83D\uDCB0 Fiyat Debug:",{price:F.price,priceType:typeof F.price,isNull:null===F.price,isUndefined:void 0===F.price,isNaN:isNaN(F.price),variant:F}),null==e||void 0===e)?"Fiyat Belirtilmemiş":isNaN(e)?"Ge\xe7ersiz Fiyat":L(e)})()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Stok"}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:K&&(0,a.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${K.color}`,children:[K.icon,(0,a.jsxs)("span",{className:"ml-1",children:[F.stock," - ",K.text]})]})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"PV"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:F.pv})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"CV"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:F.cv})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"SP"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:F.sp})]})]}),F.extraDiscount>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Ekstra İndirim"}),(0,a.jsxs)("p",{className:"text-lg font-semibold text-green-600",children:["%",F.extraDiscount]})]}),F.features.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"\xd6zellikler"}),(0,a.jsx)("div",{className:"space-y-2",children:F.features.map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[e.featureName,":"]}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.featureValue})]},s))})]})]}):(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-5 w-5 text-yellow-600 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-yellow-800",children:"Varyant Bilgileri Eksik"}),(0,a.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"Bu \xfcr\xfcn i\xe7in varyant bilgileri bulunamadı. \xdcr\xfcn\xfc d\xfczenleyerek varyant ekleyebilirsiniz."})]})]})}),C.description&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"A\xe7ıklama"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:C.description})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-2",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 flex items-center",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 mr-2 text-gray-600"}),"Tarihler"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Oluşturulma:"}),(0,a.jsx)("span",{className:"text-gray-900",children:U(C.createdAt)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Son G\xfcncelleme:"}),(0,a.jsx)("span",{className:"text-gray-900",children:U(C.updatedAt)})]})]})]}),C.createdByUserId&&(0,a.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg border",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 mr-2 text-gray-600"}),"Oluşturan"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Kullanıcı Adı:"}),(0,a.jsx)("p",{className:"text-gray-900 font-medium",children:C.createdByUserName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Rol:"}),(0,a.jsx)("p",{className:"text-gray-900 font-medium",children:C.createdByUserRole?.toLowerCase()==="admin"?"Admin":C.createdByUserRole?.toLowerCase()==="dealership"?"Satıcı":C.createdByUserRole?.toLowerCase()==="customer"?"M\xfcşteri":C.createdByUserRole})]})]})]}),v&&(0,a.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg border",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2 text-gray-600"}),"Admin \xdcr\xfcn Notu"]}),q?(0,a.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 animate-spin text-gray-400 mr-2"}),(0,a.jsx)("span",{className:"text-gray-500",children:"Not y\xfckleniyor..."})]}):R?(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"mb-3",children:R.message?(0,a.jsx)("p",{className:"text-gray-800 font-medium",children:R.message}):(0,a.jsx)("p",{className:"text-gray-500 italic",children:"Admin notu girilmemiş"})}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Değiştiren:"})," ",R.approvedByUser.fullName," (",R.approvedByUser.role,")"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Tarih:"})," ",new Date(R.changedAtUtc).toLocaleString("tr-TR")]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Durum:"}),(0,a.jsx)("span",{className:`ml-1 ${1===R.newStatus?"text-green-600":2===R.newStatus?"text-red-600":"text-yellow-600"}`,children:1===R.newStatus?"Onaylandı":2===R.newStatus?"Reddedildi":"Beklemede"})]})]})]}),(0,a.jsx)("div",{className:"ml-4",children:1===R.newStatus?(0,a.jsx)(d.A,{className:"h-5 w-5 text-green-500"}):2===R.newStatus?(0,a.jsx)(o.A,{className:"h-5 w-5 text-red-500"}):(0,a.jsx)(c.A,{className:"h-5 w-5 text-yellow-500"})})]})}):(0,a.jsx)("div",{className:"text-center py-4",children:C?.status===N.Sz.Pending?(0,a.jsxs)("div",{className:"flex items-center justify-center text-yellow-600",children:[(0,a.jsx)(c.A,{className:"h-5 w-5 mr-2"}),(0,a.jsx)("span",{children:"Onay bekleniyor"})]}):(0,a.jsxs)("div",{className:"flex items-center justify-center text-gray-500",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2"}),(0,a.jsx)("span",{children:"Admin notu girilmemiş"})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsx)("button",{onClick:$,className:"flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors",children:"Kapat"}),(0,a.jsx)("button",{className:"flex-1 px-4 py-2 text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors",onClick:()=>{$();let e=v?"pending-products":"products";w.push(`/admin/products/edit/${C.id}?from=${e}`)},children:"D\xfczenle"})]})]})]})]})]})})}},28559:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35071:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40228:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41862:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},47033:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},58869:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62688:(e,s,t)=>{t.d(s,{A:()=>x});var a=t(43210);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=(...e)=>e.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim(),c=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:t=2,absoluteStrokeWidth:r,className:l="",children:i,iconNode:o,...x},m)=>(0,a.createElement)("svg",{ref:m,...d,width:s,height:s,stroke:e,strokeWidth:r?24*Number(t)/Number(s):t,className:n("lucide",l),...!i&&!c(x)&&{"aria-hidden":"true"},...x},[...o.map(([e,s])=>(0,a.createElement)(e,s)),...Array.isArray(i)?i:[i]])),x=(e,s)=>{let t=(0,a.forwardRef)(({className:t,...l},c)=>(0,a.createElement)(o,{ref:c,iconNode:s,className:n(`lucide-${r(i(e))}`,`lucide-${e}`,t),...l}));return t.displayName=i(e),t}},63143:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},70440:(e,s,t)=>{t.r(s),t.d(s,{default:()=>r});var a=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},78122:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78259:(e,s,t)=>{t.d(s,{F:()=>r,Sz:()=>i,vn:()=>a,w7:()=>l});var a=function(e){return e[e.Default=0]="Default",e[e.PriceAsc=1]="PriceAsc",e[e.PriceDesc=2]="PriceDesc",e[e.RatingDesc=3]="RatingDesc",e}({}),r=function(e){return e[e.None=0]="None",e[e.Baslangic=1]="Baslangic",e[e.Girisimci=2]="Girisimci",e[e.Bronz=3]="Bronz",e[e.Gumus=4]="Gumus",e[e.Altin=5]="Altin",e[e.Platin=6]="Platin",e[e.PlatinMax=7]="PlatinMax",e}({}),l=function(e){return e[e.Unspecified=0]="Unspecified",e[e.Male=1]="Male",e[e.Female=2]="Female",e[e.Other=3]="Other",e}({}),i=function(e){return e[e.Pending=0]="Pending",e[e.Accepted=1]="Accepted",e[e.Rejected=2]="Rejected",e}({})},93613:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},99270:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};