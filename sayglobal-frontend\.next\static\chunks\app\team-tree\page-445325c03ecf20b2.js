(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8957],{31395:(e,t,i)=>{"use strict";i.d(t,{At:()=>n,Zq:()=>s,dt:()=>r,wI:()=>l,x1:()=>a});let s=[{id:1,distributorId:2,date:"2023-04-15",reference:"<PERSON><PERSON><PERSON>",points:120,amount:240.5,level:1,percentage:8},{id:2,distributorId:2,date:"2023-04-20",reference:"<PERSON><PERSON> Kılı\xe7",points:85,amount:170,level:2,percentage:5},{id:3,distributorId:2,date:"2023-04-28",reference:"Arda Altun",points:150,amount:300.75,level:1,percentage:8},{id:4,distributorId:2,date:"2023-05-05",reference:"Burak \xd6zt\xfcrk",points:60,amount:120.25,level:3,percentage:3},{id:5,distributorId:2,date:"2023-05-12",reference:"Selin Kara",points:200,amount:400,level:1,percentage:8}],a=[{id:101,firstName:"Aylin",lastName:"Şahin",level:1,joinDate:"2023-02-10",points:450,isActive:!0},{id:102,firstName:"Emre",lastName:"Kılı\xe7",level:2,joinDate:"2023-02-15",points:320,isActive:!0},{id:103,firstName:"Arda",lastName:"Altun",level:1,joinDate:"2023-03-01",points:580,isActive:!0},{id:104,firstName:"Burak",lastName:"\xd6zt\xfcrk",level:3,joinDate:"2023-03-12",points:150,isActive:!1},{id:105,firstName:"Selin",lastName:"Kara",level:1,joinDate:"2023-03-25",points:650,isActive:!0},{id:106,firstName:"Murat",lastName:"Aydın",level:2,joinDate:"2023-04-05",points:280,isActive:!0},{id:107,firstName:"Elif",lastName:"\xc7elik",level:3,joinDate:"2023-04-18",points:120,isActive:!1}],l={totalEarnings:4250.75,monthlyPoints:350,monthlyActivityPercentage:75,teamSize:a.length,monthlyBalance:1230.5,totalBalance:4250.75,totalPoints:2150,organizationPoints:5680,monthlyEarnings:[{month:"Ocak",earnings:850.25,activity:65},{month:"Şubat",earnings:920.5,activity:70},{month:"Mart",earnings:1050.75,activity:80},{month:"Nisan",earnings:1230.5,activity:75},{month:"Mayıs",earnings:980.25,activity:72}],monthlyPointsHistory:[{month:"Ocak",points:280,target:300},{month:"Şubat",points:320,target:300},{month:"Mart",points:390,target:350},{month:"Nisan",points:420,target:350},{month:"Mayıs",points:350,target:400},{month:"Haziran",points:310,target:400}],monthlyActivityTrend:[{month:"Ocak",activityPercentage:65,teamSize:4,newMembers:1},{month:"Şubat",activityPercentage:70,teamSize:5,newMembers:1},{month:"Mart",activityPercentage:80,teamSize:6,newMembers:1},{month:"Nisan",activityPercentage:75,teamSize:6,newMembers:0},{month:"Mayıs",activityPercentage:72,teamSize:7,newMembers:1},{month:"Haziran",activityPercentage:78,teamSize:7,newMembers:0}],nextLevelPoints:500,currentLevel:"G\xfcm\xfcş",nextLevel:"Altın"},n={id:1,name:"Distrib\xfct\xf6r (Sen)",level:3,points:2150,joinDate:"2023-01-01",isActive:!0,totalEarnings:4250.75,monthlyPoints:350,children:{left:{id:101,name:"Aylin Şahin",level:1,points:450,joinDate:"2023-02-10",isActive:!0,parentId:1,position:"left",totalEarnings:1200.5,monthlyPoints:120,children:{left:{id:103,name:"Arda Altun",level:1,points:580,joinDate:"2023-03-01",isActive:!0,parentId:101,position:"left",totalEarnings:850.25,monthlyPoints:95,children:{left:{id:107,name:"Elif \xc7elik",level:3,points:120,joinDate:"2023-04-18",isActive:!1,parentId:103,position:"left",totalEarnings:240,monthlyPoints:25}}},right:{id:105,name:"Selin Kara",level:1,points:650,joinDate:"2023-03-25",isActive:!0,parentId:101,position:"right",totalEarnings:980.75,monthlyPoints:135}}},right:{id:102,name:"Emre Kılı\xe7",level:2,points:320,joinDate:"2023-02-15",isActive:!0,parentId:1,position:"right",totalEarnings:750.25,monthlyPoints:85,children:{left:{id:104,name:"Burak \xd6zt\xfcrk",level:3,points:150,joinDate:"2023-03-12",isActive:!1,parentId:102,position:"left",totalEarnings:320,monthlyPoints:40},right:{id:106,name:"Murat Aydın",level:2,points:280,joinDate:"2023-04-05",isActive:!0,parentId:102,position:"right",totalEarnings:480.5,monthlyPoints:65}}}}},r={totalMembers:7,activeMembers:5,totalLevels:3,totalPoints:2550,totalEarnings:8950,monthlyGrowth:12.5}},34800:(e,t,i)=>{Promise.resolve().then(i.bind(i,63650))},63650:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>Y});var s=i(95155),a=i(12115),l=i(31395),n=i(60760),r=i(76408),o=i(54416),d=i(71007),c=i(69074),m=i(4516),x=i(35169),h=i(92138);let u=e=>{let{isOpen:t,onClose:i,pendingMembers:a,onPlaceLeft:l,onPlaceRight:u}=e;return(0,s.jsx)(n.N,{children:t&&(0,s.jsx)(r.P.div,{className:"fixed inset-0 bg-white/20 backdrop-blur-lg flex items-center justify-center z-50",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:i,children:(0,s.jsxs)(r.P.div,{className:"bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden",initial:{scale:.9,opacity:0,y:20},animate:{scale:1,opacity:1,y:0},exit:{scale:.9,opacity:0,y:20},transition:{duration:.3,type:"spring",damping:25,stiffness:300},onClick:e=>e.stopPropagation(),children:[(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4 text-white",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-2xl font-bold",children:"Yerleşim Bekleyen \xdcyeler"}),(0,s.jsx)("p",{className:"text-blue-100 mt-1",children:"Ekibe yeni katılan \xfcyeleri ağaca yerleştirin"})]}),(0,s.jsx)("button",{onClick:i,className:"p-2 hover:bg-white/30 hover:bg-opacity-20 rounded-lg transition-colors",children:(0,s.jsx)(o.A,{className:"h-6 w-6"})})]})}),(0,s.jsx)("div",{className:"p-6",children:0===a.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(d.A,{className:"h-8 w-8 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Yerleşim Bekleyen \xdcye Yok"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Şu anda ağaca yerleştirilmeyi bekleyen yeni \xfcye bulunmuyor."})]}):(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"Yerleştirme Kuralları"}),(0,s.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,s.jsxs)("li",{children:["• ",(0,s.jsx)("strong",{children:"Sola Ekle:"})," \xdcyeyi ağacın en sol boş pozisyonuna yerleştirir"]}),(0,s.jsxs)("li",{children:["• ",(0,s.jsx)("strong",{children:"Sağa Ekle:"})," \xdcyeyi ağacın en sağ boş pozisyonuna yerleştirir"]}),(0,s.jsx)("li",{children:"• Yerleştirme \xf6nceliği: \xdcst seviyelerden alt seviyelere doğru"})]})]})}),(0,s.jsx)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:a.map(e=>(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-gray-300 transition-colors",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full flex items-center justify-center",children:(0,s.jsx)(d.A,{className:"h-6 w-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.email}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.phone})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-500 mb-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-1"}),new Date(e.joinDate).toLocaleDateString("tr-TR")]}),(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-1"}),"Sponsor: ",e.sponsorName]})]})]}),(0,s.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsxs)("button",{onClick:()=>l(e.id),className:"flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Sola Ekle"]}),(0,s.jsxs)("button",{onClick:()=>u(e.id),className:"flex-1 inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Sağa Ekle"]})]})})]},e.id))})]})})]})})})};var p=i(40646);let g=e=>{let{isOpen:t,onClose:i,memberName:a,position:l}=e;return(0,s.jsx)(n.N,{children:t&&(0,s.jsx)(r.P.div,{className:"fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:i,children:(0,s.jsxs)(r.P.div,{className:"bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4",initial:{scale:.8,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.8,opacity:0,y:50},transition:{duration:.3,type:"spring",damping:20,stiffness:300},onClick:e=>e.stopPropagation(),children:[(0,s.jsx)(r.P.div,{className:"flex justify-center mb-6",initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring",damping:15},children:(0,s.jsx)("div",{className:"w-20 h-20 bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)(r.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",damping:12},children:(0,s.jsx)(p.A,{className:"h-12 w-12 text-green-600"})})})}),(0,s.jsxs)(r.P.div,{className:"text-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-3",children:"Başarıyla Yerleştirildi! \uD83C\uDF89"}),(0,s.jsxs)("p",{className:"text-gray-600 mb-6",children:[(0,s.jsx)("span",{className:"font-semibold text-green-600",children:a})," ","ekip ağacının ",(0,s.jsx)("span",{className:"font-semibold",children:l})," tarafına başarıyla yerleştirildi."]}),(0,s.jsx)("div",{className:"flex space-x-3",children:(0,s.jsx)(r.P.button,{onClick:i,className:"flex-1 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Harika!"})})]}),(0,s.jsx)(r.P.div,{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-400 to-emerald-500 rounded-t-2xl",initial:{scaleX:0},animate:{scaleX:1},transition:{delay:.4,duration:.6}})]})})})};var y=i(59212),b=i(79121),f=i(17951),j=i(47863),v=i(66474),N=i(17580),w=i(79397),k=i(69037),A=i(38564),S=i(33109),M=i(12318),D=i(6262),P=i(54481),E=i(40133);let z=[{id:201,name:"Ayşe Demir",email:"<EMAIL>",phone:"+90 ************",joinDate:"2024-01-15",sponsorId:1,sponsorName:"Sen (Distrib\xfct\xf6r)"},{id:202,name:"Mehmet Kaya",email:"<EMAIL>",phone:"+90 ************",joinDate:"2024-01-16",sponsorId:2,sponsorName:"Ahmet Yılmaz"},{id:203,name:"Fatma \xd6z",email:"<EMAIL>",phone:"+90 ************",joinDate:"2024-01-17",sponsorId:1,sponsorName:"Sen (Distrib\xfct\xf6r)"}],C=e=>{let{d3Node:t,onToggleExpand:i,isExpanded:a}=e,l=t.data,n=l.children&&(l.children.left||l.children.right);return(0,s.jsx)("div",{className:"absolute transition-all duration-500",style:{left:"".concat(t.x,"px"),top:"".concat(t.y,"px"),transform:"translate(-50%, -50%)"},children:(0,s.jsxs)("div",{className:"relative bg-white rounded-xl shadow-lg p-4 w-64 border-2 transition-all duration-300 hover:shadow-xl ".concat(l.isActive?"border-green-400 hover:border-green-500":"border-red-300 hover:border-red-400"," ").concat(1===l.id?"ring-2 ring-blue-400":""),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,s.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center ".concat(1===l.id?"bg-gradient-to-r from-blue-500 to-purple-600":l.isActive?"bg-gradient-to-r from-green-400 to-green-600":"bg-gradient-to-r from-gray-400 to-gray-600"),children:1===l.id?(0,s.jsx)(f.A,{className:"h-6 w-6 text-white"}):(0,s.jsx)(d.A,{className:"h-6 w-6 text-white"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 text-sm",children:l.name}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["Seviye ",l.level]}),1!==l.id&&l.position&&(0,s.jsx)("div",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium mt-1 ".concat("left"===l.position?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"),children:"left"===l.position?"Sol Kol":"Sağ Kol"})]}),(0,s.jsx)("div",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(l.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:l.isActive?"Aktif":"Pasif"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,s.jsxs)("div",{className:"bg-blue-50 p-2 rounded",children:[(0,s.jsx)("div",{className:"font-semibold text-blue-700",children:l.points}),(0,s.jsx)("div",{className:"text-blue-600",children:"Puan"})]}),(0,s.jsxs)("div",{className:"bg-green-50 p-2 rounded",children:[(0,s.jsxs)("div",{className:"font-semibold text-green-700",children:["₺",l.totalEarnings.toLocaleString("tr-TR")]}),(0,s.jsx)("div",{className:"text-green-600",children:"Kazan\xe7"})]})]}),(0,s.jsx)("div",{className:"mt-2 pt-2 border-t border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,s.jsx)(c.A,{className:"h-3 w-3 mr-1"}),new Date(l.joinDate).toLocaleDateString("tr-TR")]})}),n&&(0,s.jsx)("button",{onClick:()=>{i(l.id)},className:"absolute -bottom-4 left-1/2 transform -translate-x-1/2 bg-white border-2 border-gray-300 rounded-full p-1 hover:border-blue-400 transition-colors",children:a?(0,s.jsx)(j.A,{className:"h-4 w-4 text-gray-600"}):(0,s.jsx)(v.A,{className:"h-4 w-4 text-gray-600"})})]})})},I=e=>{let{source:t,destination:i}=e,a=t.x,l=t.y,n=i.x,r=i.y,o="M ".concat(a," ").concat(l," C ").concat(a," ").concat((l+r)/2,", ").concat(n," ").concat((l+r)/2,", ").concat(n," ").concat(r);return(0,s.jsx)("path",{d:o,fill:"none",stroke:"#d1d5db",strokeWidth:"2",className:"transition-all duration-500"})},Y=()=>{let e=l.dt,[t,i]=(0,a.useState)(l.At),[n,r]=(0,a.useState)(!1),[o,d]=(0,a.useState)(z),[c,m]=(0,a.useState)(!1),[x,h]=(0,a.useState)({memberName:"",position:"sol"}),[p,f]=(0,a.useState)(.52),[Y,K]=(0,a.useState)(0),[L,T]=(0,a.useState)(0),B=(0,a.useRef)(null),[X,O]=(0,a.useState)(!1),[R,H]=(0,a.useState)({x:0,y:0}),[_,q]=(0,a.useState)({x:0,y:0}),[G,F]=(0,a.useState)(!0),[W,Z]=(0,a.useState)(()=>new Set),U=(0,a.useCallback)(e=>{Z(t=>{let i=new Set(t);return i.has(e)?i.delete(e):i.add(e),i})},[]),J=(0,a.useMemo)(()=>{let e=(0,y.A)().nodeSize([340,240]).separation((e,t)=>e.parent===t.parent?1.5:2)((0,b.Ay)(t,e=>{var t,i;if(W.has(e.id))return;let s=[];return(null==(t=e.children)?void 0:t.left)&&s.push(e.children.left),(null==(i=e.children)?void 0:i.right)&&s.push(e.children.right),s.length>0?s:void 0}));return console.log("D3 Layout sonu\xe7ları:",{nodeCount:e.descendants().length,nodePositions:e.descendants().map(e=>({id:e.data.id,name:e.data.name,x:Math.round(e.x),y:Math.round(e.y),depth:e.depth}))}),e},[t,W]),{allNodes:Q,allLinks:V}=(0,a.useMemo)(()=>{let e=[],t=[],i=s=>{e.push(s),!W.has(s.data.id)&&s.children&&s.children.forEach(e=>{t.push({source:s,target:e}),i(e)})};return i(J),{allNodes:e,allLinks:t}},[J,W]),{width:$,height:ee,offsetX:et,offsetY:ei}=(0,a.useMemo)(()=>{if(0===Q.length)return{width:800,height:600,offsetX:0,offsetY:0};let e=Math.min(...Q.map(e=>e.x)),t=Math.max(...Q.map(e=>e.x)),i=Math.min(...Q.map(e=>e.y)),s=Math.max(...Q.map(e=>e.y)),a=t-e+340+300,l=s-i+240+300,n=-e+170+150,r=-i+120+150;return console.log("Container boyutları hesaplandı:",{nodeCount:Q.length,bounds:{minX:e,maxX:t,minY:i,maxY:s},totalWidth:a,totalHeight:l,offsetX:n,offsetY:r,nodePositions:Q.map(e=>({id:e.data.id,x:Math.round(e.x),y:Math.round(e.y)}))}),{width:a,height:l,offsetX:n,offsetY:r}},[Q]);(0,a.useEffect)(()=>{if(G&&B.current&&Q.length>0&&$>0&&ee>0&&void 0!==et&&void 0!==ei){let e=B.current.clientWidth,t=B.current.clientHeight;if(0===e||0===t)return;let i=Math.min(...Q.map(e=>e.x)),s=Math.max(...Q.map(e=>e.x)),a=Math.min(...Q.map(e=>e.y)),l=Math.max(...Q.map(e=>e.y)),n=s-i,r=l-a,o=i+et,d=a+ei,c=(e/2-o-n/2)/p,m=(t/2-d-r/2)/p;console.log("Direkt merkeze alma:",{containerSize:{width:e,height:t},treeBounds:{minX:i,maxX:s,minY:a,maxY:l},treeSize:{width:n,height:r},treePosition:{left:o,top:d},offset:{x:et,y:ei},zoom:p,calculatedPan:{x:c,y:m}}),K(c),T(m),q({x:c,y:m}),F(!1)}},[G,Q.length,$,ee,et,ei,p]),(0,a.useEffect)(()=>{let e=B.current;if(!e)return;let t=e=>(e.preventDefault(),e.stopPropagation(),f(Math.max(.3,Math.min(3,p*(e.deltaY>0?.9:1.1)))),!1);return e.addEventListener("wheel",t,{passive:!1}),()=>{e.removeEventListener("wheel",t)}},[p]);let es=(0,a.useCallback)(()=>{f(Math.min(1.2*p,3))},[p]),ea=(0,a.useCallback)(()=>{f(Math.max(p/1.2,.3))},[p]),el=(0,a.useCallback)(e=>{0===e.button&&(O(!0),H({x:e.clientX,y:e.clientY}),q({x:Y,y:L}),e.preventDefault())},[Y,L]),en=(0,a.useCallback)(e=>{if(X){let t=(e.clientX-R.x)/p,i=(e.clientY-R.y)/p;K(_.x+t),T(_.y+i)}},[X,R,_,p]),er=(0,a.useCallback)(()=>{O(!1)},[]),[eo,ed]=(0,a.useState)(null),[ec,em]=(0,a.useState)(null),ex=(0,a.useCallback)(e=>{if(2===e.touches.length){let t=e.touches[0],i=e.touches[1];ed(Math.sqrt(Math.pow(i.clientX-t.clientX,2)+Math.pow(i.clientY-t.clientY,2)))}else if(1===e.touches.length){let t=e.touches[0];em({x:t.clientX,y:t.clientY}),q({x:Y,y:L})}},[Y,L]),eh=(0,a.useCallback)(e=>{if(2===e.touches.length&&eo){e.preventDefault();let t=e.touches[0],i=e.touches[1],s=Math.sqrt(Math.pow(i.clientX-t.clientX,2)+Math.pow(i.clientY-t.clientY,2));f(Math.max(.3,Math.min(3,s/eo*p))),ed(s)}else if(1===e.touches.length&&ec){e.preventDefault();let t=e.touches[0],i=(t.clientX-ec.x)/p,s=(t.clientY-ec.y)/p;K(_.x+i),T(_.y+s)}},[eo,ec,_,p]),eu=(0,a.useCallback)(()=>{ed(null),em(null)},[]),ep=e=>{if(null===e||"object"!=typeof e)return e;if(e instanceof Date)return new Date(e.getTime());if(e instanceof Array)return e.map(e=>ep(e));if("object"==typeof e){let t={};return Object.keys(e).forEach(i=>{t[i]=ep(e[i])}),t}},eg=(e,t)=>{let i=[],s=(e,t)=>{if(e.id===t)return!0;if(e.children){for(let a of[e.children.left,e.children.right].filter(Boolean))if(s(a,t))return i.push(e.id),!0}return!1};return s(e,t),i.reverse()},ey=e=>{var t,i;let s=null==(t=e.children)?void 0:t.left;if(!s)return{parentId:e.id,position:"left"};let a=s;for(;null==(i=a.children)?void 0:i.left;)a=a.children.left;return{parentId:a.id,position:"left"}},eb=e=>{var t,i;let s=null==(t=e.children)?void 0:t.right;if(!s)return{parentId:e.id,position:"right"};let a=s;for(;null==(i=a.children)?void 0:i.right;)a=a.children.right;return{parentId:a.id,position:"right"}},ef=(e,t)=>{var i,s;if(e.id===t)return e;if(null==(i=e.children)?void 0:i.left){let i=ef(e.children.left,t);if(i)return i}if(null==(s=e.children)?void 0:s.right){let i=ef(e.children.right,t);if(i)return i}return null},ej=(e,s)=>{let a=o.find(t=>t.id===e);if(!a)return;let l="left"===s?ey(t):eb(t);if(!l)return void alert("Ağa\xe7ta uygun boş pozisyon bulunamadı!");let n=ef(t,l.parentId),c=n?n.level+1:1,x={id:a.id,name:a.name,level:c,points:0,monthlyPoints:0,totalEarnings:0,isActive:!0,joinDate:a.joinDate,parentId:l.parentId,position:l.position};console.log("Yeni \xfcye ekleniyor:",{id:x.id,name:x.name,parentId:l.parentId,position:l.position});let u=ep(t);ev(u,l.parentId,x,l.position),i(u);let p=eg(u,x.id);Z(e=>{let t=new Set(e);return p.forEach(e=>t.delete(e)),t.delete(l.parentId),t}),d(t=>t.filter(t=>t.id!==e)),h({memberName:a.name,position:"left"===s?"sol":"sağ"}),m(!0),r(!1)},ev=(e,t,i,s)=>{var a,l;return e.id===t?(e.children||(e.children={}),e.children[s]=i,!0):!!((null==(a=e.children)?void 0:a.left)&&ev(e.children.left,t,i,s)||(null==(l=e.children)?void 0:l.right)&&ev(e.children.right,t,i,s))};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Ekip Ağacı Y\xf6netimi"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Ekibinizdeki t\xfcm bağlantıları ve hiyerarşiyi g\xf6rselleştirin"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-8",children:[(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-4 border-l-4 border-blue-500",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam \xdcye"}),(0,s.jsx)("p",{className:"text-xl font-bold text-gray-900",children:e.totalMembers})]}),(0,s.jsx)(N.A,{className:"h-6 w-6 text-blue-600"})]})}),(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-4 border-l-4 border-green-500",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Aktif \xdcye"}),(0,s.jsx)("p",{className:"text-xl font-bold text-gray-900",children:e.activeMembers})]}),(0,s.jsx)(w.A,{className:"h-6 w-6 text-green-600"})]})}),(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-4 border-l-4 border-purple-500",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Seviye"}),(0,s.jsx)("p",{className:"text-xl font-bold text-gray-900",children:e.totalLevels})]}),(0,s.jsx)(k.A,{className:"h-6 w-6 text-purple-600"})]})}),(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-4 border-l-4 border-yellow-500",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Puan"}),(0,s.jsx)("p",{className:"text-xl font-bold text-gray-900",children:e.totalPoints.toLocaleString("tr-TR")})]}),(0,s.jsx)(A.A,{className:"h-6 w-6 text-yellow-600"})]})}),(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-4 border-l-4 border-indigo-500",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Kazan\xe7"}),(0,s.jsxs)("p",{className:"text-lg font-bold text-gray-900",children:["₺",e.totalEarnings.toLocaleString("tr-TR")]})]}),(0,s.jsx)(S.A,{className:"h-6 w-6 text-indigo-600"})]})}),(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-4 border-l-4 border-orange-500",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Aylık B\xfcy\xfcme"}),(0,s.jsxs)("p",{className:"text-xl font-bold text-gray-900",children:["%",e.monthlyGrowth]})]}),(0,s.jsx)(S.A,{className:"h-6 w-6 text-orange-600"})]})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Yerleştirme Kontrolleri"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Yeni katılan \xfcyeleri ağaca yerleştirin"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[o.length>0&&(0,s.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:[o.length," \xfcye yerleşim bekliyor"]}),(0,s.jsxs)("button",{onClick:()=>r(!0),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[(0,s.jsx)(M.A,{className:"h-4 w-4 mr-2"}),"Yerleşim Bekleyenler (",o.length,")"]})]})]}),(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,s.jsx)(M.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-blue-900",children:"Yerleştirme Sistemi"}),(0,s.jsxs)("p",{className:"text-sm text-blue-700 mt-1",children:["• Yeni \xfcyeler otomatik olarak sisteme eklenir ve yerleşim bekler",(0,s.jsx)("br",{}),'• "Sola Ekle" \xfcyeyi ağacın en sol boş pozisyonuna yerleştirir',(0,s.jsx)("br",{}),'• "Sağa Ekle" \xfcyeyi ağacın en sağ boş pozisyonuna yerleştirir',(0,s.jsx)("br",{}),"• Yerleştirme işlemi \xfcst seviyelerden alt seviyelere doğru yapılır"]})]})]})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8 mb-8",style:{overflow:"hidden"},children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-2",children:"Ekip Hiyerarşisi"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Ekibinizdeki t\xfcm \xfcyeler ve aralarındaki bağlantılar. D\xfcğ\xfcmlere tıklayarak alt seviyeleri daraltıp genişletebilirsiniz."})]}),(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsxs)("div",{className:"bg-gray-100 rounded-lg p-1 flex items-center space-x-1",children:[(0,s.jsx)("button",{onClick:ea,className:"p-2 rounded-md hover:bg-gray-200 transition-colors",title:"K\xfc\xe7\xfclt",children:(0,s.jsx)(D.A,{className:"h-4 w-4 text-gray-600"})}),(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-700 min-w-[60px] text-center",children:[Math.round(100*p),"%"]}),(0,s.jsx)("button",{onClick:es,className:"p-2 rounded-md hover:bg-gray-200 transition-colors",title:"B\xfcy\xfclt",children:(0,s.jsx)(P.A,{className:"h-4 w-4 text-gray-600"})}),(0,s.jsx)("div",{className:"w-px h-6 bg-gray-300 mx-1"}),(0,s.jsx)("button",{onClick:()=>{f(.52),K(0),T(0),q({x:0,y:0}),F(!0)},className:"p-2 rounded-md hover:bg-gray-200 transition-colors",title:"Sıfırla",children:(0,s.jsx)(E.A,{className:"h-4 w-4 text-gray-600"})})]})})]}),(0,s.jsxs)("div",{className:"mt-4 text-sm text-gray-500",children:["\uD83D\uDCA1 ",(0,s.jsx)("strong",{children:"İpucu:"})," Mouse tekerleği ile zoom yapabilir, mobilde iki parmakla b\xfcy\xfct\xfcp k\xfc\xe7\xfcltebilirsiniz."]})]}),(0,s.jsxs)("div",{ref:B,className:"relative border border-gray-200 rounded-lg",style:{width:"100%",height:"600px",overflow:"hidden",touchAction:"none",cursor:X?"grabbing":"grab"},onMouseDown:el,onMouseMove:en,onMouseUp:er,onMouseLeave:er,onTouchStart:ex,onTouchMove:eh,onTouchEnd:eu,children:[(0,s.jsxs)("div",{className:"absolute inset-0",style:{transform:"scale(".concat(p,") translate(").concat(Y,"px, ").concat(L,"px)"),transformOrigin:"center center",transition:X?"none":"transform 0.1s ease-out",width:$,height:ee,pointerEvents:"none"},children:[(0,s.jsx)("svg",{className:"absolute top-0 left-0",width:$,height:ee,children:(0,s.jsx)("g",{transform:"translate(".concat(et,", ").concat(ei,")"),children:V.map((e,t)=>(0,s.jsx)(I,{source:e.source,destination:e.target},"link-".concat(e.source.data.id,"-to-").concat(e.target.data.id)))})}),(0,s.jsx)("div",{className:"relative",style:{transform:"translate(".concat(et,"px, ").concat(ei,"px)")},children:Q.map(e=>(0,s.jsx)("div",{style:{pointerEvents:"auto"},children:(0,s.jsx)(C,{d3Node:e,onToggleExpand:U,isExpanded:!W.has(e.data.id)})},"node-".concat(e.data.id)))})]}),(0,s.jsxs)("div",{className:"absolute bottom-4 left-4 bg-black bg-opacity-75 text-white px-3 py-1 rounded-full text-sm",children:[Math.round(100*p),"%"]}),(0,s.jsx)("div",{className:"absolute top-4 left-4 bg-blue-500 bg-opacity-75 text-white px-3 py-1 rounded-full text-sm",children:"\uD83D\uDDB1️ S\xfcr\xfckle & Zoom"})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Renk Kodları ve A\xe7ıklamalar"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded border-2 border-blue-400"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Distrib\xfct\xf6r (Sen)"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 bg-gradient-to-r from-green-400 to-green-600 rounded border-2 border-green-400"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Aktif \xdcye"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 bg-gradient-to-r from-gray-400 to-gray-600 rounded border-2 border-red-300"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Pasif \xdcye"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 text-gray-600"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Alt seviyeyi genişlet"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(j.A,{className:"h-4 w-4 text-gray-600"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Alt seviyeyi daralt"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(M.A,{className:"h-4 w-4 text-blue-600"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Yerleşim bekleyen \xfcye"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs font-medium",children:"Sol Kol"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Sol kolda yer alan \xfcye"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"px-2 py-0.5 bg-purple-100 text-purple-800 rounded-full text-xs font-medium",children:"Sağ Kol"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Sağ kolda yer alan \xfcye"})]})]})]}),(0,s.jsx)(u,{isOpen:n,onClose:()=>r(!1),pendingMembers:o,onPlaceLeft:e=>ej(e,"left"),onPlaceRight:e=>ej(e,"right")}),(0,s.jsx)(g,{isOpen:c,onClose:()=>m(!1),memberName:x.memberName,position:x.position})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6408,6771,8441,1684,7358],()=>t(34800)),_N_E=e.O()}]);