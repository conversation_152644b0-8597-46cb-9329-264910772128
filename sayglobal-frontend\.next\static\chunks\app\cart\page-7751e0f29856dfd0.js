(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4005],{5196:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5278:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(95155),s=r(76408),n=r(5196);function i(e){let{checked:t,onChange:r,label:i,disabled:l=!1,size:o="md",className:c=""}=e,d={sm:{checkbox:"w-4 h-4",text:"text-xs",icon:"w-2.5 h-2.5"},md:{checkbox:"w-5 h-5",text:"text-sm",icon:"w-3 h-3"},lg:{checkbox:"w-6 h-6",text:"text-base",icon:"w-4 h-4"}}[o];return(0,a.jsxs)(s.P.label,{className:"flex items-center space-x-3 cursor-pointer select-none ".concat(l?"opacity-50 cursor-not-allowed":""," ").concat(c),whileHover:l?{}:{scale:1.01},whileTap:l?{}:{scale:.99},children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"checkbox",checked:t,onChange:e=>!l&&r(e.target.checked),disabled:l,className:"sr-only"}),(0,a.jsx)(s.P.div,{className:"\n                        ".concat(d.checkbox,"\n                        rounded-md\n                        border-2\n                        flex\n                        items-center\n                        justify-center\n                        transition-all\n                        duration-200\n                        ").concat(t?"bg-gradient-to-br from-purple-500 to-purple-700 border-purple-600 shadow-lg shadow-purple-500/25":"bg-white border-gray-300 hover:border-purple-400","\n                        ").concat(!l&&"hover:shadow-md","\n                    "),initial:!1,animate:{scale:t?1.05:1,boxShadow:t?"0 10px 25px -5px rgba(147, 51, 234, 0.25), 0 4px 6px -2px rgba(147, 51, 234, 0.05)":"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"},transition:{duration:.2,ease:"easeInOut"},children:(0,a.jsx)(s.P.div,{initial:!1,animate:{scale:+!!t,opacity:+!!t},transition:{duration:.15,ease:"easeInOut"},children:(0,a.jsx)(n.A,{className:"".concat(d.icon," text-white stroke-[3]")})})})]}),(0,a.jsx)("span",{className:"\n                    ".concat(d.text," \n                    text-gray-700 \n                    font-medium \n                    truncate \n                    flex-1\n                    ").concat(!l&&"group-hover:text-gray-900","\n                "),children:i})]})}},13841:(e,t,r)=>{"use strict";r.d(t,{$9:()=>c,AP:()=>x,IM:()=>u,PL:()=>d,Yu:()=>m,vG:()=>p,y$:()=>o});var a=r(32960),s=r(26715),n=r(5041),i=r(80722),l=r(87220);let o=()=>{let{isAuthenticated:e}=(0,l.A)();return(0,a.I)({queryKey:["cartItems",e],queryFn:async()=>{let e=await i.CV.getCartItems();if(e.success)return e.data.data;throw Error(e.error||"Sepet i\xe7erikleri alınamadı")},staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0})},c=()=>{let{isAuthenticated:e}=(0,l.A)();return(0,a.I)({queryKey:["cartCount",e],queryFn:async()=>{let e=await i.CV.getCartCount();if(e.success)return e.data.data;throw Error(e.error||"Sepet \xfcr\xfcn sayısı alınamadı")},staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0})},d=()=>(0,a.I)({queryKey:["discountRate"],queryFn:async()=>{try{let e=await i.Dv.getDiscountRate();if(console.log("\uD83D\uDD0D Discount Rate API Response:",e),e.success)return e.data||{discountRate:0};return console.warn("İndirim oranı alınamadı:",e.error),{discountRate:0}}catch(e){return console.warn("İndirim oranı alınırken hata:",e),{discountRate:0}}},staleTime:3e5,refetchOnWindowFocus:!1,refetchOnMount:!0,retry:!1}),x=()=>{let e=(0,s.jE)();return(0,n.n)({mutationFn:async e=>{let{productVariantId:t,quantity:r,isCustomerPrice:a}=e,s=await i.CV.addToCart(t,r,a);if(!s.success)throw Error(s.error||"\xdcr\xfcn sepete eklenemedi");return s.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepete \xfcr\xfcn ekleme hatası:",e)}})},u=()=>{let e=(0,s.jE)();return(0,n.n)({mutationFn:async e=>{let t=await i.CV.removeFromCart(e);if(!t.success)throw Error(t.error||"\xdcr\xfcn sepetten \xe7ıkarılamadı");return t.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepetten \xfcr\xfcn \xe7ıkarma hatası:",e)}})},m=()=>{let e=(0,s.jE)();return(0,n.n)({mutationFn:async e=>{let{productVariantId:t,quantity:r}=e,a=await i.CV.updateCartQuantity(t,r);if(!a.success)throw Error(a.error||"\xdcr\xfcn miktarı g\xfcncellenemedi");return a.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepet \xfcr\xfcn miktarı g\xfcncelleme hatası:",e)}})},p=()=>{let e=(0,s.jE)();return(0,n.n)({mutationFn:async()=>{let e=await i.Dv.updateCartType();if(!e.success)throw Error(e.error||"Sepet tipi g\xfcncellenemedi");return e.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepet tipi g\xfcncelleme hatası:",e)}})}},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var a=r(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:d="",children:x,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:t,...c,width:s,height:s,stroke:r,strokeWidth:i?24*Number(n)/Number(s):n,className:l("lucide",d),...!x&&!o(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(x)?x:[x]])}),x=(e,t)=>{let r=(0,a.forwardRef)((r,n)=>{let{className:o,...c}=r;return(0,a.createElement)(d,{ref:n,iconNode:t,className:l("lucide-".concat(s(i(e))),"lucide-".concat(e),o),...c})});return r.displayName=i(e),r}},43496:(e,t,r)=>{Promise.resolve().then(r.bind(r,72843))},72843:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var a=r(95155),s=r(13841),n=r(66766),i=r(6874),l=r.n(i),o=r(76408),c=r(5278);function d(){let{data:e,isLoading:t,error:r,refetch:i}=(0,s.y$)(),{data:d}=(0,s.PL)(),x=(0,s.IM)(),u=(0,s.Yu)(),m=(0,s.vG)(),p=(null==e?void 0:e.items)||[],h=(null==e?void 0:e.isCustomerPrice)||!1,y=(null==d?void 0:d.discountRate)||0;console.log("\uD83D\uDD0D Discount Data Debug:",{discountData:d,discountRate:y,isCustomerPrice:h});let g=async()=>{try{await m.mutateAsync()}catch(e){console.error("Sepet tipi g\xfcncelleme hatası:",e)}},v=async e=>{try{console.log("\uD83D\uDD0D handleRemoveFromCart \xe7ağrıldı, productVariantId:",e),await x.mutateAsync(e)}catch(e){console.error("Sepetten \xfcr\xfcn \xe7ıkarma hatası:",e)}},w=async(e,t)=>{if(t<=0)return void await v(e);try{await u.mutateAsync({productVariantId:e,quantity:t})}catch(e){console.error("Sepet \xfcr\xfcn miktarı g\xfcncelleme hatası:",e)}},j=0===p.length?{totalPrice:0,totalPV:0,totalCV:0,totalSP:0}:p.reduce((e,t)=>{let r=t.quantity,a=t.price;!h&&y&&y>0&&(a*=1-y/100);let s=t.extraDiscount||0;s>0&&(a*=1-s/100);let n=t.price*(t.pv/100),i=t.price*(t.cv/100),l=t.price*(t.sp/100);return{totalPrice:e.totalPrice+a*r,totalPV:e.totalPV+n*r,totalCV:e.totalCV+i*r,totalSP:e.totalSP+l*r}},{totalPrice:0,totalPV:0,totalCV:0,totalSP:0});return t?(0,a.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(o.P.div,{initial:{opacity:0},animate:{opacity:1},className:"flex flex-col items-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Sepetiniz y\xfckleniyor..."})]})})}):r?(0,a.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto",children:[(0,a.jsx)("div",{className:"text-red-600 mb-4",children:(0,a.jsx)("svg",{className:"h-12 w-12 mx-auto",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:"Sepet Y\xfcklenemedi"}),(0,a.jsx)("p",{className:"text-red-600 mb-4",children:"Sepetiniz y\xfcklenirken bir hata oluştu."}),(0,a.jsx)("button",{onClick:()=>i(),className:"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors",children:"Tekrar Dene"})]})})}):0===p.length?(0,a.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-24 w-24 text-gray-400 mx-auto mb-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Sepetiniz Boş"}),(0,a.jsx)("p",{className:"text-gray-600 mb-8 max-w-md mx-auto",children:"Hen\xfcz sepetinizde \xfcr\xfcn bulunmuyor. Alışverişe başlamak i\xe7in \xfcr\xfcnlerimizi keşfedin."}),(0,a.jsx)(o.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsxs)(l(),{href:"/products",className:"bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 inline-flex items-center space-x-2",children:[(0,a.jsx)("span",{children:"Alışverişe Başla"}),(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]})})]})})}):(0,a.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("h1",{className:"text-3xl font-bold text-white",children:["Sepetim (",p.length," \xfcr\xfcn)"]})}),(0,a.jsxs)(o.P.div,{className:"mb-6 bg-white rounded-xl shadow-md p-4",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,a.jsx)(c.A,{checked:h,onChange:g,label:"M\xfcşteri Fiyatlarını G\xf6ster ".concat(y>0?"(\xdcye indirimi uygulanmaz)":""),size:"md",className:"flex items-center gap-3",disabled:m.isPending}),y>0&&(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-2 ml-8",children:["Bu se\xe7enek aktif olduğunda \xfcye indiriminiz (%",y,") uygulanmaz ve \xfcr\xfcnler m\xfcşteri fiyatları ile g\xf6r\xfcnt\xfclenir."]}),m.isPending&&(0,a.jsx)("p",{className:"text-sm text-blue-600 mt-2 ml-8",children:"G\xfcncelleniyor..."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2 space-y-4",children:p.map((e,t)=>{let r=e.price,s=!1;if(h){let t=e.extraDiscount||0;t>0&&(r*=1-t/100,s=!0)}else{y&&y>0&&(r*=1-y/100,s=!0);let t=e.extraDiscount||0;t>0&&(r*=1-t/100,s=!0)}let i=e.price*(e.pv/100),l=e.price*(e.cv/100),c=e.price*(e.sp/100);return console.log("\uD83D\uDCB0 Fiyat & Puan Debug:",{productName:e.productName,originalPrice:e.price,finalPrice:r,isCustomerPrice:h,discountRate:y,extraDiscount:e.extraDiscount,hasDiscount:s,pv:e.pv,cv:e.cv,sp:e.sp,calculatedPV:i,calculatedCV:l,calculatedSP:c}),(0,a.jsx)(o.P.div,{className:"bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*t,duration:.5},children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"relative w-20 h-20 flex-shrink-0",children:(0,a.jsx)(n.default,{src:e.mainImageUrl,alt:e.productName,fill:!0,className:"object-cover rounded-lg"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:e.productName}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:e.brandName}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsxs)("span",{className:"text-lg font-bold text-purple-700",children:[(r*e.quantity).toFixed(2)," ₺"]}),s&&(0,a.jsxs)("span",{className:"text-sm text-gray-500 line-through",children:[(e.price*e.quantity).toFixed(2)," ₺"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[!h&&y&&y>0&&(0,a.jsxs)("span",{className:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-2 py-1 rounded-full text-xs font-medium",children:["%",y," \xdcye İndirimi"]}),(()=>{let t=e.extraDiscount||0;return t>0&&(0,a.jsxs)("span",{className:"bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-medium",children:["%",t," İndirim"]})})()]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[i>0&&(0,a.jsxs)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium",children:["PV: ",(i*e.quantity).toFixed(0)]}),l>0&&(0,a.jsxs)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium",children:["CV: ",(l*e.quantity).toFixed(0)]}),c>0&&(0,a.jsxs)("span",{className:"bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full font-medium",children:["SP: ",(c*e.quantity).toFixed(0)]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center border border-gray-300 rounded-lg",children:[(0,a.jsx)(o.P.button,{onClick:()=>w(e.variantId,e.quantity-1),className:"p-2 hover:bg-gray-100 transition-colors text-purple-800 disabled:opacity-50 disabled:cursor-not-allowed",whileTap:{scale:.9},disabled:e.quantity<=1||u.isPending,children:u.isPending?(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"}):(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 12H4"})})}),(0,a.jsx)("span",{className:"px-4 py-2 text-gray-800 font-medium",children:e.quantity}),(0,a.jsx)(o.P.button,{onClick:()=>w(e.variantId,e.quantity+1),className:"p-2 hover:bg-gray-100 transition-colors text-purple-800 disabled:opacity-50 disabled:cursor-not-allowed",whileTap:{scale:.9},disabled:e.quantity>=e.stock||u.isPending,children:u.isPending?(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"}):(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})})]}),(0,a.jsx)(o.P.button,{onClick:()=>v(e.variantId),className:"text-red-600 hover:text-red-700 p-2",whileHover:{scale:1.1},whileTap:{scale:.9},disabled:x.isPending,children:x.isPending?(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-red-600"}):(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})},e.variantId)})}),(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)(o.P.div,{className:"bg-white rounded-lg p-6 shadow-md sticky top-8",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.3,duration:.6},children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-6",children:"Sipariş \xd6zeti"}),(0,a.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsx)("span",{children:"\xdcr\xfcn Toplamı:"}),(0,a.jsxs)("span",{children:[j.totalPrice.toFixed(2)," ₺"]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsx)("span",{children:"Kargo:"}),(0,a.jsx)("span",{className:"text-green-600",children:"\xdccretsiz"})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg space-y-2",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Kazanacağınız Puanlar:"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[j.totalPV>0&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium block",children:["PV: ",j.totalPV.toFixed(0)]})}),j.totalCV>0&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium block",children:["CV: ",j.totalCV.toFixed(0)]})}),j.totalSP>0&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("span",{className:"bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full font-medium block",children:["SP: ",j.totalSP.toFixed(0)]})})]})]}),(0,a.jsx)("div",{className:"border-t pt-3",children:(0,a.jsxs)("div",{className:"flex justify-between text-lg font-bold text-gray-800",children:[(0,a.jsx)("span",{children:"Toplam:"}),(0,a.jsxs)("span",{className:"text-purple-700",children:[j.totalPrice.toFixed(2)," ₺"]})]})})]}),(0,a.jsx)(l(),{href:"/checkout",children:(0,a.jsx)(o.P.button,{className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},children:"\xd6demeye Ge\xe7"})}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsxs)(l(),{href:"/products",className:"text-purple-600 hover:text-purple-700 font-medium inline-flex items-center space-x-1",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),(0,a.jsx)("span",{children:"Alışverişe Devam Et"})]})})]})})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6408,6874,7323,6766,6681,8441,1684,7358],()=>t(43496)),_N_E=e.O()}]);