(()=>{var e={};e.id=621,e.ids=[621],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13964:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38665:(e,t,s)=>{Promise.resolve().then(s.bind(s,81231))},44399:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),l=s(88170),n=s.n(l),i=s(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let c={children:["",{children:["admin",{children:["pending-products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,83853)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\pending-products\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\pending-products\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/pending-products/page",pathname:"/admin/pending-products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81231:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>z});var r=s(60687),a=s(43210),l=s(15908),n=s(16189),i=s(85814),d=s.n(i),c=s(8693),o=s(28559),x=s(48730),m=s(13964),p=s(11860),u=s(19080),h=s(99270),g=s(41862),y=s(78122),b=s(58869),j=s(13861),f=s(63143),v=s(47033),N=s(14952),w=s(78259),k=s(55245),S=s(40237),A=s(26001);let C=({product:e,isOpen:t,onClose:s,onConfirm:l,action:n,isLoading:i=!1})=>{let[d,c]=(0,a.useState)(""),[o,x]=(0,a.useState)("");return t&&e?(0,r.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-lg flex items-center justify-center p-4 z-50",children:(0,r.jsxs)(A.P.div,{className:"bg-white rounded-lg max-w-md w-full p-6",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.2},children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"approve"===n?"\xdcr\xfcn\xfc Onayla":"\xdcr\xfcn\xfc Reddet"}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:[(0,r.jsx)("strong",{children:"title"in e?e.title:e.name})," adlı \xfcr\xfcn\xfc ","approve"===n?"onaylamak":"reddetmek"," istediğinizden emin misiniz?"]})}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"adminNotes",className:"block text-sm font-medium text-gray-700 mb-2",children:"approve"===n?"Onay Notu (İsteğe bağlı)":"Red Sebebi *"}),(0,r.jsx)("textarea",{id:"adminNotes",value:d,onChange:e=>{c(e.target.value),o&&x("")},rows:3,className:`w-full px-3 py-2 border rounded-md focus:ring-2 focus:border-transparent text-black ${o?"border-red-500 focus:ring-red-500":"border-gray-300 focus:ring-red-500"}`,placeholder:"approve"===n?"Onay ile ilgili notunuz...":"Red sebebinizi a\xe7ıklayın...",required:"reject"===n}),o&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)("button",{onClick:()=>{c(""),x(""),s()},disabled:i,className:"flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed rounded-md transition-colors",children:"İptal"}),(0,r.jsx)("button",{onClick:()=>{if("reject"===n&&!d.trim())return void x("Red sebebi zorunludur.");x(""),l(d),c("")},disabled:i,className:`flex-1 px-4 py-2 text-white rounded-md transition-colors flex items-center justify-center ${"approve"===n?"bg-green-600 hover:bg-green-700 disabled:bg-green-400":"bg-red-600 hover:bg-red-700 disabled:bg-red-400"}`,children:i?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"İşleniyor..."]}):"approve"===n?"Onayla":"Reddet"})]})]})}):null};var P=s(21254);let z=()=>{let{user:e,isLoading:t}=(0,l.A)(),s=(0,n.useRouter)(),i=(0,c.jE)(),[A,z]=(0,a.useState)(1),[q,R]=(0,a.useState)(""),[O,_]=(0,a.useState)(""),[D,U]=(0,a.useState)("all"),[E,G]=(0,a.useState)(null),[B,T]=(0,a.useState)(!1),[F,K]=(0,a.useState)("approve"),[M,I]=(0,a.useState)(null),[$,L]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=setTimeout(()=>{_(q),z(1)},300);return()=>{clearTimeout(e)}},[q]),(0,a.useEffect)(()=>{t||e&&"admin"===e.role||s.push("/login")},[e,t,s]),(0,a.useEffect)(()=>{e&&"admin"===e.role&&(i.invalidateQueries({queryKey:["adminProductStatistics"]}),i.invalidateQueries({queryKey:["adminProducts"]}),console.log("\uD83D\uDCCA \xdcr\xfcn onay y\xf6netimi sayfası y\xfcklendi, istatistikler ve \xfcr\xfcn listesi yenileniyor..."))},[e,i]);let{data:Y,isLoading:Q}=(0,k.tA)(),{data:Z,isLoading:H,isFetching:V}=(0,k.Bv)(A,O),X=(0,k.IG)(),J=(0,S.Z9)(e=>e.clearProductCache),{refreshProductLists:W}=(0,k.E0)(),ee=Z?.filter(e=>"all"===D||("pending"===D?e.status===w.Sz.Pending:"approved"===D?e.status===w.Sz.Accepted:"rejected"!==D||e.status===w.Sz.Rejected))||[],et=(e,t)=>{G(e),K(t),T(!0)},es=e=>{I(e),L(!0)},er=e=>{switch(e){case w.Sz.Pending:return"bg-yellow-100 text-yellow-800 border-yellow-200";case w.Sz.Accepted:return"bg-green-100 text-green-800 border-green-200";case w.Sz.Rejected:return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},ea=e=>{switch(e){case w.Sz.Pending:return"Onay Bekliyor";case w.Sz.Accepted:return"Onaylandı";case w.Sz.Rejected:return"Reddedildi";default:return"Bilinmiyor"}},el=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),en=(e,t=22)=>e.length<=t?e:e.substring(0,t)+"...";return t?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):e&&"admin"===e.role?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(d(),{href:"/admin",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 mr-2"}),"Admin Paneli"]})})}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"\xdcr\xfcn Onay Y\xf6netimi"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Distrib\xfct\xf6rler tarafından eklenen \xfcr\xfcnleri inceleyin ve onaylayın"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-yellow-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,r.jsx)(x.A,{className:"h-6 w-6 text-yellow-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Onay Bekleyen"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:Q?"...":Y?.pendingCount||0})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-green-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,r.jsx)(m.A,{className:"h-6 w-6 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Onaylanan"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:Q?"...":Y?.acceptedCount||0})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-red-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,r.jsx)(p.A,{className:"h-6 w-6 text-red-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Reddedilen"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:Q?"...":Y?.rejectedCount||0})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-blue-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,r.jsx)(u.A,{className:"h-6 w-6 text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:Q?"...":Y?.totalProductCount||0})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,r.jsx)("input",{type:"text",placeholder:"\xdcr\xfcn veya kullanıcı ara...",value:q,onChange:e=>R(e.target.value),className:"pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-black"}),V&&(0,r.jsx)(g.A,{className:"h-5 w-5 text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2 animate-spin"})]}),(0,r.jsxs)("select",{value:D,onChange:e=>U(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-gray-600",children:[(0,r.jsx)("option",{value:"all",children:"T\xfcm Durumlar"}),(0,r.jsx)("option",{value:"pending",children:"Onay Bekleyen"}),(0,r.jsx)("option",{value:"approved",children:"Onaylanan"}),(0,r.jsx)("option",{value:"rejected",children:"Reddedilen"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{onClick:()=>{console.log("\uD83D\uDD04 Manuel cache yenileme başlatıldı..."),W()},className:"flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",title:"Verileri yenile",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Yenile"]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[ee.length," \xfcr\xfcn g\xf6steriliyor"]})]})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden",children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"\xdcr\xfcn"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Oluşturan Kullanıcı"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kategori"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fiyat"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Durum"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tarih"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"İşlemler"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:ee.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("img",{className:"h-12 w-12 rounded-lg object-cover",src:e.imageUrl||"https://picsum.photos/id/50/200/200",alt:e.name}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",title:e.name,children:en(e.name)}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.brandName})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.createdByUserName}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:e.createdByUserRole})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.categoryName})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:["₺",e.price.toFixed(2)]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${er(e.status)}`,children:ea(e.status)})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-900",children:el(e.updatedAt)})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,r.jsx)("button",{onClick:()=>es(e.id),className:"text-gray-600 hover:text-gray-900",title:"Detayları G\xf6r\xfcnt\xfcle",children:(0,r.jsx)(j.A,{className:"h-4 w-4"})}),(0,r.jsx)(d(),{href:`/admin/products/edit/${e.id}?from=pending-products`,className:"text-blue-600 hover:text-blue-800",title:"\xdcr\xfcn\xfc D\xfczenle",children:(0,r.jsx)(f.A,{className:"h-4 w-4"})}),e.status===w.Sz.Pending&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>et(e,"approve"),className:"text-green-600 hover:text-green-800",title:"Onayla",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>et(e,"reject"),className:"text-red-600 hover:text-red-800",title:"Reddet",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})})]})]})})]},e.id))})]})}),(0,r.jsxs)("div",{className:"px-6 py-4 border-t border-gray-200 flex items-center justify-between",children:[(0,r.jsxs)("button",{onClick:()=>z(e=>Math.max(e-1,1)),disabled:1===A,className:"flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"\xd6nceki"]}),(0,r.jsxs)("span",{className:"text-sm text-gray-700",children:["Sayfa ",(0,r.jsx)("span",{className:"font-bold",children:A})]}),(0,r.jsxs)("button",{onClick:()=>z(e=>Z&&10===Z.length?e+1:e),disabled:!Z||Z.length<10,className:"flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:["Sonraki",(0,r.jsx)(N.A,{className:"h-4 w-4 ml-2"})]})]}),0===ee.length&&!V&&!H&&(0,r.jsxs)("div",{className:"px-6 py-12 text-center",children:[(0,r.jsx)(u.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"\xdcr\xfcn bulunamadı"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Arama veya filtreleme kriterlerinizi değiştirerek tekrar deneyin."})]})]}),H&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"\xdcr\xfcnler y\xfckleniyor..."})]}),!H&&0===ee.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(u.A,{className:"h-24 w-24 text-gray-300 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:q||"all"!==D?"Arama kriterlerine uygun \xfcr\xfcn bulunamadı":"Hen\xfcz \xfcr\xfcn yok"}),(0,r.jsx)("p",{className:"text-gray-600",children:q||"all"!==D?"Farklı kriterler deneyebilirsiniz.":"\xdcr\xfcnler eklendiğinde burada g\xf6r\xfcnecektir."})]}),(0,r.jsx)(C,{product:E,isOpen:B,onClose:()=>T(!1),onConfirm:e=>{if(!E)return;let t="approve"===F;X.mutate({productId:E.id,isApproved:t,message:e||(t?"\xdcr\xfcn onaylandı":"\xdcr\xfcn reddedildi")},{onSuccess:()=>{console.log("✅ \xdcr\xfcn durumu başarıyla g\xfcncellendi"),J(E.id),console.log(`🧹 Zustand cache temizlendi - ProductID: ${E.id}`),T(!1),G(null)},onError:e=>{console.error("❌ \xdcr\xfcn durumu g\xfcncellenirken hata:",e)}})},action:F,isLoading:X.isPending}),(0,r.jsx)(P.A,{productId:M,isOpen:$,onClose:()=>{L(!1),I(null),i.invalidateQueries({queryKey:["adminProductStatistics"]}),console.log("\uD83D\uDD04 Yeni modal kapandı, istatistik cache temizlendi")},showApprovalStatus:!0})]})}):null}},81630:e=>{"use strict";e.exports=require("http")},83853:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\admin\\\\pending-products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\pending-products\\page.tsx","default")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96809:(e,t,s)=>{Promise.resolve().then(s.bind(s,83853))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,181,658,85,32,313],()=>s(44399));module.exports=r})();