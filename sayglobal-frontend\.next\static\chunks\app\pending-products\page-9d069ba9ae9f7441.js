(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9489],{8332:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>E});var a=t(95155),l=t(12115),r=t(87220),i=t(35695),n=t(6874),c=t.n(n),d=t(76408),m=t(14186),o=t(40646),x=t(54861),h=t(37108),u=t(35169),y=t(84616),g=t(47924),p=t(51154),j=t(69074),N=t(71007),f=t(92657),b=t(13717),v=t(42355),w=t(13052),k=t(56407);let A=(0,t(65453).v)(e=>({searchTerm:"",statusFilter:-1,setSearchTerm:s=>e({searchTerm:s}),setStatusFilter:s=>e({statusFilter:s}),resetFilters:()=>e({searchTerm:"",statusFilter:-1})}));var C=t(26715),S=t(85339),B=t(54416),M=t(23903);let z=e=>{var s,t,r,n;let{productId:c,isOpen:m,onClose:u}=e,y=(0,i.useRouter)(),g=(0,C.jE)(),f=(0,M.Q6)(),b=(0,M.e$)(),A=(0,M.Z9)(e=>e.setCurrentImage),z=(0,M.Z9)(e=>e.setSelectedVariant),{data:E,isLoading:D,error:R}=(0,k.N5)(c),{data:O,isLoading:T}=(0,k.qr)(c,m);if((0,l.useEffect)(()=>{if(E){var e,s,t,a,l,r;console.log("\uD83D\uDD0D Dealership Modal ProductDetail Data:",{id:E.id,name:E.name,variantsCount:(null==(e=E.variants)?void 0:e.length)||0,variants:E.variants,hasVariants:E.variants&&E.variants.length>0,firstVariant:null==(s=E.variants)?void 0:s[0],firstVariantPrice:null==(a=E.variants)||null==(t=a[0])?void 0:t.price,firstVariantPriceType:typeof(null==(r=E.variants)||null==(l=r[0])?void 0:l.price)})}},[E]),(0,l.useEffect)(()=>(m?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[m]),(0,l.useEffect)(()=>{A(0)},[f,A]),(0,l.useEffect)(()=>{m&&c&&(console.log("\uD83D\uDD04 Dealership Modal opened, forcing fresh data fetch for product:",c),g.invalidateQueries({queryKey:["dealershipProductDetail",c]}))},[m,c,g]),!m)return null;let L=e=>new Intl.NumberFormat("tr-TR",{style:"currency",currency:"TRY"}).format(e),P=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),U=(null==E?void 0:E.variants)&&E.variants.length>0,F=U?Math.min(f,E.variants.length-1):0,V=U?E.variants[F]:null,q=(null==V||null==(s=V.images)?void 0:s.map(e=>e.url))||[],I=V?(e=>0===e?{text:"Stokta Yok",color:"bg-red-100 text-red-800",icon:(0,a.jsx)(x.A,{className:"h-4 w-4"})}:e<20?{text:"Az Stok",color:"bg-yellow-100 text-yellow-800",icon:(0,a.jsx)(S.A,{className:"h-4 w-4"})}:{text:"Stokta",color:"bg-green-100 text-green-800",icon:(0,a.jsx)(o.A,{className:"h-4 w-4"})})(V.stock):null;return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-lg flex items-center justify-center p-4 z-50",onClick:u,children:(0,a.jsxs)(d.P.div,{className:"bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.2},onClick:e=>e.stopPropagation(),children:[(0,a.jsx)("div",{className:"sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-xl",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-3",children:(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"\xdcr\xfcn Detayları"})}),(0,a.jsx)("button",{onClick:u,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(B.A,{className:"w-6 h-6"})})]})}),(0,a.jsxs)("div",{className:"p-6",children:[D&&(0,a.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,a.jsx)(p.A,{className:"h-8 w-8 animate-spin text-blue-600"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:"\xdcr\xfcn detayları y\xfckleniyor..."})]}),R&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(S.A,{className:"h-6 w-6 text-red-600 mt-1 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-red-800 mb-2",children:"\xdcr\xfcn Detayları Y\xfcklenemedi"}),(0,a.jsx)("p",{className:"text-red-700 mb-4",children:"\xdcr\xfcn detayları y\xfcklenirken bir hata oluştu. L\xfctfen tekrar deneyin."}),(0,a.jsxs)("div",{className:"text-sm text-red-600 bg-red-100 rounded p-2 font-mono",children:["Hata: ",(null==R?void 0:R.message)||"Bilinmeyen hata"]}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Sayfayı Yenile"})]})]})}),E&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[U&&E.variants.length>1&&(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:E.variants.map((e,s)=>(0,a.jsxs)("button",{onClick:()=>z(s),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(f===s?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:["Varyant ",s+1,e.features.length>0&&(0,a.jsxs)("span",{className:"ml-1 text-xs",children:["(",e.features.map(e=>e.featureValue).join(", "),")"]})]},e.id))})}),(0,a.jsx)("div",{className:"relative aspect-square bg-gray-100 rounded-lg overflow-hidden",children:q.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("img",{src:q[b],alt:E.name,className:"w-full h-full object-cover"}),q.length>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>{var e;if(!V||!(null==(e=V.images)?void 0:e.length))return;let s=V.images.length;A((b-1+s)%s)},className:"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors",children:(0,a.jsx)(v.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>{var e;if(V&&(null==(e=V.images)?void 0:e.length))A((b+1)%V.images.length)},className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors",children:(0,a.jsx)(w.A,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:[b+1," / ",q.length]})]})]}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-16 w-16 text-gray-400"})})}),q.length>1&&(0,a.jsx)("div",{className:"grid grid-cols-4 gap-2",children:q.map((e,s)=>(0,a.jsx)("button",{onClick:()=>A(s),className:"aspect-square rounded-lg overflow-hidden border-2 ".concat(b===s?"border-blue-500":"border-gray-200 hover:border-gray-300"),children:(0,a.jsx)("img",{src:e,alt:"".concat(E.name," ").concat(s+1),className:"w-full h-full object-cover"})},s))})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(E.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:E.isActive?"Aktif":"Pasif"}),(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full border ".concat((e=>{switch(e){case 0:return"bg-yellow-100 text-yellow-800 border-yellow-200";case 1:return"bg-green-100 text-green-800 border-green-200";case 2:return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(E.status)),children:[(e=>{switch(e){case 0:default:return(0,a.jsx)(S.A,{className:"h-4 w-4"});case 1:return(0,a.jsx)(o.A,{className:"h-4 w-4"});case 2:return(0,a.jsx)(x.A,{className:"h-4 w-4"})}})(E.status),(0,a.jsx)("span",{className:"ml-1",children:(e=>{switch(e){case 0:return"Onay Bekliyor";case 1:return"Onaylandı";case 2:return"Reddedildi";default:return"Bilinmiyor"}})(E.status)})]})]}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:E.categoryName})]}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:E.name}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:E.brandName})]}),V?(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Varyant Bilgileri"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Fiyat"}),(0,a.jsx)("p",{className:"text-xl font-bold text-gray-900",children:(()=>{let e=V.price;return null==e||void 0===e?"Fiyat Belirtilmemiş":isNaN(e)?"Ge\xe7ersiz Fiyat":L(e)})()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Stok"}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:I&&(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(I.color),children:[I.icon,(0,a.jsxs)("span",{className:"ml-1",children:[V.stock," - ",I.text]})]})})]})]}),V.features.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"\xd6zellikler"}),(0,a.jsx)("div",{className:"space-y-2",children:V.features.map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[e.featureName,":"]}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.featureValue})]},s))})]})]}):(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(S.A,{className:"h-5 w-5 text-yellow-600 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-yellow-800",children:"Varyant Bilgileri Eksik"}),(0,a.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"Bu \xfcr\xfcn i\xe7in varyant bilgileri bulunamadı. \xdcr\xfcn\xfc d\xfczenleyerek varyant ekleyebilirsiniz."})]})]})}),E.description&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"A\xe7ıklama"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:E.description})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-2",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-900 flex items-center",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 mr-2 text-gray-600"}),"Tarihler"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Oluşturulma:"}),(0,a.jsx)("span",{className:"text-gray-900",children:P(E.createdAt)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Son G\xfcncelleme:"}),(0,a.jsx)("span",{className:"text-gray-900",children:P(E.updatedAt)})]})]})]}),E.createdByUserId&&(0,a.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg border",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 mr-2 text-gray-600"}),"Oluşturan"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Kullanıcı Adı:"}),(0,a.jsx)("p",{className:"text-gray-900 font-medium",children:E.createdByUserName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Rol:"}),(0,a.jsx)("p",{className:"text-gray-900 font-medium",children:(null==(t=E.createdByUserRole)?void 0:t.toLowerCase())==="admin"?"Admin":(null==(r=E.createdByUserRole)?void 0:r.toLowerCase())==="dealership"?"Satıcı":(null==(n=E.createdByUserRole)?void 0:n.toLowerCase())==="customer"?"M\xfcşteri":E.createdByUserRole})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg border",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 mr-2 text-gray-600"}),"Admin \xdcr\xfcn Notu"]}),T?(0,a.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 animate-spin text-gray-400 mr-2"}),(0,a.jsx)("span",{className:"text-gray-500",children:"Not y\xfckleniyor..."})]}):O?(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"mb-3",children:O.message?(0,a.jsx)("p",{className:"text-gray-800 font-medium",children:O.message}):(0,a.jsx)("p",{className:"text-gray-500 italic",children:"Admin notu girilmemiş"})}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Değiştiren:"})," ",O.approvedByUser.fullName," (",O.approvedByUser.role,")"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Tarih:"})," ",new Date(O.changedAtUtc).toLocaleString("tr-TR")]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Durum:"}),(0,a.jsx)("span",{className:"ml-1 ".concat(1===O.newStatus?"text-green-600":2===O.newStatus?"text-red-600":"text-yellow-600"),children:1===O.newStatus?"Onaylandı":2===O.newStatus?"Reddedildi":"Beklemede"})]})]})]}),(0,a.jsx)("div",{className:"ml-4",children:1===O.newStatus?(0,a.jsx)(o.A,{className:"h-5 w-5 text-green-500"}):2===O.newStatus?(0,a.jsx)(x.A,{className:"h-5 w-5 text-red-500"}):(0,a.jsx)(S.A,{className:"h-5 w-5 text-yellow-500"})})]})}):(0,a.jsx)("div",{className:"text-center py-4",children:(null==E?void 0:E.status)===0?(0,a.jsxs)("div",{className:"flex items-center justify-center text-yellow-600",children:[(0,a.jsx)(S.A,{className:"h-5 w-5 mr-2"}),(0,a.jsx)("span",{children:"Onay bekleniyor"})]}):(0,a.jsxs)("div",{className:"flex items-center justify-center text-gray-500",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 mr-2"}),(0,a.jsx)("span",{children:"Admin notu girilmemiş"})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsx)("button",{onClick:u,className:"flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors",children:"Kapat"}),0===E.status&&(0,a.jsx)("button",{className:"flex-1 px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors",onClick:()=>{u(),y.push("/edit-product/".concat(E.id))},children:"D\xfczenle"})]})]})]})]})]})})},E=()=>{var e,s;let{user:t,isLoading:n}=(0,r.A)(),S=(0,i.useRouter)(),B=(0,C.jE)(),[M,E]=(0,l.useState)(1),[D,R]=(0,l.useState)(null),[O,T]=(0,l.useState)(!1),{searchTerm:L,statusFilter:P,setSearchTerm:U,setStatusFilter:F}=A(),[V,q]=(0,l.useState)("");(0,l.useEffect)(()=>{let e=setTimeout(()=>{q(L),E(1)},300);return()=>{clearTimeout(e)}},[L]),(0,l.useEffect)(()=>{if(!n&&!t||!n&&t&&"dealership"!==t.role&&"admin"!==t.role)return void S.push("/login")},[t,n,S]),(0,l.useEffect)(()=>{t&&("dealership"===t.role||"admin"===t.role)&&(B.invalidateQueries({queryKey:["myProductStatistics"]}),B.invalidateQueries({queryKey:["myProducts"]}),console.log("\uD83D\uDCCA \xdcr\xfcn y\xf6netimi sayfası y\xfcklendi, istatistikler ve \xfcr\xfcn listesi yenileniyor..."))},[t,B]);let{data:I,isLoading:Y}=(0,k.Ee)(),{data:_,isLoading:H,isFetching:K}=(0,k.i5)(M,V,P),Q=e=>{switch(e){case 0:return"bg-yellow-100 text-yellow-800";case 1:return"bg-green-100 text-green-800";case 2:return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},Z=e=>{switch(e){case 0:return(0,a.jsx)(m.A,{className:"h-4 w-4"});case 1:return(0,a.jsx)(o.A,{className:"h-4 w-4"});case 2:return(0,a.jsx)(x.A,{className:"h-4 w-4"});default:return(0,a.jsx)(h.A,{className:"h-4 w-4"})}},W=e=>{switch(e){case 0:return"Onay Bekliyor";case 1:return"Onaylandı";case 2:return"Reddedildi";default:return"Bilinmiyor"}},$=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),G=e=>{R(e),T(!0)},J=(null==_||null==(e=_.data)?void 0:e.items)||[],X=(null==_||null==(s=_.data)?void 0:s.totalCount)||0,ee=(null==I?void 0:I.totalProductCount)||0,es=(null==I?void 0:I.pendingCount)||0,et=(null==I?void 0:I.approvedCount)||0,ea=(null==I?void 0:I.rejectedCount)||0;return n||Y?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):t&&("dealership"===t.role||"admin"===t.role)?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)(c(),{href:"/panel",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"Panele D\xf6n"]})}),(0,a.jsxs)(c(),{href:"/add-product",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2",children:[(0,a.jsx)(y.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Yeni \xdcr\xfcn Ekle"})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"\xdcr\xfcn Y\xf6netimi"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Eklediğiniz \xfcr\xfcnlerin onay durumunu takip edin ve yeni \xfcr\xfcnler ekleyin"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,a.jsx)(m.A,{className:"h-6 w-6 text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Onay Bekleyen"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:es})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)(o.A,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Onaylanan"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:et})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,a.jsx)(x.A,{className:"h-6 w-6 text-red-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Reddedilen"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:ea})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)(h.A,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:ee})]})]})})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,a.jsx)("input",{type:"text",placeholder:"\xdcr\xfcn ara...",value:L,onChange:e=>U(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"}),K&&(0,a.jsx)(p.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 animate-spin"})]}),(0,a.jsxs)("select",{value:P,onChange:e=>F(Number(e.target.value)),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-600",children:[(0,a.jsx)("option",{value:-1,children:"T\xfcm Durumlar"}),(0,a.jsx)("option",{value:0,children:"Onay Bekleyen"}),(0,a.jsx)("option",{value:1,children:"Onaylanan"}),(0,a.jsx)("option",{value:2,children:"Reddedilen"})]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[X," \xfcr\xfcn g\xf6steriliyor"]})]})}),H?(0,a.jsxs)("div",{className:"flex justify-center items-center py-12",children:[(0,a.jsx)(p.A,{className:"h-8 w-8 animate-spin text-blue-600"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:"\xdcr\xfcnler y\xfckleniyor..."})]}):0===J.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(h.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Hen\xfcz \xfcr\xfcn yok"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"İlk \xfcr\xfcn\xfcn\xfcz\xfc ekleyerek başlayın"}),(0,a.jsxs)(c(),{href:"/add-product",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center space-x-2",children:[(0,a.jsx)(y.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"\xdcr\xfcn Ekle"})]})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:J.map(e=>(0,a.jsxs)(d.P.div,{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,a.jsx)("div",{className:"aspect-w-16 aspect-h-12 bg-gray-200",children:e.imageUrl?(0,a.jsx)("img",{src:e.imageUrl,alt:e.name,className:"w-full h-48 object-cover"}):(0,a.jsx)("div",{className:"w-full h-48 bg-gray-200 flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-12 w-12 text-gray-400"})})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(Q(e.statusId)),children:[Z(e.statusId),(0,a.jsx)("span",{className:"ml-1",children:W(e.statusId)})]}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:e.categoryName})]}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 line-clamp-2",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:["₺",e.price.toFixed(2)]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-blue-500"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-blue-600",children:[e.totalStock," adet"]})]})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"h-3 w-3 mr-1"}),"Oluşturuldu: ",$(e.createdAt)]}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)(N.A,{className:"h-3 w-3 mr-1"}),"Marka: ",e.brandName]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>G(e.id),className:"flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-3 rounded-lg text-sm font-medium transition-colors flex items-center justify-center",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-1"}),"Detay"]}),0===e.statusId&&(0,a.jsxs)(c(),{href:"/edit-product/".concat(e.id),className:"flex-1 bg-blue-100 hover:bg-blue-200 text-blue-800 py-2 px-3 rounded-lg text-sm font-medium transition-colors flex items-center justify-center",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-1"}),"D\xfczenle"]})]})]})]},e.id))}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm mt-8",children:(0,a.jsxs)("div",{className:"px-6 py-4 border-t border-gray-200 flex items-center justify-between",children:[(0,a.jsxs)("button",{onClick:()=>E(e=>Math.max(e-1,1)),disabled:1===M,className:"flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"\xd6nceki"]}),(0,a.jsxs)("span",{className:"text-sm text-gray-700",children:["Sayfa ",(0,a.jsx)("span",{className:"font-bold",children:M})]}),(0,a.jsxs)("button",{onClick:()=>E(e=>10===J.length?e+1:e),disabled:J.length<10,className:"flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:["Sonraki",(0,a.jsx)(w.A,{className:"h-4 w-4 ml-2"})]})]})})]}),D&&(0,a.jsx)(z,{productId:D,isOpen:O,onClose:()=>{R(null),T(!1)}})]}):null}},13052:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},13717:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14186:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},19946:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var a=t(12115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=r(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()},c=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let m=(0,a.forwardRef)((e,s)=>{let{color:t="currentColor",size:l=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:m="",children:o,iconNode:x,...h}=e;return(0,a.createElement)("svg",{ref:s,...d,width:l,height:l,stroke:t,strokeWidth:i?24*Number(r)/Number(l):r,className:n("lucide",m),...!o&&!c(h)&&{"aria-hidden":"true"},...h},[...x.map(e=>{let[s,t]=e;return(0,a.createElement)(s,t)}),...Array.isArray(o)?o:[o]])}),o=(e,s)=>{let t=(0,a.forwardRef)((t,r)=>{let{className:c,...d}=t;return(0,a.createElement)(m,{ref:r,iconNode:s,className:n("lucide-".concat(l(i(e))),"lucide-".concat(e),c),...d})});return t.displayName=i(e),t}},35169:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},37108:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},40646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},42355:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},47924:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},51154:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54416:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},54861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},69074:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71007:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},84616:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},92657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},98688:(e,s,t)=>{Promise.resolve().then(t.bind(t,8332))}},e=>{var s=s=>e(e.s=s);e.O(0,[6408,6874,7323,6681,6407,8441,1684,7358],()=>s(98688)),_N_E=e.O()}]);