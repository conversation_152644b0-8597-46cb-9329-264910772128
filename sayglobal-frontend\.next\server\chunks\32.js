"use strict";exports.id=32,exports.ids=[32],exports.modules={5336:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},40237:(e,t,r)=>{r.d(t,{Q6:()=>l,Z9:()=>o,e$:()=>d});var a=r(26787),c=r(59350);let n={isModalOpen:!1,selectedProductId:null,selectedVariantIndex:0,currentImageIndex:0,cachedProducts:new Map,lastFetchTime:new Map,isLoading:!1,error:null,prefetchQueue:new Set},o=(0,a.v)()((0,c.lt)((e,t)=>({...n,openModal:t=>{e({isModalOpen:!0,selectedProductId:t,selectedVariantIndex:0,currentImageIndex:0,error:null},!1,"productDetail/openModal")},closeModal:()=>{e({isModalOpen:!1,selectedProductId:null,selectedVariantIndex:0,currentImageIndex:0,error:null},!1,"productDetail/closeModal")},setSelectedVariant:t=>{e({selectedVariantIndex:t,currentImageIndex:0},!1,"productDetail/setSelectedVariant")},setCurrentImage:t=>{e({currentImageIndex:t},!1,"productDetail/setCurrentImage")},nextImage:()=>{let{selectedProductId:r,selectedVariantIndex:a,currentImageIndex:c,cachedProducts:n}=t();if(!r)return;let o=n.get(r);if(!o||!o.variants[a])return;let l=o.variants[a].images.length;l>0&&e({currentImageIndex:(c+1)%l},!1,"productDetail/nextImage")},prevImage:()=>{let{selectedProductId:r,selectedVariantIndex:a,currentImageIndex:c,cachedProducts:n}=t();if(!r)return;let o=n.get(r);if(!o||!o.variants[a])return;let l=o.variants[a].images.length;l>0&&e({currentImageIndex:(c-1+l)%l},!1,"productDetail/prevImage")},setCachedProduct:(r,a)=>{let{cachedProducts:c,lastFetchTime:n}=t(),o=new Map(c),l=new Map(n);o.set(r,a),l.set(r,Date.now()),e({cachedProducts:o,lastFetchTime:l},!1,"productDetail/setCachedProduct")},getCachedProduct:e=>{let{cachedProducts:r}=t();return r.get(e)||null},isCacheValid:(e,r=3e5)=>{let{lastFetchTime:a}=t(),c=a.get(e);return!!c&&Date.now()-c<r},clearCache:()=>{e({cachedProducts:new Map,lastFetchTime:new Map},!1,"productDetail/clearCache")},clearProductCache:r=>{let{cachedProducts:a,lastFetchTime:c}=t(),n=new Map(a),o=new Map(c);n.delete(r),o.delete(r),e({cachedProducts:n,lastFetchTime:o},!1,"productDetail/clearProductCache")},addToPrefetchQueue:r=>{let{prefetchQueue:a}=t(),c=new Set(a);c.add(r),e({prefetchQueue:c},!1,"productDetail/addToPrefetchQueue")},removeFromPrefetchQueue:r=>{let{prefetchQueue:a}=t(),c=new Set(a);c.delete(r),e({prefetchQueue:c},!1,"productDetail/removeFromPrefetchQueue")},clearPrefetchQueue:()=>{e({prefetchQueue:new Set},!1,"productDetail/clearPrefetchQueue")},setLoading:t=>{e({isLoading:t},!1,"productDetail/setLoading")},setError:t=>{e({error:t},!1,"productDetail/setError")},reset:()=>{e({...n,cachedProducts:new Map,lastFetchTime:new Map},!1,"productDetail/reset")}}),{name:"product-detail-store",enabled:!1})),l=()=>o(e=>e.selectedVariantIndex),d=()=>o(e=>e.currentImageIndex)},55245:(e,t,r)=>{r.d(t,{Bv:()=>y,E0:()=>w,Ee:()=>u,IG:()=>P,LJ:()=>f,N5:()=>p,W$:()=>g,i5:()=>m,mn:()=>h,qr:()=>I,tA:()=>i});var a=r(51423),c=r(8693),n=r(54050),o=r(64298),l=r(31212),d=r(40237),s=r(43210);let i=()=>(0,a.I)({queryKey:["adminProductStatistics"],queryFn:async()=>{let e=await o.jU.getAdminProductStatistics();if(e.success)return e.data;throw Error(e.error||"İstatistikler alınamadı")},staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0,refetchInterval:6e4}),u=()=>(0,a.I)({queryKey:["myProductStatistics"],queryFn:async()=>{let e=await o.jU.getMyProductStatistics();if(e.success)return e.data;throw Error(e.error||"İstatistikler alınamadı")},staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0,refetchInterval:6e4}),h=()=>{let e=(0,c.jE)();return(0,n.n)({mutationFn:e=>(console.log("\uD83D\uDD04 Hook: updateSimpleProduct mutation başlatılıyor..."),o.jU.updateSimpleProduct(e)),onSuccess:t=>{console.log("✅ Hook: \xdcr\xfcn başarıyla g\xfcncellendi. Cache temizleniyor..."),console.log("\uD83D\uDCC4 Hook: Success data:",t),e.invalidateQueries({queryKey:["myProducts"]}),e.invalidateQueries({queryKey:["myProductStatistics"]}),e.invalidateQueries({queryKey:["products"]})},onError:e=>{console.error("❌ Hook: \xdcr\xfcn g\xfcncelleme hatası:",e),console.error("❌ Hook: Error details:",{message:e.message,response:e.response?.data,status:e.response?.status})}})},y=(e,t)=>(0,a.I)({queryKey:["adminProducts",e,t],queryFn:()=>o.jU.getAdminProducts({pageNumber:e,pageSize:10,productName:t}),placeholderData:l.rX,staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0,refetchInterval:12e4}),m=(e,t,r)=>(0,a.I)({queryKey:["myProducts",e,t,r],queryFn:async()=>{let a={page:e,pageSize:10,name:t};void 0!==r&&-1!==r&&(a.status=r);let c=await o.jU.getMyProducts(a);if(c.success)return c.data;throw Error(c.error||"\xdcr\xfcnler alınamadı")},placeholderData:l.rX,staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0,refetchInterval:12e4}),p=e=>(0,a.I)({queryKey:["dealershipProductDetail",e],queryFn:async()=>{if(!e)return null;let t=await o.jU.getDealershipProductDetail(e);if(t.success)return t.data;throw Error(t.error||"\xdcr\xfcn detayı alınamadı")},enabled:!!e,staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0}),g=()=>{let{refreshProductLists:e}=w();return(0,n.n)({mutationFn:e=>o.jU.deleteProduct(e),onSuccess:(t,r)=>{console.log("✅ \xdcr\xfcn başarıyla silindi. Listeler g\xfcncelleniyor..."),e(),console.log(`🔄 \xdcr\xfcn ${r} silindi, cache'ler yenilendi`)},onError:e=>{console.error("❌ Silme işlemi sırasında hata:",e)}})},f=e=>{let t=(0,d.Z9)(e=>e.getCachedProduct),r=(0,d.Z9)(e=>e.isCacheValid),c=(0,d.Z9)(e=>e.setCachedProduct),n=(0,d.Z9)(e=>e.setLoading),i=(0,d.Z9)(e=>e.setError),u=(0,s.useMemo)(()=>e?t(e):null,[e,t]),h=(0,s.useMemo)(()=>!!e&&r(e),[e,r]),y=(0,a.I)({queryKey:["productDetail",e],queryFn:async()=>{if(!e)throw Error("Product ID is required");if(h&&u)return console.log("\uD83C\uDFAF Using cached product data for ID:",e),u;console.log("\uD83C\uDF10 Fetching fresh product data for ID:",e),n(!0);try{let t=await o.jU.getProductDetail(e);if(!t.success)throw Error(t.error||"\xdcr\xfcn detayı alınamadı");return c(e,t.data),i(null),t.data}catch(e){throw i(e.message),e}finally{n(!1)}},enabled:!!e,staleTime:6e5,gcTime:9e5,retry:2,retryDelay:e=>Math.min(1e3*2**e,3e4),initialData:h&&u?u:void 0,placeholderData:l.rX});return(0,s.useEffect)(()=>{y.data&&e&&!y.isError&&c(e,y.data)},[y.data,e,y.isError,c]),{...y,isCached:h&&!!u,cacheAge:0}},P=()=>{let{refreshProductLists:e,refreshProductDetail:t}=w();return(0,n.n)({mutationFn:({productId:e,isApproved:t,message:r})=>o.jU.updateProductStatus(e,t,r),onSuccess:(r,a)=>{console.log("✅ \xdcr\xfcn durumu başarıyla g\xfcncellendi. Cache temizleniyor..."),e(),t(a.productId),console.log(`🔄 \xdcr\xfcn ${a.productId} i\xe7in t\xfcm cache'ler yenilendi`)},onError:e=>{console.error("❌ \xdcr\xfcn durumu g\xfcncellenirken hata:",e)}})},I=(e,t=!0)=>(0,a.I)({queryKey:["productMessage",e],queryFn:async()=>{if(!e)return null;console.log("\uD83D\uDCDD Product message API \xe7ağrısı yapılıyor, productId:",e);let t=await o.jU.getProductMessage(e);return t.success?(console.log("✅ Product message başarıyla alındı:",t.data),t.data):(console.warn("Product message API hatası:",t.error),null)},enabled:t&&!!e,staleTime:6e4,cacheTime:3e5,retry:!1,refetchOnWindowFocus:!0}),w=()=>{let e=(0,c.jE)();return{refreshProductLists:()=>{console.log("\uD83D\uDD04 \xdcr\xfcn listeleri cache'i yenileniyor..."),e.invalidateQueries({queryKey:["adminProducts"]}),e.invalidateQueries({queryKey:["adminProductStatistics"]})},refreshProductDetail:t=>{console.log(`🔄 \xdcr\xfcn detayı cache'i yenileniyor (ID: ${t})...`),e.invalidateQueries({queryKey:["productDetail",t]}),e.invalidateQueries({queryKey:["productMessage",t]})},refreshAllProductData:()=>{console.log("\uD83D\uDD04 T\xfcm \xfcr\xfcn verileri cache'i yenileniyor..."),e.invalidateQueries({queryKey:["adminProducts"]}),e.invalidateQueries({queryKey:["adminProductStatistics"]}),e.invalidateQueries({queryKey:["productDetail"]}),e.invalidateQueries({queryKey:["productMessage"]})}}}}};