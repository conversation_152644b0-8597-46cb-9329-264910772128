(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3680],{11518:(e,t,s)=>{"use strict";e.exports=s(82269).style},60760:(e,t,s)=>{"use strict";s.d(t,{N:()=>v});var i=s(95155),n=s(12115),r=s(90869),u=s(82885),o=s(97494),a=s(80845),l=s(27351),h=s(51508);class c extends n.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,s=(0,l.s)(e)&&e.offsetWidth||0,i=this.props.sizeRef.current;i.height=t.offsetHeight||0,i.width=t.offsetWidth||0,i.top=t.offsetTop,i.left=t.offsetLeft,i.right=s-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(e){let{children:t,isPresent:s,anchorX:r}=e,u=(0,n.useId)(),o=(0,n.useRef)(null),a=(0,n.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,n.useContext)(h.Q);return(0,n.useInsertionEffect)(()=>{let{width:e,height:t,top:i,left:n,right:h}=a.current;if(s||!o.current||!e||!t)return;o.current.dataset.motionPopId=u;let c=document.createElement("style");return l&&(c.nonce=l),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(u,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===r?"left: ".concat(n):"right: ".concat(h),"px !important;\n            top: ").concat(i,"px !important;\n          }\n        ")),()=>{document.head.contains(c)&&document.head.removeChild(c)}},[s]),(0,i.jsx)(c,{isPresent:s,childRef:o,sizeRef:a,children:n.cloneElement(t,{ref:o})})}let f=e=>{let{children:t,initial:s,isPresent:r,onExitComplete:o,custom:l,presenceAffectsLayout:h,mode:c,anchorX:f}=e,m=(0,u.M)(p),y=(0,n.useId)(),g=!0,v=(0,n.useMemo)(()=>(g=!1,{id:y,initial:s,isPresent:r,custom:l,onExitComplete:e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;o&&o()},register:e=>(m.set(e,!1),()=>m.delete(e))}),[r,m,o]);return h&&g&&(v={...v}),(0,n.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[r]),n.useEffect(()=>{r||m.size||!o||o()},[r]),"popLayout"===c&&(t=(0,i.jsx)(d,{isPresent:r,anchorX:f,children:t})),(0,i.jsx)(a.t.Provider,{value:v,children:t})};function p(){return new Map}var m=s(32082);let y=e=>e.key||"";function g(e){let t=[];return n.Children.forEach(e,e=>{(0,n.isValidElement)(e)&&t.push(e)}),t}let v=e=>{let{children:t,custom:s,initial:a=!0,onExitComplete:l,presenceAffectsLayout:h=!0,mode:c="sync",propagate:d=!1,anchorX:p="left"}=e,[v,_]=(0,m.xQ)(d),S=(0,n.useMemo)(()=>g(t),[t]),b=d&&!v?[]:S.map(y),C=(0,n.useRef)(!0),w=(0,n.useRef)(S),q=(0,u.M)(()=>new Map),[O,R]=(0,n.useState)(S),[F,Q]=(0,n.useState)(S);(0,o.E)(()=>{C.current=!1,w.current=S;for(let e=0;e<F.length;e++){let t=y(F[e]);b.includes(t)?q.delete(t):!0!==q.get(t)&&q.set(t,!1)}},[F,b.length,b.join("-")]);let j=[];if(S!==O){let e=[...S];for(let t=0;t<F.length;t++){let s=F[t],i=y(s);b.includes(i)||(e.splice(t,0,s),j.push(s))}return"wait"===c&&j.length&&(e=j),Q(g(e)),R(S),null}let{forceRender:P}=(0,n.useContext)(r.L);return(0,i.jsx)(i.Fragment,{children:F.map(e=>{let t=y(e),n=(!d||!!v)&&(S===F||b.includes(t));return(0,i.jsx)(f,{isPresent:n,initial:(!C.current||!!a)&&void 0,custom:s,presenceAffectsLayout:h,mode:c,onExitComplete:n?void 0:()=>{if(!q.has(t))return;q.set(t,!0);let e=!0;q.forEach(t=>{t||(e=!1)}),e&&(null==P||P(),Q(w.current),d&&(null==_||_()),l&&l())},anchorX:p,children:e},t)})})}},65356:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},68375:()=>{},82269:(e,t,s)=>{"use strict";var i=s(49509);s(68375);var n=s(12115),r=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(n),u=void 0!==i&&i.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},a=function(){function e(e){var t=void 0===e?{}:e,s=t.name,i=void 0===s?"stylesheet":s,n=t.optimizeForSpeed,r=void 0===n?u:n;l(o(i),"`name` must be a string"),this._name=i,this._deletedRulePlaceholder="#"+i+"-deleted-rule____{}",l("boolean"==typeof r,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=r,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var a="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=a?a.getAttribute("content"):null}var t,s=e.prototype;return s.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},s.isOptimizeForSpeed=function(){return this._optimizeForSpeed},s.inject=function(){var e=this;if(l(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(u||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),s},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},s.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},s.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},s.insertRule=function(e,t){if(l(o(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var s=this.getSheet();"number"!=typeof t&&(t=s.cssRules.length);try{s.insertRule(e,t)}catch(t){return u||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var i=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,i))}return this._rulesCount++},s.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var s="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(t,e)}catch(i){u||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),s.insertRule(this._deletedRulePlaceholder,e)}}else{var i=this._tags[e];l(i,"old rule at index `"+e+"` not found"),i.textContent=t}return e},s.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];l(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},s.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},s.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,s){return s?t=t.concat(Array.prototype.map.call(e.getSheetForTag(s).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},s.makeStyleTag=function(e,t,s){t&&l(o(t),"makeStyleTag accepts only strings as second parameter");var i=document.createElement("style");this._nonce&&i.setAttribute("nonce",this._nonce),i.type="text/css",i.setAttribute("data-"+e,""),t&&i.appendChild(document.createTextNode(t));var n=document.head||document.getElementsByTagName("head")[0];return s?n.insertBefore(i,s):n.appendChild(i),i},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var s=0;s<t.length;s++){var i=t[s];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(e.prototype,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var h=function(e){for(var t=5381,s=e.length;s;)t=33*t^e.charCodeAt(--s);return t>>>0},c={};function d(e,t){if(!t)return"jsx-"+e;var s=String(t),i=e+s;return c[i]||(c[i]="jsx-"+h(e+"-"+s)),c[i]}function f(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var s=e+t;return c[s]||(c[s]=t.replace(/__jsx-style-dynamic-selector/g,e)),c[s]}var p=function(){function e(e){var t=void 0===e?{}:e,s=t.styleSheet,i=void 0===s?null:s,n=t.optimizeForSpeed,r=void 0!==n&&n;this._sheet=i||new a({name:"styled-jsx",optimizeForSpeed:r}),this._sheet.inject(),i&&"boolean"==typeof r&&(this._sheet.setOptimizeForSpeed(r),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var s=this.getIdAndRules(e),i=s.styleId,n=s.rules;if(i in this._instancesCounts){this._instancesCounts[i]+=1;return}var r=n.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[i]=r,this._instancesCounts[i]=1},t.remove=function(e){var t=this,s=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(s in this._instancesCounts,"styleId: `"+s+"` not found"),this._instancesCounts[s]-=1,this._instancesCounts[s]<1){var i=this._fromServer&&this._fromServer[s];i?(i.parentNode.removeChild(i),delete this._fromServer[s]):(this._indices[s].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[s]),delete this._instancesCounts[s]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],s=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return s[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,s;return t=this.cssRules(),void 0===(s=e)&&(s={}),t.map(function(e){var t=e[0],i=e[1];return r.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:s.nonce?s.nonce:void 0,dangerouslySetInnerHTML:{__html:i}})})},t.getIdAndRules=function(e){var t=e.children,s=e.dynamic,i=e.id;if(s){var n=d(i,s);return{styleId:n,rules:Array.isArray(t)?t.map(function(e){return f(n,e)}):[f(n,t)]}}return{styleId:d(i),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=n.createContext(null);m.displayName="StyleSheetContext";var y=r.default.useInsertionEffect||r.default.useLayoutEffect,g="undefined"!=typeof window?new p:void 0;function v(e){var t=g||n.useContext(m);return t&&("undefined"==typeof window?t.add(e):y(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}v.dynamic=function(e){return e.map(function(e){return d(e[0],e[1])}).join(" ")},t.style=v},87017:(e,t,s)=>{"use strict";s.d(t,{E:()=>m});var i=s(52020),n=s(39853),r=s(7165),u=s(25910),o=class extends u.Q{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,t,s){let r=t.queryKey,u=t.queryHash??(0,i.F$)(r,t),o=this.get(u);return o||(o=new n.X({client:e,queryKey:r,queryHash:u,options:e.defaultQueryOptions(t),state:s,defaultOptions:e.getQueryDefaults(r)}),this.add(o)),o}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#e.get(e.queryHash);t&&(e.destroy(),t===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){r.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,i.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,i.MK)(e,t)):t}notify(e){r.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){r.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){r.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},a=s(34560),l=class extends u.Q{constructor(e={}){super(),this.config=e,this.#t=new Set,this.#s=new Map,this.#i=0}#t;#s;#i;build(e,t,s){let i=new a.s({mutationCache:this,mutationId:++this.#i,options:e.defaultMutationOptions(t),state:s});return this.add(i),i}add(e){this.#t.add(e);let t=h(e);if("string"==typeof t){let s=this.#s.get(t);s?s.push(e):this.#s.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#t.delete(e)){let t=h(e);if("string"==typeof t){let s=this.#s.get(t);if(s)if(s.length>1){let t=s.indexOf(e);-1!==t&&s.splice(t,1)}else s[0]===e&&this.#s.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=h(e);if("string"!=typeof t)return!0;{let s=this.#s.get(t),i=s?.find(e=>"pending"===e.state.status);return!i||i===e}}runNext(e){let t=h(e);if("string"!=typeof t)return Promise.resolve();{let s=this.#s.get(t)?.find(t=>t!==e&&t.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){r.jG.batch(()=>{this.#t.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#t.clear(),this.#s.clear()})}getAll(){return Array.from(this.#t)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,i.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,i.nJ)(e,t))}notify(e){r.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return r.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(i.lQ))))}};function h(e){return e.options.scope?.id}var c=s(50920),d=s(21239);function f(e){return{onFetch:(t,s)=>{let n=t.options,r=t.fetchOptions?.meta?.fetchMore?.direction,u=t.state.data?.pages||[],o=t.state.data?.pageParams||[],a={pages:[],pageParams:[]},l=0,h=async()=>{let s=!1,h=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?s=!0:t.signal.addEventListener("abort",()=>{s=!0}),t.signal)})},c=(0,i.ZM)(t.options,t.fetchOptions),d=async(e,n,r)=>{if(s)return Promise.reject();if(null==n&&e.pages.length)return Promise.resolve(e);let u=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:n,direction:r?"backward":"forward",meta:t.options.meta};return h(e),e})(),o=await c(u),{maxPages:a}=t.options,l=r?i.ZZ:i.y9;return{pages:l(e.pages,o,a),pageParams:l(e.pageParams,n,a)}};if(r&&u.length){let e="backward"===r,t={pages:u,pageParams:o},s=(e?function(e,{pages:t,pageParams:s}){return t.length>0?e.getPreviousPageParam?.(t[0],t,s[0],s):void 0}:p)(n,t);a=await d(t,s,e)}else{let t=e??u.length;do{let e=0===l?o[0]??n.initialPageParam:p(n,a);if(l>0&&null==e)break;a=await d(a,e),l++}while(l<t)}return a};t.options.persister?t.fetchFn=()=>t.options.persister?.(h,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s):t.fetchFn=h}}}function p(e,{pages:t,pageParams:s}){let i=t.length-1;return t.length>0?e.getNextPageParam(t[i],t,s[i],s):void 0}var m=class{#n;#r;#u;#o;#a;#l;#h;#c;constructor(e={}){this.#n=e.queryCache||new o,this.#r=e.mutationCache||new l,this.#u=e.defaultOptions||{},this.#o=new Map,this.#a=new Map,this.#l=0}mount(){this.#l++,1===this.#l&&(this.#h=c.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#n.onFocus())}),this.#c=d.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#n.onOnline())}))}unmount(){this.#l--,0===this.#l&&(this.#h?.(),this.#h=void 0,this.#c?.(),this.#c=void 0)}isFetching(e){return this.#n.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#r.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#n.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),s=this.#n.build(this,t),n=s.state.data;return void 0===n?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime((0,i.d2)(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(n))}getQueriesData(e){return this.#n.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,s){let n=this.defaultQueryOptions({queryKey:e}),r=this.#n.get(n.queryHash),u=r?.state.data,o=(0,i.Zw)(t,u);if(void 0!==o)return this.#n.build(this,n).setData(o,{...s,manual:!0})}setQueriesData(e,t,s){return r.jG.batch(()=>this.#n.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,s)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#n.get(t.queryHash)?.state}removeQueries(e){let t=this.#n;r.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let s=this.#n;return r.jG.batch(()=>(s.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let s={revert:!0,...t};return Promise.all(r.jG.batch(()=>this.#n.findAll(e).map(e=>e.cancel(s)))).then(i.lQ).catch(i.lQ)}invalidateQueries(e,t={}){return r.jG.batch(()=>(this.#n.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let s={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(r.jG.batch(()=>this.#n.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,s);return s.throwOnError||(t=t.catch(i.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(i.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let s=this.#n.build(this,t);return s.isStaleByTime((0,i.d2)(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(i.lQ).catch(i.lQ)}fetchInfiniteQuery(e){return e.behavior=f(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(i.lQ).catch(i.lQ)}ensureInfiniteQueryData(e){return e.behavior=f(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return d.t.isOnline()?this.#r.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#n}getMutationCache(){return this.#r}getDefaultOptions(){return this.#u}setDefaultOptions(e){this.#u=e}setQueryDefaults(e,t){this.#o.set((0,i.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#o.values()],s={};return t.forEach(t=>{(0,i.Cp)(e,t.queryKey)&&Object.assign(s,t.defaultOptions)}),s}setMutationDefaults(e,t){this.#a.set((0,i.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#a.values()],s={};return t.forEach(t=>{(0,i.Cp)(e,t.mutationKey)&&Object.assign(s,t.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#u.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,i.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===i.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#u.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#n.clear(),this.#r.clear()}}}}]);