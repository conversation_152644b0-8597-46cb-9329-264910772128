(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2327],{675:(t,e,r)=>{"use strict";r.d(e,{R:()=>n});var n=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]}},2348:(t,e,r)=>{"use strict";r.d(e,{W:()=>u});var n=r(12115),o=r(52596),i=r(70788),a=["children","className"];function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var u=n.forwardRef(function(t,e){var r=t.children,u=t.className,l=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,a),s=(0,o.A)("recharts-layer",u);return n.createElement("g",c({className:s},(0,i.J9)(l,!0),{ref:e}),r)})},2494:(t,e,r)=>{"use strict";r.d(e,{s:()=>c});var n=r(3711),o=r.n(n),i=r(40139),a=r.n(i);function c(t,e,r){return!0===e?o()(t,r):a()(e)?o()(t,e):t}},3562:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return -1}},3698:(t,e,r)=>{var n=r(77969),o=r(69363);t.exports=function(t,e){return n(o(t,e),1)}},3711:(t,e,r)=>{var n=r(18028),o=r(65836);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},4217:(t,e,r)=>{var n=r(36713),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},4854:(t,e,r)=>{var n=r(67472),o=r(51911);t.exports=function(t,e,r,i){var a=r.length,c=a,u=!i;if(null==t)return!c;for(t=Object(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<c;){var s=(l=r[a])[0],f=t[s],p=l[1];if(u&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(i)var d=i(f,p,s,t,e,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},5516:(t,e,r)=>{var n=r(5658);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=+(r.size!=o),this}},5658:(t,e,r)=>{var n=r(30699);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},6305:(t,e,r)=>{var n=r(53516),o=r(22471);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},6997:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},7512:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},7548:(t,e,r)=>{var n=r(16746);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},7771:(t,e,r)=>{var n=r(31598),o=r(18686),i=r(88748);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},7985:(t,e,r)=>{t.exports="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g},8870:function(t,e,r){var n;!function(o){"use strict";var i,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},c=!0,u="[DecimalError] ",l=u+"Invalid argument: ",s=u+"Exponent out of range: ",f=Math.floor,p=Math.pow,h=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,d=f(1286742750677284.5),y={};function v(t,e){var r,n,o,i,a,u,l,s,f=t.constructor,p=f.precision;if(!t.s||!e.s)return e.s||(e=new f(t)),c?P(e,p):e;if(l=t.d,s=e.d,a=t.e,o=e.e,l=l.slice(),i=a-o){for(i<0?(n=l,i=-i,u=s.length):(n=s,o=a,u=l.length),i>(u=(a=Math.ceil(p/7))>u?a+1:u+1)&&(i=u,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((u=l.length)-(i=s.length)<0&&(i=u,n=s,s=l,l=n),r=0;i;)r=(l[--i]=l[i]+s[i]+r)/1e7|0,l[i]%=1e7;for(r&&(l.unshift(r),++o),u=l.length;0==l[--u];)l.pop();return e.d=l,e.e=o,c?P(e,p):e}function m(t,e,r){if(t!==~~t||t<e||t>r)throw Error(l+t)}function b(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=j(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=j(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}y.absoluteValue=y.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},y.comparedTo=y.cmp=function(t){var e,r,n,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(o=t.d.length)?n:o;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},y.dividedBy=y.div=function(t){return g(this,new this.constructor(t))},y.dividedToIntegerBy=y.idiv=function(t){var e=this.constructor;return P(g(this,new e(t),0,1),e.precision)},y.equals=y.eq=function(t){return!this.cmp(t)},y.exponent=function(){return O(this)},y.greaterThan=y.gt=function(t){return this.cmp(t)>0},y.greaterThanOrEqualTo=y.gte=function(t){return this.cmp(t)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(t){return 0>this.cmp(t)},y.lessThanOrEqualTo=y.lte=function(t){return 1>this.cmp(t)},y.logarithm=y.log=function(t){var e,r=this.constructor,n=r.precision,o=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(i))throw Error(u+"NaN");if(this.s<1)throw Error(u+(this.s?"NaN":"-Infinity"));return this.eq(i)?new r(0):(c=!1,e=g(S(this,o),S(t,o),o),c=!0,P(e,n))},y.minus=y.sub=function(t){return t=new this.constructor(t),this.s==t.s?E(this,t):v(this,(t.s=-t.s,t))},y.modulo=y.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(u+"NaN");return this.s?(c=!1,e=g(this,t,0,1).times(t),c=!0,this.minus(e)):P(new r(this),n)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return S(this)},y.negated=y.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},y.plus=y.add=function(t){return t=new this.constructor(t),this.s==t.s?v(this,t):E(this,(t.s=-t.s,t))},y.precision=y.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(l+t);if(e=O(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},y.squareRoot=y.sqrt=function(){var t,e,r,n,o,i,a,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(u+"NaN")}for(t=O(this),c=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=b(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=f((t+1)/2)-(t<0||t%2),n=new l(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new l(o.toString()),o=a=(r=l.precision)+3;;)if(n=(i=n).plus(g(this,i,a+2)).times(.5),b(i.d).slice(0,a)===(e=b(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(P(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=e)break;a+=4}return c=!0,P(n,r)},y.times=y.mul=function(t){var e,r,n,o,i,a,u,l,s,f=this.constructor,p=this.d,h=(t=new f(t)).d;if(!this.s||!t.s)return new f(0);for(t.s*=this.s,r=this.e+t.e,(l=p.length)<(s=h.length)&&(i=p,p=h,h=i,a=l,l=s,s=a),i=[],n=a=l+s;n--;)i.push(0);for(n=s;--n>=0;){for(e=0,o=l+n;o>n;)u=i[o]+h[n]*p[o-n-1]+e,i[o--]=u%1e7|0,e=u/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,c?P(t,f.precision):t},y.toDecimalPlaces=y.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(m(t,0,1e9),void 0===e?e=n.rounding:m(e,0,8),P(r,t+O(r)+1,e))},y.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=k(n,!0):(m(t,0,1e9),void 0===e?e=o.rounding:m(e,0,8),r=k(n=P(new o(n),t+1,e),!0,t+1)),r},y.toFixed=function(t,e){var r,n,o=this.constructor;return void 0===t?k(this):(m(t,0,1e9),void 0===e?e=o.rounding:m(e,0,8),r=k((n=P(new o(this),t+O(this)+1,e)).abs(),!1,t+O(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var t=this.constructor;return P(new t(this),O(this)+1,t.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(t){var e,r,n,o,a,l,s=this,p=s.constructor,h=+(t=new p(t));if(!t.s)return new p(i);if(!(s=new p(s)).s){if(t.s<1)throw Error(u+"Infinity");return s}if(s.eq(i))return s;if(n=p.precision,t.eq(i))return P(s,n);if(l=(e=t.e)>=(r=t.d.length-1),a=s.s,l){if((r=h<0?-h:h)<=0x1fffffffffffff){for(o=new p(i),e=Math.ceil(n/7+4),c=!1;r%2&&M((o=o.times(s)).d,e),0!==(r=f(r/2));)M((s=s.times(s)).d,e);return c=!0,t.s<0?new p(i).div(o):P(o,n)}}else if(a<0)throw Error(u+"NaN");return a=a<0&&1&t.d[Math.max(e,r)]?-1:1,s.s=1,c=!1,o=t.times(S(s,n+12)),c=!0,(o=x(o)).s=a,o},y.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?(r=O(o),n=k(o,r<=i.toExpNeg||r>=i.toExpPos)):(m(t,1,1e9),void 0===e?e=i.rounding:m(e,0,8),r=O(o=P(new i(o),t,e)),n=k(o,t<=r||r<=i.toExpNeg,t)),n},y.toSignificantDigits=y.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(m(t,1,1e9),void 0===e?e=r.rounding:m(e,0,8)),P(new r(this),t,e)},y.toString=y.valueOf=y.val=y.toJSON=function(){var t=O(this),e=this.constructor;return k(this,t<=e.toExpNeg||t>=e.toExpPos)};var g=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var c,l,s,f,p,h,d,y,v,m,b,g,x,w,j,S,A,E,k=n.constructor,M=n.s==o.s?1:-1,_=n.d,T=o.d;if(!n.s)return new k(n);if(!o.s)throw Error(u+"Division by zero");for(s=0,l=n.e-o.e,A=T.length,j=_.length,y=(d=new k(M)).d=[];T[s]==(_[s]||0);)++s;if(T[s]>(_[s]||0)&&--l,(g=null==i?i=k.precision:a?i+(O(n)-O(o))+1:i)<0)return new k(0);if(g=g/7+2|0,s=0,1==A)for(f=0,T=T[0],g++;(s<j||f)&&g--;s++)x=1e7*f+(_[s]||0),y[s]=x/T|0,f=x%T|0;else{for((f=1e7/(T[0]+1)|0)>1&&(T=t(T,f),_=t(_,f),A=T.length,j=_.length),w=A,m=(v=_.slice(0,A)).length;m<A;)v[m++]=0;(E=T.slice()).unshift(0),S=T[0],T[1]>=1e7/2&&++S;do f=0,(c=e(T,v,A,m))<0?(b=v[0],A!=m&&(b=1e7*b+(v[1]||0)),(f=b/S|0)>1?(f>=1e7&&(f=1e7-1),h=(p=t(T,f)).length,m=v.length,1==(c=e(p,v,h,m))&&(f--,r(p,A<h?E:T,h))):(0==f&&(c=f=1),p=T.slice()),(h=p.length)<m&&p.unshift(0),r(v,p,m),-1==c&&(m=v.length,(c=e(T,v,A,m))<1&&(f++,r(v,A<m?E:T,m))),m=v.length):0===c&&(f++,v=[0]),y[s++]=f,c&&v[0]?v[m++]=_[w]||0:(v=[_[w]],m=1);while((w++<j||void 0!==v[0])&&g--)}return y[0]||y.shift(),d.e=l,P(d,a?i+O(d)+1:i)}}();function x(t,e){var r,n,o,a,u,l=0,f=0,h=t.constructor,d=h.precision;if(O(t)>16)throw Error(s+O(t));if(!t.s)return new h(i);for(null==e?(c=!1,u=d):u=e,a=new h(.03125);t.abs().gte(.1);)t=t.times(a),f+=5;for(u+=Math.log(p(2,f))/Math.LN10*2+5|0,r=n=o=new h(i),h.precision=u;;){if(n=P(n.times(t),u),r=r.times(++l),b((a=o.plus(g(n,r,u))).d).slice(0,u)===b(o.d).slice(0,u)){for(;f--;)o=P(o.times(o),u);return h.precision=d,null==e?(c=!0,P(o,d)):o}o=a}}function O(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function w(t,e,r){if(e>t.LN10.sd())throw c=!0,r&&(t.precision=r),Error(u+"LN10 precision limit exceeded");return P(new t(t.LN10),e)}function j(t){for(var e="";t--;)e+="0";return e}function S(t,e){var r,n,o,a,l,s,f,p,h,d=1,y=t,v=y.d,m=y.constructor,x=m.precision;if(y.s<1)throw Error(u+(y.s?"NaN":"-Infinity"));if(y.eq(i))return new m(0);if(null==e?(c=!1,p=x):p=e,y.eq(10))return null==e&&(c=!0),w(m,p);if(m.precision=p+=10,n=(r=b(v)).charAt(0),!(15e14>Math.abs(a=O(y))))return f=w(m,p+2,x).times(a+""),y=S(new m(n+"."+r.slice(1)),p-10).plus(f),m.precision=x,null==e?(c=!0,P(y,x)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=b((y=y.times(t)).d)).charAt(0),d++;for(a=O(y),n>1?(y=new m("0."+r),a++):y=new m(n+"."+r.slice(1)),s=l=y=g(y.minus(i),y.plus(i),p),h=P(y.times(y),p),o=3;;){if(l=P(l.times(h),p),b((f=s.plus(g(l,new m(o),p))).d).slice(0,p)===b(s.d).slice(0,p))return s=s.times(2),0!==a&&(s=s.plus(w(m,p+2,x).times(a+""))),s=g(s,new m(d),p),m.precision=x,null==e?(c=!0,P(s,x)):s;s=f,o+=2}}function A(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,t.e=f((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),c&&(t.e>d||t.e<-d))throw Error(s+r)}else t.s=0,t.e=0,t.d=[0];return t}function P(t,e,r){var n,o,i,a,u,l,h,y,v=t.d;for(a=1,i=v[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,h=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(i=v.length))return t;for(a=1,h=i=v[y];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(u=h/(i=p(10,a-o-1))%10|0,l=e<0||void 0!==v[y+1]||h%i,l=r<4?(u||l)&&(0==r||r==(t.s<0?3:2)):u>5||5==u&&(4==r||l||6==r&&(n>0?o>0?h/p(10,a-o):0:v[y-1])%10&1||r==(t.s<0?8:7))),e<1||!v[0])return l?(i=O(t),v.length=1,e=e-i-1,v[0]=p(10,(7-e%7)%7),t.e=f(-e/7)||0):(v.length=1,v[0]=t.e=t.s=0),t;if(0==n?(v.length=y,i=1,y--):(v.length=y+1,i=p(10,7-n),v[y]=o>0?(h/p(10,a-o)%p(10,o)|0)*i:0),l)for(;;)if(0==y){1e7==(v[0]+=i)&&(v[0]=1,++t.e);break}else{if(v[y]+=i,1e7!=v[y])break;v[y--]=0,i=1}for(n=v.length;0===v[--n];)v.pop();if(c&&(t.e>d||t.e<-d))throw Error(s+O(t));return t}function E(t,e){var r,n,o,i,a,u,l,s,f,p,h=t.constructor,d=h.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new h(t),c?P(e,d):e;if(l=t.d,p=e.d,n=e.e,s=t.e,l=l.slice(),a=s-n){for((f=a<0)?(r=l,a=-a,u=p.length):(r=p,n=s,u=l.length),a>(o=Math.max(Math.ceil(d/7),u)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((f=(o=l.length)<(u=p.length))&&(u=o),o=0;o<u;o++)if(l[o]!=p[o]){f=l[o]<p[o];break}a=0}for(f&&(r=l,l=p,p=r,e.s=-e.s),u=l.length,o=p.length-u;o>0;--o)l[u++]=0;for(o=p.length;o>a;){if(l[--o]<p[o]){for(i=o;i&&0===l[--i];)l[i]=1e7-1;--l[i],l[o]+=1e7}l[o]-=p[o]}for(;0===l[--u];)l.pop();for(;0===l[0];l.shift())--n;return l[0]?(e.d=l,e.e=n,c?P(e,d):e):new h(0)}function k(t,e,r){var n,o=O(t),i=b(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+j(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+j(-o-1)+i,r&&(n=r-a)>0&&(i+=j(n))):o>=a?(i+=j(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+j(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=j(n))),t.s<0?"-"+i:i}function M(t,e){if(t.length>e)return t.length=e,!0}function _(t){if(!t||"object"!=typeof t)throw Error(u+"Object expected");var e,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]]))if(f(n)===n&&n>=o[e+1]&&n<=o[e+2])this[r]=n;else throw Error(l+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(l+r+": "+n);return this}(a=function t(e){var r,n,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(l+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return A(this,t.toString())}if("string"!=typeof t)throw Error(l+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,h.test(t))A(this,t);else throw Error(l+t)}if(i.prototype=y,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=_,void 0===e&&(e={}),e)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}(a)).default=a.Decimal=a,i=new a(1),void 0===(n=(function(){return a}).call(e,r,e,t))||(t.exports=n)}(0)},9557:(t,e,r)=>{"use strict";r.d(e,{Ay:()=>tS});var n=r(12115),o=r(38637),i=r.n(o),a=Object.getOwnPropertyNames,c=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty;function l(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function s(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var c=t(e,r,n);return o.delete(e),o.delete(r),c}}function f(t){return a(t).concat(c(t))}var p=Object.hasOwn||function(t,e){return u.call(t,e)};function h(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var d=Object.getOwnPropertyDescriptor,y=Object.keys;function v(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function m(t,e){return h(t.getTime(),e.getTime())}function b(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function g(t,e){return t===e}function x(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,h=o.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}function O(t,e,r){var n=y(t),o=n.length;if(y(e).length!==o)return!1;for(;o-- >0;)if(!k(t,e,r,n[o]))return!1;return!0}function w(t,e,r){var n,o,i,a=f(t),c=a.length;if(f(e).length!==c)return!1;for(;c-- >0;)if(!k(t,e,r,n=a[c])||(o=d(t,n),i=d(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function j(t,e){return h(t.valueOf(),e.valueOf())}function S(t,e){return t.source===e.source&&t.flags===e.flags}function A(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),l=!1,s=0;(o=u.next())&&!o.done;){if(!a[s]&&r.equals(n.value,o.value,n.value,o.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function P(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function E(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function k(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||p(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var M=Array.isArray,_="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,T=Object.assign,C=Object.prototype.toString.call.bind(Object.prototype.toString),D=I();function I(t){void 0===t&&(t={});var e,r,n,o,i,a,c,u,f,p,d,y,k,D=t.circular,I=t.createInternalComparator,N=t.createState,B=t.strict,R=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?w:v,areDatesEqual:m,areErrorsEqual:b,areFunctionsEqual:g,areMapsEqual:n?l(x,w):x,areNumbersEqual:h,areObjectsEqual:n?w:O,arePrimitiveWrappersEqual:j,areRegExpsEqual:S,areSetsEqual:n?l(A,w):A,areTypedArraysEqual:n?w:P,areUrlsEqual:E};if(r&&(o=T({},o,r(o))),e){var i=s(o.areArraysEqual),a=s(o.areMapsEqual),c=s(o.areObjectsEqual),u=s(o.areSetsEqual);o=T({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return o}(t)).areArraysEqual,n=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,f=e.arePrimitiveWrappersEqual,p=e.areRegExpsEqual,d=e.areSetsEqual,y=e.areTypedArraysEqual,k=e.areUrlsEqual,function(t,e,l){if(t===e)return!0;if(null==t||null==e)return!1;var s=typeof t;if(s!==typeof e)return!1;if("object"!==s)return"number"===s?c(t,e,l):"function"===s&&i(t,e,l);var h=t.constructor;if(h!==e.constructor)return!1;if(h===Object)return u(t,e,l);if(M(t))return r(t,e,l);if(null!=_&&_(t))return y(t,e,l);if(h===Date)return n(t,e,l);if(h===RegExp)return p(t,e,l);if(h===Map)return a(t,e,l);if(h===Set)return d(t,e,l);var v=C(t);return"[object Date]"===v?n(t,e,l):"[object RegExp]"===v?p(t,e,l):"[object Map]"===v?a(t,e,l):"[object Set]"===v?d(t,e,l):"[object Object]"===v?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,l):"[object URL]"===v?k(t,e,l):"[object Error]"===v?o(t,e,l):"[object Arguments]"===v?u(t,e,l):("[object Boolean]"===v||"[object Number]"===v||"[object String]"===v)&&f(t,e,l)}),L=I?I(R):function(t,e,r,n,o,i,a){return R(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:o,meta:c.meta,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:void 0!==D&&D,comparator:R,createState:N,equals:L,strict:void 0!==B&&B})}function N(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>e)t(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function B(t){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function R(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function L(t){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function z(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function U(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?z(Object(r),!0).forEach(function(e){F(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function F(t,e,r){var n;return(n=function(t,e){if("object"!==L(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==L(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===L(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}I({strict:!0}),I({circular:!0}),I({circular:!0,strict:!0}),I({createInternalComparator:function(){return h}}),I({strict:!0,createInternalComparator:function(){return h}}),I({circular:!0,createInternalComparator:function(){return h}}),I({circular:!0,createInternalComparator:function(){return h},strict:!0});var W=function(t){return t},$=function(t,e){return Object.keys(e).reduce(function(r,n){return U(U({},r),{},F({},n,t(n,e[n])))},{})},q=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},X=function(t,e,r,n,o,i,a,c){};function H(t,e){if(t){if("string"==typeof t)return V(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return V(t,e)}}function V(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var G=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},Y=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},K=function(t,e){return function(r){return Y(G(t,e),r)}},J=function(){for(var t,e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,c=1,u=1;break;case"ease":i=.25,a=.1,c=.25,u=1;break;case"ease-in":i=.42,a=0,c=1,u=1;break;case"ease-out":i=.42,a=0,c=.58,u=1;break;case"ease-in-out":i=0,a=0,c=.58,u=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(s,4)||H(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],c=f[2],u=f[3]}else X(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}X([i,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=K(i,c),h=K(a,u),d=(t=i,e=c,function(r){var n;return Y([].concat(function(t){if(Array.isArray(t))return V(t)}(n=G(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||H(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o,i=p(r)-e,a=d(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return h(r)};return y.isStepper=!1,y},Z=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,c=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(i)?[e,0]:[c,i]};return c.isStepper=!0,c.dt=a,c},Q=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return J(n);case"spring":return Z();default:if("cubic-bezier"===n.split("(")[0])return J(n);X(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(X(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function tt(t){return(tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function te(t){return function(t){if(Array.isArray(t))return ta(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ti(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tr(Object(r),!0).forEach(function(e){to(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tr(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function to(t,e,r){var n;return(n=function(t,e){if("object"!==tt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==tt(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===tt(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ti(t,e){if(t){if("string"==typeof t)return ta(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ta(t,e)}}function ta(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tc=function(t,e,r){return t+(e-t)*r},tu=function(t){return t.from!==t.to},tl=function t(e,r,n){var o=$(function(t,r){if(tu(r)){var n,o=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(n,2)||ti(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return tn(tn({},r),{},{from:i,velocity:a})}return r},r);return n<1?$(function(t,e){return tu(e)?tn(tn({},e),{},{velocity:tc(e.velocity,o[t].velocity,n),from:tc(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};let ts=function(t,e,r,n,o){var i,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return tn(tn({},r),{},to({},n,[t[n],e[n]]))},{}),l=c.reduce(function(r,n){return tn(tn({},r),{},to({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;l=tl(r,l,a),o(tn(tn(tn({},t),e),$(function(t,e){return e.from},l))),i=n,Object.values(l).filter(tu).length&&(s=requestAnimationFrame(f))}:function(i){a||(a=i);var c=(i-a)/n,l=$(function(t,e){return tc.apply(void 0,te(e).concat([r(c)]))},u);if(o(tn(tn(tn({},t),e),l)),c<1)s=requestAnimationFrame(f);else{var p=$(function(t,e){return tc.apply(void 0,te(e).concat([r(1)]))},u);o(tn(tn(tn({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function tf(t){return(tf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tp=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function th(t){return function(t){if(Array.isArray(t))return td(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return td(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return td(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function td(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ty(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ty(Object(r),!0).forEach(function(e){tm(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ty(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tm(t,e,r){return(e=tb(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tb(t){var e=function(t,e){if("object"!==tf(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==tf(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===tf(e)?e:String(e)}function tg(t,e){return(tg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tx(t,e){if(e&&("object"===tf(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return tO(t)}function tO(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function tw(t){return(tw=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var tj=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");i.prototype=Object.create(t&&t.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),t&&tg(i,t);var e,r,o=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=tw(i);return t=e?Reflect.construct(r,arguments,tw(this).constructor):r.apply(this,arguments),tx(this,t)});function i(t,e){if(!(this instanceof i))throw TypeError("Cannot call a class as a function");var r=o.call(this,t,e),n=r.props,a=n.isActive,c=n.attributeName,u=n.from,l=n.to,s=n.steps,f=n.children,p=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(tO(r)),r.changeStyle=r.changeStyle.bind(tO(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),tx(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},tx(r);r.state={style:c?tm({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var l={style:o?tm({},o,a):a};this.state&&u&&(o&&u[o]!==a||!o&&u!==a)&&this.setState(l);return}if(!D(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?c:t.to;if(this.state&&u){var p={style:o?tm({},o,f):f};(o&&u[o]!==f||!o&&u!==f)&&this.setState(p)}this.runAnimation(tv(tv({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=ts(r,n,Q(i),o,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},o,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,c=i.duration;return this.manager.start([o].concat(th(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(th(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:i,easing:c}),i]);var h=q(p,i,c),d=tv(tv(tv({},f.style),u),{},{transition:h});return[].concat(th(t),[d,i,s]).filter(W)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=(r=function(){return null},n=!1,o=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var o=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return R(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return R(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);return"number"==typeof i?void N(t.bind(null,a),i):(t(i),void N(t.bind(null,a)))}"object"===B(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,o(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}}));var e,r,n,o,i=t.begin,a=t.duration,c=t.attributeName,u=t.to,l=t.easing,s=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,h=t.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof h||"spring"===l)return void this.runJSAnimation(t);if(p.length>1)return void this.runStepAnimation(t);var y=c?tm({},c,u):u,v=q(Object.keys(y),a,l);d.start([s,i,tv(tv({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),o=(t.attributeName,t.easing,t.isActive),i=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tp)),a=n.Children.count(e),c=this.state.style;if("function"==typeof e)return e(c);if(!o||0===a||r<=0)return e;var u=function(t){var e=t.props,r=e.style,o=e.className;return(0,n.cloneElement)(t,tv(tv({},i),{},{style:tv(tv({},void 0===r?{}:r),c),className:o}))};return 1===a?u(n.Children.only(e)):n.createElement("div",null,n.Children.map(e,function(t){return u(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tb(n.key),n)}}(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.PureComponent);tj.displayName="Animate",tj.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},tj.propTypes={from:i().oneOfType([i().object,i().string]),to:i().oneOfType([i().object,i().string]),attributeName:i().string,duration:i().number,begin:i().number,easing:i().oneOfType([i().string,i().func]),steps:i().arrayOf(i().shape({duration:i().number.isRequired,style:i().object.isRequired,easing:i().oneOfType([i().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),i().func]),properties:i().arrayOf("string"),onAnimationEnd:i().func})),children:i().oneOfType([i().node,i().func]),isActive:i().bool,canBegin:i().bool,onAnimationEnd:i().func,shouldReAnimate:i().bool,onAnimationStart:i().func,onAnimationReStart:i().func};let tS=tj},9699:(t,e,r)=>{var n=r(11011);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,c=i.length,u=r.length;++o<c;){var l=n(i[o],a[o]);if(l){if(o>=u)return l;return l*("desc"==r[o]?-1:1)}}return t.index-e.index}},9795:(t,e,r)=>{"use strict";r.d(e,{i:()=>D});var n=r(12115),o=r(23633),i=r.n(o);let a=Math.cos,c=Math.sin,u=Math.sqrt,l=Math.PI,s=2*l,f={draw(t,e){let r=u(e/l);t.moveTo(r,0),t.arc(0,0,r,0,s)}},p=u(1/3),h=2*p,d=c(l/10)/c(7*l/10),y=c(s/10)*d,v=-a(s/10)*d,m=u(3),b=u(3)/2,g=1/u(12),x=(g/2+1)*3;var O=r(85654),w=r(31847);u(3),u(3);var j=r(52596),S=r(70788);function A(t){return(A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var P=["type","size","sizeType"];function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function M(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=A(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=A(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==A(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var _={symbolCircle:f,symbolCross:{draw(t,e){let r=u(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=u(e/h),n=r*p;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=u(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=u(.8908130915292852*e),n=y*r,o=v*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){let i=s*e/5,u=a(i),l=c(i);t.lineTo(l*r,-u*r),t.lineTo(u*n-l*o,l*n+u*o)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-u(e/(3*m));t.moveTo(0,2*r),t.lineTo(-m*r,-r),t.lineTo(m*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=u(e/x),n=r/2,o=r*g,i=r*g+r,a=-n;t.moveTo(n,o),t.lineTo(n,i),t.lineTo(a,i),t.lineTo(-.5*n-b*o,b*n+-.5*o),t.lineTo(-.5*n-b*i,b*n+-.5*i),t.lineTo(-.5*a-b*i,b*a+-.5*i),t.lineTo(-.5*n+b*o,-.5*o-b*n),t.lineTo(-.5*n+b*i,-.5*i-b*n),t.lineTo(-.5*a+b*i,-.5*i-b*a),t.closePath()}}},T=Math.PI/180,C=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*T;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},D=function(t){var e,r=t.type,o=void 0===r?"circle":r,a=t.size,c=void 0===a?64:a,u=t.sizeType,l=void 0===u?"area":u,s=M(M({},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,P)),{},{type:o,size:c,sizeType:l}),p=s.className,h=s.cx,d=s.cy,y=(0,S.J9)(s,!0);return h===+h&&d===+d&&c===+c?n.createElement("path",E({},y,{className:(0,j.A)("recharts-symbols",p),transform:"translate(".concat(h,", ").concat(d,")"),d:(e=_["symbol".concat(i()(o))]||f,(function(t,e){let r=null,n=(0,w.i)(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:(0,O.A)(t||f),e="function"==typeof e?e:(0,O.A)(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:(0,O.A)(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:(0,O.A)(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o})().type(e).size(C(c,l,o))())})):null};D.registerSymbol=function(t,e){_["symbol".concat(i()(t))]=e}},9813:(t,e,r)=>{var n=r(22143),o=r(48611),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable;t.exports=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!c.call(t,"callee")}},9819:(t,e,r)=>{"use strict";function n(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}r.d(e,{A:()=>n}),Array.prototype.slice},10537:(t,e,r)=>{var n=r(96540),o=r(31598),i=r(18686);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},11011:(t,e,r)=>{var n=r(70771);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),c=void 0!==e,u=null===e,l=e==e,s=n(e);if(!u&&!s&&!a&&t>e||a&&c&&l&&!u&&!s||o&&c&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||u&&r&&i||!c&&i||!l)return -1}return 0}},11670:(t,e,r)=>{var n=r(79401),o=r(9813),i=r(39608),a=r(33497),c=r(99544),u=r(35190),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),s=!r&&o(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&u(t),h=r||s||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)(e||l.call(t,v))&&!(h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&d.push(v);return d}},12486:t=>{t.exports=function(t){return t!=t}},12814:(t,e,r)=>{"use strict";r.d(e,{s0:()=>n5,gH:()=>n0,YB:()=>oo,HQ:()=>or,xi:()=>oi,Hj:()=>og,BX:()=>n3,tA:()=>n2,DW:()=>oh,y2:()=>op,nb:()=>of,PW:()=>n7,Ay:()=>nQ,vf:()=>n9,Mk:()=>oy,Ps:()=>n1,Mn:()=>ol,kA:()=>od,Rh:()=>ot,w7:()=>os,zb:()=>oO,kr:()=>nZ,_L:()=>n4,KC:()=>ox,A1:()=>n8,W7:()=>on,AQ:()=>ob,_f:()=>oa});var n,o,i,a,c,u,l,s={};r.r(s),r.d(s,{scaleBand:()=>f.A,scaleDiverging:()=>function t(){var e=tN(rZ()(tv));return e.copy=function(){return rY(e,t())},tj.K.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=tq(rZ()).domain([.1,1,10]);return e.copy=function(){return rY(e,t()).base(e.base())},tj.K.apply(e,arguments)},scaleDivergingPow:()=>rQ,scaleDivergingSqrt:()=>r0,scaleDivergingSymlog:()=>function t(){var e=tV(rZ());return e.copy=function(){return rY(e,t()).constant(e.constant())},tj.K.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,td),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,td):[0,1],tN(n)},scaleImplicit:()=>tG.h,scaleLinear:()=>tB,scaleLog:()=>function t(){let e=tq(tO()).domain([1,10]);return e.copy=()=>tx(e,t()).base(e.base()),tj.C.apply(e,arguments),e},scaleOrdinal:()=>tG.A,scalePoint:()=>f.z,scalePow:()=>tQ,scaleQuantile:()=>function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,r=j){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(r(t[i+1],i+1,t)-a)*(o-i)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[A(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(g),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},tj.C.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[A(i,t,0,o)]:e}function u(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},c.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return i.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},tj.C.apply(tN(c),arguments)},scaleRadial:()=>function t(){var e,r=tw(),n=[0,1],o=!1;function i(t){var n,i=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return r.invert(t1(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,td)).map(t1)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},tj.C.apply(i,arguments),tN(i)},scaleSequential:()=>function t(){var e=tN(rG()(tv));return e.copy=function(){return rY(e,t())},tj.K.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=tq(rG()).domain([1,10]);return e.copy=function(){return rY(e,t()).base(e.base())},tj.K.apply(e,arguments)},scaleSequentialPow:()=>rK,scaleSequentialQuantile:()=>function t(){var e=[],r=tv;function n(t){if(null!=t&&!isNaN(t*=1))return r((A(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(g),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return t3(t);if(e>=1)return t2(t);var n,o=(n-1)*e,i=Math.floor(o),a=t2((function t(e,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=r&&r<=o))return e;for(i=void 0===i?t5:function(t=g){if(t===g)return t5;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(i);o>n;){if(o-n>600){let a=o-n+1,c=r-n+1,u=Math.log(a),l=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+s)),p=Math.min(o,Math.floor(r+(a-c)*l/a+s));t(e,r,f,p,i)}let a=e[r],c=n,u=o;for(t6(e,n,r),i(e[o],a)>0&&t6(e,n,o);c<u;){for(t6(e,c,u),++c,--u;0>i(e[c],a);)++c;for(;i(e[u],a)>0;)--u}0===i(e[n],a)?t6(e,n,u):t6(e,++u,o),u<=r&&(n=u+1),r<=u&&(o=u-1)}return e})(t,i).subarray(0,i+1));return a+(t3(t.subarray(i+1))-a)*(o-i)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},tj.K.apply(n,arguments)},scaleSequentialSqrt:()=>rJ,scaleSequentialSymlog:()=>function t(){var e=tV(rG());return e.copy=function(){return rY(e,t()).constant(e.constant())},tj.K.apply(e,arguments)},scaleSqrt:()=>t0,scaleSymlog:()=>function t(){var e=tV(tO());return e.copy=function(){return tx(e,t()).constant(e.constant())},tj.C.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[A(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(o=Math.min((r=Array.from(t)).length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},tj.C.apply(i,arguments)},scaleTime:()=>rH,scaleUtc:()=>rV,tickFormat:()=>tI});var f=r(81519);let p=Math.sqrt(50),h=Math.sqrt(10),d=Math.sqrt(2);function y(t,e,r){let n,o,i,a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),u=a/Math.pow(10,c),l=u>=p?10:u>=h?5:u>=d?2:1;return(c<0?(n=Math.round(t*(i=Math.pow(10,-c)/l)),o=Math.round(e*i),n/i<t&&++n,o/i>e&&--o,i=-i):(n=Math.round(t/(i=Math.pow(10,c)*l)),o=Math.round(e/i),n*i<t&&++n,o*i>e&&--o),o<n&&.5<=r&&r<2)?y(t,e,2*r):[n,o,i]}function v(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[o,i,a]=n?y(e,t,r):y(t,e,r);if(!(i>=o))return[];let c=i-o+1,u=Array(c);if(n)if(a<0)for(let t=0;t<c;++t)u[t]=-((i-t)/a);else for(let t=0;t<c;++t)u[t]=(i-t)*a;else if(a<0)for(let t=0;t<c;++t)u[t]=-((o+t)/a);else for(let t=0;t<c;++t)u[t]=(o+t)*a;return u}function m(t,e,r){return y(t*=1,e*=1,r*=1)[2]}function b(t,e,r){e*=1,t*=1,r*=1;let n=e<t,o=n?m(e,t,r):m(t,e,r);return(n?-1:1)*(o<0?-(1/o):o)}function g(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function x(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function O(t){let e,r,n;function o(t,n,i=0,a=t.length){if(i<a){if(0!==e(n,n))return a;do{let e=i+a>>>1;0>r(t[e],n)?i=e+1:a=e}while(i<a)}return i}return 2!==t.length?(e=g,r=(e,r)=>g(t(e),r),n=(e,r)=>t(e)-r):(e=t===g||t===x?t:w,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){let a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>=r(t[e],n)?o=e+1:i=e}while(o<i)}return o}}}function w(){return 0}function j(t){return null===t?NaN:+t}let S=O(g),A=S.right;function P(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function E(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function k(){}S.left,O(j).center;var M="\\s*([+-]?\\d+)\\s*",_="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",T="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",C=/^#([0-9a-f]{3,8})$/,D=RegExp(`^rgb\\(${M},${M},${M}\\)$`),I=RegExp(`^rgb\\(${T},${T},${T}\\)$`),N=RegExp(`^rgba\\(${M},${M},${M},${_}\\)$`),B=RegExp(`^rgba\\(${T},${T},${T},${_}\\)$`),R=RegExp(`^hsl\\(${_},${T},${T}\\)$`),L=RegExp(`^hsla\\(${_},${T},${T},${_}\\)$`),z={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function U(){return this.rgb().formatHex()}function F(){return this.rgb().formatRgb()}function W(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=C.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?$(e):3===r?new H(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?q(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?q(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=D.exec(t))?new H(e[1],e[2],e[3],1):(e=I.exec(t))?new H(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=N.exec(t))?q(e[1],e[2],e[3],e[4]):(e=B.exec(t))?q(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=R.exec(t))?Z(e[1],e[2]/100,e[3]/100,1):(e=L.exec(t))?Z(e[1],e[2]/100,e[3]/100,e[4]):z.hasOwnProperty(t)?$(z[t]):"transparent"===t?new H(NaN,NaN,NaN,0):null}function $(t){return new H(t>>16&255,t>>8&255,255&t,1)}function q(t,e,r,n){return n<=0&&(t=e=r=NaN),new H(t,e,r,n)}function X(t,e,r,n){var o;return 1==arguments.length?((o=t)instanceof k||(o=W(o)),o)?new H((o=o.rgb()).r,o.g,o.b,o.opacity):new H:new H(t,e,r,null==n?1:n)}function H(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function V(){return`#${J(this.r)}${J(this.g)}${J(this.b)}`}function G(){let t=Y(this.opacity);return`${1===t?"rgb(":"rgba("}${K(this.r)}, ${K(this.g)}, ${K(this.b)}${1===t?")":`, ${t})`}`}function Y(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function K(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function J(t){return((t=K(t))<16?"0":"")+t.toString(16)}function Z(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new tt(t,e,r,n)}function Q(t){if(t instanceof tt)return new tt(t.h,t.s,t.l,t.opacity);if(t instanceof k||(t=W(t)),!t)return new tt;if(t instanceof tt)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,c=i-o,u=(i+o)/2;return c?(a=e===i?(r-n)/c+(r<n)*6:r===i?(n-e)/c+2:(e-r)/c+4,c/=u<.5?i+o:2-i-o,a*=60):c=u>0&&u<1?0:a,new tt(a,c,u,t.opacity)}function tt(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function te(t){return(t=(t||0)%360)<0?t+360:t}function tr(t){return Math.max(0,Math.min(1,t||0))}function tn(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function to(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}P(k,W,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:U,formatHex:U,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Q(this).formatHsl()},formatRgb:F,toString:F}),P(H,X,E(k,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new H(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new H(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new H(K(this.r),K(this.g),K(this.b),Y(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:V,formatHex:V,formatHex8:function(){return`#${J(this.r)}${J(this.g)}${J(this.b)}${J((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:G,toString:G})),P(tt,function(t,e,r,n){return 1==arguments.length?Q(t):new tt(t,e,r,null==n?1:n)},E(k,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tt(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tt(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new H(tn(t>=240?t-240:t+120,o,n),tn(t,o,n),tn(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new tt(te(this.h),tr(this.s),tr(this.l),Y(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=Y(this.opacity);return`${1===t?"hsl(":"hsla("}${te(this.h)}, ${100*tr(this.s)}%, ${100*tr(this.l)}%${1===t?")":`, ${t})`}`}}));let ti=t=>()=>t;function ta(t,e){var r,n,o=e-t;return o?(r=t,n=o,function(t){return r+t*n}):ti(isNaN(t)?e:t)}let tc=function t(e){var r,n=1==(r=+e)?ta:function(t,e){var n,o,i;return e-t?(n=t,o=e,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(t){return Math.pow(n+t*o,i)}):ti(isNaN(t)?e:t)};function o(t,e){var r=n((t=X(t)).r,(e=X(e)).r),o=n(t.g,e.g),i=n(t.b,e.b),a=ta(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function tu(t){return function(e){var r,n,o=e.length,i=Array(o),a=Array(o),c=Array(o);for(r=0;r<o;++r)n=X(e[r]),i[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return i=t(i),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=c(t),n+""}}}tu(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,c=n<e-1?t[n+2]:2*i-o;return to((r-n/e)*e,a,o,i,c)}}),tu(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return to((r-n/e)*e,o,i,a,c)}});function tl(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var ts=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tf=RegExp(ts.source,"g");function tp(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?ti(e):("number"===o?tl:"string"===o?(n=W(e))?(e=n,tc):function(t,e){var r,n,o,i,a,c=ts.lastIndex=tf.lastIndex=0,u=-1,l=[],s=[];for(t+="",e+="";(o=ts.exec(t))&&(i=tf.exec(e));)(a=i.index)>c&&(a=e.slice(c,a),l[u]?l[u]+=a:l[++u]=a),(o=o[0])===(i=i[0])?l[u]?l[u]+=i:l[++u]=i:(l[++u]=null,s.push({i:u,x:tl(o,i)})),c=tf.lastIndex;return c<e.length&&(a=e.slice(c),l[u]?l[u]+=a:l[++u]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof W?tc:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=tp(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},o={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=tp(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}:tl:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}})(t,e)}function th(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function td(t){return+t}var ty=[0,1];function tv(t){return t}function tm(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function tb(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=tm(o,n),i=r(a,i)):(n=tm(n,o),i=r(i,a)),function(t){return i(n(t))}}function tg(t,e,r){var n=Math.min(t.length,e.length)-1,o=Array(n),i=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=tm(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=A(t,e,1,n)-1;return i[r](o[r](e))}}function tx(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function tO(){var t,e,r,n,o,i,a=ty,c=ty,u=tp,l=tv;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==tv&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?tg:tb,o=i=null,f}function f(e){return null==e||isNaN(e*=1)?r:(o||(o=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(c,a.map(t),tl)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,td),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=th,s()},f.clamp=function(t){return arguments.length?(l=!!t||tv,s()):l!==tv},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function tw(){return tO()(tv,tv)}var tj=r(28749),tS=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function tA(t){var e;if(!(e=tS.exec(t)))throw Error("invalid format: "+t);return new tP({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function tP(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function tE(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function tk(t){return(t=tE(Math.abs(t)))?t[1]:NaN}function tM(t,e){var r=tE(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}tA.prototype=tP.prototype,tP.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let t_={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>tM(100*t,e),r:tM,s:function(t,e){var r=tE(t,e);if(!r)return t+"";var o=r[0],i=r[1],a=i-(n=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,c=o.length;return a===c?o:a>c?o+Array(a-c+1).join("0"):a>0?o.slice(0,a)+"."+o.slice(a):"0."+Array(1-a).join("0")+tE(t,Math.max(0,e+a-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function tT(t){return t}var tC=Array.prototype.map,tD=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function tI(t,e,r,n){var o,c,u,l=b(t,e,r);switch((n=tA(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(u=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(tk(s)/3)))-tk(Math.abs(l))))||(n.precision=u),a(n,s);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(u=Math.max(0,tk(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(o=Math.abs(o=l)))-tk(o))+1)||(n.precision=u-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(u=Math.max(0,-tk(Math.abs(l))))||(n.precision=u-("%"===n.type)*2)}return i(n)}function tN(t){var e=t.domain;return t.ticks=function(t){var r=e();return v(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return tI(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,c=i.length-1,u=i[a],l=i[c],s=10;for(l<u&&(o=u,u=l,l=o,o=a,a=c,c=o);s-- >0;){if((o=m(u,l,r))===n)return i[a]=u,i[c]=l,e(i);if(o>0)u=Math.floor(u/o)*o,l=Math.ceil(l/o)*o;else if(o<0)u=Math.ceil(u*o)/o,l=Math.floor(l*o)/o;else break;n=o}return t},t}function tB(){var t=tw();return t.copy=function(){return tx(t,tB())},tj.C.apply(t,arguments),tN(t)}function tR(t,e){t=t.slice();var r,n=0,o=t.length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function tL(t){return Math.log(t)}function tz(t){return Math.exp(t)}function tU(t){return-Math.log(-t)}function tF(t){return-Math.exp(-t)}function tW(t){return isFinite(t)?+("1e"+t):t<0?0:t}function t$(t){return(e,r)=>-t(-e,r)}function tq(t){let e,r,n=t(tL,tz),o=n.domain,a=10;function c(){var i,c;return e=(i=a)===Math.E?Math.log:10===i&&Math.log10||2===i&&Math.log2||(i=Math.log(i),t=>Math.log(t)/i),r=10===(c=a)?tW:c===Math.E?Math.exp:t=>Math.pow(c,t),o()[0]<0?(e=t$(e),r=t$(r),t(tU,tF)):t(tL,tz),n}return n.base=function(t){return arguments.length?(a=+t,c()):a},n.domain=function(t){return arguments.length?(o(t),c()):o()},n.ticks=t=>{let n,i,c=o(),u=c[0],l=c[c.length-1],s=l<u;s&&([u,l]=[l,u]);let f=e(u),p=e(l),h=null==t?10:+t,d=[];if(!(a%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),u>0){for(;f<=p;++f)for(n=1;n<a;++n)if(!((i=f<0?n/r(-f):n*r(f))<u)){if(i>l)break;d.push(i)}}else for(;f<=p;++f)for(n=a-1;n>=1;--n)if(!((i=f>0?n/r(-f):n*r(f))<u)){if(i>l)break;d.push(i)}2*d.length<h&&(d=v(u,l,h))}else d=v(f,p,Math.min(p-f,h)).map(r);return s?d.reverse():d},n.tickFormat=(t,o)=>{if(null==t&&(t=10),null==o&&(o=10===a?"s":","),"function"!=typeof o&&(a%1||null!=(o=tA(o)).precision||(o.trim=!0),o=i(o)),t===1/0)return o;let c=Math.max(1,a*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*a<a-.5&&(n*=a),n<=c?o(t):""}},n.nice=()=>o(tR(o(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function tX(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function tH(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function tV(t){var e=1,r=t(tX(1),tH(e));return r.constant=function(r){return arguments.length?t(tX(e=+r),tH(e)):e},tN(r)}i=(o=function(t){var e,r,o,i=void 0===t.grouping||void 0===t.thousands?tT:(e=tC.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,c=e[0],u=0;o>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),i.push(t.substring(o-=c,o+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return i.reverse().join(r)}),a=void 0===t.currency?"":t.currency[0]+"",c=void 0===t.currency?"":t.currency[1]+"",u=void 0===t.decimal?".":t.decimal+"",l=void 0===t.numerals?tT:(o=tC.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return o[+t]})}),s=void 0===t.percent?"%":t.percent+"",f=void 0===t.minus?"−":t.minus+"",p=void 0===t.nan?"NaN":t.nan+"";function h(t){var e=(t=tA(t)).fill,r=t.align,o=t.sign,h=t.symbol,d=t.zero,y=t.width,v=t.comma,m=t.precision,b=t.trim,g=t.type;"n"===g?(v=!0,g="g"):t_[g]||(void 0===m&&(m=12),b=!0,g="g"),(d||"0"===e&&"="===r)&&(d=!0,e="0",r="=");var x="$"===h?a:"#"===h&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",O="$"===h?c:/[%p]/.test(g)?s:"",w=t_[g],j=/[defgprs%]/.test(g);function S(t){var a,c,s,h=x,S=O;if("c"===g)S=w(t)+S,t="";else{var A=(t*=1)<0||1/t<0;if(t=isNaN(t)?p:w(Math.abs(t),m),b&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),A&&0==+t&&"+"!==o&&(A=!1),h=(A?"("===o?o:f:"-"===o||"("===o?"":o)+h,S=("s"===g?tD[8+n/3]:"")+S+(A&&"("===o?")":""),j){for(a=-1,c=t.length;++a<c;)if(48>(s=t.charCodeAt(a))||s>57){S=(46===s?u+t.slice(a+1):t.slice(a))+S,t=t.slice(0,a);break}}}v&&!d&&(t=i(t,1/0));var P=h.length+t.length+S.length,E=P<y?Array(y-P+1).join(e):"";switch(v&&d&&(t=i(E+t,E.length?y-S.length:1/0),E=""),r){case"<":t=h+t+S+E;break;case"=":t=h+E+t+S;break;case"^":t=E.slice(0,P=E.length>>1)+h+t+S+E.slice(P);break;default:t=E+h+t+S}return l(t)}return m=void 0===m?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),S.toString=function(){return t+""},S}return{format:h,formatPrefix:function(t,e){var r=h(((t=tA(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(tk(e)/3))),o=Math.pow(10,-n),i=tD[8+n/3];return function(t){return r(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,a=o.formatPrefix;var tG=r(95442);function tY(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function tK(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function tJ(t){return t<0?-t*t:t*t}function tZ(t){var e=t(tv,tv),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(tv,tv):.5===r?t(tK,tJ):t(tY(r),tY(1/r)):r},tN(e)}function tQ(){var t=tZ(tO());return t.copy=function(){return tx(t,tQ()).exponent(t.exponent())},tj.C.apply(t,arguments),t}function t0(){return tQ.apply(null,arguments).exponent(.5)}function t1(t){return Math.sign(t)*t*t}function t2(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function t3(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}function t5(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function t6(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let t8=new Date,t9=new Date;function t4(t,e,r,n){function o(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{let e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{let a,c=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return c;do c.push(a=new Date(+r)),e(r,i),t(r);while(a<r&&r<n);return c},o.filter=r=>t4(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(o.count=(e,n)=>(t8.setTime(+e),t9.setTime(+n),t(t8),t(t9),Math.floor(r(t8,t9))),o.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?o.filter(n?e=>n(e)%t==0:e=>o.count(0,e)%t==0):o:null),o}let t7=t4(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);t7.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?t4(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):t7:null,t7.range;let et=t4(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());et.range;let ee=t4(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());ee.range;let er=t4(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());er.range;let en=t4(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());en.range;let eo=t4(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());eo.range;let ei=t4(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);ei.range;let ea=t4(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);ea.range;let ec=t4(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function eu(t){return t4(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}ec.range;let el=eu(0),es=eu(1),ef=eu(2),ep=eu(3),eh=eu(4),ed=eu(5),ey=eu(6);function ev(t){return t4(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}el.range,es.range,ef.range,ep.range,eh.range,ed.range,ey.range;let em=ev(0),eb=ev(1),eg=ev(2),ex=ev(3),eO=ev(4),ew=ev(5),ej=ev(6);em.range,eb.range,eg.range,ex.range,eO.range,ew.range,ej.range;let eS=t4(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());eS.range;let eA=t4(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());eA.range;let eP=t4(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());eP.every=t=>isFinite(t=Math.floor(t))&&t>0?t4(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,eP.range;let eE=t4(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function ek(t,e,r,n,o,i){let a=[[et,1,1e3],[et,5,5e3],[et,15,15e3],[et,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let o=Math.abs(r-e)/n,i=O(([,,t])=>t).right(a,o);if(i===a.length)return t.every(b(e/31536e6,r/31536e6,n));if(0===i)return t7.every(Math.max(b(e,r,n),1));let[c,u]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let o=r&&"function"==typeof r.range?r:c(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},c]}eE.every=t=>isFinite(t=Math.floor(t))&&t>0?t4(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,eE.range;let[eM,e_]=ek(eE,eA,em,ec,eo,er),[eT,eC]=ek(eP,eS,el,ei,en,ee);function eD(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function eI(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function eN(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var eB={"-":"",_:" ",0:"0"},eR=/^\s*\d+/,eL=/^%/,ez=/[\\^$*+?|[\]().{}]/g;function eU(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?Array(r-i+1).join(e)+o:o)}function eF(t){return t.replace(ez,"\\$&")}function eW(t){return RegExp("^(?:"+t.map(eF).join("|")+")","i")}function e$(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function eq(t,e,r){var n=eR.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function eX(t,e,r){var n=eR.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function eH(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function eV(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function eG(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function eY(t,e,r){var n=eR.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function eK(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function eJ(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function eZ(t,e,r){var n=eR.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function eQ(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function e0(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function e1(t,e,r){var n=eR.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function e2(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function e3(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function e5(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function e6(t,e,r){var n=eR.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function e8(t,e,r){var n=eR.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function e9(t,e,r){var n=eL.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function e4(t,e,r){var n=eR.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function e7(t,e,r){var n=eR.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function rt(t,e){return eU(t.getDate(),e,2)}function re(t,e){return eU(t.getHours(),e,2)}function rr(t,e){return eU(t.getHours()%12||12,e,2)}function rn(t,e){return eU(1+ei.count(eP(t),t),e,3)}function ro(t,e){return eU(t.getMilliseconds(),e,3)}function ri(t,e){return ro(t,e)+"000"}function ra(t,e){return eU(t.getMonth()+1,e,2)}function rc(t,e){return eU(t.getMinutes(),e,2)}function ru(t,e){return eU(t.getSeconds(),e,2)}function rl(t){var e=t.getDay();return 0===e?7:e}function rs(t,e){return eU(el.count(eP(t)-1,t),e,2)}function rf(t){var e=t.getDay();return e>=4||0===e?eh(t):eh.ceil(t)}function rp(t,e){return t=rf(t),eU(eh.count(eP(t),t)+(4===eP(t).getDay()),e,2)}function rh(t){return t.getDay()}function rd(t,e){return eU(es.count(eP(t)-1,t),e,2)}function ry(t,e){return eU(t.getFullYear()%100,e,2)}function rv(t,e){return eU((t=rf(t)).getFullYear()%100,e,2)}function rm(t,e){return eU(t.getFullYear()%1e4,e,4)}function rb(t,e){var r=t.getDay();return eU((t=r>=4||0===r?eh(t):eh.ceil(t)).getFullYear()%1e4,e,4)}function rg(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+eU(e/60|0,"0",2)+eU(e%60,"0",2)}function rx(t,e){return eU(t.getUTCDate(),e,2)}function rO(t,e){return eU(t.getUTCHours(),e,2)}function rw(t,e){return eU(t.getUTCHours()%12||12,e,2)}function rj(t,e){return eU(1+ea.count(eE(t),t),e,3)}function rS(t,e){return eU(t.getUTCMilliseconds(),e,3)}function rA(t,e){return rS(t,e)+"000"}function rP(t,e){return eU(t.getUTCMonth()+1,e,2)}function rE(t,e){return eU(t.getUTCMinutes(),e,2)}function rk(t,e){return eU(t.getUTCSeconds(),e,2)}function rM(t){var e=t.getUTCDay();return 0===e?7:e}function r_(t,e){return eU(em.count(eE(t)-1,t),e,2)}function rT(t){var e=t.getUTCDay();return e>=4||0===e?eO(t):eO.ceil(t)}function rC(t,e){return t=rT(t),eU(eO.count(eE(t),t)+(4===eE(t).getUTCDay()),e,2)}function rD(t){return t.getUTCDay()}function rI(t,e){return eU(eb.count(eE(t)-1,t),e,2)}function rN(t,e){return eU(t.getUTCFullYear()%100,e,2)}function rB(t,e){return eU((t=rT(t)).getUTCFullYear()%100,e,2)}function rR(t,e){return eU(t.getUTCFullYear()%1e4,e,4)}function rL(t,e){var r=t.getUTCDay();return eU((t=r>=4||0===r?eO(t):eO.ceil(t)).getUTCFullYear()%1e4,e,4)}function rz(){return"+0000"}function rU(){return"%"}function rF(t){return+t}function rW(t){return Math.floor(t/1e3)}function r$(t){return new Date(t)}function rq(t){return t instanceof Date?+t:+new Date(+t)}function rX(t,e,r,n,o,i,a,c,u,l){var s=tw(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function O(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,rq)):p().map(r$)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?O:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(tR(r,t)):s},s.copy=function(){return tx(s,rX(t,e,r,n,o,i,a,c,u,l))},s}function rH(){return tj.C.apply(rX(eT,eC,eP,eS,el,ei,en,ee,et,u).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function rV(){return tj.C.apply(rX(eM,e_,eE,eA,em,ea,eo,er,et,l).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function rG(){var t,e,r,n,o,i=0,a=1,c=tv,u=!1;function l(e){return null==e||isNaN(e*=1)?o:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i*=1),e=n(a*=1),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(tp),l.rangeRound=s(th),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function rY(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function rK(){var t=tZ(rG());return t.copy=function(){return rY(t,rK()).exponent(t.exponent())},tj.K.apply(t,arguments)}function rJ(){return rK.apply(null,arguments).exponent(.5)}function rZ(){var t,e,r,n,o,i,a,c=0,u=.5,l=1,s=1,f=tv,p=!1;function h(t){return isNaN(t*=1)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=tp);for(var r=0,n=e.length-1,o=e[0],i=Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=i(c*=1),e=i(u*=1),r=i(l*=1),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(tp),h.rangeRound=d(th),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function rQ(){var t=tZ(rZ());return t.copy=function(){return rY(t,rQ()).exponent(t.exponent())},tj.K.apply(t,arguments)}function r0(){return rQ.apply(null,arguments).exponent(.5)}function r1(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],c=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}u=(c=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=eW(o),s=e$(o),f=eW(i),p=e$(i),h=eW(a),d=e$(a),y=eW(c),v=e$(c),m=eW(u),b=e$(u),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:rt,e:rt,f:ri,g:rv,G:rb,H:re,I:rr,j:rn,L:ro,m:ra,M:rc,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:rF,s:rW,S:ru,u:rl,U:rs,V:rp,w:rh,W:rd,x:null,X:null,y:ry,Y:rm,Z:rg,"%":rU},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:rx,e:rx,f:rA,g:rB,G:rL,H:rO,I:rw,j:rj,L:rS,m:rP,M:rE,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:rF,s:rW,S:rk,u:rM,U:r_,V:rC,w:rD,W:rI,x:null,X:null,y:rN,Y:rR,Z:rz,"%":rU},O={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:e0,e:e0,f:e8,g:eK,G:eY,H:e2,I:e2,j:e1,L:e6,m:eQ,M:e3,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:eZ,Q:e4,s:e7,S:e5,u:eX,U:eH,V:eV,w:eq,W:eG,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:eK,Y:eY,Z:eJ,"%":e9};function w(t,e){return function(r){var n,o,i,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(o=eB[n=t.charAt(++c)])?n=t.charAt(++c):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,o,i=eN(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!e||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=eI(eN(i.y,0,1))).getUTCDay())>4||0===o?eb.ceil(n):eb(n),n=ea.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=eD(eN(i.y,0,1))).getDay())>4||0===o?es.ceil(n):es(n),n=ei.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),o="Z"in i?eI(eN(i.y,0,1)).getUTCDay():eD(eN(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,eI(i)):eD(i)}}function S(t,e,r,n){for(var o,i,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(o=e.charCodeAt(a++))){if(!(i=O[(o=e.charAt(a++))in eB?e.charAt(a++):o])||(n=i(t,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=w(r,g),g.X=w(n,g),g.c=w(e,g),x.x=w(r,x),x.X=w(n,x),x.c=w(e,x),{format:function(t){var e=w(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,c.parse,l=c.utcFormat,c.utcParse;var r2=r(9819),r3=r(85654);function r5(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function r6(t,e){return t[e]}function r8(t){let e=[];return e.key=t,e}var r9=r(22315),r4=r.n(r9),r7=r(89053),nt=r.n(r7),ne=r(59882),nr=r.n(ne),nn=r(40139),no=r.n(nn),ni=r(15438),na=r.n(ni),nc=r(48973),nu=r.n(nc),nl=r(3698),ns=r.n(nl),nf=r(13908),np=r.n(nf),nh=r(23633),nd=r.n(nh),ny=r(60245),nv=r.n(ny),nm=r(67206),nb=r.n(nm),ng=r(8870),nx=r.n(ng);function nO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nw=function(t){return t},nj={},nS=function(t){return t===nj},nA=function(t){return function e(){return 0==arguments.length||1==arguments.length&&nS(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},nP=function(t){return function t(e,r){return 1===e?r:nA(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==nj}).length;return a>=e?r.apply(void 0,o):t(e-a,nA(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return nS(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return nO(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return nO(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nO(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},nE=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},nk=nP(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),nM=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return nw;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},n_=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},nT=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};nP(function(t,e,r){var n=+t;return n+r*(e-n)}),nP(function(t,e,r){var n=e-t;return(r-t)/(n=n||1/0)}),nP(function(t,e,r){var n=e-t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let nC={rangeStep:function(t,e,r){for(var n=new(nx())(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new(nx())(t).abs().log(10).toNumber())+1}};function nD(t){return function(t){if(Array.isArray(t))return nB(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||nN(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nI(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}}(t,e)||nN(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nN(t,e){if(t){if("string"==typeof t)return nB(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nB(t,e)}}function nB(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nR(t){var e=nI(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function nL(t,e,r){if(t.lte(0))return new(nx())(0);var n=nC.getDigitCount(t.toNumber()),o=new(nx())(10).pow(n),i=t.div(o),a=1!==n?.05:.1,c=new(nx())(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?c:new(nx())(Math.ceil(c))}function nz(t,e,r){var n=1,o=new(nx())(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new(nx())(10).pow(nC.getDigitCount(t)-1),o=new(nx())(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new(nx())(Math.floor(t)))}else 0===t?o=new(nx())(Math.floor((e-1)/2)):r||(o=new(nx())(Math.floor(t)));var a=Math.floor((e-1)/2);return nM(nk(function(t){return o.add(new(nx())(t-a).mul(n)).toNumber()}),nE)(0,e)}var nU=nT(function(t){var e=nI(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=nI(nR([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(nD(nE(0,o-1).map(function(){return 1/0}))):[].concat(nD(nE(0,o-1).map(function(){return-1/0})),[l]);return r>n?n_(s):s}if(u===l)return nz(u,o,i);var f=function t(e,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new(nx())(0),tickMin:new(nx())(0),tickMax:new(nx())(0)};var c=nL(new(nx())(r).sub(e).div(n-1),o,a),u=Math.ceil((i=e<=0&&r>=0?new(nx())(0):(i=new(nx())(e).add(r).div(2)).sub(new(nx())(i).mod(c))).sub(e).div(c).toNumber()),l=Math.ceil(new(nx())(r).sub(i).div(c).toNumber()),s=u+l+1;return s>n?t(e,r,n,o,a+1):(s<n&&(l=r>0?l+(n-s):l,u=r>0?u:u+(n-s)),{step:c,tickMin:i.sub(new(nx())(u).mul(c)),tickMax:i.add(new(nx())(l).mul(c))})}(u,l,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=nC.rangeStep(h,d.add(new(nx())(.1).mul(p)),p);return r>n?n_(y):y});nT(function(t){var e=nI(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=nI(nR([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return nz(u,o,i);var s=nL(new(nx())(l).sub(u).div(a-1),i,0),f=nM(nk(function(t){return new(nx())(u).add(new(nx())(t).mul(s)).toNumber()}),nE)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?n_(f):f});var nF=nT(function(t,e){var r=nI(t,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=nI(nR([n,o]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,o];if(c===u)return[c];var l=Math.max(e,2),s=nL(new(nx())(u).sub(c).div(l-1),i,0),f=[].concat(nD(nC.rangeStep(new(nx())(c),new(nx())(u).sub(new(nx())(.99).mul(s)),s)),[u]);return n>o?n_(f):f}),nW=r(94011),n$=r(16377),nq=r(70788),nX=r(83197);function nH(t){return(nH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nV(t){return function(t){if(Array.isArray(t))return nG(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return nG(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nG(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nG(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nY(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nK(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nY(Object(r),!0).forEach(function(e){nJ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nY(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nJ(t,e,r){var n;return(n=function(t,e){if("object"!=nH(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==nH(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nZ(t,e,r){return nr()(t)||nr()(e)?r:(0,n$.vh)(e)?nu()(t,e,r):no()(e)?e(t):r}function nQ(t,e,r,n){var o=ns()(t,function(t){return nZ(t,e)});if("number"===r){var i=o.filter(function(t){return(0,n$.Et)(t)||parseFloat(t)});return i.length?[nt()(i),r4()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!nr()(t)}):o).map(function(t){return(0,n$.vh)(t)||t instanceof Date?t:""})}var n0=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!=(e=null==r?void 0:r.length)?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var c=o.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if((0,n$.sA)(s-l)!==(0,n$.sA)(f-s)){var h=[];if((0,n$.sA)(f-s)===(0,n$.sA)(c[1]-c[0])){p=f;var d=s+c[1]-c[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+c[1]-c[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},n1=function(t){var e,r,n=t.type.displayName,o=null!=(e=t.type)&&e.defaultProps?nK(nK({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},n2=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var l=o[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return(0,nq.Mn)(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?nK(nK({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var O=nr()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:nr()(O)?void 0:(0,n$.F4)(O,r,0)})}}return i},n3=function(t){var e,r=t.barGap,n=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,c=t.maxBarSize,u=a.length;if(u<1)return null;var l=(0,n$.F4)(r,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/u,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=o&&(h-=(u-1)*l,l=0),h>=o&&p>0&&(f=!0,p*=.9,h=u*p);var d={offset:((o-h)/2|0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(nV(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=(0,n$.F4)(n,o,0,!0);o-2*y-(u-1)*l<=0&&(l=0);var v=(o-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(nV(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},n5=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=i-(a.left||0)-(a.right||0),u=(0,nX.g)({children:o,legendWidth:c});if(u){var l=n||{},s=l.width,f=l.height,p=u.align,h=u.verticalAlign,d=u.layout;if(("vertical"===d||"horizontal"===d&&"middle"===h)&&"center"!==p&&(0,n$.Et)(t[p]))return nK(nK({},t),{},nJ({},p,t[p]+(s||0)));if(("horizontal"===d||"vertical"===d&&"center"===p)&&"middle"!==h&&(0,n$.Et)(t[h]))return nK(nK({},t),{},nJ({},h,t[h]+(f||0)))}return t},n6=function(t,e,r,n,o){var i=e.props.children,a=(0,nq.aS)(i,nW.u).filter(function(t){var e;return e=t.props.direction,!!nr()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(a&&a.length){var c=a.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=nZ(e,r);if(nr()(n))return t;var o=Array.isArray(n)?[nt()(n),r4()(n)]:[n,n],i=c.reduce(function(t,r){var n=nZ(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},n8=function(t,e,r,n,o){var i=e.map(function(e){return n6(t,e,r,o,n)}).filter(function(t){return!nr()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},n9=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&n6(t,e,i,n)||nQ(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},n4=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},n7=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var o,i,a=t.map(function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate});return o||a.push(e),i||a.push(r),a},ot=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*(0,n$.sA)(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(o?o.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!np()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}})},oe=new WeakMap,or=function(t,e){if("function"!=typeof e)return t;oe.has(t)||oe.set(t,new WeakMap);var r=oe.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},on=function(t,e,r){var n=t.scale,o=t.type,i=t.layout,a=t.axisType;if("auto"===n)return"radial"===i&&"radiusAxis"===a?{scale:f.A(),realScaleType:"band"}:"radial"===i&&"angleAxis"===a?{scale:tB(),realScaleType:"linear"}:"category"===o&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:f.z(),realScaleType:"point"}:"category"===o?{scale:f.A(),realScaleType:"band"}:{scale:tB(),realScaleType:"linear"};if(na()(n)){var c="scale".concat(nd()(n));return{scale:(s[c]||f.z)(),realScaleType:s[c]?c:"point"}}return no()(n)?{scale:n}:{scale:f.z(),realScaleType:"point"}},oo=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}},oi=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},oa=function(t,e){if(!e||2!==e.length||!(0,n$.Et)(e[0])||!(0,n$.Et)(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!(0,n$.Et)(t[0])||t[0]<r)&&(o[0]=r),(!(0,n$.Et)(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},oc={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=np()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}r1(t,e)}},none:r1,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}r1(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<o;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,u&&(i-=l/u)}r[a-1][1]+=r[a-1][0]=i,r1(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=np()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},ou=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=oc[r];return(function(){var t=(0,r3.A)([]),e=r5,r=r1,n=r6;function o(o){var i,a,c=Array.from(t.apply(this,arguments),r8),u=c.length,l=-1;for(let t of o)for(i=0,++l;i<u;++i)(c[i][l]=[0,+n(t,c[i].key,l,o)]).data=t;for(i=0,a=(0,r2.A)(e(c));i<u;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:(0,r3.A)(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:(0,r3.A)(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?r5:"function"==typeof t?t:(0,r3.A)(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?r1:t,o):r},o})().keys(n).value(function(t,e){return+nZ(t,e,0)}).order(r5).offset(o)(t)},ol=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!=(o=e.type)&&o.defaultProps?nK(nK({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if((0,n$.vh)(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[(0,n$.NF)("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return nK(nK({},t),{},nJ({},c,u))},{});return Object.keys(a).reduce(function(e,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,i){var a=c.stackGroups[i];return nK(nK({},e),{},nJ({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:ou(t,a.items,o)}))},{})),nK(nK({},e),{},nJ({},i,c))},{})},os=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var l=nU(u,o,a);return t.domain([nt()(l),r4()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:nF(t.domain(),o,a)}:null};function of(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!nr()(o[e.dataKey])){var c=(0,n$.eP)(r,"value",o[e.dataKey]);if(c)return c.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var u=nZ(o,nr()(a)?e.dataKey:a);return nr()(u)?null:e.scale(u)}var op=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=nZ(i,e.dataKey,e.domain[a]);return nr()(c)?null:e.scale(c)-o/2+n},oh=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},od=function(t,e){var r,n=(null!=(r=t.type)&&r.defaultProps?nK(nK({},t.type.defaultProps),t.props):t.props).stackId;if((0,n$.vh)(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},oy=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[nt()(e.concat([t[0]]).filter(n$.Et)),r4()(e.concat([t[1]]).filter(n$.Et))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},ov=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,om=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ob=function(t,e,r){if(no()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if((0,n$.Et)(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(ov.test(t[0])){var o=+ov.exec(t[0])[1];n[0]=e[0]-o}else no()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if((0,n$.Et)(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(om.test(t[1])){var i=+om.exec(t[1])[1];n[1]=e[1]+i}else no()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},og=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=nb()(e,function(t){return t.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],l=o[a-1];i=Math.min((u.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},ox=function(t,e,r){return!t||!t.length||nv()(t,nu()(r,"type.defaultProps.domain"))?e:t},oO=function(t,e){var r=t.type.defaultProps?nK(nK({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return nK(nK({},(0,nq.J9)(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:n1(t),value:nZ(e,n),type:c,payload:e,chartType:u,hide:l})}},13122:(t,e,r)=>{var n=r(40139),o=r(38985),i=r(67460),a=r(7512),c=/^\[object .+?Constructor\]$/,u=Object.prototype,l=Function.prototype.toString,s=u.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?f:c).test(a(t))}},13364:(t,e,r)=>{var n=r(75899),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},13465:t=>{t.exports=function(t){return t}},13908:(t,e,r)=>{var n=r(40566);t.exports=function(t){return n(t)&&t!=+t}},15232:(t,e,r)=>{"use strict";function n(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}r.d(e,{b:()=>n})},15438:(t,e,r)=>{var n=r(98233),o=r(39608),i=r(48611);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},15473:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},15631:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}},16238:(t,e,r)=>{"use strict";r.d(e,{h:()=>v});var n=r(12115),o=r(52596),i=r(50091),a=r(45167),c=r(12814);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(l=function(){return!!t})()}function s(t){return(s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f(t,e){return(f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function p(t,e,r){return(e=h(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}function d(){return(d=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var y=function(t){var e=t.yAxisId,r=(0,i.yi)(),u=(0,i.rY)(),l=(0,i.Nk)(e);return null==l?null:n.createElement(a.u,d({},l,{className:(0,o.A)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:u},ticksGenerator:function(t){return(0,c.Rh)(t,!0)}}))},v=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=s(t),function(t,e){if(e&&("object"===u(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,l()?Reflect.construct(t,e||[],s(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&f(r,t),e=[{key:"render",value:function(){return n.createElement(y,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);p(v,"displayName","YAxis"),p(v,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1})},16377:(t,e,r)=>{"use strict";r.d(e,{CG:()=>w,Dj:()=>j,Et:()=>y,F4:()=>x,NF:()=>g,_3:()=>d,ck:()=>A,eP:()=>S,lX:()=>O,sA:()=>h,uy:()=>v,vh:()=>m});var n=r(15438),o=r.n(n),i=r(13908),a=r.n(i),c=r(48973),u=r.n(c),l=r(40566),s=r.n(l),f=r(59882),p=r.n(f),h=function(t){return 0===t?0:t>0?1:-1},d=function(t){return o()(t)&&t.indexOf("%")===t.length-1},y=function(t){return s()(t)&&!a()(t)},v=function(t){return p()(t)},m=function(t){return y(t)||o()(t)},b=0,g=function(t){var e=++b;return"".concat(t||"").concat(e)},x=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!y(t)&&!o()(t))return n;if(d(t)){var c=t.indexOf("%");r=e*parseFloat(t.slice(0,c))/100}else r=+t;return a()(r)&&(r=n),i&&r>e&&(r=e),r},O=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},w=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},j=function(t,e){return y(t)&&y(e)?function(r){return t+r*(e-t)}:function(){return e}};function S(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):u()(t,e))===r}):null}var A=function(t,e){return y(t)&&y(e)?t-e:o()(t)&&o()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))}},16571:(t,e,r)=>{var n=r(50687),o=r(54906),i=r(13465);t.exports=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i},16613:(t,e,r)=>{var n=r(24376),o=r(57213),i=r(39608),a=r(70771),c=1/0,u=n?n.prototype:void 0,l=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return l?l.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},16746:(t,e,r)=>{var n=r(3562),o=r(12486),i=r(69806);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},17489:(t,e,r)=>{var n=r(91113);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},17855:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+e+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+u+c+")*",s=RegExp(n+"(?="+n+")|"+("(?:"+[o+r+"?",r,i,a,"["+e+"]"].join("|"))+")"+(u+c+l),"g");t.exports=function(t){return t.match(s)||[]}},18028:(t,e,r)=>{var n=r(55910),o=r(96699),i=r(13465),a=r(39608),c=r(28126);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):c(t)}},18489:(t,e,r)=>{var n=r(96294),o=r(72043),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},18686:(t,e,r)=>{t.exports=r(83711)(r(82500),"Map")},18940:(t,e,r)=>{t.exports=r(64189)()},20134:(t,e,r)=>{var n=r(86452),o=r(50111);t.exports=function(t,e){return null!=t&&o(t,e,n)}},20480:(t,e,r)=>{var n=r(86216),o=r(35095);t.exports=function(t,e){return t&&n(t,e,o)}},20570:(t,e,r)=>{var n=r(24376),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[c]=r:delete t[c]),o}},20963:(t,e,r)=>{var n=r(65646),o=r(38649),i=r(35095);t.exports=function(t){return n(t,i,o)}},20988:(t,e,r)=>{var n=r(75899);t.exports=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},21087:(t,e,r)=>{var n=r(13465),o=r(64588),i=r(61632);t.exports=function(t,e){return i(o(t,e,n),t+"")}},21582:t=>{t.exports=function(t,e){return t>e}},21790:(t,e,r)=>{var n=r(54360);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},22143:(t,e,r)=>{var n=r(98233),o=r(48611);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},22315:(t,e,r)=>{var n=r(58918),o=r(21582),i=r(13465);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},22471:(t,e,r)=>{var n=r(40139),o=r(15631);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},23360:(t,e,r)=>{var n=r(42233);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},23633:(t,e,r)=>{t.exports=r(62962)("toUpperCase")},24026:(t,e,r)=>{"use strict";r.d(e,{s:()=>D});var n=r(12115),o=r(40139),i=r.n(o),a=r(52596),c=r(675),u=r(72790),l=r(9795),s=r(43597);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(){return(p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(d=function(){return!!t})()}function y(t){return(y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function v(t,e){return(v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function m(t,e,r){return(e=b(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function b(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var g=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=y(t),function(t,e){if(e&&("object"===f(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,d()?Reflect.construct(t,e||[],y(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&v(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,o=32/3,i=t.inactive?e:t.color;if("plainline"===t.type)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:i,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:i,d:"M0,".concat(16,"h").concat(o,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*o,",").concat(16,"\n            H").concat(32,"M").concat(2*o,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(o,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return n.createElement("path",{stroke:"none",fill:i,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(t.legendIcon)){var a=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){m(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete a.legendIcon,n.cloneElement(t.legendIcon,a)}return n.createElement(l.i,{fill:i,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,o=e.iconSize,l=e.layout,f=e.formatter,h=e.inactiveColor,d={x:0,y:0,width:32,height:32},y={display:"horizontal"===l?"inline-block":"block",marginRight:10},v={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var l=e.formatter||f,b=(0,a.A)(m(m({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var g=i()(e.value)?null:e.value;(0,c.R)(!i()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var x=e.inactive?h:e.color;return n.createElement("li",p({className:b,style:y,key:"legend-item-".concat(r)},(0,s.XC)(t.props,e,r)),n.createElement(u.u,{width:o,height:o,viewBox:d,style:v},t.renderIcon(e)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:x}},l?l(g,e,r):g))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,o=t.align;return e&&e.length?n.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?o:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,b(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent);m(g,"displayName","Legend"),m(g,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var x=r(16377),O=r(2494);function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var j=["ref"];function S(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?S(Object(r),!0).forEach(function(e){_(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function P(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,T(n.key),n)}}function E(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(E=function(){return!!t})()}function k(t){return(k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function M(t,e){return(M=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function _(t,e,r){return(e=T(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function T(t){var e=function(t,e){if("object"!=w(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=w(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==w(e)?e:e+""}function C(t){return t.value}var D=function(t){var e,r;function o(){var t,e,r;if(!(this instanceof o))throw TypeError("Cannot call a class as a function");for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=o,r=[].concat(i),e=k(e),_(t=function(t,e){if(e&&("object"===w(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,E()?Reflect.construct(e,r||[],k(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return o.prototype=Object.create(t&&t.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),t&&M(o,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?A({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),A(A({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,o=e.width,i=e.height,a=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,l=A(A({position:"absolute",width:o||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return n.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(n.isValidElement(t))return n.cloneElement(t,e);if("function"==typeof t)return n.createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,j);return n.createElement(g,r)}(r,A(A({},this.props),{},{payload:(0,O.s)(u,c,C)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=A(A({},this.defaultProps),t.props).layout;return"vertical"===r&&(0,x.Et)(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&P(o.prototype,e),r&&P(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);_(D,"displayName","Legend"),_(D,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},24376:(t,e,r)=>{t.exports=r(82500).Symbol},25641:(t,e,r)=>{"use strict";function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach(function(e){(function(t,e,r){var o;(o=function(t,e){if("object"!=n(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,e||"default");if("object"!=n(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==n(o)?o:o+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r})(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}r.d(e,{IZ:()=>c,Kg:()=>a,yy:()=>f}),r(59882),r(12115),r(40139);var a=Math.PI/180,c=function(t,e,r,n){return{x:t+Math.cos(-a*n)*r,y:e+Math.sin(-a*n)*r}},u=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},l=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=u({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=Math.acos((r-o)/a);return n>i&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},s=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},f=function(t,e){var r,n=l({x:t.x,y:t.y},e),o=n.radius,a=n.angle,c=e.innerRadius,u=e.outerRadius;if(o<c||o>u)return!1;if(0===o)return!0;var f=s(e),p=f.startAngle,h=f.endAngle,d=a;if(p<=h){for(;d>h;)d-=360;for(;d<p;)d+=360;r=d>=p&&d<=h}else{for(;d>p;)d-=360;for(;d<h;)d+=360;r=d>=h&&d<=p}return r?i(i({},e),{},{radius:o,angle:d+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null}},26151:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=!!e,e}},27569:(t,e,r)=>{var n=r(32197),o=r(35095);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},28126:(t,e,r)=>{var n=r(96548),o=r(93294),i=r(79595),a=r(94356);t.exports=function(t){return i(t)?n(a(t)):o(t)}},28749:(t,e,r)=>{"use strict";function n(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function o(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}r.d(e,{C:()=>n,K:()=>o})},28897:(t,e,r)=>{var n=r(54906);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},29794:(t,e,r)=>{var n=r(48628),o=r(74888),i=r(18028),a=r(39608),c=r(39641);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},30152:(t,e,r)=>{t.exports=r(82500)["__core-js_shared__"]},30699:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},30716:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++o];if(!1===r(i[u],u,i))break}return e}}},31431:t=>{t.exports=function(){}},31545:(t,e,r)=>{var n=r(75899);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},31598:(t,e,r)=>{var n=r(31887),o=r(90929),i=r(45170),a=r(61830),c=r(21790);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},31847:(t,e,r)=>{"use strict";r.d(e,{i:()=>u});let n=Math.PI,o=2*n,i=o-1e-6;function a(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class c{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?a:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return a;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,o,i){if(t*=1,e*=1,r*=1,o*=1,(i*=1)<0)throw Error(`negative radius: ${i}`);let a=this._x1,c=this._y1,u=r-t,l=o-e,s=a-t,f=c-e,p=s*s+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(p>1e-6)if(Math.abs(f*u-l*s)>1e-6&&i){let h=r-a,d=o-c,y=u*u+l*l,v=Math.sqrt(y),m=Math.sqrt(p),b=i*Math.tan((n-Math.acos((y+p-(h*h+d*d))/(2*v*m)))/2),g=b/m,x=b/v;Math.abs(g-1)>1e-6&&this._append`L${t+g*s},${e+g*f}`,this._append`A${i},${i},0,0,${+(f*h>s*d)},${this._x1=t+x*u},${this._y1=e+x*l}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,a,c,u){if(t*=1,e*=1,r*=1,u=!!u,r<0)throw Error(`negative radius: ${r}`);let l=r*Math.cos(a),s=r*Math.sin(a),f=t+l,p=e+s,h=1^u,d=u?a-c:c-a;null===this._x1?this._append`M${f},${p}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-p)>1e-6)&&this._append`L${f},${p}`,r&&(d<0&&(d=d%o+o),d>i?this._append`A${r},${r},0,1,${h},${t-l},${e-s}A${r},${r},0,1,${h},${this._x1=f},${this._y1=p}`:d>1e-6&&this._append`A${r},${r},0,${+(d>=n)},${h},${this._x1=t+r*Math.cos(c)},${this._y1=e+r*Math.sin(c)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function u(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new c(e)}c.prototype},31887:t=>{t.exports=function(){this.__data__=[],this.size=0}},32197:(t,e,r)=>{var n=r(67460);t.exports=function(t){return t==t&&!n(t)}},33332:t=>{t.exports=function(t){return function(e){return t(e)}}},33497:(t,e,r)=>{t=r.nmd(t);var n=r(82500),o=r(44158),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,c=a&&a.exports===i?n.Buffer:void 0,u=c?c.isBuffer:void 0;t.exports=u||o},34210:t=>{t.exports=function(t){return this.__data__.has(t)}},34711:(t,e,r)=>{var n=r(73800),o=r(94356);t.exports=function(t,e){e=n(e,t);for(var r=0,i=e.length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},35095:(t,e,r)=>{var n=r(11670),o=r(18489),i=r(22471);t.exports=function(t){return i(t)?n(t):o(t)}},35190:(t,e,r)=>{var n=r(89316),o=r(33332),i=r(49840),a=i&&i.isTypedArray;t.exports=a?o(a):n},36079:(t,e,r)=>{"use strict";r.d(e,{Z:()=>A});var n=r(12115),o=r(59882),i=r.n(o),a=r(67460),c=r.n(a),u=r(40139),l=r.n(u),s=r(83979),f=r.n(s),p=r(60379),h=r(2348),d=r(70788),y=r(12814);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var m=["valueAccessor"],b=["data","dataKey","clockWise","id","textBreakAll"];function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function x(){return(x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function j(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var S=function(t){return Array.isArray(t.value)?f()(t.value):t.value};function A(t){var e=t.valueAccessor,r=void 0===e?S:e,o=j(t,m),a=o.data,c=o.dataKey,u=o.clockWise,l=o.id,s=o.textBreakAll,f=j(o,b);return a&&a.length?n.createElement(h.W,{className:"recharts-label-list"},a.map(function(t,e){var o=i()(c)?r(t,e):(0,y.kr)(t&&t.payload,c),a=i()(l)?{}:{id:"".concat(l,"-").concat(e)};return n.createElement(p.J,x({},(0,d.J9)(t,!0),f,a,{parentViewBox:t.parentViewBox,value:o,textBreakAll:s,viewBox:p.J.parseViewBox(i()(u)?t:w(w({},t),{},{clockWise:u})),key:"label-".concat(e),index:e}))})):null}A.displayName="LabelList",A.renderCallByParent=function(t,e){var r,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var i=t.children,a=(0,d.aS)(i,A).map(function(t,r){return(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return o?[(r=t.label,!r?null:!0===r?n.createElement(A,{key:"labelList-implicit",data:e}):n.isValidElement(r)||l()(r)?n.createElement(A,{key:"labelList-implicit",data:e,content:r}):c()(r)?n.createElement(A,x({data:e},r,{key:"labelList-implicit"})):null)].concat(function(t){if(Array.isArray(t))return g(t)}(a)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(a)||function(t,e){if(t){if("string"==typeof t)return g(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(t,e)}}(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):a}},36314:(t,e,r)=>{var n=r(24376),o=r(9813),i=r(39608),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},36447:(t,e,r)=>{"use strict";r.d(e,{f:()=>d});var n=r(40139),o=r.n(n),i=r(16377),a=r(46605),c=r(41643),u=r(53707);function l(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e)if(void 0!==r&&!0!==r(t[o]))return;else n.push(t[o]);return n}function s(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d(t,e,r){var n,f,p,d,y,v=t.tick,m=t.ticks,b=t.viewBox,g=t.minTickGap,x=t.orientation,O=t.interval,w=t.tickFormatter,j=t.unit,S=t.angle;if(!m||!m.length||!v)return[];if((0,i.Et)(O)||c.m.isSsr)return l(m,("number"==typeof O&&(0,i.Et)(O)?O:0)+1);var A=[],P="top"===x||"bottom"===x?"width":"height",E=j&&"width"===P?(0,a.Pu)(j,{fontSize:e,letterSpacing:r}):{width:0,height:0},k=function(t,n){var i,c,l=o()(w)?w(t.value,n):t.value;return"width"===P?(i=(0,a.Pu)(l,{fontSize:e,letterSpacing:r}),c={width:i.width+E.width,height:i.height+E.height},(0,u.bx)(c,S)):(0,a.Pu)(l,{fontSize:e,letterSpacing:r})[P]},M=m.length>=2?(0,i.sA)(m[1].coordinate-m[0].coordinate):1,_=(n="width"===P,f=b.x,p=b.y,d=b.width,y=b.height,1===M?{start:n?f:p,end:n?f+d:p+y}:{start:n?f+d:p+y,end:n?f:p});return"equidistantPreserveStart"===O?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,f=0,p=1,h=c;p<=a.length;)if(i=function(){var e,i=null==n?void 0:n[f];if(void 0===i)return{v:l(n,p)};var a=f,d=function(){return void 0===e&&(e=r(i,a)),e},y=i.coordinate,v=0===f||s(t,y,d,h,u);v||(f=0,h=c,p+=1),v&&(h=y+t*(d()/2+o),f+=p)}())return i.v;return[]}(M,_,k,m,g):("preserveStart"===O||"preserveStartEnd"===O?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(i){var f=n[c-1],p=r(f,c-1),d=t*(f.coordinate+t*p/2-l);a[c-1]=f=h(h({},f),{},{tickCoord:d>0?f.coordinate-d*t:f.coordinate}),s(t,f.tickCoord,function(){return p},u,l)&&(l=f.tickCoord-t*(p/2+o),a[c-1]=h(h({},f),{},{isShow:!0}))}for(var y=i?c-1:c,v=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var f=t*(i.coordinate-t*c()/2-u);a[e]=i=h(h({},i),{},{tickCoord:f<0?i.coordinate-f*t:i.coordinate})}else a[e]=i=h(h({},i),{},{tickCoord:i.coordinate});s(t,i.tickCoord,c,u,l)&&(u=i.tickCoord+t*(c()/2+o),a[e]=h(h({},i),{},{isShow:!0}))},m=0;m<y;m++)v(m);return a}(M,_,k,m,g,"preserveStartEnd"===O):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,l=function(e){var n,l=i[e],f=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var p=t*(l.coordinate+t*f()/2-u);i[e]=l=h(h({},l),{},{tickCoord:p>0?l.coordinate-p*t:l.coordinate})}else i[e]=l=h(h({},l),{},{tickCoord:l.coordinate});s(t,l.tickCoord,f,c,u)&&(u=l.tickCoord-t*(f()/2+o),i[e]=h(h({},l),{},{isShow:!0}))},f=a-1;f>=0;f--)l(f);return i}(M,_,k,m,g)).filter(function(t){return t.isShow})}},36713:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},36730:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},36815:(t,e,r)=>{var n=r(4217),o=r(67460),i=r(70771),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return a;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||l.test(t)?s(t.slice(2),r?2:8):c.test(t)?a:+t}},37835:(t,e,r)=>{var n=r(17489),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g;t.exports=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e})},37929:t=>{t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},38008:(t,e,r)=>{t.exports=r(83711)(r(82500),"Set")},38406:(t,e,r)=>{var n=r(85090),o=r(92313),i=r(82954);t.exports=function(t,e,r,a,c,u){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=u.get(t),h=u.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,u):a(m,b,d,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,u))){y=!1;break}}return u.delete(t),u.delete(e),y}},38637:(t,e,r)=>{t.exports=r(79399)()},38649:(t,e,r)=>{var n=r(38675),o=r(43720),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;t.exports=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return i.call(t,e)})}:o},38675:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},38985:(t,e,r)=>{var n=r(30152),o=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!o&&o in t}},39608:t=>{t.exports=Array.isArray},39641:(t,e,r)=>{var n=r(58817),o=r(22471),i=r(99544),a=r(67460);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(o(r)&&i(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},39984:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},40139:(t,e,r)=>{var n=r(98233),o=r(67460);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},40382:(t,e,r)=>{t.exports=r(83711)(r(82500),"WeakMap")},40566:(t,e,r)=>{var n=r(98233),o=r(48611);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},41287:(t,e,r)=>{"use strict";r.d(e,{Q:()=>eB});var n=r(12115),o=r(59882),i=r.n(o),a=r(40139),c=r.n(a),u=r(18940),l=r.n(u),s=r(48973),f=r.n(s),p=r(67206),h=r.n(p),d=r(91959),y=r.n(d),v=r(52596),m=r(93179),b=r(72790),g=r(2348),x=r(94517),O=r(24026),w=r(51172),j=r(44538),S=r(70788),A=r(81519),P=r(79095),E=r(12814),k=r(16377);function M(t){return(M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function T(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_(Object(r),!0).forEach(function(e){C(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function C(t,e,r){var n;return(n=function(t,e){if("object"!=M(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=M(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==M(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var D=["Webkit","Moz","O","ms"],I=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=D.reduce(function(t,n){return T(T({},t),{},C({},n+r,e))},{});return n[t]=e,n};function N(t){return(N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function B(){return(B=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function R(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function L(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?R(Object(r),!0).forEach(function(e){$(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function z(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,q(n.key),n)}}function U(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(U=function(){return!!t})()}function F(t){return(F=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function W(t,e){return(W=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function $(t,e,r){return(e=q(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function q(t){var e=function(t,e){if("object"!=N(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=N(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==N(e)?e:e+""}var X=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=(0,A.z)().domain(l()(0,c)).range([o,o+i-a]),s=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:s}},H=function(t){return t.changedTouches&&!!t.changedTouches.length},V=function(t){var e,r;function o(t){var e,r,n;if(!(this instanceof o))throw TypeError("Cannot call a class as a function");return r=o,n=[t],r=F(r),$(e=function(t,e){if(e&&("object"===N(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,U()?Reflect.construct(r,n||[],F(this).constructor):r.apply(this,n)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),$(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),$(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})}),e.detachDragEndListener()}),$(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),$(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),$(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),$(e,"handleSlideDragStart",function(t){var r=H(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return o.prototype=Object.create(t&&t.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),t&&W(o,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,n=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=Math.min(e,r),l=Math.max(e,r),s=o.getIndexInRange(n,u),f=o.getIndexInRange(n,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=(0,E.kr)(r[t],o,t);return c()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=H(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||!!(o<i)&&b===t||"endX"===n&&(o>i?b%p==0:m%p==0)||!!(o>i)&&b===t};this.setState($($({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var l=u+t;if(-1!==l&&!(l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState($({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.fill,c=t.stroke;return n.createElement("rect",{stroke:c,fill:a,x:e,y:r,width:o,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.data,c=t.children,u=t.padding,l=n.Children.only(c);return l?n.cloneElement(l,{x:e,y:r,width:o,height:i,margin:u,compact:!0,data:a}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,i,a=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,d=c.startIndex,y=c.endIndex,v=Math.max(t,this.props.x),m=L(L({},(0,S.J9)(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null==(r=h[d])?void 0:r.name,", Max value: ").concat(null==(i=h[y])?void 0:i.name);return n.createElement(g.W,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},o.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,o=r.y,i=r.height,a=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return n.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:u,y:o,width:l,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,o=t.y,i=t.height,a=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return n.createElement(g.W,{className:"recharts-brush-texts"},n.createElement(P.E,B({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:o+i/2},f),this.getTextOfTick(e)),n.createElement(P.E,B({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:o+i/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,o=t.children,i=t.x,a=t.y,c=t.width,u=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,m=s.isTravellerFocused;if(!e||!e.length||!(0,k.Et)(i)||!(0,k.Et)(a)||!(0,k.Et)(c)||!(0,k.Et)(u)||c<=0||u<=0)return null;var b=(0,v.A)("recharts-brush",r),x=1===n.Children.count(o),O=I("userSelect","none");return n.createElement(g.W,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:O},this.renderBackground(),x&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||m||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,o=t.width,i=t.height,a=t.stroke,c=Math.floor(r+i/2)-1;return n.createElement(n.Fragment,null,n.createElement("rect",{x:e,y:r,width:o,height:i,fill:a,stroke:"none"}),n.createElement("line",{x1:e+1,y1:c,x2:e+o-1,y2:c,fill:"none",stroke:"#fff"}),n.createElement("line",{x1:e+1,y1:c+2,x2:e+o-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):c()(t)?t(e):o.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return L({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?X({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);t[i]>e?o=i:n=i}return e>=t[o]?o:n}}],e&&z(o.prototype,e),r&&z(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);$(V,"displayName","Brush"),$(V,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var G=r(46605),Y=r(83197),K=r(60379),J=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},Z=r(53707),Q=r(675);function tt(){return(tt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function te(t){return(te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tr(Object(r),!0).forEach(function(e){tc(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tr(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function to(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(to=function(){return!!t})()}function ti(t){return(ti=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ta(t,e){return(ta=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tc(t,e,r){return(e=tu(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tu(t){var e=function(t,e){if("object"!=te(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=te(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==te(e)?e:e+""}var tl=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=(0,Z.P2)({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return J(t,"discard")&&!i.isInRange(a)?null:a},ts=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=ti(t),function(t,e){if(e&&("object"===te(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,to()?Reflect.construct(t,e||[],ti(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&ta(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,o=t.y,i=t.r,a=t.alwaysShow,c=t.clipPathId,u=(0,k.vh)(e),l=(0,k.vh)(o);if((0,Q.R)(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!l)return null;var s=tl(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,d=h.shape,y=h.className,m=tn(tn({clipPath:J(this.props,"hidden")?"url(#".concat(c,")"):void 0},(0,S.J9)(this.props,!0)),{},{cx:f,cy:p});return n.createElement(g.W,{className:(0,v.A)("recharts-reference-dot",y)},r.renderDot(d,m),K.J.renderCallByParent(this.props,{x:f-i,y:p-i,width:2*i,height:2*i}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tu(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);tc(ts,"displayName","ReferenceDot"),tc(ts,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),tc(ts,"renderDot",function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):c()(t)?t(e):n.createElement(w.c,tt({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var tf=r(71571),tp=r.n(tf),th=r(50091);function td(t){return(td="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ty(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ty=function(){return!!t})()}function tv(t){return(tv=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tm(t,e){return(tm=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tb(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tb(Object(r),!0).forEach(function(e){tx(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tb(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tx(t,e,r){return(e=tO(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tO(t){var e=function(t,e){if("object"!=td(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=td(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==td(e)?e:e+""}function tw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tj(){return(tj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tS=function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):c()(t)?t(e):n.createElement("line",tj({},e,{className:"recharts-reference-line-line"}))},tA=function(t,e,r,n,o,i,a,c,u){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=u.y,d=t.y.apply(h,{position:i});if(J(u,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:i});if(J(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:i})});return J(u,"discard")&&tp()(g,function(e){return!t.isInRange(e)})?null:g}return null};function tP(t){var e=t.x,r=t.y,o=t.segment,i=t.xAxisId,a=t.yAxisId,c=t.shape,u=t.className,l=t.alwaysShow,s=(0,th.Yp)(),f=(0,th.AF)(i),p=(0,th.Nk)(a),h=(0,th.sk)();if(!s||!h)return null;(0,Q.R)(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var d=tA((0,Z.P2)({x:f.scale,y:p.scale}),(0,k.vh)(e),(0,k.vh)(r),o&&2===o.length,h,t.position,f.orientation,p.orientation,t);if(!d)return null;var y=function(t){if(Array.isArray(t))return t}(d)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(d,2)||function(t,e){if(t){if("string"==typeof t)return tw(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tw(t,e)}}(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),m=y[0],b=m.x,x=m.y,O=y[1],w=O.x,j=O.y,A=tg(tg({clipPath:J(t,"hidden")?"url(#".concat(s,")"):void 0},(0,S.J9)(t,!0)),{},{x1:b,y1:x,x2:w,y2:j});return n.createElement(g.W,{className:(0,v.A)("recharts-reference-line",u)},tS(c,A),K.J.renderCallByParent(t,(0,Z.vh)({x1:b,y1:x,x2:w,y2:j})))}var tE=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=tv(t),function(t,e){if(e&&("object"===td(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ty()?Reflect.construct(t,e||[],tv(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&tm(r,t),e=[{key:"render",value:function(){return n.createElement(tP,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tO(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);function tk(){return(tk=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tM(t){return(tM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t_(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tT(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t_(Object(r),!0).forEach(function(e){tN(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t_(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}tx(tE,"displayName","ReferenceLine"),tx(tE,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function tC(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tC=function(){return!!t})()}function tD(t){return(tD=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tI(t,e){return(tI=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tN(t,e,r){return(e=tB(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tB(t){var e=function(t,e){if("object"!=tM(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tM(e)?e:e+""}var tR=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=(0,Z.P2)({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!J(o,"discard")||f.isInRange(p)&&f.isInRange(h)?(0,Z.sl)(p,h):null},tL=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=tD(t),function(t,e){if(e&&("object"===tM(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,tC()?Reflect.construct(t,e||[],tD(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&tI(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,o=t.x2,i=t.y1,a=t.y2,c=t.className,u=t.alwaysShow,l=t.clipPathId;(0,Q.R)(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=(0,k.vh)(e),f=(0,k.vh)(o),p=(0,k.vh)(i),h=(0,k.vh)(a),d=this.props.shape;if(!s&&!f&&!p&&!h&&!d)return null;var y=tR(s,f,p,h,this.props);if(!y&&!d)return null;var m=J(this.props,"hidden")?"url(#".concat(l,")"):void 0;return n.createElement(g.W,{className:(0,v.A)("recharts-reference-area",c)},r.renderRect(d,tT(tT({clipPath:m},(0,S.J9)(this.props,!0)),y)),K.J.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tB(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);function tz(t){return function(t){if(Array.isArray(t))return tU(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return tU(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tU(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tU(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}tN(tL,"displayName","ReferenceArea"),tN(tL,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),tN(tL,"renderRect",function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):c()(t)?t(e):n.createElement(j.M,tk({},e,{className:"recharts-reference-area-rect"}))});var tF=function(t,e,r,n,o){var i=(0,S.aS)(t,tE),a=(0,S.aS)(t,ts),c=[].concat(tz(i),tz(a)),u=(0,S.aS)(t,tL),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&J(e.props,"extendDomain")&&(0,k.Et)(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&J(e.props,"extendDomain")&&(0,k.Et)(e.props[p])&&(0,k.Et)(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return(0,k.Et)(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},tW=r(25641),t$=r(15232),tq=r(82661),tX=new(r.n(tq)()),tH="recharts.syncMouseEvents",tV=r(43597);function tG(t){return(tG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tY(t,e,r){return(e=tK(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tK(t){var e=function(t,e){if("object"!=tG(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tG(e)?e:e+""}var tJ=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");tY(this,"activeIndex",0),tY(this,"coordinateList",[]),tY(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!=(e=null!=n?n:this.coordinateList)?e:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null==(t=window)?void 0:t.scrollX)||0,u=(null==(e=window)?void 0:e.scrollY)||0,l=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tK(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}(),tZ=r(67790),tQ=r(70688);function t0(t){return(t0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var t1=["x","y","top","left","width","height","className"];function t2(){return(t2=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function t3(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var t5=function(t){var e=t.x,r=void 0===e?0:e,o=t.y,i=void 0===o?0:o,a=t.top,c=void 0===a?0:a,u=t.left,l=void 0===u?0:u,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,d=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t3(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=t0(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t0(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t3(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:i,top:c,left:l,width:f,height:h},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,t1));return(0,k.Et)(r)&&(0,k.Et)(i)&&(0,k.Et)(f)&&(0,k.Et)(h)&&(0,k.Et)(c)&&(0,k.Et)(l)?n.createElement("path",t2({},(0,S.J9)(y,!0),{className:(0,v.A)("recharts-cross",d),d:"M".concat(r,",").concat(c,"v").concat(h,"M").concat(l,",").concat(i,"h").concat(f)})):null};function t6(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[(0,tW.IZ)(e,r,n,o),(0,tW.IZ)(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}var t8=r(77283);function t9(t){return(t9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t4(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t7(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t4(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=t9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t9(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t4(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function et(t){var e,r,o,i,a=t.element,c=t.tooltipEventType,u=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,h=t.tooltipAxisBandSize,d=t.layout,y=t.chartName,m=null!=(r=a.props.cursor)?r:null==(o=a.type.defaultProps)?void 0:o.cursor;if(!a||!m||!u||!l||"ScatterChart"!==y&&"axis"!==c)return null;var b=tQ.I;if("ScatterChart"===y)i=l,b=t5;else if("BarChart"===y)e=h/2,i={stroke:"none",fill:"#ccc",x:"horizontal"===d?l.x-e:f.left+.5,y:"horizontal"===d?f.top+.5:l.y-e,width:"horizontal"===d?h:f.width-1,height:"horizontal"===d?f.height-1:h},b=j.M;else if("radial"===d){var g=t6(l),x=g.cx,O=g.cy,w=g.radius;i={cx:x,cy:O,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:w,outerRadius:w},b=t8.h}else i={points:function(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return t6(e);else{var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=(0,tW.IZ)(c,u,l,f),h=(0,tW.IZ)(c,u,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}(d,l,f)},b=tQ.I;var A=t7(t7(t7(t7({stroke:"#ccc",pointerEvents:"none"},f),i),(0,S.J9)(m,!1)),{},{payload:s,payloadIndex:p,className:(0,v.A)("recharts-tooltip-cursor",m.className)});return(0,n.isValidElement)(m)?(0,n.cloneElement)(m,A):(0,n.createElement)(b,A)}var ee=["item"],er=["children","className","width","height","style","compact","title","desc"];function en(t){return(en="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eo(){return(eo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ei(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||ef(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ea(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function ec(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ec=function(){return!!t})()}function eu(t){return(eu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function el(t,e){return(el=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function es(t){return function(t){if(Array.isArray(t))return ep(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ef(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ef(t,e){if(t){if("string"==typeof t)return ep(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ep(t,e)}}function ep(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function eh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ed(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eh(Object(r),!0).forEach(function(e){ey(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eh(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ey(t,e,r){return(e=ev(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ev(t){var e=function(t,e){if("object"!=en(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=en(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==en(e)?e:e+""}var em={xAxis:["bottom","top"],yAxis:["left","right"]},eb={width:"100%",height:"100%"},eg={x:0,y:0};function ex(t){return t}var eO=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return ed(ed(ed({},n),(0,tW.IZ)(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return ed(ed(ed({},n),(0,tW.IZ)(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return eg},ew=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(es(t),es(r)):t},[]);return i.length>0?i:t&&t.length&&(0,k.Et)(n)&&(0,k.Et)(o)?t.slice(n,o+1):[]};function ej(t){return"number"===t?[0,"auto"]:void 0}var eS=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=ew(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var u,l,s=null!=(u=c.props.data)?u:e;if(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory){var f=void 0===s?a:s;l=(0,k.eP)(f,i.dataKey,n)}else l=s&&s[r]||a[r];return l?[].concat(es(o),[(0,E.zb)(c,l)]):o},[])},eA=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=(0,E.gH)(i,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=eS(t,e,l,s),p=eO(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},eP=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,a=e.axisIdKey,c=e.stackGroups,u=e.dataStartIndex,s=e.dataEndIndex,f=t.layout,p=t.children,h=t.stackOffset,d=(0,E._L)(f,o);return r.reduce(function(e,r){var y=void 0!==r.type.defaultProps?ed(ed({},r.type.defaultProps),r.props):r.props,v=y.type,m=y.dataKey,b=y.allowDataOverflow,g=y.allowDuplicatedCategory,x=y.scale,O=y.ticks,w=y.includeHidden,j=y[a];if(e[j])return e;var S=ew(t.data,{graphicalItems:n.filter(function(t){var e;return(a in t.props?t.props[a]:null==(e=t.type.defaultProps)?void 0:e[a])===j}),dataStartIndex:u,dataEndIndex:s}),A=S.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&(0,k.Et)(n)&&(0,k.Et)(o))return!0}return!1})(y.domain,b,v)&&(_=(0,E.AQ)(y.domain,null,b),d&&("number"===v||"auto"!==x)&&(C=(0,E.Ay)(S,m,"category")));var P=ej(v);if(!_||0===_.length){var M,_,T,C,D,I=null!=(D=y.domain)?D:P;if(m){if(_=(0,E.Ay)(S,m,v),"category"===v&&d){var N=(0,k.CG)(_);g&&N?(T=_,_=l()(0,A)):g||(_=(0,E.KC)(I,_,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(es(t),[e])},[]))}else if("category"===v)_=g?_.filter(function(t){return""!==t&&!i()(t)}):(0,E.KC)(I,_,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||i()(e)?t:[].concat(es(t),[e])},[]);else if("number"===v){var B=(0,E.A1)(S,n.filter(function(t){var e,r,n=a in t.props?t.props[a]:null==(e=t.type.defaultProps)?void 0:e[a],o="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===j&&(w||!o)}),m,o,f);B&&(_=B)}d&&("number"===v||"auto"!==x)&&(C=(0,E.Ay)(S,m,"category"))}else _=d?l()(0,A):c&&c[j]&&c[j].hasStack&&"number"===v?"expand"===h?[0,1]:(0,E.Mk)(c[j].stackGroups,u,s):(0,E.vf)(S,n.filter(function(t){var e=a in t.props?t.props[a]:t.type.defaultProps[a],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===j&&(w||!r)}),v,f,!0);"number"===v?(_=tF(p,_,j,o,O),I&&(_=(0,E.AQ)(I,_,b))):"category"===v&&I&&_.every(function(t){return I.indexOf(t)>=0})&&(_=I)}return ed(ed({},e),{},ey({},j,ed(ed({},y),{},{axisType:o,domain:_,categoricalDomain:C,duplicateDomain:T,originalDomain:null!=(M=y.domain)?M:P,isCategorical:d,layout:f})))},{})},eE=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.layout,p=t.children,h=ew(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),d=h.length,y=(0,E._L)(s,o),v=-1;return r.reduce(function(t,e){var m,b=(void 0!==e.type.defaultProps?ed(ed({},e.type.defaultProps),e.props):e.props)[i],g=ej("number");return t[b]?t:(v++,m=y?l()(0,d):a&&a[b]&&a[b].hasStack?tF(p,m=(0,E.Mk)(a[b].stackGroups,c,u),b,o):tF(p,m=(0,E.AQ)(g,(0,E.vf)(h,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null==(e=t.type.defaultProps)?void 0:e[i],o="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===b&&!o}),"number",s),n.defaultProps.allowDataOverflow),b,o),ed(ed({},t),{},ey({},b,ed(ed({axisType:o},n.defaultProps),{},{hide:!0,orientation:f()(em,"".concat(o,".").concat(v%2),null),domain:m,originalDomain:g,isCategorical:y,layout:s}))))},{})},ek=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=(0,S.aS)(l,o),p={};return f&&f.length?p=eP(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):i&&i.length&&(p=eE(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},eM=function(t){var e=(0,k.lX)(t),r=(0,E.Rh)(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:h()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:(0,E.Hj)(e,r)}},e_=function(t){var e=t.children,r=t.defaultShowTooltip,n=(0,S.BU)(e,V),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},eT=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},eC=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,p=r.margin||{},h=(0,S.BU)(s,V),d=(0,S.BU)(s,O.s),y=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:ed(ed({},t),{},ey({},n,t[n]+r.width))},{left:p.left||0,right:p.right||0}),v=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:ed(ed({},t),{},ey({},n,f()(t,"".concat(n))+r.height))},{top:p.top||0,bottom:p.bottom||0}),m=ed(ed({},v),y),b=m.bottom;h&&(m.bottom+=h.props.height||V.defaultProps.height),d&&e&&(m=(0,E.s0)(m,n,r,e));var g=u-m.left-m.right,x=l-m.top-m.bottom;return ed(ed({brushBottom:b},m),{},{width:Math.max(g,0),height:Math.max(x,0)})},eD=r(62341),eI=r(96025),eN=r(16238),eB=function(t){var e=t.chartName,r=t.GraphicalChild,o=t.defaultTooltipEventType,a=void 0===o?"axis":o,u=t.validateTooltipEventTypes,l=void 0===u?["axis"]:u,s=t.axisComponents,p=t.legendContent,h=t.formatAxisMap,d=t.defaultProps,O=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,a=e.updateId,c=e.dataStartIndex,u=e.dataEndIndex,l=t.barSize,f=t.layout,p=t.barGap,h=t.barCategoryGap,d=t.maxBarSize,y=eT(f),v=y.numericAxisName,b=y.cateAxisName,g=!!r&&!!r.length&&r.some(function(t){var e=(0,S.Mn)(t&&t.type);return e&&e.indexOf("Bar")>=0}),x=[];return r.forEach(function(r,y){var O=ew(t.data,{graphicalItems:[r],dataStartIndex:c,dataEndIndex:u}),w=void 0!==r.type.defaultProps?ed(ed({},r.type.defaultProps),r.props):r.props,j=w.dataKey,A=w.maxBarSize,P=w["".concat(v,"Id")],k=w["".concat(b,"Id")],M=s.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=w["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||(0,m.A)(!1);var i=n[o];return ed(ed({},t),{},ey(ey({},r.axisType,i),"".concat(r.axisType,"Ticks"),(0,E.Rh)(i)))},{}),_=M[b],T=M["".concat(b,"Ticks")],C=n&&n[P]&&n[P].hasStack&&(0,E.kA)(r,n[P].stackGroups),D=(0,S.Mn)(r.type).indexOf("Bar")>=0,I=(0,E.Hj)(_,T),N=[],B=g&&(0,E.tA)({barSize:l,stackGroups:n,totalSize:"xAxis"===b?M[b].width:"yAxis"===b?M[b].height:void 0});if(D){var R,L,z=i()(A)?d:A,U=null!=(R=null!=(L=(0,E.Hj)(_,T,!0))?L:z)?R:0;N=(0,E.BX)({barGap:p,barCategoryGap:h,bandSize:U!==I?U:I,sizeList:B[k],maxBarSize:z}),U!==I&&(N=N.map(function(t){return ed(ed({},t),{},{position:ed(ed({},t.position),{},{offset:t.position.offset-U/2})})}))}var F=r&&r.type&&r.type.getComposedData;F&&x.push({props:ed(ed({},F(ed(ed({},M),{},{displayedData:O,props:t,dataKey:j,item:r,bandSize:I,barPosition:N,offset:o,stackedData:C,layout:f,dataStartIndex:c,dataEndIndex:u}))),{},ey(ey(ey({key:r.key||"item-".concat(y)},v,M[v]),b,M[b]),"animationId",a)),childIndex:(0,S.AW)(r,t.children),item:r})}),x},A=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!(0,S.Me)({props:o}))return null;var u=o.children,l=o.layout,f=o.stackOffset,p=o.data,d=o.reverseStackOrder,y=eT(l),v=y.numericAxisName,m=y.cateAxisName,b=(0,S.aS)(u,r),g=(0,E.Mn)(p,b,"".concat(v,"Id"),"".concat(m,"Id"),f,d),x=s.reduce(function(t,e){var r="".concat(e.axisType,"Map");return ed(ed({},t),{},ey({},r,ek(o,ed(ed({},e),{},{graphicalItems:b,stackGroups:e.axisType===v&&g,dataStartIndex:i,dataEndIndex:a}))))},{}),w=eC(ed(ed({},x),{},{props:o,graphicalItems:b}),null==n?void 0:n.legendBBox);Object.keys(x).forEach(function(t){x[t]=h(o,x[t],w,t.replace("Map",""),e)});var j=eM(x["".concat(m,"Map")]),A=O(o,ed(ed({},x),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:b,stackGroups:g,offset:w}));return ed(ed({formattedGraphicalItems:A,graphicalItems:b,offset:w,stackGroups:g},j),x)},P=function(t){var r;function o(t){var r,a,u,l,s;if(!(this instanceof o))throw TypeError("Cannot call a class as a function");return l=o,s=[t],l=eu(l),ey(u=function(t,e){if(e&&("object"===en(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ec()?Reflect.construct(l,s||[],eu(this).constructor):l.apply(this,s)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),ey(u,"accessibilityManager",new tJ),ey(u,"handleLegendBBoxUpdate",function(t){if(t){var e=u.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;u.setState(ed({legendBBox:t},A({props:u.props,dataStartIndex:r,dataEndIndex:n,updateId:o},ed(ed({},u.state),{},{legendBBox:t}))))}}),ey(u,"handleReceiveSyncEvent",function(t,e,r){u.props.syncId===t&&(r!==u.eventEmitterSymbol||"function"==typeof u.props.syncMethod)&&u.applySyncEvent(e)}),ey(u,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==u.state.dataStartIndex||r!==u.state.dataEndIndex){var n=u.state.updateId;u.setState(function(){return ed({dataStartIndex:e,dataEndIndex:r},A({props:u.props,dataStartIndex:e,dataEndIndex:r,updateId:n},u.state))}),u.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),ey(u,"handleMouseEnter",function(t){var e=u.getMouseInfo(t);if(e){var r=ed(ed({},e),{},{isTooltipActive:!0});u.setState(r),u.triggerSyncEvent(r);var n=u.props.onMouseEnter;c()(n)&&n(r,t)}}),ey(u,"triggeredAfterMouseMove",function(t){var e=u.getMouseInfo(t),r=e?ed(ed({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};u.setState(r),u.triggerSyncEvent(r);var n=u.props.onMouseMove;c()(n)&&n(r,t)}),ey(u,"handleItemMouseEnter",function(t){u.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),ey(u,"handleItemMouseLeave",function(){u.setState(function(){return{isTooltipActive:!1}})}),ey(u,"handleMouseMove",function(t){t.persist(),u.throttleTriggeredAfterMouseMove(t)}),ey(u,"handleMouseLeave",function(t){u.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};u.setState(e),u.triggerSyncEvent(e);var r=u.props.onMouseLeave;c()(r)&&r(e,t)}),ey(u,"handleOuterEvent",function(t){var e,r,n=(0,S.X_)(t),o=f()(u.props,"".concat(n));n&&c()(o)&&o(null!=(e=/.*touch.*/i.test(n)?u.getMouseInfo(t.changedTouches[0]):u.getMouseInfo(t))?e:{},t)}),ey(u,"handleClick",function(t){var e=u.getMouseInfo(t);if(e){var r=ed(ed({},e),{},{isTooltipActive:!0});u.setState(r),u.triggerSyncEvent(r);var n=u.props.onClick;c()(n)&&n(r,t)}}),ey(u,"handleMouseDown",function(t){var e=u.props.onMouseDown;c()(e)&&e(u.getMouseInfo(t),t)}),ey(u,"handleMouseUp",function(t){var e=u.props.onMouseUp;c()(e)&&e(u.getMouseInfo(t),t)}),ey(u,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),ey(u,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.handleMouseDown(t.changedTouches[0])}),ey(u,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.handleMouseUp(t.changedTouches[0])}),ey(u,"handleDoubleClick",function(t){var e=u.props.onDoubleClick;c()(e)&&e(u.getMouseInfo(t),t)}),ey(u,"handleContextMenu",function(t){var e=u.props.onContextMenu;c()(e)&&e(u.getMouseInfo(t),t)}),ey(u,"triggerSyncEvent",function(t){void 0!==u.props.syncId&&tX.emit(tH,u.props.syncId,t,u.eventEmitterSymbol)}),ey(u,"applySyncEvent",function(t){var e=u.props,r=e.layout,n=e.syncMethod,o=u.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)u.setState(ed({dataStartIndex:i,dataEndIndex:a},A({props:u.props,dataStartIndex:i,dataEndIndex:a,updateId:o},u.state)));else if(void 0!==t.activeTooltipIndex){var c=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=u.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var y=ed(ed({},p),{},{x:p.left,y:p.top}),v=Math.min(c,y.x+y.width),m=Math.min(l,y.y+y.height),b=h[s]&&h[s].value,g=eS(u.state,u.props.data,s),x=h[s]?{x:"horizontal"===r?h[s].coordinate:v,y:"horizontal"===r?m:h[s].coordinate}:eg;u.setState(ed(ed({},t),{},{activeLabel:b,activeCoordinate:x,activePayload:g,activeTooltipIndex:s}))}else u.setState(t)}),ey(u,"renderCursor",function(t){var r,o=u.state,i=o.isTooltipActive,a=o.activeCoordinate,c=o.activePayload,l=o.offset,s=o.activeTooltipIndex,f=o.tooltipAxisBandSize,p=u.getTooltipEventType(),h=null!=(r=t.props.active)?r:i,d=u.props.layout,y=t.key||"_recharts-cursor";return n.createElement(et,{key:y,activeCoordinate:a,activePayload:c,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),ey(u,"renderPolarAxis",function(t,e,r){var o=f()(t,"type.axisType"),i=f()(u.state,"".concat(o,"Map")),a=t.type.defaultProps,c=void 0!==a?ed(ed({},a),t.props):t.props,l=i&&i[c["".concat(o,"Id")]];return(0,n.cloneElement)(t,ed(ed({},l),{},{className:(0,v.A)(o,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:(0,E.Rh)(l,!0)}))}),ey(u,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,o=e.polarAngles,i=e.polarRadius,a=u.state,c=a.radiusAxisMap,l=a.angleAxisMap,s=(0,k.lX)(c),f=(0,k.lX)(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,n.cloneElement)(t,{polarAngles:Array.isArray(o)?o:(0,E.Rh)(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:(0,E.Rh)(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),ey(u,"renderLegend",function(){var t=u.state.formattedGraphicalItems,e=u.props,r=e.children,o=e.width,i=e.height,a=u.props.margin||{},c=o-(a.left||0)-(a.right||0),l=(0,Y.g)({children:r,formattedGraphicalItems:t,legendWidth:c,legendContent:p});if(!l)return null;var s=l.item,f=ea(l,ee);return(0,n.cloneElement)(s,ed(ed({},f),{},{chartWidth:o,chartHeight:i,margin:a,onBBoxUpdate:u.handleLegendBBoxUpdate}))}),ey(u,"renderTooltip",function(){var t,e=u.props,r=e.children,o=e.accessibilityLayer,i=(0,S.BU)(r,x.m);if(!i)return null;var a=u.state,c=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!=(t=i.props.active)?t:c;return(0,n.cloneElement)(i,{viewBox:ed(ed({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:o})}),ey(u,"renderBrush",function(t){var e=u.props,r=e.margin,o=e.data,i=u.state,a=i.offset,c=i.dataStartIndex,l=i.dataEndIndex,s=i.updateId;return(0,n.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:(0,E.HQ)(u.handleBrushChange,t.props.onChange),data:o,x:(0,k.Et)(t.props.x)?t.props.x:a.left,y:(0,k.Et)(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:(0,k.Et)(t.props.width)?t.props.width:a.width,startIndex:c,endIndex:l,updateId:"brush-".concat(s)})}),ey(u,"renderReferenceElement",function(t,e,r){if(!t)return null;var o=u.clipPathId,i=u.state,a=i.xAxisMap,c=i.yAxisMap,l=i.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,n.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:c[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:o})}),ey(u,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,n=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?ed(ed({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=ed(ed({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:(0,E.Ps)(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},(0,S.J9)(s,!1)),(0,tV._U)(s));return c.push(o.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(i))),n?c.push(o.renderActiveDot(s,ed(ed({},f),{},{cx:n.x,cy:n.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c}),ey(u,"renderGraphicChild",function(t,e,r){var o=u.filterFormatItem(t,e,r);if(!o)return null;var a=u.getTooltipEventType(),c=u.state,l=c.isTooltipActive,s=c.tooltipAxis,f=c.activeTooltipIndex,p=c.activeLabel,h=u.props.children,d=(0,S.BU)(h,x.m),y=o.props,v=y.points,m=y.isRange,b=y.baseLine,g=void 0!==o.item.type.defaultProps?ed(ed({},o.item.type.defaultProps),o.item.props):o.item.props,O=g.activeDot,w=g.hide,j=g.activeBar,A=g.activeShape,P=!!(!w&&l&&d&&(O||j||A)),M={};"axis"!==a&&d&&"click"===d.props.trigger?M={onClick:(0,E.HQ)(u.handleItemMouseEnter,t.props.onClick)}:"axis"!==a&&(M={onMouseLeave:(0,E.HQ)(u.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:(0,E.HQ)(u.handleItemMouseEnter,t.props.onMouseEnter)});var _=(0,n.cloneElement)(t,ed(ed({},o.props),M));if(P)if(f>=0){if(s.dataKey&&!s.allowDuplicatedCategory){var T="function"==typeof s.dataKey?function(t){return"function"==typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());D=(0,k.eP)(v,T,p),I=m&&b&&(0,k.eP)(b,T,p)}else D=null==v?void 0:v[f],I=m&&b&&b[f];if(A||j){var C=void 0!==t.props.activeIndex?t.props.activeIndex:f;return[(0,n.cloneElement)(t,ed(ed(ed({},o.props),M),{},{activeIndex:C})),null,null]}if(!i()(D))return[_].concat(es(u.renderActivePoints({item:o,activePoint:D,basePoint:I,childIndex:f,isRange:m})))}else{var D,I,N,B=(null!=(N=u.getItemByXY(u.state.activeCoordinate))?N:{graphicalItem:_}).graphicalItem,R=B.item,L=void 0===R?t:R,z=B.childIndex,U=ed(ed(ed({},o.props),M),{},{activeIndex:z});return[(0,n.cloneElement)(L,U),null,null]}return m?[_,null,null]:[_,null]}),ey(u,"renderCustomized",function(t,e,r){return(0,n.cloneElement)(t,ed(ed({key:"recharts-customized-".concat(r)},u.props),u.state))}),ey(u,"renderMap",{CartesianGrid:{handler:ex,once:!0},ReferenceArea:{handler:u.renderReferenceElement},ReferenceLine:{handler:ex},ReferenceDot:{handler:u.renderReferenceElement},XAxis:{handler:ex},YAxis:{handler:ex},Brush:{handler:u.renderBrush,once:!0},Bar:{handler:u.renderGraphicChild},Line:{handler:u.renderGraphicChild},Area:{handler:u.renderGraphicChild},Radar:{handler:u.renderGraphicChild},RadialBar:{handler:u.renderGraphicChild},Scatter:{handler:u.renderGraphicChild},Pie:{handler:u.renderGraphicChild},Funnel:{handler:u.renderGraphicChild},Tooltip:{handler:u.renderCursor,once:!0},PolarGrid:{handler:u.renderPolarGrid,once:!0},PolarAngleAxis:{handler:u.renderPolarAxis},PolarRadiusAxis:{handler:u.renderPolarAxis},Customized:{handler:u.renderCustomized}}),u.clipPathId="".concat(null!=(r=t.id)?r:(0,k.NF)("recharts"),"-clip"),u.throttleTriggeredAfterMouseMove=y()(u.triggeredAfterMouseMove,null!=(a=t.throttleDelay)?a:1e3/60),u.state={},u}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return o.prototype=Object.create(t&&t.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),t&&el(o,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(t=this.props.margin.left)?t:0,top:null!=(e=this.props.margin.top)?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=(0,S.BU)(e,x.m);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=eS(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=ed(ed({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){(0,S.OV)([(0,S.BU)(t.children,x.m)],[(0,S.BU)(this.props.children,x.m)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=(0,S.BU)(this.props.children,x.m);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return l.indexOf(e)>=0?e:a}return a}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n=(0,G.A3)(r),o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap,s=this.getTooltipEventType(),f=eA(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&u&&l){var p=(0,k.lX)(u).scale,h=(0,k.lX)(l).scale,d=p&&p.invert?p.invert(o.chartX):null,y=h&&h.invert?h.invert(o.chartY):null;return ed(ed({},o),{},{xValue:d,yValue:y},f)}return f?ed(ed({},o),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;if(u&&l){var s=(0,k.lX)(u);return(0,tW.yy)({x:o,y:i},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=(0,S.BU)(t,x.m),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),ed(ed({},(0,tV._U)(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){tX.on(tH,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){tX.removeListener(tH,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===(0,S.Mn)(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,o=e.top,i=e.height,a=e.width;return n.createElement("defs",null,n.createElement("clipPath",{id:t},n.createElement("rect",{x:r,y:o,height:i,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=ei(e,2),n=r[0],o=r[1];return ed(ed({},t),{},ey({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=ei(e,2),n=r[0],o=r[1];return ed(ed({},t),{},ey({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null==(e=this.state.xAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null==(e=this.state.yAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?ed(ed({},u.type.defaultProps),u.props):u.props,s=(0,S.Mn)(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return(0,j.J)(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return(0,tW.yy)(t,e)});if(p)return{graphicalItem:a,payload:p}}else if((0,tZ.NE)(a,n)||(0,tZ.nZ)(a,n)||(0,tZ.xQ)(a,n)){var h=(0,tZ.GG)({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:ed(ed({},a),{},{childIndex:d}),payload:(0,tZ.xQ)(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!(0,S.Me)(this))return null;var o=this.props,i=o.children,a=o.className,c=o.width,u=o.height,l=o.style,s=o.compact,f=o.title,p=o.desc,h=ea(o,er),d=(0,S.J9)(h,!1);if(s)return n.createElement(th.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement(b.u,eo({},d,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),(0,S.ee)(i,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!=(t=this.props.tabIndex)?t:0,d.role=null!=(e=this.props.role)?e:"application",d.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},d.onFocus=function(){r.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return n.createElement(th.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement("div",eo({className:(0,v.A)("recharts-wrapper",a),style:ed({position:"relative",cursor:"default",width:c,height:u},l)},y,{ref:function(t){r.container=t}}),n.createElement(b.u,eo({},d,{width:c,height:u,title:f,desc:p,style:eb}),this.renderClipPath(),(0,S.ee)(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ev(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.Component);ey(P,"displayName",e),ey(P,"defaultProps",ed({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},d)),ey(P,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,a=t.width,c=t.height,u=t.layout,l=t.stackOffset,s=t.margin,f=e.dataStartIndex,p=e.dataEndIndex;if(void 0===e.updateId){var h=e_(t);return ed(ed(ed({},h),{},{updateId:0},A(ed(ed({props:t},h),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:c,prevLayout:u,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||a!==e.prevWidth||c!==e.prevHeight||u!==e.prevLayout||l!==e.prevStackOffset||!(0,t$.b)(s,e.prevMargin)){var d=e_(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=ed(ed({},eA(e,n,u)),{},{updateId:e.updateId+1}),m=ed(ed(ed({},d),y),v);return ed(ed(ed({},m),A(ed({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:c,prevLayout:u,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(!(0,S.OV)(o,e.prevChildren)){var b,g,x,O,w=(0,S.BU)(o,V),j=w&&null!=(b=null==(g=w.props)?void 0:g.startIndex)?b:f,P=w&&null!=(x=null==(O=w.props)?void 0:O.endIndex)?x:p,E=i()(n)||j!==f||P!==p?e.updateId+1:e.updateId;return ed(ed({updateId:E},A(ed(ed({props:t},e),{},{updateId:E,dataStartIndex:j,dataEndIndex:P}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:P})}return null}),ey(P,"renderActiveDot",function(t,e,r){var o;return o=(0,n.isValidElement)(t)?(0,n.cloneElement)(t,e):c()(t)?t(e):n.createElement(w.c,e),n.createElement(g.W,{className:"recharts-active-dot",key:r},o)});var M=(0,n.forwardRef)(function(t,e){return n.createElement(P,eo({},t,{ref:e}))});return M.displayName=P.displayName,M}({chartName:"AreaChart",GraphicalChild:eD.G,axisComponents:[{axisType:"xAxis",AxisComp:eI.W},{axisType:"yAxis",AxisComp:eN.h}],formatAxisMap:Z.pr})},41643:(t,e,r)=>{"use strict";r.d(e,{m:()=>n});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return n[t]},set:function(t,e){if("string"==typeof t)n[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){n[e]=t[e]})}}}},42233:(t,e,r)=>{var n=r(36815),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},43597:(t,e,r)=>{"use strict";r.d(e,{QQ:()=>c,VU:()=>l,XC:()=>p,_U:()=>f,j2:()=>s});var n=r(12115),o=r(67460),i=r.n(o);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var c=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],u=["points","pathLength"],l={svg:["viewBox","children"],polygon:u,polyline:u},s=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],f=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,n.isValidElement)(t)&&(r=t.props),!i()(r))return null;var o={};return Object.keys(r).forEach(function(t){s.includes(t)&&(o[t]=e||function(e){return r[t](r,e)})}),o},p=function(t,e,r){if(!i()(t)||"object"!==a(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];s.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t){return i(e,r,t),null})}),n}},43720:t=>{t.exports=function(){return[]}},44101:(t,e,r)=>{t.exports=r(83711)(r(82500),"DataView")},44158:t=>{t.exports=function(){return!1}},44482:(t,e,r)=>{var n=r(98233),o=r(73726),i=r(48611),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,l=c.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==l}},44538:(t,e,r)=>{"use strict";r.d(e,{J:()=>h,M:()=>y});var n=r(12115),o=r(52596),i=r(9557),a=r(70788);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var p=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),i+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),i+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),i+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},h=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+c),f=Math.max(i,i+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},d={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},y=function(t){var e,r=f(f({},d),t),c=(0,n.useRef)(),s=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return l(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),h=s[0],y=s[1];(0,n.useEffect)(function(){if(c.current&&c.current.getTotalLength)try{var t=c.current.getTotalLength();t&&y(t)}catch(t){}},[]);var v=r.x,m=r.y,b=r.width,g=r.height,x=r.radius,O=r.className,w=r.animationEasing,j=r.animationDuration,S=r.animationBegin,A=r.isAnimationActive,P=r.isUpdateAnimationActive;if(v!==+v||m!==+m||b!==+b||g!==+g||0===b||0===g)return null;var E=(0,o.A)("recharts-rectangle",O);return P?n.createElement(i.Ay,{canBegin:h>0,from:{width:b,height:g,x:v,y:m},to:{width:b,height:g,x:v,y:m},duration:j,animationEasing:w,isActive:P},function(t){var e=t.width,o=t.height,l=t.x,s=t.y;return n.createElement(i.Ay,{canBegin:h>0,from:"0px ".concat(-1===h?1:h,"px"),to:"".concat(h,"px 0px"),attributeName:"strokeDasharray",begin:S,duration:j,isActive:A,easing:w},n.createElement("path",u({},(0,a.J9)(r,!0),{className:E,d:p(l,s,e,o,x),ref:c})))}):n.createElement("path",u({},(0,a.J9)(r,!0),{className:E,d:p(v,m,b,g,x)}))}},45167:(t,e,r)=>{"use strict";r.d(e,{u:()=>T});var n=r(12115),o=r(40139),i=r.n(o),a=r(48973),c=r.n(a),u=r(52596),l=r(15232),s=r(2348),f=r(79095),p=r(60379),h=r(16377),d=r(43597),y=r(70788),v=r(36447),m=["viewBox"],b=["viewBox"],g=["ticks"];function x(t){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function O(){return(O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?w(Object(r),!0).forEach(function(e){M(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function S(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function A(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,_(n.key),n)}}function P(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(P=function(){return!!t})()}function E(t){return(E=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function k(t,e){return(k=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function M(t,e,r){return(e=_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _(t){var e=function(t,e){if("object"!=x(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=x(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==x(e)?e:e+""}var T=function(t){var e,r;function o(t){var e,r,n;if(!(this instanceof o))throw TypeError("Cannot call a class as a function");return r=o,n=[t],r=E(r),(e=function(t,e){if(e&&("object"===x(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,P()?Reflect.construct(r,n||[],E(this).constructor):r.apply(this,n))).state={fontSize:"",letterSpacing:""},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return o.prototype=Object.create(t&&t.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),t&&k(o,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=S(t,m),o=this.props,i=o.viewBox,a=S(o,b);return!(0,l.b)(r,i)||!(0,l.b)(n,a)||!(0,l.b)(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,d=c.tickSize,y=c.mirror,v=c.tickMargin,m=y?-1:1,b=t.tickSize||d,g=(0,h.Et)(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+!y*f)-m*b)-m*v,i=g;break;case"left":n=o=t.coordinate,i=(e=(r=u+!y*s)-m*b)-m*v,a=g;break;case"right":n=o=t.coordinate,i=(e=(r=u+y*s)+m*b)+m*v,a=g;break;default:e=r=t.coordinate,a=(n=(o=l+y*f)+m*b)+m*v,i=g}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.orientation,l=t.mirror,s=t.axisLine,f=j(j(j({},(0,y.J9)(this.props,!1)),(0,y.J9)(s,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var p=+("top"===a&&!l||"bottom"===a&&l);f=j(j({},f),{},{x1:e,y1:r+p*i,x2:e+o,y2:r+p*i})}else{var h=+("left"===a&&!l||"right"===a&&l);f=j(j({},f),{},{x1:e+h*o,y1:r,x2:e+h*o,y2:r+i})}return n.createElement("line",O({},f,{className:(0,u.A)("recharts-cartesian-axis-line",c()(s,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var a=this,l=this.props,f=l.tickLine,p=l.stroke,h=l.tick,m=l.tickFormatter,b=l.unit,g=(0,v.f)(j(j({},this.props),{},{ticks:t}),e,r),x=this.getTickTextAnchor(),w=this.getTickVerticalAnchor(),S=(0,y.J9)(this.props,!1),A=(0,y.J9)(h,!1),P=j(j({},S),{},{fill:"none"},(0,y.J9)(f,!1)),E=g.map(function(t,e){var r=a.getTickLineCoord(t),l=r.line,y=r.tick,v=j(j(j(j({textAnchor:x,verticalAnchor:w},S),{},{stroke:"none",fill:p},A),y),{},{index:e,payload:t,visibleTicksCount:g.length,tickFormatter:m});return n.createElement(s.W,O({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,d.XC)(a.props,t,e)),f&&n.createElement("line",O({},P,l,{className:(0,u.A)("recharts-cartesian-axis-tick-line",c()(f,"className"))})),h&&o.renderTickItem(h,v,"".concat(i()(m)?m(t.value,e):t.value).concat(b||"")))});return n.createElement("g",{className:"recharts-cartesian-axis-ticks"},E)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,o=e.width,a=e.height,c=e.ticksGenerator,l=e.className;if(e.hide)return null;var f=this.props,h=f.ticks,d=S(f,g),y=h;return(i()(c)&&(y=c(h&&h.length>0?this.props:d)),o<=0||a<=0||!y||!y.length)?null:n.createElement(s.W,{className:(0,u.A)("recharts-cartesian-axis",l),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),p.J.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var o,a=(0,u.A)(e.className,"recharts-cartesian-axis-tick-value");return n.isValidElement(t)?n.cloneElement(t,j(j({},e),{},{className:a})):i()(t)?t(j(j({},e),{},{className:a})):n.createElement(f.E,O({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&A(o.prototype,e),r&&A(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.Component);M(T,"displayName","CartesianAxis"),M(T,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},45170:(t,e,r)=>{var n=r(54360);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},45964:(t,e,r)=>{var n=r(67460),o=r(76685),i=r(36815),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=s}function x(){var t,r,n,i=o();if(g(i))return O(i);p=setTimeout(x,(t=i-h,r=i-d,n=e-t,v?c(n,s-r):n))}function O(t){return(p=void 0,m&&u)?b(t):(u=l=void 0,f)}function w(){var t,r=o(),n=g(r);if(u=arguments,l=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),w.cancel=function(){void 0!==p&&clearTimeout(p),d=0,u=h=l=p=void 0},w.flush=function(){return void 0===p?f:O(o())},w}},46605:(t,e,r)=>{"use strict";r.d(e,{A3:()=>p,Pu:()=>f});var n=r(41643);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=o(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var u={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s="recharts_measurement_span",f=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||n.m.isSsr)return{width:0,height:0};var o=(Object.keys(e=a({},r)).forEach(function(t){e[t]||delete e[t]}),e),i=JSON.stringify({text:t,copyStyle:o});if(u.widthCache[i])return u.widthCache[i];try{var c=document.getElementById(s);c||((c=document.createElement("span")).setAttribute("id",s),c.setAttribute("aria-hidden","true"),document.body.appendChild(c));var f=a(a({},l),o);Object.assign(c.style,f),c.textContent="".concat(t);var p=c.getBoundingClientRect(),h={width:p.width,height:p.height};return u.widthCache[i]=h,++u.cacheCount>2e3&&(u.cacheCount=0,u.widthCache={}),h}catch(t){return{width:0,height:0}}},p=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}}},47995:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},48611:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},48628:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},48659:(t,e,r)=>{var n=r(37929);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},48973:(t,e,r)=>{var n=r(34711);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},49840:(t,e,r)=>{t=r.nmd(t);var n=r(7985),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,c=function(){try{var t=i&&i.require&&i.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},49872:(t,e,r)=>{var n=r(22471);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,c=Object(r);(e?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},50091:(t,e,r)=>{"use strict";r.d(e,{DR:()=>g,pj:()=>w,rY:()=>k,yi:()=>E,Yp:()=>x,hj:()=>P,sk:()=>A,AF:()=>O,Nk:()=>S,$G:()=>j});var n=r(12115),o=r(93179),i=r(97124),a=r.n(i),c=r(29794),u=r.n(c),l=r(91113),s=r.n(l)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),f=r(16377),p=(0,n.createContext)(void 0),h=(0,n.createContext)(void 0),d=(0,n.createContext)(void 0),y=(0,n.createContext)({}),v=(0,n.createContext)(void 0),m=(0,n.createContext)(0),b=(0,n.createContext)(0),g=function(t){var e=t.state,r=e.xAxisMap,o=e.yAxisMap,i=e.offset,a=t.clipPathId,c=t.children,u=t.width,l=t.height,f=s(i);return n.createElement(p.Provider,{value:r},n.createElement(h.Provider,{value:o},n.createElement(y.Provider,{value:i},n.createElement(d.Provider,{value:f},n.createElement(v.Provider,{value:a},n.createElement(m.Provider,{value:l},n.createElement(b.Provider,{value:u},c)))))))},x=function(){return(0,n.useContext)(v)},O=function(t){var e=(0,n.useContext)(p);null==e&&(0,o.A)(!1);var r=e[t];return null==r&&(0,o.A)(!1),r},w=function(){var t=(0,n.useContext)(p);return(0,f.lX)(t)},j=function(){var t=(0,n.useContext)(h);return a()(t,function(t){return u()(t.domain,Number.isFinite)})||(0,f.lX)(t)},S=function(t){var e=(0,n.useContext)(h);null==e&&(0,o.A)(!1);var r=e[t];return null==r&&(0,o.A)(!1),r},A=function(){return(0,n.useContext)(d)},P=function(){return(0,n.useContext)(y)},E=function(){return(0,n.useContext)(b)},k=function(){return(0,n.useContext)(m)}},50111:(t,e,r)=>{var n=r(73800),o=r(9813),i=r(39608),a=r(99544),c=r(15631),u=r(94356);t.exports=function(t,e,r){e=n(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=u(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(i(t)||o(t))}},50523:(t,e,r)=>{var n=r(24376),o=r(76957),i=r(58817),a=r(38406),c=r(90724),u=r(74166),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new o(t),new o(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=c;case"[object Set]":var d=1&n;if(h||(h=u),t.size!=e.size&&!d)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},50664:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},50687:t=>{t.exports=function(t){return function(){return t}}},50851:t=>{t.exports=function(t){return t.split("")}},51172:(t,e,r)=>{"use strict";r.d(e,{c:()=>u});var n=r(12115),o=r(52596),i=r(43597),a=r(70788);function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var u=function(t){var e=t.cx,r=t.cy,u=t.r,l=t.className,s=(0,o.A)("recharts-dot",l);return e===+e&&r===+r&&u===+u?n.createElement("circle",c({},(0,a.J9)(t,!1),(0,i._U)(t),{className:s,cx:e,cy:r,r:u})):null}},51445:(t,e,r)=>{var n=r(53516);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},51911:(t,e,r)=>{var n=r(69229),o=r(48611);t.exports=function t(e,r,i,a,c){return e===r||(null!=e&&null!=r&&(o(e)||o(r))?n(e,r,i,a,t,c):e!=e&&r!=r)}},52521:t=>{t.exports=function(t,e){return t<e}},52596:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=function(){for(var t,e,r=0,n="",o=arguments.length;r<o;r++)(t=arguments[r])&&(e=function t(e){var r,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e)){var i=e.length;for(r=0;r<i;r++)e[r]&&(n=t(e[r]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n);return o}(t))&&(n&&(n+=" "),n+=e);return n}},53516:(t,e,r)=>{var n=r(20480);t.exports=r(49872)(n)},53696:(t,e,r)=>{var n=r(75899),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},53707:(t,e,r)=>{"use strict";r.d(e,{P2:()=>te,pr:()=>J,bx:()=>tr,vh:()=>Q,sl:()=>Z});var n=r(74925),o=r.n(n),i=r(29794),a=r.n(i),c=r(12814),u=r(70788),l=r(16377),s=r(12115),f=r(52596),p=r(9557),h=r(60245),d=r.n(h),y=r(59882),v=r.n(y),m=r(2348),b=r(94011),g=function(t){return null};g.displayName="Cell";var x=r(36079),O=r(41643),w=r(43597),j=r(93179),S=r(67790),A=["x","y"];function P(t){return(P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function M(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=P(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=P(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==P(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _(t,e){var r=t.x,n=t.y,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,A),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||o.height),10),u=parseInt("".concat(e.width||o.width),10);return M(M(M(M(M({},e),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function T(t){return s.createElement(S.yp,E({shapeType:"rectangle",propTransformer:_,activeClassName:"recharts-active-bar"},t))}var C=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o=(0,l.Et)(r)||(0,l.uy)(r);return o?t(r,n):(o||(0,j.A)(!1),e)}},D=["value","background"];function I(t){return(I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function N(){return(N=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function B(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function R(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?B(Object(r),!0).forEach(function(e){W(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function L(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,$(n.key),n)}}function z(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(z=function(){return!!t})()}function U(t){return(U=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function F(t,e){return(F=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function W(t,e,r){return(e=$(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function $(t){var e=function(t,e){if("object"!=I(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=I(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==I(e)?e:e+""}var q=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=U(e),W(t=function(t,e){if(e&&("object"===I(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,z()?Reflect.construct(e,r||[],U(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),W(t,"id",(0,l.NF)("recharts-bar-")),W(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),W(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&F(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,o=r.dataKey,i=r.activeIndex,a=r.activeBar,c=(0,u.J9)(this.props,!1);return t&&t.map(function(t,r){var u=r===i,l=R(R(R({},c),t),{},{isActive:u,option:u?a:n,index:r,dataKey:o,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return s.createElement(m.W,N({className:"recharts-bar-rectangle"},(0,w.XC)(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),s.createElement(T,l))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,o=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,f=this.state.prevData;return s.createElement(p.Ay,{begin:i,duration:a,isActive:o,easing:c,from:{t:0},to:{t:1},key:"bar-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var o=e.t,i=r.map(function(t,e){var r=f&&f[e];if(r){var i=(0,l.Dj)(r.x,t.x),a=(0,l.Dj)(r.y,t.y),c=(0,l.Dj)(r.width,t.width),u=(0,l.Dj)(r.height,t.height);return R(R({},t),{},{x:i(o),y:a(o),width:c(o),height:u(o)})}if("horizontal"===n){var s=(0,l.Dj)(0,t.height)(o);return R(R({},t),{},{y:t.y+t.height-s,height:s})}var p=(0,l.Dj)(0,t.width)(o);return R(R({},t),{},{width:p})});return s.createElement(m.W,null,t.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!d()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,o=e.activeIndex,i=(0,u.J9)(this.props.background,!1);return r.map(function(e,r){e.value;var a=e.background,c=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,D);if(!a)return null;var u=R(R(R(R(R({},c),{},{fill:"#eee"},a),i),(0,w.XC)(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return s.createElement(T,N({key:"background-bar-".concat(r),option:t.props.background,isActive:r===o},u))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,i=r.yAxis,a=r.layout,l=r.children,f=(0,u.aS)(l,b.u);if(!f)return null;var p="vertical"===a?n[0].height/2:n[0].width/2,h=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:(0,c.kr)(t,e)}};return s.createElement(m.W,{clipPath:t?"url(#clipPath-".concat(e,")"):null},f.map(function(t){return s.cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:a,offset:p,dataPointFormatter:h})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,o=t.xAxis,i=t.yAxis,a=t.left,c=t.top,u=t.width,l=t.height,p=t.isAnimationActive,h=t.background,d=t.id;if(e||!r||!r.length)return null;var y=this.state.isAnimationFinished,b=(0,f.A)("recharts-bar",n),g=o&&o.allowDataOverflow,O=i&&i.allowDataOverflow,w=g||O,j=v()(d)?this.id:d;return s.createElement(m.W,{className:b},g||O?s.createElement("defs",null,s.createElement("clipPath",{id:"clipPath-".concat(j)},s.createElement("rect",{x:g?a:a-u/2,y:O?c:c-l/2,width:g?u:2*u,height:O?l:2*l}))):null,s.createElement(m.W,{className:"recharts-bar-rectangles",clipPath:w?"url(#clipPath-".concat(j,")"):null},h?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(w,j),(!p||y)&&x.Z.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&L(n.prototype,e),r&&L(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(s.PureComponent);function X(t){return(X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function H(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,K(n.key),n)}}function V(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function G(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?V(Object(r),!0).forEach(function(e){Y(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):V(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Y(t,e,r){return(e=K(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function K(t){var e=function(t,e){if("object"!=X(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=X(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==X(e)?e:e+""}W(q,"displayName","Bar"),W(q,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!O.m.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),W(q,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,s=t.xAxisTicks,f=t.yAxisTicks,p=t.stackedData,h=t.dataStartIndex,d=t.displayedData,y=t.offset,v=(0,c.xi)(n,r);if(!v)return null;var m=e.layout,b=r.type.defaultProps,x=void 0!==b?R(R({},b),r.props):r.props,O=x.dataKey,w=x.children,j=x.minPointSize,S="horizontal"===m?a:i,A=p?S.scale.domain():null,P=(0,c.DW)({numericAxis:S}),E=(0,u.aS)(w,g),k=d.map(function(t,e){p?u=(0,c._f)(p[h+e],A):Array.isArray(u=(0,c.kr)(t,O))||(u=[P,u]);var n=C(j,q.defaultProps.minPointSize)(u[1],e);if("horizontal"===m){var u,d,y,b,g,x,w,S=[a.scale(u[0]),a.scale(u[1])],k=S[0],M=S[1];d=(0,c.y2)({axis:i,ticks:s,bandSize:o,offset:v.offset,entry:t,index:e}),y=null!=(w=null!=M?M:k)?w:void 0,b=v.size;var _=k-M;if(g=Number.isNaN(_)?0:_,x={x:d,y:a.y,width:b,height:a.height},Math.abs(n)>0&&Math.abs(g)<Math.abs(n)){var T=(0,l.sA)(g||n)*(Math.abs(n)-Math.abs(g));y-=T,g+=T}}else{var D=[i.scale(u[0]),i.scale(u[1])],I=D[0],N=D[1];if(d=I,y=(0,c.y2)({axis:a,ticks:f,bandSize:o,offset:v.offset,entry:t,index:e}),b=N-I,g=v.size,x={x:i.x,y:y,width:i.width,height:g},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var B=(0,l.sA)(b||n)*(Math.abs(n)-Math.abs(b));b+=B}}return R(R(R({},t),{},{x:d,y:y,width:b,height:g,value:p?u:u[1],payload:t,background:x},E&&E[e]&&E[e].props),{},{tooltipPayload:[(0,c.zb)(r,t)],tooltipPosition:{x:d+b/2,y:y+g/2}})});return R({data:k,layout:m},y)});var J=function(t,e,r,n,o){var i=t.width,a=t.height,s=t.layout,f=t.children,p=Object.keys(e),h={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},d=!!(0,u.BU)(f,q);return p.reduce(function(i,a){var u,f,p,y,v,m=e[a],b=m.orientation,g=m.domain,x=m.padding,O=void 0===x?{}:x,w=m.mirror,j=m.reversed,S="".concat(b).concat(w?"Mirror":"");if("number"===m.type&&("gap"===m.padding||"no-gap"===m.padding)){var A=g[1]-g[0],P=1/0,E=m.categoricalDomain.sort(l.ck);if(E.forEach(function(t,e){e>0&&(P=Math.min((t||0)-(E[e-1]||0),P))}),Number.isFinite(P)){var k=P/A,M="vertical"===m.layout?r.height:r.width;if("gap"===m.padding&&(u=k*M/2),"no-gap"===m.padding){var _=(0,l.F4)(t.barCategoryGap,k*M),T=k*M/2;u=T-_-(T-_)/M*_}}}f="xAxis"===n?[r.left+(O.left||0)+(u||0),r.left+r.width-(O.right||0)-(u||0)]:"yAxis"===n?"horizontal"===s?[r.top+r.height-(O.bottom||0),r.top+(O.top||0)]:[r.top+(O.top||0)+(u||0),r.top+r.height-(O.bottom||0)-(u||0)]:m.range,j&&(f=[f[1],f[0]]);var C=(0,c.W7)(m,o,d),D=C.scale,I=C.realScaleType;D.domain(g).range(f),(0,c.YB)(D);var N=(0,c.w7)(D,G(G({},m),{},{realScaleType:I}));"xAxis"===n?(v="top"===b&&!w||"bottom"===b&&w,p=r.left,y=h[S]-v*m.height):"yAxis"===n&&(v="left"===b&&!w||"right"===b&&w,p=h[S]-v*m.width,y=r.top);var B=G(G(G({},m),N),{},{realScaleType:I,x:p,y:y,scale:D,width:"xAxis"===n?r.width:m.width,height:"yAxis"===n?r.height:m.height});return B.bandSize=(0,c.Hj)(B,N),m.hide||"xAxis"!==n?m.hide||(h[S]+=(v?-1:1)*B.width):h[S]+=(v?-1:1)*B.height,G(G({},i),{},Y({},a,B))},{})},Z=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},Q=function(t){return Z({x:t.x1,y:t.y1},{x:t.x2,y:t.y2})},tt=function(){var t,e;function r(t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&H(r.prototype,t),e&&H(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();Y(tt,"EPS",1e-4);var te=function(t){var e=Object.keys(t).reduce(function(e,r){return G(G({},e),{},Y({},r,tt.create(t[r])))},{});return G(G({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,i=r.position;return o()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:i})})},isInRange:function(t){return a()(t,function(t,r){return e[r].isInRange(t)})}})},tr=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/e);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o))}},54360:(t,e,r)=>{var n=r(58817);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},54906:(t,e,r)=>{var n=r(83711);t.exports=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},55794:(t,e,r)=>{var n=r(57213),o=r(34711),i=r(18028),a=r(6305),c=r(50664),u=r(33332),l=r(9699),s=r(13465),f=r(39608);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,u(i)),c(a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},55910:(t,e,r)=>{var n=r(4854),o=r(27569),i=r(92972);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},56290:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case i:case c:case a:case p:case h:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case d:case u:return t;default:return e}}case o:return e}}}(t)===i}},56917:(t,e,r)=>{var n=r(98233),o=r(48611);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},57213:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},58096:t=>{t.exports=function(t){return this.__data__.has(t)}},58817:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},58918:(t,e,r)=>{var n=r(70771);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},59882:t=>{t.exports=function(t){return null==t}},60245:(t,e,r)=>{var n=r(51911);t.exports=function(t,e){return n(t,e)}},60379:(t,e,r)=>{"use strict";r.d(e,{J:()=>A});var n=r(12115),o=r(59882),i=r.n(o),a=r(40139),c=r.n(a),u=r(67460),l=r.n(u),s=r(52596),f=r(79095),p=r(70788),h=r(16377),d=r(25641);function y(t){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var v=["offset"];function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function g(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function x(){return(x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var O=function(t){var e=t.value,r=t.formatter,n=i()(t.children)?e:t.children;return c()(r)?r(n):n},w=function(t,e,r){var o,a,c=t.position,u=t.viewBox,l=t.offset,f=t.className,p=u.cx,y=u.cy,v=u.innerRadius,m=u.outerRadius,b=u.startAngle,g=u.endAngle,O=u.clockWise,w=(v+m)/2,j=(0,h.sA)(g-b)*Math.min(Math.abs(g-b),360),S=j>=0?1:-1;"insideStart"===c?(o=b+S*l,a=O):"insideEnd"===c?(o=g-S*l,a=!O):"end"===c&&(o=g+S*l,a=O),a=j<=0?a:!a;var A=(0,d.IZ)(p,y,w,o),P=(0,d.IZ)(p,y,w,o+(a?1:-1)*359),E="M".concat(A.x,",").concat(A.y,"\n    A").concat(w,",").concat(w,",0,1,").concat(+!a,",\n    ").concat(P.x,",").concat(P.y),k=i()(t.id)?(0,h.NF)("recharts-radial-line-"):t.id;return n.createElement("text",x({},r,{dominantBaseline:"central",className:(0,s.A)("recharts-radial-bar-label",f)}),n.createElement("defs",null,n.createElement("path",{id:k,d:E})),n.createElement("textPath",{xlinkHref:"#".concat(k)},e))},j=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e.cx,i=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=(0,d.IZ)(o,i,c+r,u),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=(0,d.IZ)(o,i,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},S=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e.x,a=e.y,c=e.width,u=e.height,s=u>=0?1:-1,f=s*n,p=s>0?"end":"start",d=s>0?"start":"end",y=c>=0?1:-1,v=y*n,m=y>0?"end":"start",b=y>0?"start":"end";if("top"===o)return g(g({},{x:i+c/2,y:a-s*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===o)return g(g({},{x:i+c/2,y:a+u+f,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===o){var x={x:i-v,y:a+u/2,textAnchor:m,verticalAnchor:"middle"};return g(g({},x),r?{width:Math.max(x.x-r.x,0),height:u}:{})}if("right"===o){var O={x:i+c+v,y:a+u/2,textAnchor:b,verticalAnchor:"middle"};return g(g({},O),r?{width:Math.max(r.x+r.width-O.x,0),height:u}:{})}var w=r?{width:c,height:u}:{};return"insideLeft"===o?g({x:i+v,y:a+u/2,textAnchor:b,verticalAnchor:"middle"},w):"insideRight"===o?g({x:i+c-v,y:a+u/2,textAnchor:m,verticalAnchor:"middle"},w):"insideTop"===o?g({x:i+c/2,y:a+f,textAnchor:"middle",verticalAnchor:d},w):"insideBottom"===o?g({x:i+c/2,y:a+u-f,textAnchor:"middle",verticalAnchor:p},w):"insideTopLeft"===o?g({x:i+v,y:a+f,textAnchor:b,verticalAnchor:d},w):"insideTopRight"===o?g({x:i+c-v,y:a+f,textAnchor:m,verticalAnchor:d},w):"insideBottomLeft"===o?g({x:i+v,y:a+u-f,textAnchor:b,verticalAnchor:p},w):"insideBottomRight"===o?g({x:i+c-v,y:a+u-f,textAnchor:m,verticalAnchor:p},w):l()(o)&&((0,h.Et)(o.x)||(0,h._3)(o.x))&&((0,h.Et)(o.y)||(0,h._3)(o.y))?g({x:i+(0,h.F4)(o.x,c),y:a+(0,h.F4)(o.y,u),textAnchor:"end",verticalAnchor:"end"},w):g({x:i+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},w)};function A(t){var e,r=t.offset,o=g({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,v)),a=o.viewBox,u=o.position,l=o.value,d=o.children,y=o.content,m=o.className,b=o.textBreakAll;if(!a||i()(l)&&i()(d)&&!(0,n.isValidElement)(y)&&!c()(y))return null;if((0,n.isValidElement)(y))return(0,n.cloneElement)(y,o);if(c()(y)){if(e=(0,n.createElement)(y,o),(0,n.isValidElement)(e))return e}else e=O(o);var A="cx"in a&&(0,h.Et)(a.cx),P=(0,p.J9)(o,!0);if(A&&("insideStart"===u||"insideEnd"===u||"end"===u))return w(o,e,P);var E=A?j(o):S(o);return n.createElement(f.E,x({className:(0,s.A)("recharts-label",void 0===m?"":m)},P,E,{breakAll:b}),e)}A.displayName="Label";var P=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,d=t.left,y=t.width,v=t.height,m=t.clockWise,b=t.labelViewBox;if(b)return b;if((0,h.Et)(y)&&(0,h.Et)(v)){if((0,h.Et)(s)&&(0,h.Et)(f))return{x:s,y:f,width:y,height:v};if((0,h.Et)(p)&&(0,h.Et)(d))return{x:p,y:d,width:y,height:v}}return(0,h.Et)(s)&&(0,h.Et)(f)?{x:s,y:f,width:0,height:0}:(0,h.Et)(e)&&(0,h.Et)(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:m}:t.viewBox?t.viewBox:{}};A.parseViewBox=P,A.renderCallByParent=function(t,e){var r,o,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var a=t.children,u=P(t),s=(0,p.aS)(a,A).map(function(t,r){return(0,n.cloneElement)(t,{viewBox:e||u,key:"label-".concat(r)})});if(!i)return s;return[(r=t.label,o=e||u,!r?null:!0===r?n.createElement(A,{key:"label-implicit",viewBox:o}):(0,h.vh)(r)?n.createElement(A,{key:"label-implicit",viewBox:o,value:r}):(0,n.isValidElement)(r)?r.type===A?(0,n.cloneElement)(r,{key:"label-implicit",viewBox:o}):n.createElement(A,{key:"label-implicit",content:r,viewBox:o}):c()(r)?n.createElement(A,{key:"label-implicit",content:r,viewBox:o}):l()(r)?n.createElement(A,x({viewBox:o},r,{key:"label-implicit"})):null)].concat(function(t){if(Array.isArray(t))return m(t)}(s)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(s)||function(t,e){if(t){if("string"==typeof t)return m(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return m(t,e)}}(s)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())}},61632:(t,e,r)=>{var n=r(16571);t.exports=r(36730)(n)},61830:(t,e,r)=>{var n=r(54360);t.exports=function(t){return n(this.__data__,t)>-1}},62341:(t,e,r)=>{"use strict";r.d(e,{G:()=>R});var n=r(12115),o=r(52596),i=r(9557),a=r(40139),c=r.n(a),u=r(22315),l=r.n(u),s=r(59882),f=r.n(s),p=r(13908),h=r.n(p),d=r(60245),y=r.n(d),v=r(70688),m=r(51172),b=r(2348),g=r(36079),x=r(41643),O=r(16377),w=r(12814),j=r(70788),S=["layout","type","stroke","connectNulls","isRange","ref"],A=["key"];function P(t){return(P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function E(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function k(){return(k=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function M(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?M(Object(r),!0).forEach(function(e){N(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function T(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,B(n.key),n)}}function C(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(C=function(){return!!t})()}function D(t){return(D=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function I(t,e){return(I=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function N(t,e,r){return(e=B(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function B(t){var e=function(t,e){if("object"!=P(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=P(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==P(e)?e:e+""}var R=function(t){var e,r;function a(){var t,e,r;if(!(this instanceof a))throw TypeError("Cannot call a class as a function");for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e=a,r=[].concat(o),e=D(e),N(t=function(t,e){if(e&&("object"===P(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,C()?Reflect.construct(e,r||[],D(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!0}),N(t,"id",(0,O.NF)("recharts-area-")),N(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),c()(e)&&e()}),N(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),c()(e)&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return a.prototype=Object.create(t&&t.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),Object.defineProperty(a,"prototype",{writable:!1}),t&&I(a,t),e=[{key:"renderDots",value:function(t,e,r){var o=this.props.isAnimationActive,i=this.state.isAnimationFinished;if(o&&!i)return null;var c=this.props,u=c.dot,l=c.points,s=c.dataKey,f=(0,j.J9)(this.props,!1),p=(0,j.J9)(u,!0),h=l.map(function(t,e){var r=_(_(_({key:"dot-".concat(e),r:3},f),p),{},{index:e,cx:t.x,cy:t.y,dataKey:s,value:t.value,payload:t.payload,points:l});return a.renderDotItem(u,r)}),d={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(r,")"):null};return n.createElement(b.W,k({className:"recharts-area-dots"},d),h)}},{key:"renderHorizontalRect",value:function(t){var e=this.props,r=e.baseLine,o=e.points,i=e.strokeWidth,a=o[0].x,c=o[o.length-1].x,u=t*Math.abs(a-c),s=l()(o.map(function(t){return t.y||0}));return((0,O.Et)(r)&&"number"==typeof r?s=Math.max(r,s):r&&Array.isArray(r)&&r.length&&(s=Math.max(l()(r.map(function(t){return t.y||0})),s)),(0,O.Et)(s))?n.createElement("rect",{x:a<c?a:a-u,y:0,width:u,height:Math.floor(s+(i?parseInt("".concat(i),10):1))}):null}},{key:"renderVerticalRect",value:function(t){var e=this.props,r=e.baseLine,o=e.points,i=e.strokeWidth,a=o[0].y,c=o[o.length-1].y,u=t*Math.abs(a-c),s=l()(o.map(function(t){return t.x||0}));return((0,O.Et)(r)&&"number"==typeof r?s=Math.max(r,s):r&&Array.isArray(r)&&r.length&&(s=Math.max(l()(r.map(function(t){return t.x||0})),s)),(0,O.Et)(s))?n.createElement("rect",{x:0,y:a<c?a:a-u,width:s+(i?parseInt("".concat(i),10):1),height:Math.floor(u)}):null}},{key:"renderClipRect",value:function(t){return"vertical"===this.props.layout?this.renderVerticalRect(t):this.renderHorizontalRect(t)}},{key:"renderAreaStatically",value:function(t,e,r,o){var i=this.props,a=i.layout,c=i.type,u=i.stroke,l=i.connectNulls,s=i.isRange,f=(i.ref,E(i,S));return n.createElement(b.W,{clipPath:r?"url(#clipPath-".concat(o,")"):null},n.createElement(v.I,k({},(0,j.J9)(f,!0),{points:t,connectNulls:l,type:c,baseLine:e,layout:a,stroke:"none",className:"recharts-area-area"})),"none"!==u&&n.createElement(v.I,k({},(0,j.J9)(this.props,!1),{className:"recharts-area-curve",layout:a,type:c,connectNulls:l,fill:"none",points:t})),"none"!==u&&s&&n.createElement(v.I,k({},(0,j.J9)(this.props,!1),{className:"recharts-area-curve",layout:a,type:c,connectNulls:l,fill:"none",points:e})))}},{key:"renderAreaWithAnimation",value:function(t,e){var r=this,o=this.props,a=o.points,c=o.baseLine,u=o.isAnimationActive,l=o.animationBegin,s=o.animationDuration,p=o.animationEasing,d=o.animationId,y=this.state,v=y.prevPoints,m=y.prevBaseLine;return n.createElement(i.Ay,{begin:l,duration:s,isActive:u,easing:p,from:{t:0},to:{t:1},key:"area-".concat(d),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(o){var i=o.t;if(v){var u,l=v.length/a.length,s=a.map(function(t,e){var r=Math.floor(e*l);if(v[r]){var n=v[r],o=(0,O.Dj)(n.x,t.x),a=(0,O.Dj)(n.y,t.y);return _(_({},t),{},{x:o(i),y:a(i)})}return t});return u=(0,O.Et)(c)&&"number"==typeof c?(0,O.Dj)(m,c)(i):f()(c)||h()(c)?(0,O.Dj)(m,0)(i):c.map(function(t,e){var r=Math.floor(e*l);if(m[r]){var n=m[r],o=(0,O.Dj)(n.x,t.x),a=(0,O.Dj)(n.y,t.y);return _(_({},t),{},{x:o(i),y:a(i)})}return t}),r.renderAreaStatically(s,u,t,e)}return n.createElement(b.W,null,n.createElement("defs",null,n.createElement("clipPath",{id:"animationClipPath-".concat(e)},r.renderClipRect(i))),n.createElement(b.W,{clipPath:"url(#animationClipPath-".concat(e,")")},r.renderAreaStatically(a,c,t,e)))})}},{key:"renderArea",value:function(t,e){var r=this.props,n=r.points,o=r.baseLine,i=r.isAnimationActive,a=this.state,c=a.prevPoints,u=a.prevBaseLine,l=a.totalLength;return i&&n&&n.length&&(!c&&l>0||!y()(c,n)||!y()(u,o))?this.renderAreaWithAnimation(t,e):this.renderAreaStatically(n,o,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,i=e.dot,a=e.points,c=e.className,u=e.top,l=e.left,s=e.xAxis,p=e.yAxis,h=e.width,d=e.height,y=e.isAnimationActive,v=e.id;if(r||!a||!a.length)return null;var m=this.state.isAnimationFinished,x=1===a.length,O=(0,o.A)("recharts-area",c),w=s&&s.allowDataOverflow,S=p&&p.allowDataOverflow,A=w||S,P=f()(v)?this.id:v,E=null!=(t=(0,j.J9)(i,!1))?t:{r:3,strokeWidth:2},k=E.r,M=E.strokeWidth,_=((0,j.sT)(i)?i:{}).clipDot,T=void 0===_||_,C=2*(void 0===k?3:k)+(void 0===M?2:M);return n.createElement(b.W,{className:O},w||S?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(P)},n.createElement("rect",{x:w?l:l-h/2,y:S?u:u-d/2,width:w?h:2*h,height:S?d:2*d})),!T&&n.createElement("clipPath",{id:"clipPath-dots-".concat(P)},n.createElement("rect",{x:l-C/2,y:u-C/2,width:h+C,height:d+C}))):null,x?null:this.renderArea(A,P),(i||x)&&this.renderDots(A,T,P),(!y||m)&&g.Z.renderCallByParent(this.props,a))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,curBaseLine:t.baseLine,prevPoints:e.curPoints,prevBaseLine:e.curBaseLine}:t.points!==e.curPoints||t.baseLine!==e.curBaseLine?{curPoints:t.points,curBaseLine:t.baseLine}:null}}],e&&T(a.prototype,e),r&&T(a,r),Object.defineProperty(a,"prototype",{writable:!1}),a}(n.PureComponent);N(R,"displayName","Area"),N(R,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!x.m.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),N(R,"getBaseValue",function(t,e,r,n){var o=t.layout,i=t.baseValue,a=e.props.baseValue,c=null!=a?a:i;if((0,O.Et)(c)&&"number"==typeof c)return c;var u="horizontal"===o?n:r,l=u.scale.domain();if("number"===u.type){var s=Math.max(l[0],l[1]),f=Math.min(l[0],l[1]);return"dataMin"===c?f:"dataMax"===c||s<0?s:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===c?l[0]:"dataMax"===c?l[1]:l[0]}),N(R,"getComposedData",function(t){var e,r=t.props,n=t.item,o=t.xAxis,i=t.yAxis,a=t.xAxisTicks,c=t.yAxisTicks,u=t.bandSize,l=t.dataKey,s=t.stackedData,f=t.dataStartIndex,p=t.displayedData,h=t.offset,d=r.layout,y=s&&s.length,v=R.getBaseValue(r,n,o,i),m="horizontal"===d,b=!1,g=p.map(function(t,e){y?r=s[f+e]:Array.isArray(r=(0,w.kr)(t,l))?b=!0:r=[v,r];var r,n=null==r[1]||y&&null==(0,w.kr)(t,l);return m?{x:(0,w.nb)({axis:o,ticks:a,bandSize:u,entry:t,index:e}),y:n?null:i.scale(r[1]),value:r,payload:t}:{x:n?null:o.scale(r[1]),y:(0,w.nb)({axis:i,ticks:c,bandSize:u,entry:t,index:e}),value:r,payload:t}});return e=y||b?g.map(function(t){var e=Array.isArray(t.value)?t.value[0]:null;return m?{x:t.x,y:null!=e&&null!=t.y?i.scale(e):null}:{x:null!=e?o.scale(e):null,y:t.y}}):m?i.scale(v):o.scale(v),_({points:g,baseLine:e,layout:d,isRange:b},h)}),N(R,"renderDotItem",function(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(c()(t))r=t(e);else{var i=(0,o.A)("recharts-area-dot","boolean"!=typeof t?t.className:""),a=e.key,u=E(e,A);r=n.createElement(m.c,k({},u,{key:a,className:i}))}return r})},62464:(t,e,r)=>{var n=r(3562),o=r(18028),i=r(23360),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:i(r);return u<0&&(u=a(c+u,0)),n(t,o(e,3),u)}},62962:(t,e,r)=>{var n=r(48659),o=r(65531),i=r(75145),a=r(85855);t.exports=function(t){return function(e){var r=o(e=a(e))?i(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},64189:(t,e,r)=>{var n=r(74366),o=r(39641),i=r(42233);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},64439:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},64588:(t,e,r)=>{var n=r(84760),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,c=o(i.length-e,0),u=Array(c);++a<c;)u[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(u),n(t,this,l)}}},65531:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},65646:(t,e,r)=>{var n=r(91569),o=r(39608);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},65796:t=>{t.exports=function(t){return this.__data__.get(t)}},65836:(t,e,r)=>{var n=r(85090),o=r(7548),i=r(39984),a=r(82954),c=r(82596),u=r(74166);t.exports=function(t,e,r){var l=-1,s=o,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=i;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,s=a,d=new n}else d=e?[]:h;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue e;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},66373:(t,e,r)=>{t.exports=r(83711)(r(82500),"Promise")},67206:(t,e,r)=>{var n=r(77969),o=r(55794),i=r(21087),a=r(39641);t.exports=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])})},67460:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},67472:(t,e,r)=>{var n=r(31598),o=r(90453),i=r(47995),a=r(65796),c=r(58096),u=r(7771);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=c,l.prototype.set=u,t.exports=l},67790:(t,e,r)=>{"use strict";r.d(e,{yp:()=>C,GG:()=>z,NE:()=>D,nZ:()=>I,xQ:()=>N});var n=r(12115),o=r(40139),i=r.n(o),a=r(44482),c=r.n(a),u=r(56917),l=r.n(u),s=r(60245),f=r.n(s),p=r(44538),h=r(52596),d=r(9557),y=r(70788);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function m(){return(m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var O=function(t,e,r,n,o){var i,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+o)+"L ".concat(t+r-a/2-n,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},w={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},j=function(t){var e,r=x(x({},w),t),o=(0,n.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return b(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return b(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),a=i[0],c=i[1];(0,n.useEffect)(function(){if(o.current&&o.current.getTotalLength)try{var t=o.current.getTotalLength();t&&c(t)}catch(t){}},[]);var u=r.x,l=r.y,s=r.upperWidth,f=r.lowerWidth,p=r.height,v=r.className,g=r.animationEasing,j=r.animationDuration,S=r.animationBegin,A=r.isUpdateAnimationActive;if(u!==+u||l!==+l||s!==+s||f!==+f||p!==+p||0===s&&0===f||0===p)return null;var P=(0,h.A)("recharts-trapezoid",v);return A?n.createElement(d.Ay,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:p,x:u,y:l},to:{upperWidth:s,lowerWidth:f,height:p,x:u,y:l},duration:j,animationEasing:g,isActive:A},function(t){var e=t.upperWidth,i=t.lowerWidth,c=t.height,u=t.x,l=t.y;return n.createElement(d.Ay,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:S,duration:j,easing:g},n.createElement("path",m({},(0,y.J9)(r,!0),{className:P,d:O(u,l,e,i,c),ref:o})))}):n.createElement("g",null,n.createElement("path",m({},(0,y.J9)(r,!0),{className:P,d:O(u,l,s,f,p)})))},S=r(77283),A=r(2348),P=r(9795),E=["option","shapeType","propTransformer","activeClassName","isActive"];function k(t){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function M(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?M(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=k(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=k(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==k(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function T(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return n.createElement(p.M,r);case"trapezoid":return n.createElement(j,r);case"sector":return n.createElement(S.h,r);case"symbols":if("symbols"===e)return n.createElement(P.i,r);break;default:return null}}function C(t){var e,r=t.option,o=t.shapeType,a=t.propTransformer,u=t.activeClassName,s=t.isActive,f=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,E);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,_(_({},f),(0,n.isValidElement)(r)?r.props:r));else if(i()(r))e=r(f);else if(c()(r)&&!l()(r)){var p=(void 0===a?function(t,e){return _(_({},e),t)}:a)(r,f);e=n.createElement(T,{shapeType:o,elementProps:p})}else e=n.createElement(T,{shapeType:o,elementProps:f});return s?n.createElement(A.W,{className:void 0===u?"recharts-active-shape":u},e):e}function D(t,e){return null!=e&&"trapezoids"in t.props}function I(t,e){return null!=e&&"sectors"in t.props}function N(t,e){return null!=e&&"points"in t.props}function B(t,e){var r,n,o=t.x===(null==e||null==(r=e.labelViewBox)?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null==(n=e.labelViewBox)?void 0:n.y)||t.y===e.y;return o&&i}function R(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function L(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}function z(t){var e,r,n,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,c=(D(i,o)?e="trapezoids":I(i,o)?e="sectors":N(i,o)&&(e="points"),e),u=D(i,o)?null==(r=o.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:I(i,o)?null==(n=o.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:N(i,o)?o.payload:{},l=a.filter(function(t,e){var r=f()(u,t),n=i.props[c].filter(function(t){var e;return(D(i,o)?e=B:I(i,o)?e=R:N(i,o)&&(e=L),e)(t,o)}),a=i.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}},69229:(t,e,r)=>{var n=r(67472),o=r(38406),i=r(50523),a=r(84464),c=r(94380),u=r(39608),l=r(33497),s=r(35190),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),O=g?p:c(e);x=x==f?h:x,O=O==f?h:O;var w=x==h,j=O==h,S=x==O;if(S&&l(t)){if(!l(e))return!1;b=!0,w=!1}if(S&&!w)return m||(m=new n),b||s(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var A=w&&d.call(t,"__wrapped__"),P=j&&d.call(e,"__wrapped__");if(A||P){var E=A?t.value():t,k=P?e.value():e;return m||(m=new n),v(E,k,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},69363:(t,e,r)=>{var n=r(57213),o=r(18028),i=r(6305),a=r(39608);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},69806:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return -1}},70688:(t,e,r)=>{"use strict";r.d(e,{I:()=>V});var n=r(12115);function o(){}function i(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function a(t){this._context=t}function c(t){this._context=t}function u(t){this._context=t}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:i(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},c.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},u.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class l{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function s(t){this._context=t}function f(t){this._context=t}function p(t){return new f(t)}s.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function h(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function d(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function y(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,c=(i-n)/3;t._context.bezierCurveTo(n+c,o+c*e,i-c,a-c*r,i,a)}function v(t){this._context=t}function m(t){this._context=new b(t)}function b(t){this._context=t}function g(t){this._context=t}function x(t){var e,r,n=t.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[n-1]=(t[n]+o[n-1])/2;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function O(t,e){this._context=t,this._t=e}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,d(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,y(this,d(this,r=h(this,t,e)),r);break;default:y(this,this._t0,r=h(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(m.prototype=Object.create(v.prototype)).point=function(t,e){v.prototype.point.call(this,e,t)},b.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=x(t),o=x(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},O.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var w=r(9819),j=r(85654),S=r(31847);function A(t){return t[0]}function P(t){return t[1]}function E(t,e){var r=(0,j.A)(!0),n=null,o=p,i=null,a=(0,S.i)(c);function c(c){var u,l,s,f=(c=(0,w.A)(c)).length,p=!1;for(null==n&&(i=o(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,u,c),+e(l,u,c));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?A:(0,j.A)(t),e="function"==typeof e?e:void 0===e?P:(0,j.A)(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:(0,j.A)(!!t),c):r},c.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),c):o},c.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),c):n},c}function k(t,e,r){var n=null,o=(0,j.A)(!0),i=null,a=p,c=null,u=(0,S.i)(l);function l(l){var s,f,p,h,d,y=(l=(0,w.A)(l)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(c=a(d=u())),s=0;s<=y;++s){if(!(s<y&&o(h=l[s],s,l))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return c=null,d+""||null}function s(){return E().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?A:(0,j.A)(+t),e="function"==typeof e?e:void 0===e?(0,j.A)(0):(0,j.A)(+e),r="function"==typeof r?r:void 0===r?P:(0,j.A)(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:(0,j.A)(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,j.A)(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:(0,j.A)(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(c=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=c=null:c=a(i=t),l):i},l}var M=r(23633),_=r.n(M),T=r(40139),C=r.n(T),D=r(52596),I=r(43597),N=r(70788),B=r(16377);function R(t){return(R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function L(){return(L=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function z(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function U(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?z(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=R(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=R(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==R(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var F={curveBasisClosed:function(t){return new c(t)},curveBasisOpen:function(t){return new u(t)},curveBasis:function(t){return new a(t)},curveBumpX:function(t){return new l(t,!0)},curveBumpY:function(t){return new l(t,!1)},curveLinearClosed:function(t){return new s(t)},curveLinear:p,curveMonotoneX:function(t){return new v(t)},curveMonotoneY:function(t){return new m(t)},curveNatural:function(t){return new g(t)},curveStep:function(t){return new O(t,.5)},curveStepAfter:function(t){return new O(t,1)},curveStepBefore:function(t){return new O(t,0)}},W=function(t){return t.x===+t.x&&t.y===+t.y},$=function(t){return t.x},q=function(t){return t.y},X=function(t,e){if(C()(t))return t;var r="curve".concat(_()(t));return("curveMonotone"===r||"curveBump"===r)&&e?F["".concat(r).concat("vertical"===e?"Y":"X")]:F[r]||p},H=function(t){var e,r=t.type,n=t.points,o=void 0===n?[]:n,i=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,l=X(void 0===r?"linear":r,a),s=u?o.filter(function(t){return W(t)}):o;if(Array.isArray(i)){var f=u?i.filter(function(t){return W(t)}):i,p=s.map(function(t,e){return U(U({},t),{},{base:f[e]})});return(e="vertical"===a?k().y(q).x1($).x0(function(t){return t.base.x}):k().x($).y1(q).y0(function(t){return t.base.y})).defined(W).curve(l),e(p)}return(e="vertical"===a&&(0,B.Et)(i)?k().y(q).x1($).x0(i):(0,B.Et)(i)?k().x($).y1(q).y0(i):E().x($).y(q)).defined(W).curve(l),e(s)},V=function(t){var e=t.className,r=t.points,o=t.path,i=t.pathRef;if((!r||!r.length)&&!o)return null;var a=r&&r.length?H(t):o;return n.createElement("path",L({},(0,N.J9)(t,!1),(0,I._U)(t),{className:(0,D.A)("recharts-curve",e),d:a,ref:i}))}},70771:(t,e,r)=>{var n=r(98233),o=r(48611);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},70788:(t,e,r)=>{"use strict";r.d(e,{AW:()=>L,BU:()=>k,J9:()=>D,Me:()=>M,Mn:()=>j,OV:()=>I,X_:()=>R,aS:()=>E,ee:()=>B,sT:()=>T});var n=r(48973),o=r.n(n),i=r(59882),a=r.n(i),c=r(15438),u=r.n(c),l=r(40139),s=r.n(l),f=r(67460),p=r.n(f),h=r(12115),d=r(90702),y=r(16377),v=r(15232),m=r(43597),b=["children"],g=["children"];function x(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var w={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},j=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},S=null,A=null,P=function t(e){if(e===S&&Array.isArray(A))return A;var r=[];return h.Children.forEach(e,function(e){a()(e)||((0,d.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),A=r,S=e,r};function E(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return j(t)}):[j(e)],P(t).forEach(function(t){var e=o()(t,"type.displayName")||o()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function k(t,e){var r=E(t,e);return r&&r[0]}var M=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!(0,y.Et)(r)&&!(r<=0)&&!!(0,y.Et)(n)&&!(n<=0)},_=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],T=function(t){return t&&"object"===O(t)&&"clipDot"in t},C=function(t,e,r,n){var o,i=null!=(o=null===m.VU||void 0===m.VU?void 0:m.VU[n])?o:[];return e.startsWith("data-")||!s()(t)&&(n&&i.includes(e)||m.QQ.includes(e))||r&&m.j2.includes(e)},D=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,h.isValidElement)(t)&&(n=t.props),!p()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;C(null==(i=n)?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},I=function t(e,r){if(e===r)return!0;var n=h.Children.count(e);if(n!==h.Children.count(r))return!1;if(0===n)return!0;if(1===n)return N(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=e[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!t(i,a))return!1}else if(!N(i,a))return!1}return!0},N=function(t,e){if(a()(t)&&a()(e))return!0;if(!a()(t)&&!a()(e)){var r=t.props||{},n=r.children,o=x(r,b),i=e.props||{},c=i.children,u=x(i,g);if(n&&c)return(0,v.b)(o,u)&&I(n,c);if(!n&&!c)return(0,v.b)(o,u)}return!1},B=function(t,e){var r=[],n={};return P(t).forEach(function(t,o){var i;if((i=t)&&i.type&&u()(i.type)&&_.indexOf(i.type)>=0)r.push(t);else if(t){var a=j(t.type),c=e[a]||{},l=c.handler,s=c.once;if(l&&(!s||!n[a])){var f=l(t,a,o);r.push(f),n[a]=!0}}}),r},R=function(t){var e=t&&t.type;return e&&w[e]?w[e]:null},L=function(t,e){return P(e).indexOf(t)}},70966:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},71571:(t,e,r)=>{var n=r(92313),o=r(18028),i=r(51445),a=r(39608),c=r(39641);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},72043:(t,e,r)=>{t.exports=r(70966)(Object.keys,Object)},72790:(t,e,r)=>{"use strict";r.d(e,{u:()=>u});var n=r(12115),o=r(52596),i=r(70788),a=["children","width","height","viewBox","className","style","title","desc"];function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function u(t){var e=t.children,r=t.width,u=t.height,l=t.viewBox,s=t.className,f=t.style,p=t.title,h=t.desc,d=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,a),y=l||{width:r,height:u,x:0,y:0},v=(0,o.A)("recharts-surface",s);return n.createElement("svg",c({},(0,i.J9)(d,!0,"svg"),{className:v,width:r,height:u,style:f,viewBox:"".concat(y.x," ").concat(y.y," ").concat(y.width," ").concat(y.height)}),n.createElement("title",null,p),n.createElement("desc",null,h),e)}},72948:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},73726:(t,e,r)=>{t.exports=r(70966)(Object.getPrototypeOf,Object)},73800:(t,e,r)=>{var n=r(39608),o=r(79595),i=r(37835),a=r(85855);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},73956:(t,e,r)=>{var n=r(5658);t.exports=function(t){return n(this,t).has(t)}},74166:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},74366:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,c=r(e((n-t)/(o||1)),0),u=Array(c);c--;)u[i?c:++a]=t,t+=o;return u}},74544:(t,e,r)=>{var n=r(5658);t.exports=function(t){return n(this,t).get(t)}},74888:(t,e,r)=>{var n=r(53516);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},74925:(t,e,r)=>{var n=r(28897),o=r(20480),i=r(18028);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},75031:(t,e,r)=>{var n=r(18028),o=r(22471),i=r(35095);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!o(e)){var u=n(r,3);e=i(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},75145:(t,e,r)=>{var n=r(50851),o=r(65531),i=r(17855);t.exports=function(t){return o(t)?i(t):n(t)}},75899:(t,e,r)=>{t.exports=r(83711)(Object,"create")},76685:(t,e,r)=>{var n=r(82500);t.exports=function(){return n.Date.now()}},76957:(t,e,r)=>{t.exports=r(82500).Uint8Array},77283:(t,e,r)=>{"use strict";r.d(e,{h:()=>v});var n=r(12115),o=r(52596),i=r(70788),a=r(25641),c=r(16377);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var p=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,c=t.isExternal,u=t.cornerRadius,l=t.cornerIsExternal,s=u*(c?1:-1)+n,f=Math.asin(u/s)/a.Kg,p=l?o:o+i*f;return{center:(0,a.IZ)(e,r,s,p),circleTangency:(0,a.IZ)(e,r,n,p),lineTangency:(0,a.IZ)(e,r,s*Math.cos(f*a.Kg),l?o-i*f:o),theta:f}},h=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,u=t.endAngle,l=(0,c.sA)(u-i)*Math.min(Math.abs(u-i),359.999),s=i+l,f=(0,a.IZ)(e,r,o,i),p=(0,a.IZ)(e,r,o,s),h="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(i>s),",\n    ").concat(p.x,",").concat(p.y,"\n  ");if(n>0){var d=(0,a.IZ)(e,r,n,i),y=(0,a.IZ)(e,r,n,s);h+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(i<=s),",\n            ").concat(d.x,",").concat(d.y," Z")}else h+="L ".concat(e,",").concat(r," Z");return h},d=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,u=t.cornerIsExternal,l=t.startAngle,s=t.endAngle,f=(0,c.sA)(s-l),d=p({cx:e,cy:r,radius:o,angle:l,sign:f,cornerRadius:i,cornerIsExternal:u}),y=d.circleTangency,v=d.lineTangency,m=d.theta,b=p({cx:e,cy:r,radius:o,angle:s,sign:-f,cornerRadius:i,cornerIsExternal:u}),g=b.circleTangency,x=b.lineTangency,O=b.theta,w=u?Math.abs(l-s):Math.abs(l-s)-m-O;if(w<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):h({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:l,endAngle:s});var j="M ".concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(w>180),",").concat(+(f<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(x.x,",").concat(x.y,"\n  ");if(n>0){var S=p({cx:e,cy:r,radius:n,angle:l,sign:f,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),A=S.circleTangency,P=S.lineTangency,E=S.theta,k=p({cx:e,cy:r,radius:n,angle:s,sign:-f,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),M=k.circleTangency,_=k.lineTangency,T=k.theta,C=u?Math.abs(l-s):Math.abs(l-s)-E-T;if(C<0&&0===i)return"".concat(j,"L").concat(e,",").concat(r,"Z");j+="L".concat(_.x,",").concat(_.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(M.x,",").concat(M.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(C>180),",").concat(+(f>0),",").concat(A.x,",").concat(A.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(P.x,",").concat(P.y,"Z")}else j+="L".concat(e,",").concat(r,"Z");return j},y={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},v=function(t){var e,r=f(f({},y),t),a=r.cx,u=r.cy,s=r.innerRadius,p=r.outerRadius,v=r.cornerRadius,m=r.forceCornerRadius,b=r.cornerIsExternal,g=r.startAngle,x=r.endAngle,O=r.className;if(p<s||g===x)return null;var w=(0,o.A)("recharts-sector",O),j=p-s,S=(0,c.F4)(v,j,0,!0);return e=S>0&&360>Math.abs(g-x)?d({cx:a,cy:u,innerRadius:s,outerRadius:p,cornerRadius:Math.min(S,j/2),forceCornerRadius:m,cornerIsExternal:b,startAngle:g,endAngle:x}):h({cx:a,cy:u,innerRadius:s,outerRadius:p,startAngle:g,endAngle:x}),n.createElement("path",l({},(0,i.J9)(r,!0),{className:w,d:e,role:"img"}))}},77969:(t,e,r)=>{var n=r(91569),o=r(36314);t.exports=function t(e,r,i,a,c){var u=-1,l=e.length;for(i||(i=o),c||(c=[]);++u<l;){var s=e[u];r>0&&i(s)?r>1?t(s,r-1,i,a,c):n(c,s):a||(c[c.length]=s)}return c}},79095:(t,e,r)=>{"use strict";r.d(e,{E:()=>R});var n=r(12115),o=r(59882),i=r.n(o),a=r(52596),c=r(16377),u=r(41643),l=r(70788),s=r(46605);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(n.key),n)}}var y=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,v=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,m=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,b=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,g={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},x=Object.keys(g),O=function(){var t,e;function r(t,e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||m.test(e)||(this.num=NaN,this.unit=""),x.includes(e)&&(this.num=t*g[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=p(null!=(e=b.exec(t))?e:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],t&&d(r.prototype,t),e&&d(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function w(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=p(null!=(r=y.exec(e))?r:[],4),o=n[1],i=n[2],a=n[3],c=O.parse(null!=o?o:""),u=O.parse(null!=a?a:""),l="*"===i?c.multiply(u):c.divide(u);if(l.isNaN())return"NaN";e=e.replace(y,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=p(null!=(s=v.exec(e))?s:[],4),h=f[1],d=f[2],m=f[3],b=O.parse(null!=h?h:""),g=O.parse(null!=m?m:""),x="+"===d?b.add(g):b.subtract(g);if(x.isNaN())return"NaN";e=e.replace(v,x.toString())}return e}var j=/\(([^()]*)\)/;function S(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=p(j.exec(e),2)[1];e=e.replace(j,w(r))}return e}(e),e=w(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var A=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],P=["dx","dy","angle","className","breakAll"];function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function M(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return _(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return _(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var T=/[ \f\n\r\t\v\u2028\u2029]+/,C=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];i()(e)||(o=r?e.toString().split(""):e.toString().split(T));var a=o.map(function(t){return{word:t,width:(0,s.Pu)(t,n).width}}),c=r?0:(0,s.Pu)("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:c}}catch(t){return null}},D=function(t,e,r,n,o){var i,a=t.maxLines,u=t.children,l=t.style,s=t.breakAll,f=(0,c.Et)(a),p=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||o||c.width+a+r<Number(n))?(c.words.push(i),c.width+=a+r):t.push({words:[i],width:a}),t},[])},h=p(e);if(!f)return h;for(var d=function(t){var e=p(C({breakAll:s,style:l,children:u.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},y=0,v=u.length-1,m=0;y<=v&&m<=u.length-1;){var b=Math.floor((y+v)/2),g=M(d(b-1),2),x=g[0],O=g[1],w=M(d(b),1)[0];if(x||w||(y=b+1),x&&w&&(v=b-1),!x&&w){i=O;break}m++}return i||h},I=function(t){return[{words:i()(t)?[]:t.toString().split(T)}]},N=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!u.m.isSsr){var c=C({breakAll:i,children:n,style:o});if(!c)return I(n);var l=c.wordsWithComputedWidth,s=c.spaceWidth;return D({breakAll:i,children:n,maxLines:a,style:o},l,s,e,r)}return I(n)},B="#808080",R=function(t){var e,r=t.x,o=void 0===r?0:r,i=t.y,u=void 0===i?0:i,s=t.lineHeight,f=void 0===s?"1em":s,p=t.capHeight,h=void 0===p?"0.71em":p,d=t.scaleToFit,y=void 0!==d&&d,v=t.textAnchor,m=t.verticalAnchor,b=t.fill,g=void 0===b?B:b,x=k(t,A),O=(0,n.useMemo)(function(){return N({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:y,style:x.style,width:x.width})},[x.breakAll,x.children,x.maxLines,y,x.style,x.width]),w=x.dx,j=x.dy,M=x.angle,_=x.className,T=x.breakAll,C=k(x,P);if(!(0,c.vh)(o)||!(0,c.vh)(u))return null;var D=o+((0,c.Et)(w)?w:0),I=u+((0,c.Et)(j)?j:0);switch(void 0===m?"end":m){case"start":e=S("calc(".concat(h,")"));break;case"middle":e=S("calc(".concat((O.length-1)/2," * -").concat(f," + (").concat(h," / 2))"));break;default:e=S("calc(".concat(O.length-1," * -").concat(f,")"))}var R=[];if(y){var L=O[0].width,z=x.width;R.push("scale(".concat(((0,c.Et)(z)?z/L:1)/L,")"))}return M&&R.push("rotate(".concat(M,", ").concat(D,", ").concat(I,")")),R.length&&(C.transform=R.join(" ")),n.createElement("text",E({},(0,l.J9)(C,!0),{x:D,y:I,className:(0,a.A)("recharts-text",_),textAnchor:void 0===v?"start":v,fill:g.includes("url")?B:g}),O.map(function(t,r){var o=t.words.join(T?"":" ");return n.createElement("tspan",{x:D,dy:0===r?e:f,key:"".concat(o,"-").concat(r)},o)}))}},79399:(t,e,r)=>{"use strict";var n=r(72948);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},79401:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},79595:(t,e,r)=>{var n=r(39608),o=r(70771),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},81519:(t,e,r)=>{"use strict";r.d(e,{A:()=>i,z:()=>a});var n=r(28749),o=r(95442);function i(){var t,e,r=(0,o.A)().unknown(void 0),a=r.domain,c=r.range,u=0,l=1,s=!1,f=0,p=0,h=.5;function d(){var r=a().length,n=l<u,o=n?l:u,i=n?u:l;t=(i-o)/Math.max(1,r-f+2*p),s&&(t=Math.floor(t)),o+=(i-o-t*(r-f))*h,e=t*(1-f),s&&(o=Math.round(o),e=Math.round(e));var d=(function(t,e,r){t*=1,e*=1,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=Array(o);++n<o;)i[n]=t+n*r;return i})(r).map(function(e){return o+t*e});return c(n?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(a(t),d()):a()},r.range=function(t){return arguments.length?([u,l]=t,u*=1,l*=1,d()):[u,l]},r.rangeRound=function(t){return[u,l]=t,u*=1,l*=1,s=!0,d()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(s=!!t,d()):s},r.padding=function(t){return arguments.length?(f=Math.min(1,p=+t),d()):f},r.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),d()):f},r.paddingOuter=function(t){return arguments.length?(p=+t,d()):p},r.align=function(t){return arguments.length?(h=Math.max(0,Math.min(1,t)),d()):h},r.copy=function(){return i(a(),[u,l]).round(s).paddingInner(f).paddingOuter(p).align(h)},n.C.apply(d(),arguments)}function a(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(i.apply(null,arguments).paddingInner(1))}},82500:(t,e,r)=>{var n=r(7985),o="object"==typeof self&&self&&self.Object===Object&&self;t.exports=n||o||Function("return this")()},82596:(t,e,r)=>{var n=r(38008),o=r(31431),i=r(74166);t.exports=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o},82661:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new o(n,i||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,o,i,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,u=Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||o&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},82954:t=>{t.exports=function(t,e){return t.has(e)}},83197:(t,e,r)=>{"use strict";r.d(e,{g:()=>l});var n=r(24026),o=r(12814),i=r(70788);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var l=function(t){var e,r=t.children,a=t.formattedGraphicalItems,c=t.legendWidth,l=t.legendContent,s=(0,i.BU)(r,n.s);if(!s)return null;var f=n.s.defaultProps,p=void 0!==f?u(u({},f),s.props):{};return e=s.props&&s.props.payload?s.props&&s.props.payload:"children"===l?(a||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:s.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(a||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?u(u({},r),e.props):{},i=n.dataKey,a=n.name,c=n.legendType;return{inactive:n.hide,dataKey:i,type:p.iconType||c||"square",color:(0,o.Ps)(e),value:a||i,payload:n}}),u(u(u({},p),n.s.getWithHeight(s,c)),{},{payload:e,item:s})}},83540:(t,e,r)=>{"use strict";r.d(e,{u:()=>d});var n=r(52596),o=r(12115),i=r(91959),a=r.n(i),c=r(16377),u=r(675),l=r(70788);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var d=(0,o.forwardRef)(function(t,e){var r,i=t.aspect,s=t.initialDimension,f=void 0===s?{width:-1,height:-1}:s,d=t.width,y=void 0===d?"100%":d,v=t.height,m=void 0===v?"100%":v,b=t.minWidth,g=void 0===b?0:b,x=t.minHeight,O=t.maxHeight,w=t.children,j=t.debounce,S=void 0===j?0:j,A=t.id,P=t.className,E=t.onResize,k=t.style,M=(0,o.useRef)(null),_=(0,o.useRef)();_.current=E,(0,o.useImperativeHandle)(e,function(){return Object.defineProperty(M.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),M.current},configurable:!0})});var T=function(t){if(Array.isArray(t))return t}(r=(0,o.useState)({containerWidth:f.width,containerHeight:f.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return h(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),C=T[0],D=T[1],I=(0,o.useCallback)(function(t,e){D(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,o.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;I(n,o),null==(e=_.current)||e.call(_,n,o)};S>0&&(t=a()(t,S,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=M.current.getBoundingClientRect();return I(r.width,r.height),e.observe(M.current),function(){e.disconnect()}},[I,S]);var N=(0,o.useMemo)(function(){var t=C.containerWidth,e=C.containerHeight;if(t<0||e<0)return null;(0,u.R)((0,c._3)(y)||(0,c._3)(m),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",y,m),(0,u.R)(!i||i>0,"The aspect(%s) must be greater than zero.",i);var r=(0,c._3)(y)?t:y,n=(0,c._3)(m)?e:m;i&&i>0&&(r?n=r/i:n&&(r=n*i),O&&n>O&&(n=O)),(0,u.R)(r>0||n>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,n,y,m,g,x,i);var a=!Array.isArray(w)&&(0,l.Mn)(w.type).endsWith("Chart");return o.Children.map(w,function(t){return o.isValidElement(t)?(0,o.cloneElement)(t,p({width:r,height:n},a?{style:p({height:"100%",width:"100%",maxHeight:n,maxWidth:r},t.props.style)}:{})):t})},[i,w,m,O,x,g,C,y]);return o.createElement("div",{id:A?"".concat(A):void 0,className:(0,n.A)("recharts-responsive-container",P),style:p(p({},void 0===k?{}:k),{},{width:y,height:m,minWidth:g,minHeight:x,maxHeight:O}),ref:M},N)})},83711:(t,e,r)=>{var n=r(13122),o=r(15473);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},83979:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},84464:(t,e,r)=>{var n=r(20963),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,c){var u=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!u)return!1;for(var f=s;f--;){var p=l[f];if(!(u?p in e:o.call(e,p)))return!1}var h=c.get(t),d=c.get(e);if(h&&d)return h==e&&d==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<s;){var m=t[p=l[f]],b=e[p];if(i)var g=u?i(b,m,p,e,t,c):i(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,i,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,O=e.constructor;x!=O&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof O&&O instanceof O)&&(y=!1)}return c.delete(t),c.delete(e),y}},84760:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},85090:(t,e,r)=>{var n=r(88748),o=r(6997),i=r(34210);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},85654:(t,e,r)=>{"use strict";function n(t){return function(){return t}}r.d(e,{A:()=>n})},85855:(t,e,r)=>{var n=r(16613);t.exports=function(t){return null==t?"":n(t)}},86216:(t,e,r)=>{t.exports=r(30716)()},86452:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},88748:(t,e,r)=>{var n=r(10537),o=r(94999),i=r(74544),a=r(73956),c=r(5516);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},89053:(t,e,r)=>{var n=r(58918),o=r(52521),i=r(13465);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},89316:(t,e,r)=>{var n=r(98233),o=r(15631),i=r(48611),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},90453:(t,e,r)=>{var n=r(31598);t.exports=function(){this.__data__=new n,this.size=0}},90702:(t,e,r)=>{"use strict";t.exports=r(56290)},90724:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},90929:(t,e,r)=>{var n=r(54360),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},91113:(t,e,r)=>{var n=r(88748);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},91569:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},91959:(t,e,r)=>{var n=r(45964),o=r(67460);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},92313:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}},92972:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},93179:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});function n(t,e){if(!t)throw Error("Invariant failed")}},93294:(t,e,r)=>{var n=r(34711);t.exports=function(t){return function(e){return n(e,t)}}},94011:(t,e,r)=>{"use strict";r.d(e,{u:()=>v});var n=r(12115),o=r(93179),i=r(2348),a=r(70788),c=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f=function(){return!!t})()}function p(t){return(p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h(t,e){return(h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function d(t,e,r){return(e=y(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}var v=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=p(t),function(t,e){if(e&&("object"===u(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,f()?Reflect.construct(t,e||[],p(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&h(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,u=t.width,f=t.dataKey,p=t.data,h=t.dataPointFormatter,d=t.xAxis,y=t.yAxis,v=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,c),m=(0,a.J9)(v,!1);"x"===this.props.direction&&"number"!==d.type&&(0,o.A)(!1);var b=p.map(function(t){var o,a,c=h(t,f),p=c.x,v=c.y,b=c.value,g=c.errorVal;if(!g)return null;var x=[];if(Array.isArray(g)){var O=function(t){if(Array.isArray(t))return t}(g)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(g,2)||function(t,e){if(t){if("string"==typeof t)return s(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,e)}}(g,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=O[0],a=O[1]}else o=a=g;if("vertical"===r){var w=d.scale,j=v+e,S=j+u,A=j-u,P=w(b-o),E=w(b+a);x.push({x1:E,y1:S,x2:E,y2:A}),x.push({x1:P,y1:j,x2:E,y2:j}),x.push({x1:P,y1:S,x2:P,y2:A})}else if("horizontal"===r){var k=y.scale,M=p+e,_=M-u,T=M+u,C=k(b-o),D=k(b+a);x.push({x1:_,y1:D,x2:T,y2:D}),x.push({x1:M,y1:C,x2:M,y2:D}),x.push({x1:_,y1:C,x2:T,y2:C})}return n.createElement(i.W,l({className:"recharts-errorBar",key:"bar-".concat(x.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},m),x.map(function(t){return n.createElement("line",l({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return n.createElement(i.W,{className:"recharts-errorBars"},b)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);d(v,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),d(v,"displayName","ErrorBar")},94356:(t,e,r)=>{var n=r(70771),o=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-o?"-0":e}},94380:(t,e,r)=>{var n=r(44101),o=r(18686),i=r(66373),a=r(38008),c=r(40382),u=r(98233),l=r(7512),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(n),v=l(o),m=l(i),b=l(a),g=l(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=d||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=h)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return d;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},94517:(t,e,r)=>{"use strict";r.d(e,{m:()=>W});var n=r(12115),o=r(67206),i=r.n(o),a=r(59882),c=r.n(a),u=r(52596),l=r(16377);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(){return(f=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function y(t){return Array.isArray(t)&&(0,l.vh)(t[0])&&(0,l.vh)(t[1])?t.join(" ~ "):t}var v=function(t){var e=t.separator,r=void 0===e?" : ":e,o=t.contentStyle,a=t.itemStyle,s=void 0===a?{}:a,h=t.labelStyle,v=t.payload,m=t.formatter,b=t.itemSorter,g=t.wrapperClassName,x=t.labelClassName,O=t.label,w=t.labelFormatter,j=t.accessibilityLayer,S=d({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===o?{}:o),A=d({margin:0},void 0===h?{}:h),P=!c()(O),E=P?O:"",k=(0,u.A)("recharts-default-tooltip",g),M=(0,u.A)("recharts-tooltip-label",x);return P&&w&&null!=v&&(E=w(O,v)),n.createElement("div",f({className:k,style:S},void 0!==j&&j?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:M,style:A},n.isValidElement(E)?E:"".concat(E)),function(){if(v&&v.length){var t=(b?i()(v,b):v).map(function(t,e){if("none"===t.type)return null;var o=d({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},s),i=t.formatter||m||y,a=t.value,c=t.name,u=a,f=c;if(i&&null!=u&&null!=f){var h=i(a,c,t,e,v);if(Array.isArray(h)){var b=function(t){if(Array.isArray(t))return t}(h)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(h,2)||function(t,e){if(t){if("string"==typeof t)return p(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return p(t,e)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();u=b[0],f=b[1]}else u=h}return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:o},(0,l.vh)(f)?n.createElement("span",{className:"recharts-tooltip-item-name"},f):null,(0,l.vh)(f)?n.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,n.createElement("span",{className:"recharts-tooltip-item-value"},u),n.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function m(t){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function b(t,e,r){var n;return(n=function(t,e){if("object"!=m(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=m(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==m(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var g="recharts-tooltip-wrapper",x={visibility:"hidden"};function O(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,s=t.viewBoxDimension;if(i&&(0,l.Et)(i[n]))return i[n];var f=r[n]-c-o,p=r[n]+o;return e[n]?a[n]?f:p:a[n]?f<u[n]?Math.max(p,u[n]):Math.max(f,u[n]):p+c>u[n]+s?Math.max(f,u[n]):Math.max(p,u[n])}function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function j(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function S(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?j(Object(r),!0).forEach(function(e){k(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function A(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(A=function(){return!!t})()}function P(t){return(P=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function E(t,e){return(E=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function k(t,e,r){return(e=M(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function M(t){var e=function(t,e){if("object"!=w(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=w(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==w(e)?e:e+""}var _=function(t){var e;function r(){var t,e,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r,n=[].concat(i),e=P(e),k(t=function(t,e){if(e&&("object"===w(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,A()?Reflect.construct(e,n||[],P(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),k(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=t.props.coordinate)?void 0:n.x)?r:0,y:null!=(o=null==(i=t.props.coordinate)?void 0:i.y)?o:0}})}}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&E(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(t=this.props.coordinate)?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null==(e=this.props.coordinate)?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,o,i,a,c,s,f,p,h,d,y,v,m,w,j,A,P,E=this,k=this.props,M=k.active,_=k.allowEscapeViewBox,T=k.animationDuration,C=k.animationEasing,D=k.children,I=k.coordinate,N=k.hasPayload,B=k.isAnimationActive,R=k.offset,L=k.position,z=k.reverseDirection,U=k.useTranslate3d,F=k.viewBox,W=k.wrapperStyle,$=(d=(t={allowEscapeViewBox:_,coordinate:I,offsetTopLeft:R,position:L,reverseDirection:z,tooltipBox:this.state.lastBoundingBox,useTranslate3d:U,viewBox:F}).allowEscapeViewBox,y=t.coordinate,v=t.offsetTopLeft,m=t.position,w=t.reverseDirection,j=t.tooltipBox,A=t.useTranslate3d,P=t.viewBox,j.height>0&&j.width>0&&y?(r=(e={translateX:p=O({allowEscapeViewBox:d,coordinate:y,key:"x",offsetTopLeft:v,position:m,reverseDirection:w,tooltipDimension:j.width,viewBox:P,viewBoxDimension:P.width}),translateY:h=O({allowEscapeViewBox:d,coordinate:y,key:"y",offsetTopLeft:v,position:m,reverseDirection:w,tooltipDimension:j.height,viewBox:P,viewBoxDimension:P.height}),useTranslate3d:A}).translateX,o=e.translateY,f={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(o,"px, 0)"):"translate(".concat(r,"px, ").concat(o,"px)")}):f=x,{cssProperties:f,cssClasses:(a=(i={translateX:p,translateY:h,coordinate:y}).coordinate,c=i.translateX,s=i.translateY,(0,u.A)(g,b(b(b(b({},"".concat(g,"-right"),(0,l.Et)(c)&&a&&(0,l.Et)(a.x)&&c>=a.x),"".concat(g,"-left"),(0,l.Et)(c)&&a&&(0,l.Et)(a.x)&&c<a.x),"".concat(g,"-bottom"),(0,l.Et)(s)&&a&&(0,l.Et)(a.y)&&s>=a.y),"".concat(g,"-top"),(0,l.Et)(s)&&a&&(0,l.Et)(a.y)&&s<a.y)))}),q=$.cssClasses,X=$.cssProperties,H=S(S({transition:B&&M?"transform ".concat(T,"ms ").concat(C):void 0},X),{},{pointerEvents:"none",visibility:!this.state.dismissed&&M&&N?"visible":"hidden",position:"absolute",top:0,left:0},W);return n.createElement("div",{tabIndex:-1,className:q,style:H,ref:function(t){E.wrapperNode=t}},D)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,M(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent),T=r(41643),C=r(2494);function D(t){return(D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function I(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function N(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?I(Object(r),!0).forEach(function(e){z(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function B(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(B=function(){return!!t})()}function R(t){return(R=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function L(t,e){return(L=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function z(t,e,r){return(e=U(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function U(t){var e=function(t,e){if("object"!=D(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=D(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==D(e)?e:e+""}function F(t){return t.dataKey}var W=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=R(t),function(t,e){if(e&&("object"===D(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,B()?Reflect.construct(t,e||[],R(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&L(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,o=r.active,i=r.allowEscapeViewBox,a=r.animationDuration,c=r.animationEasing,u=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,m=r.reverseDirection,b=r.useTranslate3d,g=r.viewBox,x=r.wrapperStyle,O=null!=h?h:[];s&&O.length&&(O=(0,C.s)(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,F));var w=O.length>0;return n.createElement(_,{allowEscapeViewBox:i,animationDuration:a,animationEasing:c,isAnimationActive:f,active:o,coordinate:l,hasPayload:w,offset:p,position:y,reverseDirection:m,useTranslate3d:b,viewBox:g,wrapperStyle:x},(t=N(N({},this.props),{},{payload:O}),n.isValidElement(u)?n.cloneElement(u,t):"function"==typeof u?n.createElement(u,t):n.createElement(v,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,U(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent);z(W,"displayName","Tooltip"),z(W,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!T.m.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},94754:(t,e,r)=>{"use strict";r.d(e,{d:()=>M});var n=r(12115),o=r(40139),i=r.n(o),a=r(675),c=r(16377),u=r(70788),l=r(12814),s=r(36447),f=r(45167),p=r(50091),h=["x1","y1","x2","y2","key"],d=["offset"];function y(t){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?v(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function b(){return(b=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function g(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var x=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,o=t.x,i=t.y,a=t.width,c=t.height,u=t.ry;return n.createElement("rect",{x:o,y:i,ry:u,width:a,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function O(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(i()(t))r=t(e);else{var o=e.x1,a=e.y1,c=e.x2,l=e.y2,s=e.key,f=g(e,h),p=(0,u.J9)(f,!1),y=(p.offset,g(p,d));r=n.createElement("line",b({},y,{x1:o,y1:a,x2:c,y2:l,fill:"none",key:s}))}return r}function w(t){var e=t.x,r=t.width,o=t.horizontal,i=void 0===o||o,a=t.horizontalPoints;if(!i||!a||!a.length)return null;var c=a.map(function(n,o){return O(i,m(m({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(o),index:o}))});return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function j(t){var e=t.y,r=t.height,o=t.vertical,i=void 0===o||o,a=t.verticalPoints;if(!i||!a||!a.length)return null;var c=a.map(function(n,o){return O(i,m(m({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(o),index:o}))});return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function S(t){var e=t.horizontalFill,r=t.fillOpacity,o=t.x,i=t.y,a=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,u){var l=s[u+1]?s[u+1]-t:i+c-t;if(l<=0)return null;var f=u%e.length;return n.createElement("rect",{key:"react-".concat(u),y:t,x:o,height:l,width:a,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function A(t){var e=t.vertical,r=t.verticalFill,o=t.fillOpacity,i=t.x,a=t.y,c=t.width,u=t.height,l=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var s=l.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,e){var l=s[e+1]?s[e+1]-t:i+c-t;if(l<=0)return null;var f=e%r.length;return n.createElement("rect",{key:"react-".concat(e),x:t,y:a,width:l,height:u,stroke:"none",fill:r[f],fillOpacity:o,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var P=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return(0,l.PW)((0,s.f)(m(m(m({},f.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},E=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return(0,l.PW)((0,s.f)(m(m(m({},f.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},k={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function M(t){var e,r,o,u,l,s,f=(0,p.yi)(),h=(0,p.rY)(),d=(0,p.hj)(),v=m(m({},t),{},{stroke:null!=(e=t.stroke)?e:k.stroke,fill:null!=(r=t.fill)?r:k.fill,horizontal:null!=(o=t.horizontal)?o:k.horizontal,horizontalFill:null!=(u=t.horizontalFill)?u:k.horizontalFill,vertical:null!=(l=t.vertical)?l:k.vertical,verticalFill:null!=(s=t.verticalFill)?s:k.verticalFill,x:(0,c.Et)(t.x)?t.x:d.left,y:(0,c.Et)(t.y)?t.y:d.top,width:(0,c.Et)(t.width)?t.width:d.width,height:(0,c.Et)(t.height)?t.height:d.height}),g=v.x,O=v.y,M=v.width,_=v.height,T=v.syncWithTicks,C=v.horizontalValues,D=v.verticalValues,I=(0,p.pj)(),N=(0,p.$G)();if(!(0,c.Et)(M)||M<=0||!(0,c.Et)(_)||_<=0||!(0,c.Et)(g)||g!==+g||!(0,c.Et)(O)||O!==+O)return null;var B=v.verticalCoordinatesGenerator||P,R=v.horizontalCoordinatesGenerator||E,L=v.horizontalPoints,z=v.verticalPoints;if((!L||!L.length)&&i()(R)){var U=C&&C.length,F=R({yAxis:N?m(m({},N),{},{ticks:U?C:N.ticks}):void 0,width:f,height:h,offset:d},!!U||T);(0,a.R)(Array.isArray(F),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(y(F),"]")),Array.isArray(F)&&(L=F)}if((!z||!z.length)&&i()(B)){var W=D&&D.length,$=B({xAxis:I?m(m({},I),{},{ticks:W?D:I.ticks}):void 0,width:f,height:h,offset:d},!!W||T);(0,a.R)(Array.isArray($),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(y($),"]")),Array.isArray($)&&(z=$)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(x,{fill:v.fill,fillOpacity:v.fillOpacity,x:v.x,y:v.y,width:v.width,height:v.height,ry:v.ry}),n.createElement(w,b({},v,{offset:d,horizontalPoints:L,xAxis:I,yAxis:N})),n.createElement(j,b({},v,{offset:d,verticalPoints:z,xAxis:I,yAxis:N})),n.createElement(S,b({},v,{horizontalPoints:L})),n.createElement(A,b({},v,{verticalPoints:z})))}M.displayName="CartesianGrid"},94999:(t,e,r)=>{var n=r(5658);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=!!e,e}},95442:(t,e,r)=>{"use strict";r.d(e,{A:()=>function t(){var e=new n,r=[],o=[],i=c;function u(t){let n=e.get(t);if(void 0===n){if(i!==c)return i;e.set(t,n=r.push(t)-1)}return o[n%o.length]}return u.domain=function(t){if(!arguments.length)return r.slice();for(let o of(r=[],e=new n,t))e.has(o)||e.set(o,r.push(o)-1);return u},u.range=function(t){return arguments.length?(o=Array.from(t),u):o.slice()},u.unknown=function(t){return arguments.length?(i=t,u):i},u.copy=function(){return t(r,o).unknown(i)},a.C.apply(u,arguments),u},h:()=>c});class n extends Map{constructor(t,e=i){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(o(this,t))}has(t){return super.has(o(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function o({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function i(t){return null!==t&&"object"==typeof t?t.valueOf():t}var a=r(28749);let c=Symbol("implicit")},96025:(t,e,r)=>{"use strict";r.d(e,{W:()=>v});var n=r(12115),o=r(52596),i=r(50091),a=r(45167),c=r(12814);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(l=function(){return!!t})()}function s(t){return(s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f(t,e){return(f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function p(t,e,r){return(e=h(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}function d(){return(d=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function y(t){var e=t.xAxisId,r=(0,i.yi)(),u=(0,i.rY)(),l=(0,i.AF)(e);return null==l?null:n.createElement(a.u,d({},l,{className:(0,o.A)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:u},ticksGenerator:function(t){return(0,c.Rh)(t,!0)}}))}var v=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=s(t),function(t,e){if(e&&("object"===u(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,l()?Reflect.construct(t,e||[],s(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&f(r,t),e=[{key:"render",value:function(){return n.createElement(y,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);p(v,"displayName","XAxis"),p(v,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0})},96294:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},96540:(t,e,r)=>{var n=r(31545),o=r(26151),i=r(53696),a=r(13364),c=r(20988);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},96548:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},96699:(t,e,r)=>{var n=r(51911),o=r(48973),i=r(20134),a=r(79595),c=r(32197),u=r(92972),l=r(94356);t.exports=function(t,e){return a(t)&&c(e)?u(l(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},97124:(t,e,r)=>{t.exports=r(75031)(r(62464))},98233:(t,e,r)=>{var n=r(24376),o=r(20570),i=r(64439),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},99544:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}}}]);