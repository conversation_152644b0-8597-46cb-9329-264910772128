"use strict";exports.id=938,exports.ids=[938],exports.modules={25635:(e,t,i)=>{i.d(t,{A:()=>d});var s=i(60687),a=i(88920),n=i(26001),l=i(85814),r=i.n(l),o=i(30474);function d({isOpen:e,onClose:t,product:i,quantity:l=1}){return(0,s.jsx)(a.N,{children:e&&(0,s.jsxs)(n.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:t,children:[(0,s.jsx)("div",{className:"absolute inset-0"}),(0,s.jsxs)(n.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.7,opacity:0,y:50},transition:{type:"spring",stiffness:300,damping:25,duration:.5},onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)(n.P.div,{className:"text-center mb-6",initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},children:[(0,s.jsx)("div",{className:"mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(n.P.svg,{className:"w-10 h-10 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",initial:{pathLength:0},animate:{pathLength:1},transition:{delay:.4,duration:.6},children:(0,s.jsx)(n.P.path,{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17",initial:{pathLength:0},animate:{pathLength:1},transition:{delay:.4,duration:.6}})})}),(0,s.jsx)(n.P.h2,{className:"text-2xl font-bold text-gray-800 mb-2",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:"Sepete Eklendi!"}),(0,s.jsx)(n.P.p,{className:"text-gray-600",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4},children:"\xdcr\xfcn başarıyla sepetinize eklendi."})]}),i&&(0,s.jsx)(n.P.div,{className:"bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-xl p-4 mb-6",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"relative w-16 h-16 flex-shrink-0",children:[(0,s.jsx)(o.default,{src:i.thumbnail,alt:i.title,fill:!0,className:"object-cover rounded-lg"}),(0,s.jsx)(n.P.div,{className:"absolute -top-2 -right-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-full w-7 h-7 flex items-center justify-center text-xs font-bold shadow-lg",initial:{scale:0},animate:{scale:1},transition:{delay:.6,type:"spring",stiffness:200},children:l})]}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-800 text-sm mb-1",children:i.title}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:i.brand}),(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("div",{className:"flex items-center space-x-2",children:i.membershipDiscount>0||i.extraDiscount>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("p",{className:"text-purple-600 font-bold text-lg",children:[i.discountedPrice.toFixed(2)," ₺"]}),(0,s.jsxs)("p",{className:"text-gray-500 line-through text-sm",children:[i.price.toFixed(2)," ₺"]})]}):(0,s.jsxs)("p",{className:"text-purple-600 font-bold text-lg",children:[i.discountedPrice.toFixed(2)," ₺"]})}),(0,s.jsxs)("div",{className:"bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs font-medium",children:[l," adet"]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[i.membershipDiscount>0&&(0,s.jsxs)("span",{className:"bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-2 py-1 rounded-full text-xs font-medium",children:["%",i.membershipDiscount.toFixed(0)," \xdcye İndirimi"]}),i.extraDiscount>0&&(0,s.jsxs)("span",{className:"bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-medium",children:["%",i.extraDiscount.toFixed(0)," İndirim"]})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-1 mt-2 justify-start",children:[i.pvPoints>0&&(0,s.jsxs)("div",{className:"bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg flex items-center gap-1",children:[(0,s.jsx)("span",{className:"font-bold",children:"PV"}),(0,s.jsx)("span",{children:(i.pvPoints*l).toFixed(0)})]}),i.cvPoints>0&&(0,s.jsxs)("div",{className:"bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg flex items-center gap-1",children:[(0,s.jsx)("span",{className:"font-bold",children:"CV"}),(0,s.jsx)("span",{children:(i.cvPoints*l).toFixed(0)})]}),i.spPoints>0&&(0,s.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg flex items-center gap-1",children:[(0,s.jsx)("span",{className:"font-bold",children:"SP"}),(0,s.jsx)("span",{children:(i.spPoints*l).toFixed(0)})]})]}),(0,s.jsx)("div",{className:"mt-3 pt-3 border-t border-purple-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Toplam:"}),(0,s.jsxs)("span",{className:"text-lg font-bold text-purple-700",children:[(i.discountedPrice*l).toFixed(2)," ₺"]})]})})]})]})}),(0,s.jsxs)(n.P.div,{className:"flex flex-col space-y-3",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},children:[(0,s.jsx)(r(),{href:"/cart",children:(0,s.jsxs)(n.P.button,{className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-4 px-6 rounded-xl font-semibold hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2",whileHover:{scale:1.02},whileTap:{scale:.98},onClick:t,children:[(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"})}),(0,s.jsx)("span",{children:"Sepeti G\xf6r\xfcnt\xfcle"})]})}),(0,s.jsxs)(n.P.button,{className:"w-full bg-white border-2 border-gray-200 text-gray-700 py-4 px-6 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-300 transition-all duration-300 flex items-center justify-center space-x-2",whileHover:{scale:1.02},whileTap:{scale:.98},onClick:t,children:[(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16l-4-4m0 0l4-4m-4 4h18"})}),(0,s.jsx)("span",{children:"Alışverişe Devam Et"})]})]}),(0,s.jsx)(n.P.button,{onClick:t,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors",initial:{opacity:0},animate:{opacity:1},transition:{delay:.7},whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]})})}},70440:(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});var s=i(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};