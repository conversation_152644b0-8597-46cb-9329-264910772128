{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/admin/products/edit/[id]", "regex": "^/admin/products/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/products/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/edit-product/[id]", "regex": "^/edit\\-product/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/edit\\-product/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/order-details/[orderId]", "regex": "^/order\\-details/([^/]+?)(?:/)?$", "routeKeys": {"nxtPorderId": "nxtPorderId"}, "namedRegex": "^/order\\-details/(?<nxtPorderId>[^/]+?)(?:/)?$"}, {"page": "/product/[id]", "regex": "^/product/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/product/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/account", "regex": "^/account(?:/)?$", "routeKeys": {}, "namedRegex": "^/account(?:/)?$"}, {"page": "/account/edit", "regex": "^/account/edit(?:/)?$", "routeKeys": {}, "namedRegex": "^/account/edit(?:/)?$"}, {"page": "/add-product", "regex": "^/add\\-product(?:/)?$", "routeKeys": {}, "namedRegex": "^/add\\-product(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/admin/category-management", "regex": "^/admin/category\\-management(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/category\\-management(?:/)?$"}, {"page": "/admin/dealership-applications", "regex": "^/admin/dealership\\-applications(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/dealership\\-applications(?:/)?$"}, {"page": "/admin/orders", "regex": "^/admin/orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/orders(?:/)?$"}, {"page": "/admin/pending-products", "regex": "^/admin/pending\\-products(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/pending\\-products(?:/)?$"}, {"page": "/admin/products", "regex": "^/admin/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/products(?:/)?$"}, {"page": "/admin/products/add", "regex": "^/admin/products/add(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/products/add(?:/)?$"}, {"page": "/admin/reports", "regex": "^/admin/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/reports(?:/)?$"}, {"page": "/admin/users", "regex": "^/admin/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/users(?:/)?$"}, {"page": "/announcements", "regex": "^/announcements(?:/)?$", "routeKeys": {}, "namedRegex": "^/announcements(?:/)?$"}, {"page": "/become-dealer", "regex": "^/become\\-dealer(?:/)?$", "routeKeys": {}, "namedRegex": "^/become\\-dealer(?:/)?$"}, {"page": "/cart", "regex": "^/cart(?:/)?$", "routeKeys": {}, "namedRegex": "^/cart(?:/)?$"}, {"page": "/checkout", "regex": "^/checkout(?:/)?$", "routeKeys": {}, "namedRegex": "^/checkout(?:/)?$"}, {"page": "/earnings", "regex": "^/earnings(?:/)?$", "routeKeys": {}, "namedRegex": "^/earnings(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/forgot-password", "regex": "^/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/forgot\\-password(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/my-team", "regex": "^/my\\-team(?:/)?$", "routeKeys": {}, "namedRegex": "^/my\\-team(?:/)?$"}, {"page": "/panel", "regex": "^/panel(?:/)?$", "routeKeys": {}, "namedRegex": "^/panel(?:/)?$"}, {"page": "/pending-products", "regex": "^/pending\\-products(?:/)?$", "routeKeys": {}, "namedRegex": "^/pending\\-products(?:/)?$"}, {"page": "/products", "regex": "^/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/products(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/reset-password", "regex": "^/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/reset\\-password(?:/)?$"}, {"page": "/team-tree", "regex": "^/team\\-tree(?:/)?$", "routeKeys": {}, "namedRegex": "^/team\\-tree(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": [{"source": "/api/:path*", "destination": "https://api.sayglobalweb.com/api/:path*", "regex": "^/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}]}