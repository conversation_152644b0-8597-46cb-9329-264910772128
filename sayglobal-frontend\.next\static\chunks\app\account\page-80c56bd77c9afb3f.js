(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1298],{1894:(e,t,a)=>{"use strict";a.d(t,{d:()=>i});var s=a(31802);class r{static async convertToWebP(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.DEFAULT_WEBP_OPTIONS,a=performance.now();console.log("\uD83D\uDDBC️ WebP d\xf6n\xfcşt\xfcrme başlıyor...",{originalName:e.name,originalSize:"".concat((e.size/1024/1024).toFixed(2),"MB"),originalType:e.type});try{let r={maxSizeMB:t.maxSizeMB||1,maxWidthOrHeight:t.maxWidthOrHeight||800,useWebWorker:!1!==t.useWebWorker,fileType:t.fileType||"image/webp",quality:t.quality||.8,alwaysKeepResolution:!1,initialQuality:t.quality||.8},i=await (0,s.A)(e,r),l=performance.now()-a,n={file:i,originalSize:e.size,compressedSize:i.size,compressionRatio:(1-i.size/e.size)*100,processingTime:l};return console.log("✅ WebP d\xf6n\xfcşt\xfcrme tamamlandı!",{compressedName:i.name,compressedSize:"".concat((i.size/1024/1024).toFixed(2),"MB"),compressedType:i.type,compressionRatio:"".concat(n.compressionRatio.toFixed(1),"%"),processingTime:"".concat(l.toFixed(1),"ms")}),n}catch(e){throw console.error("❌ WebP d\xf6n\xfcşt\xfcrme hatası:",e),Error("Resim d\xf6n\xfcşt\xfcr\xfcl\xfcrken hata oluştu")}}static async convertProfilePictureToWebP(e){return this.convertToWebP(e,this.PROFILE_WEBP_OPTIONS)}static async convertProductImageToWebP(e){return this.convertToWebP(e,this.PRODUCT_WEBP_OPTIONS)}static validateImageFile(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return e.type.startsWith("image/")?e.size>1024*t*1024?{isValid:!1,error:"Dosya boyutu ".concat(t,"MB'dan k\xfc\xe7\xfck olmalıdır")}:["image/jpeg","image/jpg","image/png","image/webp","image/gif"].includes(e.type.toLowerCase())?{isValid:!0}:{isValid:!1,error:"Desteklenen formatlar: JPEG, PNG, WebP, GIF"}:{isValid:!1,error:"L\xfctfen bir resim dosyası se\xe7in"}}static createPreviewUrl(e){return new Promise((t,a)=>{let s=new FileReader;s.onloadend=()=>t(s.result),s.onerror=a,s.readAsDataURL(e)})}static async convertMultipleToWebP(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.DEFAULT_WEBP_OPTIONS;console.log("\uD83D\uDDBC️ ".concat(e.length," resim d\xf6n\xfcşt\xfcr\xfcl\xfcyor..."));let a=await Promise.allSettled(e.map(e=>this.convertToWebP(e,t))),s=[],r=[];return a.forEach((t,a)=>{"fulfilled"===t.status?s.push(t.value):r.push("".concat(e[a].name,": ").concat(t.reason.message))}),r.length>0&&console.warn("⚠️ Bazı resimler işlenirken hata oluştu:",r),console.log("✅ ".concat(s.length,"/").concat(e.length," resim başarıyla d\xf6n\xfcşt\xfcr\xfcld\xfc")),s}static formatFileSize(e){if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]}static formatCompressionRatio(e){return"".concat(e.toFixed(1),"%")}}r.DEFAULT_WEBP_OPTIONS={maxSizeMB:1,maxWidthOrHeight:800,quality:.8,fileType:"image/webp",useWebWorker:!0},r.PROFILE_WEBP_OPTIONS={maxSizeMB:.5,maxWidthOrHeight:400,quality:.85,fileType:"image/webp",useWebWorker:!0},r.PRODUCT_WEBP_OPTIONS={maxSizeMB:1.5,maxWidthOrHeight:1200,quality:.9,fileType:"image/webp",useWebWorker:!0};let i=r},8207:(e,t,a)=>{Promise.resolve().then(a.bind(a,15705))},13051:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var s=a(95155),r=a(60760),i=a(76408),l=a(6874),n=a.n(l),o=a(66766);function d(e){let{isOpen:t,onClose:a,product:l,isAdded:d}=e;return(0,s.jsx)(r.N,{children:t&&(0,s.jsxs)(i.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:a,children:[(0,s.jsx)("div",{className:"absolute inset-0"}),(0,s.jsxs)(i.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.7,opacity:0,y:50},transition:{type:"spring",stiffness:300,damping:25,duration:.5},onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)(i.P.div,{className:"text-center mb-6",initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},children:[(0,s.jsx)("div",{className:"mx-auto w-20 h-20 ".concat(d?"bg-red-100":"bg-gray-100"," rounded-full flex items-center justify-center mb-4"),children:(0,s.jsx)(i.P.svg,{className:"w-10 h-10 ".concat(d?"text-red-600":"text-gray-500"),fill:d?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24",initial:{scale:0},animate:{scale:1},transition:{delay:.3,type:"spring",stiffness:200},children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})}),(0,s.jsx)(i.P.h2,{className:"text-2xl font-bold text-gray-800 mb-2",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:d?"Favorilere Eklendi!":"Favorilerden \xc7ıkarıldı!"}),(0,s.jsx)(i.P.p,{className:"text-gray-600",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4},children:d?"\xdcr\xfcn favori listenize başarıyla eklendi.":"\xdcr\xfcn favori listenizden \xe7ıkarıldı."})]}),l&&(0,s.jsxs)(i.P.div,{className:"".concat(d?"bg-red-50 border-red-200":"bg-gray-50 border-gray-200"," border rounded-lg p-4 mb-6 flex items-center space-x-3"),initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,s.jsx)("div",{className:"relative w-16 h-16 flex-shrink-0",children:(0,s.jsx)(o.default,{src:l.thumbnail,alt:l.title,fill:!0,className:"object-cover rounded-lg"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900 truncate",children:l.title}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:l.brand}),(0,s.jsx)("p",{className:"text-sm font-medium text-purple-600",children:l.discountPercentage?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("span",{className:"line-through text-gray-400 mr-2",children:["₺",l.price.toFixed(2)]}),"₺",(l.price*(1-l.discountPercentage/100)).toFixed(2)]}):"₺".concat(l.price.toFixed(2))})]})]}),(0,s.jsxs)(i.P.div,{className:"flex flex-col space-y-3",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},children:[(0,s.jsx)(n(),{href:"/account?tab=favorites",children:(0,s.jsx)(i.P.button,{className:"w-full ".concat(d?"bg-gradient-to-r from-red-600 to-pink-600":"bg-gradient-to-r from-gray-600 to-gray-700"," text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300"),whileHover:{scale:1.02},whileTap:{scale:.98},onClick:a,children:"Favorilerime Git"})}),(0,s.jsx)(i.P.button,{className:"w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},onClick:a,children:"Alışverişe Devam Et"})]}),(0,s.jsx)(i.P.button,{onClick:a,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors",initial:{opacity:0},animate:{opacity:1},transition:{delay:.7},whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]})})}},15705:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>R});var s=a(95155),r=a(12115),i=a(76408),l=a(6874),n=a.n(l),o=a(35695),d=a(87220),c=a(69848),m=a(74157),u=a(20181),x=a(83540),p=a(41287),h=a(94754),g=a(96025),y=a(16238),b=a(94517),f=a(62341),v=a(60760),j=a(59959),N=a(26715),w=a(66681),k=a(45106);function C(e){let{isOpen:t,onClose:a}=e,l=(0,k.VS)(),n=(0,k.Gc)(),{closeEditPersonalInfoModal:o}=(0,k.QR)(),{user:c}=(0,d.A)(),{data:m}=(0,w.dS)(),u=(0,N.jE)(),x=l||t||!1,p=()=>{l&&o(),a&&a()},[h,g]=(0,r.useState)(()=>({firstName:"",lastName:"",phoneNumber:""})),[y,b]=(0,r.useState)(!1);(0,r.useEffect)(()=>{x&&g({firstName:(null==m?void 0:m.firstName)||(null==c?void 0:c.firstName)||(null==n?void 0:n.firstName)||"",lastName:(null==m?void 0:m.lastName)||(null==c?void 0:c.lastName)||(null==n?void 0:n.lastName)||"",phoneNumber:(null==c?void 0:c.phoneNumber)||(null==n?void 0:n.phoneNumber)||""})},[x,m,c,n]);let f=e=>{let{name:t,value:a}=e.target;g(e=>({...e,[t]:a}))},C=async e=>{e.preventDefault(),b(!0);try{let e={};if(h.firstName&&h.firstName.trim()&&(e.firstName=h.firstName.trim()),h.lastName&&h.lastName.trim()&&(e.lastName=h.lastName.trim()),h.phoneNumber&&h.phoneNumber.trim()&&(e.phoneNumber=h.phoneNumber.trim()),(null==m?void 0:m.dateOfBirth)&&(e.dateOfBirth=m.dateOfBirth),(null==m?void 0:m.gender)!==void 0&&(null==m?void 0:m.gender)!==null&&(e.gender=m.gender),(null==m?void 0:m.location)&&m.location.trim()&&(e.location=m.location.trim()),0===Object.keys(e).length)return void p();await j.y.updateProfile(e),u.invalidateQueries({queryKey:w.ZF.user()}),u.invalidateQueries({queryKey:w.ZF.profileInfo()}),await Promise.all([u.refetchQueries({queryKey:w.ZF.user()}),u.refetchQueries({queryKey:w.ZF.profileInfo()})]),p()}catch(e){console.error("❌ Profil g\xfcncellenirken hata:",e),alert("Profil g\xfcncellenirken bir hata oluştu: "+(e.message||"Bilinmeyen hata"))}finally{b(!1)}};return(0,s.jsx)(v.N,{children:x&&(0,s.jsxs)(i.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:p,children:[(0,s.jsx)("div",{className:"absolute inset-0"}),(0,s.jsxs)(i.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.7,opacity:0,y:50},transition:{type:"spring",stiffness:300,damping:25,duration:.5},onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:"Kişisel Bilgilerimi G\xfcncelle"}),(0,s.jsx)(i.P.button,{onClick:p,className:"text-gray-400 hover:text-gray-600 transition-colors",whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Ad"}),(0,s.jsx)("input",{type:"text",id:"firstName",name:"firstName",value:h.firstName||"",onChange:f,className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",placeholder:"Adınız",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Soyad"}),(0,s.jsx)("input",{type:"text",id:"lastName",name:"lastName",value:h.lastName||"",onChange:f,className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",placeholder:"Soyadınız",required:!0})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phoneNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:"Telefon"}),(0,s.jsx)("input",{type:"tel",id:"phoneNumber",name:"phoneNumber",value:h.phoneNumber||"",onChange:f,className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",placeholder:"Telefon numaranız"})]}),(0,s.jsxs)("div",{className:"flex flex-col space-y-3 pt-4",children:[(0,s.jsx)(i.P.button,{type:"submit",disabled:y,className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed",whileHover:{scale:y?1:1.02},whileTap:{scale:y?1:.98},children:y?"G\xfcncelleniyor...":"Bilgileri G\xfcncelle"}),(0,s.jsx)(i.P.button,{type:"button",onClick:p,className:"w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},children:"İptal"})]})]})]})]})})}var P=a(35159),D=a(82073);function A(e){var t;let{isOpen:a,onClose:l,onAddressAdded:n}=e,o=(0,P.n)(),{user:c}=(0,d.A)(),m=o.user||c,u=(0,k.QK)(),x=(0,k.lA)(),{closeAddAddressModal:p}=(0,k.QR)(),{data:h=[]}=(0,D._c)(),g=0===h.length,y=u||a||!1,b=()=>{u&&p(),l&&l()},[f,j]=(0,r.useState)(()=>({title:"",fullAddress:"",city:"",district:"",postalCode:"",isDefault:!1}));(0,r.useEffect)(()=>{x?j(x):y&&(j({title:"",fullAddress:"",city:"",district:"",postalCode:"",isDefault:g}),g&&console.log("\uD83C\uDFE0 Modal: İlk adres - checkbox otomatik checked"))},[x,y,g]);let N=(0,D.aO)(),w=e=>{let{name:t,value:a,type:s}=e.target;j(r=>({...r,[t]:"checkbox"===s?e.target.checked:a}))},C=async e=>{if(e.preventDefault(),console.log("\uD83C\uDFE0 Modal: Adres ekleme başlıyor...",{hasUser:!!m,userId:null==m?void 0:m.id,isAuthenticated:o.isAuthenticated,formData:f}),!m)return void console.error("❌ Modal: User bulunamadı, adres ekleme iptal edildi",{authStoreUser:o.user,contextUser:c,isAuthenticated:o.isAuthenticated,authUserExists:!!o.user,contextUserExists:!!c});N.mutate(f,{onSuccess:e=>{console.log("✅ Modal: Address created successfully",e),j({title:"",fullAddress:"",city:"",district:"",postalCode:"",isDefault:!1}),n&&n(),b()},onError:e=>{console.error("❌ Modal: Address creation failed:",e),console.error("❌ Modal: Error name:",e.name),console.error("❌ Modal: Error message:",e.message)}})},A=N.isPending,S=null==(t=N.error)?void 0:t.message;return(0,s.jsx)(v.N,{children:y&&(0,s.jsxs)(i.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:b,children:[(0,s.jsx)("div",{className:"absolute inset-0"}),(0,s.jsxs)(i.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-lg w-full mx-4 shadow-2xl max-h-[90vh] overflow-y-auto",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.7,opacity:0,y:50},transition:{type:"spring",stiffness:300,damping:25,duration:.5},onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:"Yeni Adres Ekle"}),(0,s.jsx)(i.P.button,{onClick:b,className:"text-gray-400 hover:text-gray-600 transition-colors",whileHover:{scale:1.1},whileTap:{scale:.9},disabled:A,children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),S&&(0,s.jsx)("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg",children:S}),(0,s.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:"Adres Başlığı *"}),(0,s.jsx)("input",{type:"text",id:"title",name:"title",value:f.title,onChange:w,className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",placeholder:"Ev, İş, vb.",required:!0,disabled:A})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"city",className:"block text-sm font-medium text-gray-700 mb-1",children:"Şehir *"}),(0,s.jsxs)("select",{id:"city",name:"city",value:f.city,onChange:w,className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",required:!0,disabled:A,children:[(0,s.jsx)("option",{value:"",children:"Şehir se\xe7in"}),["İstanbul","Ankara","İzmir","Antalya","Bursa","Adana","Konya","Gaziantep","Mersin","Diyarbakır"].map(e=>(0,s.jsx)("option",{value:e,children:e},e))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"district",className:"block text-sm font-medium text-gray-700 mb-1",children:"İl\xe7e *"}),(0,s.jsx)("input",{type:"text",id:"district",name:"district",value:f.district,onChange:w,className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",placeholder:"İl\xe7e",required:!0,disabled:A})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"fullAddress",className:"block text-sm font-medium text-gray-700 mb-1",children:"Adres *"}),(0,s.jsx)("textarea",{id:"fullAddress",name:"fullAddress",value:f.fullAddress,onChange:w,rows:3,className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black resize-none",placeholder:"Detaylı adres bilgisi",required:!0,disabled:A})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"postalCode",className:"block text-sm font-medium text-gray-700 mb-1",children:"Posta Kodu (Opsiyonel)"}),(0,s.jsx)("input",{type:"text",id:"postalCode",name:"postalCode",value:f.postalCode,onChange:w,className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",placeholder:"Posta kodu (isteğe bağlı)",disabled:A})]}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"isDefault",name:"isDefault",checked:f.isDefault,onChange:w,className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded",disabled:A||g}),(0,s.jsx)("label",{htmlFor:"isDefault",className:"ml-2 block text-sm ".concat(g?"text-gray-500":"text-gray-700"),children:"Bu adresi varsayılan adres olarak ayarla"})]}),g&&(0,s.jsx)("p",{className:"text-xs text-purple-600 mt-1 ml-6",children:"İlk adres otomatik olarak varsayılan adres olur"})]}),(0,s.jsxs)("div",{className:"flex flex-col space-y-3 mt-6",children:[(0,s.jsx)(i.P.button,{type:"submit",className:"w-full py-3 px-4 rounded-lg font-medium transition-all duration-300 ".concat(A?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:shadow-lg"),whileHover:A?{}:{scale:1.02},whileTap:A?{}:{scale:.98},disabled:A,children:A?"Kaydediliyor...":"Adresi Kaydet"}),(0,s.jsx)(i.P.button,{type:"button",onClick:b,className:"w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},disabled:A,children:"İptal"})]})]})]})]})})}function S(e){let{isOpen:t,onClose:a,onConfirm:l,targetAddress:n,deletedDefaultAddress:o}=e,d=(0,k.Ph)(),c=(0,k.u6)(),{closeSetDefaultConfirmationModal:m}=(0,k.QR)(),u=d||t||!1,x=(null==c?void 0:c.targetAddress)||n,p=(null==c?void 0:c.deletedDefaultAddress)||o,h=!!p,g=()=>{d&&m(),a&&a()},y=()=>{l&&l(),g()};return(0,r.useEffect)(()=>{if(u&&h){let e=e=>{"Escape"===e.key&&(e.preventDefault(),e.stopPropagation())};return document.addEventListener("keydown",e,!0),()=>{document.removeEventListener("keydown",e,!0)}}},[u,h]),(0,s.jsx)(v.N,{children:u&&x&&(0,s.jsxs)(i.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:h?void 0:g,children:[(0,s.jsx)("div",{className:"absolute inset-0"}),(0,s.jsxs)(i.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-lg w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.7,opacity:0,y:50},transition:{type:"spring",stiffness:300,damping:25,duration:.5},onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mr-4",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:"Varsayılan Adres"})]}),!h&&(0,s.jsx)(i.P.button,{onClick:g,className:"text-gray-400 hover:text-gray-600 transition-colors",whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)("div",{className:"mb-8",children:[h?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("p",{className:"text-gray-700 leading-relaxed",children:[(0,s.jsxs)("span",{className:"font-semibold text-red-600",children:['"',null==p?void 0:p.title,'"']})," adresiniz silindi."]}),(0,s.jsx)("div",{className:"bg-amber-50 border border-amber-200 rounded-lg p-4",children:(0,s.jsxs)("p",{className:"text-amber-800 text-sm",children:[(0,s.jsxs)("span",{className:"font-semibold",children:['"',x.title,'"']})," adresiniz otomatik olarak varsayılan adres yapılacak."]})})]}):(0,s.jsxs)("p",{className:"text-gray-700 leading-relaxed",children:[(0,s.jsxs)("span",{className:"font-semibold text-purple-600",children:['"',x.title,'"']})," adresini varsayılan adres olarak ayarlamak ister misiniz?"]}),(0,s.jsx)("div",{className:"mt-6 bg-gray-50 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,s.jsxs)("svg",{className:"w-5 h-5 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900",children:x.title}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[x.district,", ",x.city]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:x.fullAddress})]})]})})]}),h?(0,s.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,s.jsx)(i.P.button,{onClick:y,className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Tamam, Bu Adresi Varsayılan Yap"}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 text-center space-y-1",children:[(0,s.jsx)("p",{children:"Bir varsayılan adres se\xe7meniz zorunludur"}),(0,s.jsx)("p",{className:"text-purple-600",children:"\uD83D\uDCA1 Dilerseniz sonradan varsayılan adresinizi değiştirebilirsiniz"})]})]}):(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:space-x-3 space-y-3 sm:space-y-0",children:[(0,s.jsx)(i.P.button,{onClick:y,className:"flex-1 bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Evet, Varsayılan Yap"}),(0,s.jsx)(i.P.button,{onClick:g,className:"flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},children:"İptal"})]})]})]})})}function M(e){let{addresses:t,onAddressDeleted:a}=e,{user:r}=(0,d.A)(),{openSetDefaultConfirmationModal:l}=(0,k.QR)(),n=(0,k.u6)(),o=(0,D.Xk)(),c=(0,D.fB)((e,t)=>{l({targetAddress:{id:e.id,title:e.title,fullAddress:e.fullAddress,city:e.city,district:e.district},deletedDefaultAddress:{id:(null==t?void 0:t.id)||0,title:(null==t?void 0:t.title)||"Silinmiş Adres"}})}),m=Array.isArray(t)?t.filter(e=>e&&void 0!==e.id&&e.title&&e.fullAddress&&e.city&&e.district):[],u=async e=>{r&&e&&(m.find(t=>t.id===e),c.mutate(e,{onSuccess:()=>{console.log("✅ Address deleted successfully"),a&&a()},onError:e=>{console.error("❌ Address deletion failed:",e)}}))},x=e=>{r&&e.id&&!e.isDefault&&l({targetAddress:{id:e.id,title:e.title,fullAddress:e.fullAddress,city:e.city,district:e.district}})},p=e=>{r&&e.id&&o.mutate(e.id,{onSuccess:()=>{console.log("✅ Default address set successfully"),a&&a()},onError:e=>{console.error("❌ Set default address failed:",e)}})};return 0===m.length?(0,s.jsxs)("div",{className:"text-center py-10 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 mx-auto text-gray-400 mb-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Hen\xfcz kayıtlı adresiniz bulunmuyor"}),(0,s.jsx)("p",{className:"text-gray-500 mb-6",children:"Siparişlerinizin teslimatı i\xe7in adres bilgilerinizi ekleyebilirsiniz."})]}):(0,s.jsxs)("div",{className:"space-y-4",children:[c.error&&(0,s.jsx)("div",{className:"p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg",children:c.error.message||"Adres silinirken bir hata oluştu"}),(0,s.jsx)("div",{className:"grid gap-4 md:grid-cols-2",children:(0,s.jsx)(v.N,{mode:"popLayout",children:m.map((e,t)=>(0,s.jsxs)(i.P.div,{layoutId:"address-".concat(e.id||t),initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{type:"spring",stiffness:500,damping:30,duration:.3},layout:!0,className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow duration-300",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900",children:e.title}),e.isDefault&&(0,s.jsx)("span",{className:"bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded",children:"Varsayılan"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.isDefault&&(0,s.jsx)(i.P.button,{onClick:()=>x(e),disabled:o.isPending,className:"px-3 py-1 text-xs font-medium rounded transition-colors ".concat(o.isPending?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-purple-50 text-purple-600 hover:bg-purple-100 hover:text-purple-700"),whileHover:{scale:o.isPending?1:1.05},whileTap:{scale:o.isPending?1:.95},title:"Bu adresi varsayılan yap",children:o.isPending?(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)("svg",{className:"w-3 h-3 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,s.jsx)("span",{children:"Ayarlanıyor..."})]}):(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,s.jsx)("span",{children:"Varsayılan Yap"})]})}),(0,s.jsx)(i.P.button,{onClick:()=>u(e.id),disabled:c.isPending,className:"p-2 rounded-lg transition-colors ".concat(c.isPending?"bg-gray-100 text-gray-400 cursor-not-allowed":"text-red-500 hover:bg-red-50 hover:text-red-700"),whileHover:{scale:c.isPending?1:1.1},whileTap:{scale:c.isPending?1:.95},title:"Bu adresi sil",children:c.isPending?(0,s.jsx)("svg",{className:"w-5 h-5 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}):(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,s.jsxs)("p",{className:"flex items-start",children:[(0,s.jsxs)("svg",{className:"w-4 h-4 mt-0.5 mr-2 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,s.jsx)("span",{children:e.fullAddress})]}),(0,s.jsxs)("p",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})}),(0,s.jsxs)("span",{children:[e.district,", ",e.city]})]}),e.postalCode&&(0,s.jsxs)("p",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 7.89a2 2 0 002.83 0L21 9M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),(0,s.jsxs)("span",{children:["Posta Kodu: ",e.postalCode]})]})]})]},e.id||"address-".concat(t)))})}),(0,s.jsx)(S,{onConfirm:()=>{(null==n?void 0:n.targetAddress)&&p({id:n.targetAddress.id,title:n.targetAddress.title,fullAddress:n.targetAddress.fullAddress,city:n.targetAddress.city,district:n.targetAddress.district,postalCode:"",isDefault:!1})}})]})}var z=a(13051);function L(){let e=(0,k.JZ)(),t=(0,k.gA)(),{closeBankingModal:a}=(0,k.QR)(),[l,n]=(0,r.useState)({accountHolderName:"",bankName:"",iban:"",branchCode:"",branchName:""}),o=(0,r.useMemo)(()=>t?{accountHolderName:t.accountHolderName||"",bankName:t.bankName||"",iban:t.iban||"",branchCode:t.branchCode||"",branchName:t.branchName||""}:null,[null==t?void 0:t.accountHolderName,null==t?void 0:t.bankName,null==t?void 0:t.iban,null==t?void 0:t.branchCode,null==t?void 0:t.branchName]);(0,r.useEffect)(()=>{o&&n(o)},[o]),(0,r.useEffect)(()=>{if(e){let e=window.innerWidth-document.documentElement.clientWidth;document.body.style.overflow="hidden",document.body.style.paddingRight="".concat(e,"px")}else document.body.style.overflow="unset",document.body.style.paddingRight="0px";return()=>{document.body.style.overflow="unset",document.body.style.paddingRight="0px"}},[e]);let d=e=>{let{name:t,value:a}=e.target;n(e=>({...e,[t]:a}))},c=e=>{let t=e.replace(/\s/g,"").toUpperCase();return t.startsWith("TR")?t.replace(/(.{4})/g,"$1 ").trim():t};return(0,s.jsx)(v.N,{children:e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:a,className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50"}),(0,s.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:(0,s.jsxs)(i.P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},className:"bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"IBAN Bilgilerini D\xfczenle"}),(0,s.jsx)(i.P.button,{onClick:a,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),console.log("IBAN Bilgileri:",l),a()},className:"p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"accountHolderName",className:"block text-sm font-medium text-gray-700 mb-2",children:["Hesap Sahibi Adı ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",id:"accountHolderName",name:"accountHolderName",value:l.accountHolderName,onChange:d,className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",placeholder:"Hesap sahibinin tam adını girin",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"bankName",className:"block text-sm font-medium text-gray-700 mb-2",children:["Banka Adı ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("select",{id:"bankName",name:"bankName",value:l.bankName,onChange:d,className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",required:!0,children:[(0,s.jsx)("option",{value:"",children:"Banka se\xe7iniz"}),(0,s.jsx)("option",{value:"akbank",children:"Akbank"}),(0,s.jsx)("option",{value:"garanti",children:"Garanti BBVA"}),(0,s.jsx)("option",{value:"isbank",children:"T\xfcrkiye İş Bankası"}),(0,s.jsx)("option",{value:"yapi-kredi",children:"Yapı Kredi"}),(0,s.jsx)("option",{value:"halkbank",children:"Halkbank"}),(0,s.jsx)("option",{value:"ziraat",children:"Ziraat Bankası"}),(0,s.jsx)("option",{value:"vakifbank",children:"VakıfBank"}),(0,s.jsx)("option",{value:"teb",children:"TEB"}),(0,s.jsx)("option",{value:"enpara",children:"Enpara.com"}),(0,s.jsx)("option",{value:"denizbank",children:"DenizBank"}),(0,s.jsx)("option",{value:"other",children:"Diğer"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"iban",className:"block text-sm font-medium text-gray-700 mb-2",children:["IBAN ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",id:"iban",name:"iban",value:l.iban,onChange:e=>{let t=c(e.target.value);n(e=>({...e,iban:t}))},className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black font-mono",placeholder:"TR00 0000 0000 0000 0000 0000 00",maxLength:32,required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"IBAN numaranızı TR ile başlayarak girin"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"branchCode",className:"block text-sm font-medium text-gray-700 mb-2",children:"Şube Kodu"}),(0,s.jsx)("input",{type:"text",id:"branchCode",name:"branchCode",value:l.branchCode,onChange:d,className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",placeholder:"Şube kodunu girin (\xf6r: 444)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"branchName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Şube Adı"}),(0,s.jsx)("input",{type:"text",id:"branchName",name:"branchName",value:l.branchName,onChange:d,className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",placeholder:"Şube adını girin"})]})]}),(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-blue-800",children:"G\xfcvenlik Bilgilendirmesi"}),(0,s.jsx)("div",{className:"mt-2 text-sm text-blue-700",children:(0,s.jsx)("p",{children:"IBAN bilgileriniz sadece size \xf6deme yapmak i\xe7in kullanılır ve g\xfcvenli şekilde saklanır."})})]})]})}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,s.jsx)(i.P.button,{type:"button",onClick:a,className:"px-6 py-3 text-gray-700 bg-gray-100 rounded-lg font-medium hover:bg-gray-200 transition-colors",whileHover:{scale:1.02},whileTap:{scale:.98},children:"İptal"}),(0,s.jsx)(i.P.button,{type:"submit",className:"px-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg font-medium hover:shadow-lg transition duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Bilgileri Kaydet"})]})]})]})})]})})}function B(){let e=(0,k.c6)(),t=(0,k.hg)(),{closeAddCardModal:a}=(0,k.QR)(),[l,n]=(0,r.useState)({cardHolderName:"",cardType:"",cardNumber:"",expirationDate:"",cvv:"",saveCard:!0,setAsDefault:!1});(0,r.useEffect)(()=>{t?n(t):e&&n({cardHolderName:"",cardType:"",cardNumber:"",expirationDate:"",cvv:"",saveCard:!0,setAsDefault:!1})},[t,e]),(0,r.useEffect)(()=>{if(e){let e=window.innerWidth-document.documentElement.clientWidth;document.body.style.overflow="hidden",document.body.style.paddingRight="".concat(e,"px")}else document.body.style.overflow="unset",document.body.style.paddingRight="0px";return()=>{document.body.style.overflow="unset",document.body.style.paddingRight="0px"}},[e]);let o=e=>{let{name:t,value:a,type:s}=e.target;if("checkbox"===s){let a=e.target.checked;n(e=>({...e,[t]:a}))}else n(e=>({...e,[t]:a}))},d=e=>e.replace(/\D/g,"").replace(/(\d{4})(?=\d)/g,"$1 "),c=e=>{let t=e.replace(/\D/g,"");return t.length>=2?t.slice(0,2)+"/"+t.slice(2,4):t},m=(e=>{let t=e.replace(/\s/g,"");return t.startsWith("4")?"Visa":t.startsWith("5")||t.startsWith("2")?"MasterCard":t.startsWith("3")?"American Express":t.startsWith("9")?"Troy":""})(l.cardNumber);return(0,s.jsx)(v.N,{children:e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:a,className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50"}),(0,s.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:(0,s.jsxs)(i.P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},className:"bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"Yeni Kart Ekle"}),(0,s.jsx)(i.P.button,{onClick:a,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),console.log("Yeni Kart:",l),n({cardHolderName:"",cardType:"",cardNumber:"",expirationDate:"",cvv:"",saveCard:!0,setAsDefault:!1}),a()},className:"p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl p-6 text-white",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,s.jsx)("div",{className:"text-sm opacity-90",children:m||"Kart T\xfcr\xfc"}),(0,s.jsx)("div",{className:"w-8 h-8 bg-white/20 rounded-full"})]}),(0,s.jsx)("div",{className:"text-lg font-mono mb-4 tracking-wider",children:l.cardNumber||"**** **** **** ****"}),(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{className:"opacity-90",children:l.cardHolderName.toUpperCase()||"AD SOYAD"}),(0,s.jsx)("span",{className:"opacity-90",children:l.expirationDate||"MM/YY"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"cardHolderName",className:"block text-sm font-medium text-gray-700 mb-2",children:["Kart Sahibi Adı ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",id:"cardHolderName",name:"cardHolderName",value:l.cardHolderName,onChange:o,className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black uppercase placeholder:normal-case",placeholder:"Kartın \xfczerindeki ismi girin",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"cardType",className:"block text-sm font-medium text-gray-700 mb-2",children:"Kart T\xfcr\xfc"}),(0,s.jsxs)("select",{id:"cardType",name:"cardType",value:l.cardType,onChange:o,className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",children:[(0,s.jsx)("option",{value:"",children:"Otomatik Algıla"}),(0,s.jsx)("option",{value:"visa",children:"Visa"}),(0,s.jsx)("option",{value:"mastercard",children:"MasterCard"}),(0,s.jsx)("option",{value:"amex",children:"American Express"}),(0,s.jsx)("option",{value:"troy",children:"Troy"})]}),m&&(0,s.jsxs)("p",{className:"text-xs text-green-600 mt-1",children:["Algılanan: ",m]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"cardNumber",className:"block text-sm font-medium text-gray-700 mb-2",children:["Kart Numarası ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",id:"cardNumber",name:"cardNumber",value:l.cardNumber,onChange:e=>{let t=d(e.target.value);t.replace(/\s/g,"").length<=16&&n(e=>({...e,cardNumber:t}))},className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black font-mono text-lg tracking-wider",placeholder:"0000 0000 0000 0000",required:!0})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"expirationDate",className:"block text-sm font-medium text-gray-700 mb-2",children:["Son Kullanma ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",id:"expirationDate",name:"expirationDate",value:l.expirationDate,onChange:e=>{let t=c(e.target.value);n(e=>({...e,expirationDate:t}))},className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black font-mono text-center",placeholder:"MM/YY",maxLength:5,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"cvv",className:"block text-sm font-medium text-gray-700 mb-2",children:["CVV ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",id:"cvv",name:"cvv",value:l.cvv,onChange:e=>{let t=e.target.value.replace(/\D/g,"");t.length<=4&&n(e=>({...e,cvv:t}))},className:"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black font-mono text-center",placeholder:"123",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex items-center h-5",children:(0,s.jsx)("input",{id:"saveCard",name:"saveCard",type:"checkbox",checked:l.saveCard,onChange:o,className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"})}),(0,s.jsxs)("div",{className:"ml-3 text-sm",children:[(0,s.jsx)("label",{htmlFor:"saveCard",className:"font-medium text-gray-700",children:"Kartımı g\xfcvenli şekilde kaydet"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Gelecekteki \xf6demeleriniz i\xe7in kart bilgileriniz g\xfcvenli şekilde saklanır"})]})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex items-center h-5",children:(0,s.jsx)("input",{id:"setAsDefault",name:"setAsDefault",type:"checkbox",checked:l.setAsDefault,onChange:o,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})}),(0,s.jsxs)("div",{className:"ml-3 text-sm",children:[(0,s.jsxs)("label",{htmlFor:"setAsDefault",className:"font-medium text-gray-700 flex items-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-yellow-500 mr-1",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),"Bu kartı varsayılan kart yap"]}),(0,s.jsx)("p",{className:"text-gray-500",children:"Bu kart t\xfcm \xf6demelerinizde \xf6ncelikli olarak kullanılacak"})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,s.jsx)(i.P.button,{type:"button",onClick:a,className:"px-6 py-3 text-gray-700 bg-gray-100 rounded-lg font-medium hover:bg-gray-200 transition-colors",whileHover:{scale:1.02},whileTap:{scale:.98},children:"İptal"}),(0,s.jsx)(i.P.button,{type:"submit",className:"px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg font-medium hover:shadow-lg transition duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Kartı Ekle"})]})]})]})})]})})}function T(){let e=(0,k.ig)(),t=(0,k.vQ)(),{closeSetDefaultCardModal:a}=(0,k.QR)();(0,r.useEffect)(()=>{if(e){let e=window.innerWidth-document.documentElement.clientWidth;document.body.style.overflow="hidden",document.body.style.paddingRight="".concat(e,"px")}else document.body.style.overflow="unset",document.body.style.paddingRight="0px";return()=>{document.body.style.overflow="unset",document.body.style.paddingRight="0px"}},[e]);let l=(null==t?void 0:t.card)||null,n=(null==t?void 0:t.onConfirm)||(()=>{});return l?(0,s.jsx)(v.N,{children:e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:a,className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50"}),(0,s.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:(0,s.jsxs)(i.P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},className:"bg-white rounded-2xl shadow-2xl max-w-md w-full p-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 mb-4",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",className:"w-6 h-6 text-purple-600",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Varsayılan Kart Olarak Ayarla"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Bu kartı varsayılan kart olarak ayarlamak istediğinizden emin misiniz?"}),(0,s.jsxs)("div",{className:"bg-gradient-to-r ".concat((e=>{switch(e){case"blue":return"from-blue-500 to-blue-700";case"green":return"from-green-500 to-green-700";case"purple":return"from-purple-500 to-purple-700";case"orange":return"from-orange-500 to-orange-700";default:return"from-gray-500 to-gray-700"}})(l.color)," rounded-lg p-4 text-white mb-6 mx-auto max-w-xs"),children:[(0,s.jsx)("div",{className:"flex justify-between items-start mb-3",children:(0,s.jsx)("div",{className:"text-sm opacity-90",children:(e=>{switch(e.toLowerCase()){case"visa":return"Visa";case"mastercard":return"MasterCard";case"troy":return"Troy";default:return e}})(l.cardType)})}),(0,s.jsx)("div",{className:"text-base font-mono mb-2 tracking-wider",children:l.cardNumber}),(0,s.jsxs)("div",{className:"flex justify-between text-xs opacity-90",children:[(0,s.jsx)("span",{children:l.cardHolderName}),(0,s.jsx)("span",{children:l.expirationDate})]})]})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)("button",{onClick:a,className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"İptal"}),(0,s.jsx)("button",{onClick:()=>{n(),a()},className:"flex-1 px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 font-medium",children:"Onayla"})]})]})})]})}):null}function H(e){let{isOpen:t,onClose:a,onSubmit:l}=e,n=(0,k.F0)(),o=(0,k.S)(),{closeReferenceRegistrationModal:d}=(0,k.QR)(),c=n||t||!1,m=()=>{n&&d(),a&&a()},[u,x]=(0,r.useState)(()=>({firstName:"",lastName:"",email:"",phoneNumber:"",notes:""}));(0,r.useEffect)(()=>{o?x(o):c&&x({firstName:"",lastName:"",email:"",phoneNumber:"",notes:""})},[o,c]);let p=e=>{let{name:t,value:a}=e.target;x(e=>({...e,[t]:a}))};return(0,s.jsx)(v.N,{children:c&&(0,s.jsxs)(i.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:m,children:[(0,s.jsx)("div",{className:"absolute inset-0"}),(0,s.jsxs)(i.P.div,{className:"relative bg-white rounded-2xl p-6 max-w-md w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.7,opacity:0,y:50},transition:{type:"spring",stiffness:300,damping:25,duration:.5},onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:"Referans Kayıt Et"}),(0,s.jsx)("button",{onClick:m,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),l&&l(u),x({firstName:"",lastName:"",email:"",phoneNumber:"",notes:""}),m()},className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Ad *"}),(0,s.jsx)("input",{type:"text",name:"firstName",value:u.firstName,onChange:p,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-black",placeholder:"Adınızı girin"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Soyad *"}),(0,s.jsx)("input",{type:"text",name:"lastName",value:u.lastName,onChange:p,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-black",placeholder:"Soyadınızı girin"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"E-posta *"}),(0,s.jsx)("input",{type:"email",name:"email",value:u.email,onChange:p,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-black",placeholder:"E-posta adresini girin"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Telefon Numarası *"}),(0,s.jsx)("input",{type:"tel",name:"phoneNumber",value:u.phoneNumber,onChange:p,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-black",placeholder:"Telefon numarasını girin"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Notlar (İsteğe bağlı)"}),(0,s.jsx)("textarea",{name:"notes",value:u.notes,onChange:p,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none text-black",placeholder:"Ek notlar yazabilirsiniz..."})]}),(0,s.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,s.jsx)(i.P.button,{type:"button",onClick:m,className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",whileHover:{scale:1.02},whileTap:{scale:.98},children:"İptal"}),(0,s.jsx)(i.P.button,{type:"submit",className:"flex-1 px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:shadow-lg transition-all",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Kayıt Et"})]})]})]})]})})}var E=a(1894),K=a(24618);function W(){let e,t,[a,l]=(0,r.useState)("profile"),v=(0,o.useSearchParams)(),P=(0,o.useRouter)(),{user:S,isLoading:W,isAuthenticated:R}=(0,d.A)(),{data:F,isLoading:O}=(0,w.dS)(),{favorites:V,removeFromFavorites:I}=(0,c.r)(),[G,q]=(0,r.useState)("weekly"),{data:Y=[],isLoading:Z,error:Q,refetch:U}=(0,D._c)(),_=(0,N.jE)(),[J,$]=(0,r.useState)(!1),[X,ee]=(0,r.useState)(null),[et,ea]=(0,r.useState)(null),[es,er]=(0,r.useState)(!1),{openEditPersonalInfoModal:ei,openReferenceRegistrationModal:el,openAddAddressModal:en,openBankingModal:eo,openAddCardModal:ed,openSetDefaultCardModal:ec}=(0,k.QR)(),[em,eu]=(0,r.useState)(!1),[ex,ep]=(0,r.useState)(null),[eh,eg]=(0,r.useState)(m.jK.cards),[ey,eb]=(0,r.useState)(null);if((0,r.useEffect)(()=>{let e=v.get("tab");e&&["profile","orders","addresses","favorites","balance","banking","settings"].includes(e)&&l(e)},[v]),(0,r.useEffect)(()=>{W||R||P.push("/login")},[W,R,P]),(0,r.useEffect)(()=>{"addresses"===a&&(null==S?void 0:S.id)&&U()},[a,null==S?void 0:S.id,U]),W||O)return(0,s.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,s.jsx)("div",{className:"bg-white rounded-2xl shadow-lg overflow-hidden p-8",children:(0,s.jsx)("div",{className:"animate-pulse",children:(0,s.jsxs)("div",{className:"flex items-center mb-8 pb-6 border-b border-gray-200",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-gray-300 rounded-full mr-6"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-300 rounded mb-2 w-48"}),(0,s.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-2 w-64"}),(0,s.jsx)("div",{className:"h-4 bg-gray-300 rounded w-32"})]})]})})})})});if(!R||!S)return null;let ef=e=>{ec({card:e,onConfirm:()=>{eg(eh.map(t=>({...t,isDefault:t.id===e.id})))}})},ev=async(e,t)=>{try{await navigator.clipboard.writeText(e),eb(t),setTimeout(()=>eb(null),2e3)}catch(e){console.error("Kopyalama başarısız:",e)}},ej="https://sayglobal.com/referans/".concat((null==S?void 0:S.id)||"user123"),eN="https://sayglobal.com/satis/".concat((null==S?void 0:S.id)||"user123"),ew="SG".concat(((null==S?void 0:S.id)||"user123").toString().toUpperCase(),"REF"),ek=async e=>{try{ee(null),ea(null);let t=E.d.validateImageFile(e,10);if(!t.isValid)return void ee(t.error);$(!0);let a=await E.d.convertProfilePictureToWebP(e);await j.y.updateProfilePicture(a.file),_.invalidateQueries({queryKey:w.ZF.user()}),_.invalidateQueries({queryKey:w.ZF.profileInfo()}),await Promise.all([_.refetchQueries({queryKey:w.ZF.user()}),_.refetchQueries({queryKey:w.ZF.profileInfo()})]),ea("Profil fotoğrafınız başarıyla g\xfcncellendi!"),setTimeout(()=>{ea(null)},3e3)}catch(e){ee(e instanceof Error&&"response"in e&&e.response&&"object"==typeof e.response&&"data"in e.response&&e.response.data&&"object"==typeof e.response.data&&"message"in e.response.data?String(e.response.data.message):"Profil fotoğrafı g\xfcncellenirken bir hata oluştu"),setTimeout(()=>{ee(null)},5e3)}finally{$(!1)}};return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,s.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,s.jsx)("div",{className:"bg-white rounded-2xl shadow-lg overflow-hidden",children:(0,s.jsxs)("div",{className:"p-6 md:p-8",children:[X&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"h-5 w-5 text-red-500 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("span",{className:"text-red-700",children:X})]})}),et&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"h-5 w-5 text-green-500 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("span",{className:"text-green-700",children:et})]})}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center mb-8 pb-6 border-b border-gray-200",children:[(0,s.jsxs)("div",{className:"relative mb-4 md:mb-0 md:mr-6",children:[(0,s.jsxs)("div",{className:"w-24 h-24 rounded-full bg-gradient-to-br from-purple-600 to-indigo-600 flex items-center justify-center overflow-hidden",children:[(null==F?void 0:F.profilePictureUrl)?(0,s.jsx)("img",{src:F.profilePictureUrl,alt:"Profile",className:"w-full h-full object-cover",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling.style.display="flex"}}):null,(0,s.jsx)("span",{className:"text-white text-xl font-bold ".concat((null==F?void 0:F.profilePictureUrl)?"hidden":"flex"," items-center justify-center w-full h-full"),children:(e=S.firstName,t=S.lastName,"".concat(e.charAt(0).toUpperCase()).concat(t.charAt(0).toUpperCase()))})]}),(0,s.jsx)(i.P.button,{className:"absolute bottom-0 right-0 rounded-full p-1.5 shadow-md transition-all ".concat(J?"bg-purple-600 cursor-not-allowed":"bg-white border border-gray-200 hover:bg-purple-50"),whileHover:J?{}:{scale:1.1},whileTap:J?{}:{scale:.9},onClick:()=>{er(!es)},disabled:J,children:J?(0,s.jsxs)("svg",{className:"h-4 w-4 text-white animate-spin",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-gray-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})})}),(0,s.jsx)(K.A,{isOpen:es,onClose:()=>{er(!1)},onFileSelect:ek,hasProfilePicture:!!(null==F?void 0:F.profilePictureUrl),onDeleteSuccess:()=>{ea("Profil fotoğrafınız başarıyla kaldırıldı!"),setTimeout(()=>{ea(null)},3e3)},onDeleteError:e=>{ee(e),setTimeout(()=>{ee(null)},5e3)},isDeleting:J,mode:"immediate"})]}),(0,s.jsxs)("div",{className:"text-center md:text-left",children:[(0,s.jsxs)("h1",{className:"text-2xl md:text-3xl font-bold text-gray-800 mb-1",children:[S.firstName," ",S.lastName]}),(0,s.jsx)("p",{className:"text-gray-700 mb-2",children:S.email}),(0,s.jsxs)("div",{className:"flex items-center justify-center md:justify-start space-x-4",children:[(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["\xdcyelik başlangıcı: ",(e=>{let t=new Date(e);return"".concat(["Ocak","Şubat","Mart","Nisan","Mayıs","Haziran","Temmuz","Ağustos","Eyl\xfcl","Ekim","Kasım","Aralık"][t.getMonth()]," ").concat(t.getFullYear())})(S.joinDate)]}),(0,s.jsx)("span",{className:"inline-block px-2 py-1 rounded-full text-xs font-medium ".concat("admin"===S.role?"bg-red-100 text-red-800":"dealership"===S.role?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"),children:"admin"===S.role?"Y\xf6netici":"dealership"===S.role?"Satıcı":"M\xfcşteri"})]})]}),(0,s.jsx)("div",{className:"flex items-center md:ml-auto mt-4 md:mt-0",children:(0,s.jsx)(n(),{href:"/account/edit",children:(0,s.jsxs)(i.P.button,{className:"bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:shadow-lg transition duration-300 flex items-center space-x-1",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})}),(0,s.jsx)("span",{children:"Profili D\xfczenle"})]})})})]}),(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)("div",{className:"flex flex-col sm:flex-row items-center overflow-x-auto pb-2",children:[{id:"profile",label:"Profil Bilgileri"},{id:"orders",label:"Sipariş Ge\xe7mişi"},{id:"addresses",label:"Adreslerim"},{id:"favorites",label:"Favorilerim"},{id:"balance",label:"Bakiye & Puanlar"},{id:"banking",label:"Banka Bilgileri"},{id:"settings",label:"Hesap Ayarları"}].map(e=>(0,s.jsx)(i.P.button,{className:"px-5 py-2.5 rounded-lg font-medium text-sm whitespace-nowrap mb-2 sm:mb-0 sm:mr-2 ".concat(a===e.id?"bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-md":"text-gray-600 hover:bg-gray-100"),onClick:()=>{l(e.id),window.history.pushState({},"","/account?tab=".concat(e.id))},whileHover:a!==e.id?{scale:1.05}:{},whileTap:a!==e.id?{scale:.95}:{},children:e.label},e.id))})}),(0,s.jsxs)("div",{className:"min-h-[400px]",children:["profile"===a&&(0,s.jsx)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Kişisel Bilgiler"}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-x-8",children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-black mb-1",children:"Ad Soyad"}),(0,s.jsxs)("p",{className:"font-medium text-gray-600",children:[(null==F?void 0:F.firstName)||S.firstName," ",(null==F?void 0:F.lastName)||S.lastName]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-black mb-1",children:"E-posta"}),(0,s.jsx)("p",{className:"font-medium text-gray-600",children:(null==F?void 0:F.email)||S.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-black mb-1",children:"Telefon"}),(0,s.jsx)("p",{className:"font-medium text-gray-600",children:S.phoneNumber||"Telefon bilgisi eklenmemiş"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-black mb-1",children:"Hesap T\xfcr\xfc"}),(0,s.jsx)("span",{className:"inline-block px-2 py-1 rounded-full text-xs font-medium ".concat("admin"===S.role?"bg-red-100 text-red-800":"dealership"===S.role?"bg-green-100 text-green-800":S.membershipLevel?"bg-purple-100 text-purple-800":"bg-blue-100 text-blue-800"),children:"admin"===S.role?"Y\xf6netici":"dealership"===S.role?"Satıcı":S.membershipLevel>0?"Distrib\xfct\xf6r":"M\xfcşteri"})]})]}),(0,s.jsxs)("div",{className:"space-y-6 mt-6 md:mt-0",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-black mb-1",children:"Doğum Tarihi"}),(0,s.jsx)("p",{className:"font-medium text-gray-600",children:(null==F?void 0:F.dateOfBirth)?new Date(F.dateOfBirth).toLocaleDateString("tr-TR"):"Belirtilmemiş"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-black mb-1",children:"Cinsiyet"}),(0,s.jsx)("p",{className:"font-medium text-gray-600",children:(null==F?void 0:F.gender)!==void 0&&null!==F.gender&&F.gender>0?1===F.gender?"Erkek":2===F.gender?"Kadın":3===F.gender?"Diğer":"Belirtilmemiş":"Belirtilmemiş"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-black mb-1",children:"Konum"}),(0,s.jsx)("p",{className:"font-medium text-gray-600",children:(null==F?void 0:F.location)||"Belirtilmemiş"})]})]})]}),(0,s.jsx)("div",{className:"mt-6 border-t border-gray-200 pt-5",children:(0,s.jsxs)(i.P.button,{className:"text-purple-600 text-sm font-medium flex items-center",onClick:()=>ei({firstName:(null==F?void 0:F.firstName)||(null==S?void 0:S.firstName)||"",lastName:(null==F?void 0:F.lastName)||(null==S?void 0:S.lastName)||"",phoneNumber:(null==S?void 0:S.phoneNumber)||""}),whileHover:{x:2},children:["Kişisel bilgilerimi g\xfcncelle",(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Linkler"}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-5 space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-black mb-2",children:"Referans Linki"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"text",value:ej,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-600 text-sm"}),(0,s.jsx)(i.P.button,{onClick:()=>ev(ej,"reference"),className:"px-3 py-2 rounded-lg text-sm font-medium transition-colors ".concat("reference"===ey?"bg-green-500 text-white":"bg-purple-600 text-white hover:bg-purple-700"),whileHover:{scale:1.02},whileTap:{scale:.98},children:"reference"===ey?"Kopyalandı!":"Kopyala"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-black mb-2",children:"M\xfcşteri Satış Linki"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"text",value:eN,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-600 text-sm"}),(0,s.jsx)(i.P.button,{onClick:()=>ev(eN,"sales"),className:"px-3 py-2 rounded-lg text-sm font-medium transition-colors ".concat("sales"===ey?"bg-green-500 text-white":"bg-purple-600 text-white hover:bg-purple-700"),whileHover:{scale:1.02},whileTap:{scale:.98},children:"sales"===ey?"Kopyalandı!":"Kopyala"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-black mb-2",children:"Referans Kodum"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"text",value:ew,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-600 text-sm font-mono font-semibold tracking-wider"}),(0,s.jsx)(i.P.button,{onClick:()=>ev(ew,"code"),className:"px-3 py-2 rounded-lg text-sm font-medium transition-colors ".concat("code"===ey?"bg-green-500 text-white":"bg-purple-600 text-white hover:bg-purple-700"),whileHover:{scale:1.02},whileTap:{scale:.98},children:"code"===ey?"Kopyalandı!":"Kopyala"})]})]}),(0,s.jsx)("div",{className:"pt-2",children:(0,s.jsxs)(i.P.button,{className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-4 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 flex items-center justify-center space-x-2",onClick:()=>el(),whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})}),(0,s.jsx)("span",{children:"Referans Kayıt Et"})]})})]})]}),(0,s.jsxs)("div",{className:"md:col-span-2 mt-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Satıcı Başvuru Durumu"}),(0,s.jsx)("div",{className:"bg-gray-50 rounded-lg p-5",children:(()=>{let e=u.vM.find(e=>e.userId===S.id);return"dealership"===S.role?(0,s.jsxs)("div",{className:"text-center py-6",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-green-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-green-800 mb-2",children:"Satıcı Onaylandı"}),(0,s.jsx)("p",{className:"text-green-600",children:"Tebrikler! Artık Say Global'de satıcı olarak \xfcr\xfcn satabilirsiniz."}),(0,s.jsx)(n(),{href:"/panel",className:"inline-block mt-4 bg-green-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-green-700 transition duration-300",children:"Satıcı Paneline Git"})]}):e?(0,s.jsxs)("div",{className:"text-center py-6",children:["pending"===e.status&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-yellow-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-yellow-800 mb-2",children:"Başvuru İnceleniyor"}),(0,s.jsx)("p",{className:"text-yellow-600 mb-2",children:"Satıcı başvurunuz admin ekibimiz tarafından inceleniyor."}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Başvuru Tarihi: ",new Date(e.submittedAt).toLocaleDateString("tr-TR")]})]}),"rejected"===e.status&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:"Başvuru Reddedildi"}),(0,s.jsx)("p",{className:"text-red-600 mb-2",children:"Maalesef satıcı başvurunuz reddedildi."}),e.adminNotes&&(0,s.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Admin Notu: ",e.adminNotes]}),(0,s.jsx)(n(),{href:"/become-dealer",className:"inline-block bg-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-purple-700 transition duration-300",children:"Yeni Başvuru Yap"})]})]}):(0,s.jsxs)("div",{className:"text-center py-6",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-purple-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Satıcı Başvurusu Yap"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Say Global'de satıcı olarak \xfcr\xfcn satmaya başlayın."}),(0,s.jsx)(n(),{href:"/become-dealer",className:"inline-block bg-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-purple-700 transition duration-300",children:"Satıcı Başvurusu Yap"})]})})()})]})]})}),"orders"===a&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Sipariş Ge\xe7mişiniz"}),m.Ys.length>0?(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Sipariş No"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tarih"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Durum"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Toplam"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"\xdcr\xfcnler"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"İşlemler"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.Ys.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.id}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.date}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2.5 py-1 rounded-full text-xs font-medium ".concat(e.statusClass),children:e.status})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium",children:e.total}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.items," \xfcr\xfcn"]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,s.jsx)(n(),{href:"/order-details/".concat(e.id),children:(0,s.jsx)(i.P.span,{className:"text-purple-600 hover:text-purple-800 font-medium text-sm cursor-pointer",whileHover:{x:2},children:"Detaylar"})})})]},e.id))})]})}):(0,s.jsxs)("div",{className:"text-center py-10 bg-gray-50 rounded-lg",children:[(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 mx-auto text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Hen\xfcz siparişiniz bulunmuyor"}),(0,s.jsx)("p",{className:"text-gray-500 mb-6",children:"\xdcr\xfcnlerimizi keşfedin ve ilk siparişinizi oluşturun."}),(0,s.jsx)(n(),{href:"/products",className:"bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-2.5 rounded-lg font-medium hover:shadow-lg transition duration-300",children:"Alışverişe Başla"})]})]}),"addresses"===a&&(0,s.jsx)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},className:"space-y-8",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"Kayıtlı Adresleriniz"}),(0,s.jsxs)(i.P.button,{className:"bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:shadow-lg transition duration-300 flex items-center space-x-1",onClick:()=>en(),whileHover:{scale:1.05},whileTap:{scale:.95},disabled:Z,children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),(0,s.jsx)("span",{children:"Yeni Adres Ekle"})]})]}),Q&&(0,s.jsx)("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg",children:Q.message||"Adresler y\xfcklenirken bir hata oluştu"}),Z?(0,s.jsx)("div",{className:"grid gap-4 md:grid-cols-2",children:[void 0,void 0].map((e,t)=>(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 animate-pulse",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"h-6 bg-gray-300 rounded w-24"}),(0,s.jsx)("div",{className:"h-5 bg-gray-200 rounded w-16"})]}),(0,s.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded-lg"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"w-4 h-4 bg-gray-200 rounded mt-0.5 mr-2"}),(0,s.jsx)("div",{className:"h-4 bg-gray-300 rounded flex-1"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-4 h-4 bg-gray-200 rounded mr-2"}),(0,s.jsx)("div",{className:"h-4 bg-gray-300 rounded w-32"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-4 h-4 bg-gray-200 rounded mr-2"}),(0,s.jsx)("div",{className:"h-4 bg-gray-300 rounded w-24"})]})]})]},t))}):Y&&Y.length>=0?(0,s.jsx)(M,{addresses:Y,onAddressDeleted:()=>{}}):null]})}),"favorites"===a&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Favori \xdcr\xfcnleriniz"}),0===V.length?(0,s.jsxs)("div",{className:"text-center py-10 bg-gray-50 rounded-lg",children:[(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 mx-auto text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Favori listeniz boş"}),(0,s.jsx)("p",{className:"text-gray-500 mb-6",children:"Beğendiğiniz \xfcr\xfcnleri favorilerinize ekleyerek daha sonra kolayca bulabilirsiniz."}),(0,s.jsx)(n(),{href:"/products",className:"bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-2.5 rounded-lg font-medium hover:shadow-lg transition duration-300",children:"\xdcr\xfcnlere G\xf6z At"})]}):(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:V.map(e=>(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},whileHover:{y:-5},className:"bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300",children:[(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)(n(),{href:"/product/".concat(e.product.id),children:(0,s.jsxs)("div",{className:"relative h-48 overflow-hidden",children:[(0,s.jsx)("img",{src:e.product.thumbnail,alt:e.product.title,className:"w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"}),e.product.discountPercentage&&e.product.discountPercentage>0&&(0,s.jsxs)("div",{className:"absolute top-3 right-3 bg-red-500 text-white px-2 py-1 rounded-full text-sm font-medium",children:["%",e.product.discountPercentage," İndirim"]})]})}),(0,s.jsx)(i.P.button,{onClick:()=>{I(e.product.id),ep(e.product),eu(!0)},className:"absolute top-3 left-3 p-2 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:bg-white transition-all duration-300",whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,s.jsx)("svg",{className:"h-5 w-5 text-red-500 fill-current",fill:"currentColor",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})})]}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)(n(),{href:"/product/".concat(e.product.id),children:(0,s.jsx)("h3",{className:"text-lg font-semibold mb-1 text-gray-800 hover:text-purple-600 transition-colors",children:e.product.title})}),(0,s.jsx)("p",{className:"text-gray-600 mb-2 text-sm",children:e.product.brand}),(0,s.jsx)("p",{className:"text-gray-500 text-sm mb-3 line-clamp-2",children:e.product.description}),(0,s.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("span",{className:"text-lg font-bold text-purple-700",children:[e.product.discountPercentage?(e.product.price*(1-e.product.discountPercentage/100)).toFixed(2):e.product.price.toFixed(2)," ₺"]}),e.product.discountPercentage&&e.product.discountPercentage>0&&(0,s.jsxs)("span",{className:"text-sm text-gray-500 line-through ml-2",children:[e.product.price.toFixed(2)," ₺"]})]}),(0,s.jsxs)("div",{className:"flex items-center bg-yellow-50 px-2 py-1 rounded",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),(0,s.jsx)("span",{className:"ml-1 text-xs font-medium text-yellow-700",children:e.product.rating})]})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mb-3",children:["Favorilere eklendi: ",new Date(e.addedAt).toLocaleDateString("tr-TR")]}),(0,s.jsx)(n(),{href:"/product/".concat(e.product.id),children:(0,s.jsx)(i.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-2 px-4 rounded-lg font-medium hover:from-purple-700 hover:to-indigo-700 transition-all duration-300",children:"\xdcr\xfcn\xfc İncele"})})]})]},e.id))})]}),"balance"===a&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Bakiye & Puanlar"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-100 rounded-xl p-6 border border-green-200",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"bg-green-500 p-2 rounded-full mr-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Hesap Bakiyesi"})]})}),(0,s.jsxs)("div",{className:"text-3xl font-bold text-green-600 mb-2",children:["₺",m.i6.accountBalance.toFixed(2)]}),(0,s.jsx)("p",{className:"text-green-700 text-sm",children:"Kullanılabilir bakiye"})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-indigo-100 rounded-xl p-6 border border-purple-200",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"bg-purple-500 p-2 rounded-full mr-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Toplam Puanlar"})]})}),(0,s.jsx)("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:m.i6.totalPoints.toLocaleString()}),(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsxs)("span",{className:"text-purple-700",children:["Kullanılabilir: ",m.i6.availablePoints.toLocaleString()]}),(0,s.jsxs)("span",{className:"text-purple-500",children:["Kullanılan: ",m.i6.usedPoints.toLocaleString()]})]})]})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-6",children:"Puan Kazanım Analizi"}),(0,s.jsx)("div",{className:"flex mb-6",children:(0,s.jsxs)("div",{className:"bg-gray-100 rounded-lg p-1 flex",children:[(0,s.jsx)("button",{className:"px-4 py-2 rounded-md font-medium text-sm transition-all ".concat("weekly"===G?"bg-white text-purple-600 shadow-sm":"text-gray-600 hover:text-gray-800"),onClick:()=>q("weekly"),children:"Haftalık"}),(0,s.jsx)("button",{className:"px-4 py-2 rounded-md font-medium text-sm transition-all ".concat("monthly"===G?"bg-white text-purple-600 shadow-sm":"text-gray-600 hover:text-gray-800"),onClick:()=>q("monthly"),children:"Aylık"})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,s.jsx)("div",{className:"h-80",children:(0,s.jsx)(x.u,{width:"100%",height:"100%",children:(0,s.jsxs)(p.Q,{data:"weekly"===G?m.i6.weeklyEarnings:m.i6.monthlyEarnings,margin:{top:10,right:30,left:0,bottom:0},children:[(0,s.jsx)("defs",{children:(0,s.jsxs)("linearGradient",{id:"purpleGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,s.jsx)("stop",{offset:"5%",stopColor:"#8b5cf6",stopOpacity:.8}),(0,s.jsx)("stop",{offset:"95%",stopColor:"#8b5cf6",stopOpacity:.1})]})}),(0,s.jsx)(h.d,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),(0,s.jsx)(g.W,{dataKey:"weekly"===G?"week":"month",axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#6b7280"}}),(0,s.jsx)(y.h,{axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#6b7280"}}),(0,s.jsx)(b.m,{contentStyle:{backgroundColor:"white",border:"1px solid #e5e7eb",borderRadius:"8px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"},labelStyle:{color:"#374151",fontWeight:"medium"},formatter:e=>["".concat(e," puan"),"Kazanılan Puan"]}),(0,s.jsx)(f.G,{type:"monotone",dataKey:"points",stroke:"#8b5cf6",fillOpacity:1,fill:"url(#purpleGradient)",strokeWidth:2})]})})}),(0,s.jsxs)("div",{className:"flex justify-between items-center mt-4 pt-4 border-t border-gray-100",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:["weekly"===G?"Son 8 hafta":"Son 6 ay"," i\xe7erisinde kazandığınız puanlar"]}),(0,s.jsxs)("div",{className:"text-sm font-medium text-purple-600",children:["Toplam: ",("weekly"===G?m.i6.weeklyEarnings.reduce((e,t)=>e+t.points,0):m.i6.monthlyEarnings.reduce((e,t)=>e+t.points,0)).toLocaleString()," puan"]})]})]})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Puan Ge\xe7mişi"}),(0,s.jsx)("div",{className:"bg-white rounded-lg border border-gray-200 overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tarih"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"A\xe7ıklama"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Sipariş No"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Puan"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.i6.pointHistory.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:new Date(e.date).toLocaleDateString("tr-TR")}),(0,s.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:e.description}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.orderNo}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right",children:(0,s.jsxs)("span",{className:"font-medium ".concat("earned"===e.type?"text-green-600":"text-red-600"),children:["earned"===e.type?"+":"",e.points," puan"]})})]},e.id))})]})})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Bakiye Ge\xe7mişi"}),(0,s.jsx)("div",{className:"bg-white rounded-lg border border-gray-200 overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tarih"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"A\xe7ıklama"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"T\xfcr"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tutar"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.i6.balanceHistory.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:new Date(e.date).toLocaleDateString("tr-TR")}),(0,s.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:e.description}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat("refund"===e.type?"bg-green-100 text-green-800":"gift_card"===e.type?"bg-purple-100 text-purple-800":"bg-red-100 text-red-800"),children:"refund"===e.type?"İade":"gift_card"===e.type?"Hediye Kartı":"\xc7ekim"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right",children:(0,s.jsxs)("span",{className:"font-medium ".concat(e.amount>0?"text-green-600":"text-red-600"),children:[e.amount>0?"+":"","₺",Math.abs(e.amount).toFixed(2)]})})]},e.id))})]})})})]})]}),"banking"===a&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Banka Bilgileri"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"IBAN Bilgileri"}),(0,s.jsxs)(i.P.button,{className:"text-purple-600 text-sm font-medium flex items-center",onClick:()=>eo({accountHolderName:m.jK.iban.accountHolderName,bankName:m.jK.iban.bankName,iban:m.jK.iban.iban,branchName:m.jK.iban.branchName,branchCode:m.jK.iban.branchCode}),whileHover:{x:2},children:["D\xfczenle",(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-5 space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-black mb-1",children:"Hesap Sahibi"}),(0,s.jsx)("p",{className:"font-medium text-gray-600",children:m.jK.iban.accountHolderName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-black mb-1",children:"Banka"}),(0,s.jsx)("p",{className:"font-medium text-gray-600",children:m.jK.iban.bankDisplayName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-black mb-1",children:"IBAN"}),(0,s.jsx)("p",{className:"font-medium text-gray-600",children:m.jK.iban.iban})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-black mb-1",children:"Şube"}),(0,s.jsxs)("p",{className:"font-medium text-gray-600",children:[m.jK.iban.branchName," (",m.jK.iban.branchCode,")"]})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Kayıtlı Kartlar"}),(0,s.jsxs)(i.P.button,{className:"bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:shadow-lg transition duration-300 flex items-center space-x-1",onClick:()=>ed(),whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),(0,s.jsx)("span",{children:"Yeni Kart"})]})]}),(0,s.jsx)("div",{className:"space-y-3",children:eh.map(e=>(0,s.jsxs)(i.P.div,{className:"bg-gradient-to-r ".concat((e=>{switch(e){case"blue":return"from-blue-600 to-blue-700";case"green":return"from-green-600 to-green-700";case"purple":return"from-purple-600 to-purple-700";case"orange":return"from-orange-600 to-orange-700";default:return"from-gray-600 to-gray-700"}})(e.color)," rounded-lg p-4 text-white relative cursor-pointer hover:shadow-lg transition-all duration-300"),whileHover:{scale:1.02},onClick:()=>!e.isDefault&&ef(e),children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"text-sm opacity-90",children:(e=>{switch(e){case"visa":return"Visa";case"mastercard":return"MasterCard";case"amex":return"American Express";case"troy":return"Troy";default:return e}})(e.cardType)}),e.isDefault&&(0,s.jsx)("span",{className:"bg-white/20 text-white text-xs px-2 py-1 rounded-full font-medium",children:"Varsayılan"})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[e.isDefault&&(0,s.jsx)(i.P.div,{className:"mt-1",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-yellow-400",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})})}),(0,s.jsx)(i.P.button,{className:"text-white hover:text-red-200 transition-colors p-1",onClick:e=>{e.stopPropagation()},whileHover:{scale:1.1},children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]}),(0,s.jsx)("div",{className:"text-lg font-mono mb-4 tracking-wider",children:e.cardNumber}),(0,s.jsxs)("div",{className:"flex justify-between items-end text-sm opacity-90",children:[(0,s.jsx)("span",{children:e.cardHolderName}),(0,s.jsxs)("div",{className:"flex flex-col items-end",children:[(0,s.jsx)("span",{children:e.expirationDate}),!e.isDefault&&(0,s.jsx)("span",{className:"text-xs bg-white/10 px-2 py-1 rounded-full mt-1 opacity-100 text-white",children:"Varsayılan yap"})]})]})]},e.id))})]})]}),(0,s.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"bg-green-50 rounded-lg p-4 border border-green-200",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"bg-green-500 p-2 rounded-full mr-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:["₺",m.jK.statistics.monthlyEarnings.toLocaleString()]}),(0,s.jsx)("div",{className:"text-green-700 text-sm",children:"Bu Ay Kazan\xe7"})]})]})}),(0,s.jsx)("div",{className:"bg-blue-50 rounded-lg p-4 border border-blue-200",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"bg-blue-500 p-2 rounded-full mr-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:m.jK.statistics.totalTransactions}),(0,s.jsx)("div",{className:"text-blue-700 text-sm",children:"Toplam İşlem"})]})]})}),(0,s.jsx)("div",{className:"bg-purple-50 rounded-lg p-4 border border-purple-200",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"bg-purple-500 p-2 rounded-full mr-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:["₺",m.jK.statistics.averageTransaction]}),(0,s.jsx)("div",{className:"text-purple-700 text-sm",children:"Ortalama İşlem"})]})]})})]})]}),"settings"===a&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Hesap Ayarları"}),(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"border-b border-gray-200 pb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Şifre Değiştir"}),(0,s.jsxs)("form",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"current-password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Mevcut Şifre"}),(0,s.jsx)("input",{type:"password",id:"current-password",className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",placeholder:"Mevcut şifrenizi girin"})]}),(0,s.jsxs)("div",{className:"md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"new-password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Yeni Şifre"}),(0,s.jsx)("input",{type:"password",id:"new-password",className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",placeholder:"Yeni şifrenizi girin"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirm-password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Şifre Tekrar"}),(0,s.jsx)("input",{type:"password",id:"confirm-password",className:"w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black",placeholder:"Şifrenizi tekrar girin"})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)(i.P.button,{type:"submit",className:"bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-2.5 rounded-lg font-medium hover:shadow-lg transition duration-300",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Şifreyi G\xfcncelle"})})]})]}),(0,s.jsxs)("div",{className:"border-b border-gray-200 pb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"E-posta Abonelikleri"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex items-center h-5",children:(0,s.jsx)("input",{id:"newsletter",type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"})}),(0,s.jsxs)("div",{className:"ml-3 text-sm",children:[(0,s.jsx)("label",{htmlFor:"newsletter",className:"font-medium text-gray-700",children:"Haberler ve Promosyonlar"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Yeni \xfcr\xfcnler, \xf6zel teklifler ve indirimlerden haberdar olun"})]})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex items-center h-5",children:(0,s.jsx)("input",{id:"product-updates",type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"})}),(0,s.jsxs)("div",{className:"ml-3 text-sm",children:[(0,s.jsx)("label",{htmlFor:"product-updates",className:"font-medium text-gray-700",children:"\xdcr\xfcn G\xfcncellemeleri"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Favorilediğiniz veya satın aldığınız \xfcr\xfcnlerle ilgili g\xfcncellemeler alın"})]})]}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(i.P.button,{className:"bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-2.5 rounded-lg font-medium hover:shadow-lg transition duration-300",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Tercihleri Kaydet"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Hesap İşlemleri"}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)(i.P.button,{className:"text-red-600 hover:text-red-800 font-medium flex items-center",whileHover:{x:2},children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1.5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Hesabımı Sil"]})})]})]})]})]})]})})}),(0,s.jsx)(C,{}),(0,s.jsx)(A,{onAddressAdded:()=>{console.log("✅ Address added successfully")}}),(0,s.jsx)(z.A,{isOpen:em,onClose:()=>eu(!1),product:ex,isAdded:!1}),(0,s.jsx)(L,{}),(0,s.jsx)(B,{}),(0,s.jsx)(T,{}),(0,s.jsx)(H,{onSubmit:e=>{console.log("Referans kayıt edildi:",e)}})]})}function R(){return(0,s.jsx)(r.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading..."}),children:(0,s.jsx)(W,{})})}},20181:(e,t,a)=>{"use strict";a.d(t,{CM:()=>r,nA:()=>i,vM:()=>s});let s=[{id:1,userId:6,userName:"Mehmet Yılmaz",userEmail:"<EMAIL>",applicationData:{firstName:"Mehmet",lastName:"Yılmaz",email:"<EMAIL>",phone:"+90 ************",mainProductCategory:"Elektronik",subProductCategories:["Telefon","Bilgisayar","Aksesuar"],estimatedProductCount:"50-100 adet",sampleProductListUrl:"https://example.com/products",companyName:"Yılmaz Elektronik Ltd. Şti.",taxNumber:"1234567890",taxOffice:"İstanbul Vergi Dairesi",companyAddress:"Atat\xfcrk Cad. No:123 Kadık\xf6y/İstanbul",authorizedPersonName:"Mehmet Yılmaz",authorizedPersonTcId:"12345678901",alternativeContactNumber:"+90 ************",userAgreementAccepted:!0,dealershipAgreementAccepted:!0,privacyPolicyAccepted:!0},status:"pending",submittedAt:"2024-01-15T10:30:00Z"},{id:2,userId:7,userName:"Ayşe Kaya",userEmail:"<EMAIL>",applicationData:{firstName:"Ayşe",lastName:"Kaya",email:"<EMAIL>",phone:"+90 ************",mainProductCategory:"Ev & Yaşam",subProductCategories:["Mutfak","Dekorasyon"],estimatedProductCount:"20-50 adet",companyName:"Kaya Ev Tekstili",taxNumber:"0987654321",taxOffice:"Ankara Vergi Dairesi",companyAddress:"Kızılay Cad. No:456 \xc7ankaya/Ankara",authorizedPersonName:"Ayşe Kaya",authorizedPersonTcId:"10987654321",alternativeContactNumber:"+90 ************",userAgreementAccepted:!0,dealershipAgreementAccepted:!0,privacyPolicyAccepted:!0},status:"approved",submittedAt:"2024-01-10T14:20:00Z",reviewedAt:"2024-01-12T09:15:00Z",reviewedBy:1,adminNotes:"Başvuru uygun bulunmuştur."}],r=["Elektronik","Ev & Yaşam","Giyim & Aksesuar","Spor & Outdoor","Kozmetik & Kişisel Bakım","Kitap & Kırtasiye","Oyuncak & Hobi","Otomotiv","Anne & Bebek","Diğer"],i=["1-10 adet","11-25 adet","26-50 adet","51-100 adet","101-250 adet","250+ adet"]},24618:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var s=a(95155),r=a(12115),i=a(60760),l=a(76408),n=a(59959),o=a(26715),d=a(66681);let c=e=>{let{isOpen:t,onClose:a,onFileSelect:c,hasProfilePicture:m,onDeleteSuccess:u,onDeleteError:x,isDeleting:p=!1,mode:h="immediate"}=e,g=(0,r.useRef)(null),y=(0,r.useRef)(null),b=(0,o.jE)(),[f,v]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=e=>{y.current&&!y.current.contains(e.target)&&a()};return t&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[t,a]),(0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&a()};return t&&document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}},[t,a]);let j=async()=>{if(m){if("edit"===h){null==u||u(),a();return}v(!0);try{await n.y.deleteProfilePicture(),b.invalidateQueries({queryKey:d.ZF.user()}),b.invalidateQueries({queryKey:d.ZF.profileInfo()}),await Promise.all([b.refetchQueries({queryKey:d.ZF.user()}),b.refetchQueries({queryKey:d.ZF.profileInfo()})]),null==u||u(),a()}catch(a){var e,t;null==x||x((null==(t=a.response)||null==(e=t.data)?void 0:e.message)||"Profil fotoğrafı silinirken bir hata oluştu")}finally{v(!1)}}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.N,{children:t&&(0,s.jsx)(l.P.div,{ref:y,initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},transition:{duration:.1},className:"absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50",children:(0,s.jsxs)("div",{className:"p-3 space-y-2",children:[(0,s.jsxs)(l.P.button,{type:"button",onClick:()=>{var e;null==(e=g.current)||e.click(),a()},className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-2 px-3 rounded-lg text-sm font-medium flex items-center justify-center hover:shadow-lg transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,s.jsxs)("svg",{className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 13a3 3 0 11-6 0 3 3 0 016 0z"})]}),"Fotoğraf Y\xfckle"]}),(0,s.jsx)(l.P.button,{type:"button",onClick:j,disabled:!m||f||p,className:"w-full py-2 px-3 rounded-lg text-sm flex items-center justify-center transition-all duration-300 ".concat(!m||f||p?"text-gray-400 cursor-not-allowed bg-gray-50":"text-red-600 hover:bg-red-50 hover:text-red-700"),whileHover:!m||f||p?{}:{scale:1.02},whileTap:!m||f||p?{}:{scale:.98},children:f||p?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"h-4 w-4 mr-2 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Siliniyor..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("svg",{className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Profil Fotoğrafını Kaldır"]})})]})})}),(0,s.jsx)("input",{ref:g,type:"file",accept:"image/*",onChange:e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];a&&c(a),g.current&&(g.current.value="")},className:"hidden"})]})}},45106:(e,t,a)=>{"use strict";a.d(t,{$N:()=>M,EI:()=>S,F0:()=>c,Gc:()=>d,HX:()=>D,JZ:()=>g,OG:()=>k,Ph:()=>p,QK:()=>u,QR:()=>z,S:()=>m,VS:()=>o,Zm:()=>w,_f:()=>P,c6:()=>b,fW:()=>C,gA:()=>y,hg:()=>f,ig:()=>v,lA:()=>x,nb:()=>N,qA:()=>A,u6:()=>h,vQ:()=>j});var s=a(65453),r=a(46786);let i={editPersonalInfo:!1,referenceRegistration:!1,addAddress:!1,setDefaultConfirmation:!1,banking:!1,addCard:!1,setDefaultCard:!1,registerSuccess:!1,successNotification:!1,productCategorySelector:!1,productVariantSetup:!1,productVariant:!1,productDeleteConfirmation:!1},l={editPersonalInfoUser:null,referenceRegistrationData:null,addAddressData:null,setDefaultConfirmationData:null,bankingData:null,addCardData:null,setDefaultCardData:null,successNotificationData:null,productCategorySelectorData:null,productVariantSetupData:null,productVariantData:null,productDeleteConfirmationData:null},n=(0,s.v)()((0,r.lt)(e=>({...i,...l,openModal:(t,a)=>{e(e=>({...e,[t]:!0,...a&&{["".concat(t,"User")]:a}}),!1,"modal/open/".concat(t))},closeModal:t=>{e(e=>({...e,[t]:!1,["".concat(t,"User")]:null}),!1,"modal/close/".concat(t))},closeAllModals:()=>{e({...i,...l},!1,"modal/closeAll")},openEditPersonalInfoModal:t=>{e({editPersonalInfo:!0,editPersonalInfoUser:t},!1,"modal/openEditPersonalInfo")},closeEditPersonalInfoModal:()=>{e({editPersonalInfo:!1,editPersonalInfoUser:null},!1,"modal/closeEditPersonalInfo")},openReferenceRegistrationModal:t=>{e({referenceRegistration:!0,referenceRegistrationData:t},!1,"modal/openReferenceRegistration")},closeReferenceRegistrationModal:()=>{e({referenceRegistration:!1,referenceRegistrationData:null},!1,"modal/closeReferenceRegistration")},openAddAddressModal:t=>{e({addAddress:!0,addAddressData:t},!1,"modal/openAddAddress")},closeAddAddressModal:()=>{e({addAddress:!1,addAddressData:null},!1,"modal/closeAddAddress")},openSetDefaultConfirmationModal:t=>{e({setDefaultConfirmation:!0,setDefaultConfirmationData:t},!1,"modal/openSetDefaultConfirmation")},closeSetDefaultConfirmationModal:()=>{e({setDefaultConfirmation:!1,setDefaultConfirmationData:null},!1,"modal/closeSetDefaultConfirmation")},openBankingModal:t=>{e({banking:!0,bankingData:t},!1,"modal/openBanking")},closeBankingModal:()=>{e({banking:!1,bankingData:null},!1,"modal/closeBanking")},openAddCardModal:t=>{e({addCard:!0,addCardData:t},!1,"modal/openAddCard")},closeAddCardModal:()=>{e({addCard:!1,addCardData:null},!1,"modal/closeAddCard")},openSetDefaultCardModal:t=>{e({setDefaultCard:!0,setDefaultCardData:t},!1,"modal/openSetDefaultCard")},closeSetDefaultCardModal:()=>{e({setDefaultCard:!1,setDefaultCardData:null},!1,"modal/closeSetDefaultCard")},openRegisterSuccessModal:()=>{e({registerSuccess:!0},!1,"modal/openRegisterSuccess")},closeRegisterSuccessModal:()=>{e({registerSuccess:!1},!1,"modal/closeRegisterSuccess")},openSuccessNotificationModal:t=>{e({successNotification:!0,successNotificationData:t},!1,"modal/openSuccessNotification")},closeSuccessNotificationModal:()=>{e({successNotification:!1,successNotificationData:null},!1,"modal/closeSuccessNotification")},openProductCategorySelector:t=>{e({productCategorySelector:!0,productCategorySelectorData:t},!1,"modal/openProductCategorySelector")},closeProductCategorySelector:()=>{e({productCategorySelector:!1,productCategorySelectorData:null},!1,"modal/closeProductCategorySelector")},openProductVariantSetup:t=>{e({productVariantSetup:!0,productVariantSetupData:t},!1,"modal/openProductVariantSetup")},closeProductVariantSetup:()=>{e({productVariantSetup:!1,productVariantSetupData:null},!1,"modal/closeProductVariantSetup")},openProductVariant:t=>{e({productVariant:!0,productVariantData:t},!1,"modal/openProductVariant")},closeProductVariant:()=>{e({productVariant:!1,productVariantData:null},!1,"modal/closeProductVariant")},openProductDeleteConfirmation:t=>{e({productDeleteConfirmation:!0,productDeleteConfirmationData:t},!1,"modal/openProductDeleteConfirmation")},closeProductDeleteConfirmation:()=>{e({productDeleteConfirmation:!1,productDeleteConfirmationData:null},!1,"modal/closeProductDeleteConfirmation")}}),{name:"modal-store",enabled:!1})),o=()=>n(e=>e.editPersonalInfo),d=()=>n(e=>e.editPersonalInfoUser),c=()=>n(e=>e.referenceRegistration),m=()=>n(e=>e.referenceRegistrationData),u=()=>n(e=>e.addAddress),x=()=>n(e=>e.addAddressData),p=()=>n(e=>e.setDefaultConfirmation),h=()=>n(e=>e.setDefaultConfirmationData),g=()=>n(e=>e.banking),y=()=>n(e=>e.bankingData),b=()=>n(e=>e.addCard),f=()=>n(e=>e.addCardData),v=()=>n(e=>e.setDefaultCard),j=()=>n(e=>e.setDefaultCardData),N=()=>n(e=>e.registerSuccess),w=()=>n(e=>e.successNotification),k=()=>n(e=>e.successNotificationData),C=()=>n(e=>e.productCategorySelector),P=()=>n(e=>e.productCategorySelectorData),D=()=>n(e=>e.productVariant),A=()=>n(e=>e.productVariantData),S=()=>n(e=>e.productDeleteConfirmation),M=()=>n(e=>e.productDeleteConfirmationData),z=()=>{let e=n(e=>e.openModal),t=n(e=>e.closeModal),a=n(e=>e.closeAllModals),s=n(e=>e.openEditPersonalInfoModal),r=n(e=>e.closeEditPersonalInfoModal),i=n(e=>e.openReferenceRegistrationModal),l=n(e=>e.closeReferenceRegistrationModal),o=n(e=>e.openAddAddressModal),d=n(e=>e.closeAddAddressModal),c=n(e=>e.openSetDefaultConfirmationModal),m=n(e=>e.closeSetDefaultConfirmationModal),u=n(e=>e.openBankingModal),x=n(e=>e.closeBankingModal),p=n(e=>e.openAddCardModal),h=n(e=>e.closeAddCardModal),g=n(e=>e.openSetDefaultCardModal),y=n(e=>e.closeSetDefaultCardModal),b=n(e=>e.openRegisterSuccessModal),f=n(e=>e.closeRegisterSuccessModal),v=n(e=>e.openSuccessNotificationModal),j=n(e=>e.closeSuccessNotificationModal),N=n(e=>e.openProductCategorySelector),w=n(e=>e.closeProductCategorySelector),k=n(e=>e.openProductVariantSetup),C=n(e=>e.closeProductVariantSetup),P=n(e=>e.openProductVariant),D=n(e=>e.closeProductVariant);return{openModal:e,closeModal:t,closeAllModals:a,openEditPersonalInfoModal:s,closeEditPersonalInfoModal:r,openReferenceRegistrationModal:i,closeReferenceRegistrationModal:l,openAddAddressModal:o,closeAddAddressModal:d,openSetDefaultConfirmationModal:c,closeSetDefaultConfirmationModal:m,openBankingModal:u,closeBankingModal:x,openAddCardModal:p,closeAddCardModal:h,openSetDefaultCardModal:g,closeSetDefaultCardModal:y,openRegisterSuccessModal:b,closeRegisterSuccessModal:f,openSuccessNotificationModal:v,closeSuccessNotificationModal:j,openProductCategorySelector:N,closeProductCategorySelector:w,openProductVariantSetup:k,closeProductVariantSetup:C,openProductVariant:P,closeProductVariant:D,openProductDeleteConfirmation:n(e=>e.openProductDeleteConfirmation),closeProductDeleteConfirmation:n(e=>e.closeProductDeleteConfirmation)}}},68318:(e,t,a)=>{"use strict";a.d(t,{I1:()=>r,ZE:()=>s});let s=[{id:1,title:"Vitamin C Serum",description:"Cilt lekelerine karşı etkili, antioksidan \xf6zellikli vitamin C serumu. Cildinizi dış etkenlere karşı korur ve ışıltı verir.",price:349.9,discountPercentage:10,rating:4.7,stock:120,brand:"Say Beauty",category:"Cilt Bakımı",thumbnail:"https://picsum.photos/id/1/200/200",images:["https://picsum.photos/id/1/800/800","https://picsum.photos/id/2/800/800","https://picsum.photos/id/3/800/800"],points:30},{id:2,title:"Kolajen Takviyesi",description:"Eklem ve cilt sağlığı i\xe7in g\xfcnl\xfck kolajen takviyesi. İ\xe7eriğindeki Tip I ve Tip II kolajen ile cildinizin elastikiyetini destekler.",price:299.9,discountPercentage:void 0,rating:4.5,stock:85,brand:"Say Health",category:"Takviye \xdcr\xfcnler",thumbnail:"https://picsum.photos/id/4/200/200",images:["https://picsum.photos/id/4/800/800","https://picsum.photos/id/5/800/800","https://picsum.photos/id/6/800/800"],points:30},{id:3,title:"Argan Yağlı Şampuan",description:"Kuru ve yıpranmış sa\xe7lar i\xe7in onarıcı argan yağı i\xe7eren şampuan. Sa\xe7larınızı besler ve kolay taranmasını sağlar.",price:129.9,discountPercentage:15,rating:4.3,stock:200,brand:"Say Beauty",category:"Sa\xe7 Bakımı",thumbnail:"https://picsum.photos/id/7/200/200",images:["https://picsum.photos/id/7/800/800","https://picsum.photos/id/8/800/800","https://picsum.photos/id/9/800/800"],points:10},{id:4,title:"Probiyotik Kompleks",description:"Bağırsak florası ve sindirim sistemi sağlığı i\xe7in g\xfcnl\xfck probiyotik takviyesi. 10 farklı probiyotik suşu i\xe7erir.",price:199.9,discountPercentage:5,rating:4.8,stock:150,brand:"Say Health",category:"Takviye \xdcr\xfcnler",thumbnail:"https://picsum.photos/id/10/200/200",images:["https://picsum.photos/id/10/800/800","https://picsum.photos/id/11/800/800","https://picsum.photos/id/12/800/800"],points:20},{id:5,title:"Retinol Gece Kremi",description:"Yaşlanma karşıtı, kırışıklık giderici ve cilt yenileyici retinol i\xe7erikli gece kremi. D\xfczenli kullanımda ince \xe7izgilerin g\xf6r\xfcn\xfcm\xfcn\xfc azaltır.",price:399.9,discountPercentage:void 0,rating:4.6,stock:70,brand:"Say Beauty",category:"Cilt Bakımı",thumbnail:"https://picsum.photos/id/13/200/200",images:["https://picsum.photos/id/13/800/800","https://picsum.photos/id/14/800/800","https://picsum.photos/id/15/800/800"],points:40},{id:6,title:"Multivitamin",description:"G\xfcnl\xfck vitamin ve mineral ihtiyacınızı karşılayan multivitamin takviyesi. Adan Zye t\xfcm vitaminleri i\xe7erir.",price:179.9,discountPercentage:10,rating:4.4,stock:250,brand:"Say Health",category:"Takviye \xdcr\xfcnler",thumbnail:"https://picsum.photos/id/16/200/200",images:["https://picsum.photos/id/16/800/800","https://picsum.photos/id/17/800/800","https://picsum.photos/id/18/800/800"],points:15},{id:7,title:"Hyaluronik Asit Serumu",description:"Yoğun nemlendirici hyaluronik asit serumu. Cildin nem bariyerini g\xfc\xe7lendirir ve dolgunluk sağlar.",price:279.9,discountPercentage:void 0,rating:4.9,stock:100,brand:"Say Beauty",category:"Cilt Bakımı",thumbnail:"https://picsum.photos/id/19/200/200",images:["https://picsum.photos/id/19/800/800","https://picsum.photos/id/20/800/800","https://picsum.photos/id/21/800/800"],points:25},{id:8,title:"Yeşil \xc7ay \xd6zl\xfc Tonik",description:"G\xf6zenek sıkılaştırıcı ve yağ dengeleyici yeşil \xe7ay \xf6zl\xfc tonik. Cildi temizler ve ferahlatır.",price:149.9,discountPercentage:5,rating:4.2,stock:180,brand:"Say Beauty",category:"Cilt Bakımı",thumbnail:"https://picsum.photos/id/22/200/200",images:["https://picsum.photos/id/22/800/800","https://picsum.photos/id/23/800/800","https://picsum.photos/id/24/800/800"],points:15}],r=[{id:1,productId:1,product:s[0],addedAt:"2024-01-15T10:30:00Z"},{id:2,productId:3,product:s[2],addedAt:"2024-01-14T15:20:00Z"},{id:3,productId:5,product:s[4],addedAt:"2024-01-13T09:45:00Z"}]},69848:(e,t,a)=>{"use strict";a.d(t,{H:()=>n,r:()=>o});var s=a(95155),r=a(12115),i=a(68318);let l=(0,r.createContext)(void 0);function n(e){let{children:t}=e,[a,n]=(0,r.useState)([]),[o,d]=(0,r.useState)(!1);(0,r.useEffect)(()=>{if(!o){let e=localStorage.getItem("sayGlobalFavorites");if(e)try{let t=JSON.parse(e);n(t)}catch(e){console.error("Favori verileri y\xfcklenirken hata:",e),n(i.I1)}else n(i.I1);d(!0)}},[o]),(0,r.useEffect)(()=>{o&&localStorage.setItem("sayGlobalFavorites",JSON.stringify(a))},[a,o]);let c=(0,r.useCallback)(e=>{n(t=>t.find(t=>t.productId===e.id)?t:[...t,{id:Date.now(),productId:e.id,product:e,addedAt:new Date().toISOString()}])},[]),m=(0,r.useCallback)(e=>{n(t=>t.filter(t=>t.productId!==e))},[]),u=(0,r.useCallback)(e=>a.some(t=>t.productId===e),[a]),x=(0,r.useCallback)(()=>a.length,[a]),p=(0,r.useMemo)(()=>({favorites:a,addToFavorites:c,removeFromFavorites:m,isFavorite:u,getFavoritesCount:x}),[a,c,m,u,x]);return(0,s.jsx)(l.Provider,{value:p,children:t})}function o(){let e=(0,r.useContext)(l);if(void 0===e)throw Error("useFavorites must be used within a FavoritesProvider");return e}},74157:(e,t,a)=>{"use strict";a.d(t,{Fu:()=>r,Ys:()=>s,i6:()=>i,jK:()=>l});let s=[{id:"ORD-2023-1001",date:"25 Mart 2024",status:"Teslim Edildi",total:"₺1,245.00",items:3,statusClass:"bg-green-100 text-green-800"},{id:"ORD-2023-985",date:"18 Mart 2024",status:"Teslim Edildi",total:"₺780.50",items:2,statusClass:"bg-green-100 text-green-800"},{id:"ORD-2023-964",date:"5 Mart 2024",status:"Teslim Edildi",total:"₺1,650.00",items:5,statusClass:"bg-green-100 text-green-800"}],r={"ORD-2023-1001":{id:"ORD-2023-1001",orderDate:"25 Mart 2024",status:"Teslim Edildi",statusClass:"bg-green-100 text-green-800",deliveryDate:"28 Mart 2024",trackingNumber:"TRK-2024-001234",items:[{id:1,name:"Premium Collagen Plus",brand:"SayGlobal Nutrition",image:"/images/collagen-plus.jpg",quantity:2,unitPrice:299,totalPrice:598,points:60},{id:2,name:"Omega-3 Fish Oil",brand:"SayGlobal Nutrition",image:"/images/omega3.jpg",quantity:1,unitPrice:199,totalPrice:199,points:20},{id:3,name:"Vitamin D3 + K2",brand:"SayGlobal Nutrition",image:"/images/vitamin-d3-k2.jpg",quantity:1,unitPrice:149,totalPrice:149,points:15}],pricing:{subtotal:946,discount:50,shippingCost:35,tax:314,total:1245},shippingInfo:{method:"Hızlı Kargo",address:{name:"Ahmet Yılmaz",phone:"+90 532 123 45 67",address:"Atat\xfcrk Caddesi No:123 Daire:5",district:"Kadık\xf6y",city:"İstanbul",postalCode:"34710"}},paymentInfo:{method:"Kredi Kartı",cardLast4:"1234",paymentDate:"25 Mart 2024"},timeline:[{status:"Sipariş Alındı",date:"25 Mart 2024 - 14:30",description:"Siparişiniz başarıyla alınmıştır."},{status:"Hazırlanıyor",date:"25 Mart 2024 - 16:45",description:"Siparişiniz ambalajlanmaya başlandı."},{status:"Kargoya Verildi",date:"26 Mart 2024 - 09:15",description:"Siparişiniz kargo firmasına teslim edildi."},{status:"Teslim Edildi",date:"28 Mart 2024 - 11:20",description:"Siparişiniz adresinize teslim edilmiştir."}]},"ORD-2023-985":{id:"ORD-2023-985",orderDate:"18 Mart 2024",status:"Teslim Edildi",statusClass:"bg-green-100 text-green-800",deliveryDate:"21 Mart 2024",trackingNumber:"TRK-2024-001235",items:[{id:4,name:"Hyaluronic Acid Serum",brand:"SayGlobal Beauty",image:"/images/hyaluronic-serum.jpg",quantity:1,unitPrice:399,totalPrice:399,points:40},{id:5,name:"Probiotics Advanced",brand:"SayGlobal Nutrition",image:"/images/probiotics.jpg",quantity:1,unitPrice:249,totalPrice:249,points:25}],pricing:{subtotal:648,discount:25,shippingCost:35,tax:122.5,total:780.5},shippingInfo:{method:"Standart Kargo",address:{name:"Ahmet Yılmaz",phone:"+90 532 123 45 67",address:"Atat\xfcrk Caddesi No:123 Daire:5",district:"Kadık\xf6y",city:"İstanbul",postalCode:"34710"}},paymentInfo:{method:"Kredi Kartı",cardLast4:"1234",paymentDate:"18 Mart 2024"},timeline:[{status:"Sipariş Alındı",date:"18 Mart 2024 - 10:15",description:"Siparişiniz başarıyla alınmıştır."},{status:"Hazırlanıyor",date:"18 Mart 2024 - 15:30",description:"Siparişiniz ambalajlanmaya başlandı."},{status:"Kargoya Verildi",date:"19 Mart 2024 - 08:45",description:"Siparişiniz kargo firmasına teslim edildi."},{status:"Teslim Edildi",date:"21 Mart 2024 - 14:10",description:"Siparişiniz adresinize teslim edilmiştir."}]},"ORD-2023-964":{id:"ORD-2023-964",orderDate:"5 Mart 2024",status:"Teslim Edildi",statusClass:"bg-green-100 text-green-800",deliveryDate:"8 Mart 2024",trackingNumber:"TRK-2024-001236",items:[{id:6,name:"Magnesium Complex",brand:"SayGlobal Nutrition",image:"/images/magnesium.jpg",quantity:2,unitPrice:179,totalPrice:358,points:18},{id:7,name:"Zinc + Selenium",brand:"SayGlobal Nutrition",image:"/images/zinc-selenium.jpg",quantity:1,unitPrice:129,totalPrice:129,points:13},{id:8,name:"Vitamin C 1000mg",brand:"SayGlobal Nutrition",image:"/images/vitamin-c.jpg",quantity:2,unitPrice:99,totalPrice:198,points:10},{id:9,name:"B-Complex Vitamins",brand:"SayGlobal Nutrition",image:"/images/b-complex.jpg",quantity:1,unitPrice:159,totalPrice:159,points:16},{id:10,name:"Iron Supplement",brand:"SayGlobal Nutrition",image:"/images/iron.jpg",quantity:1,unitPrice:119,totalPrice:119,points:12}],pricing:{subtotal:963,discount:75,shippingCost:35,tax:727,total:1650},shippingInfo:{method:"Express Kargo",address:{name:"Ahmet Yılmaz",phone:"+90 532 123 45 67",address:"Atat\xfcrk Caddesi No:123 Daire:5",district:"Kadık\xf6y",city:"İstanbul",postalCode:"34710"}},paymentInfo:{method:"Havale/EFT",paymentDate:"5 Mart 2024"},timeline:[{status:"Sipariş Alındı",date:"5 Mart 2024 - 12:00",description:"Siparişiniz başarıyla alınmıştır."},{status:"\xd6deme Onaylandı",date:"5 Mart 2024 - 16:30",description:"\xd6demeniz onaylanmıştır."},{status:"Hazırlanıyor",date:"6 Mart 2024 - 09:00",description:"Siparişiniz ambalajlanmaya başlandı."},{status:"Kargoya Verildi",date:"6 Mart 2024 - 17:15",description:"Siparişiniz kargo firmasına teslim edildi."},{status:"Teslim Edildi",date:"8 Mart 2024 - 10:45",description:"Siparişiniz adresinize teslim edilmiştir."}]}},i={accountBalance:125.5,totalPoints:2450,availablePoints:1850,usedPoints:600,weeklyEarnings:[{week:"28 Eki",points:85},{week:"4 Kas",points:120},{week:"11 Kas",points:95},{week:"18 Kas",points:150},{week:"25 Kas",points:110},{week:"2 Ara",points:175},{week:"9 Ara",points:130},{week:"16 Ara",points:165}],monthlyEarnings:[{month:"Tem",points:320},{month:"Ağu",points:450},{month:"Eyl",points:380},{month:"Eki",points:520},{month:"Kas",points:475},{month:"Ara",points:470}],pointHistory:[{id:1,date:"2024-06-15",type:"earned",points:125,description:"Premium Collagen Plus alışverişi",orderNo:"ORD-2024-1001"},{id:2,date:"2024-06-10",type:"used",points:-100,description:"Omega-3 Fish Oil indirimi",orderNo:"ORD-2024-0998"},{id:3,date:"2024-06-08",type:"earned",points:90,description:"Vitamin D3 + K2 alışverişi",orderNo:"ORD-2024-0995"},{id:4,date:"2024-06-05",type:"earned",points:150,description:"Hyaluronic Acid Serum alışverişi",orderNo:"ORD-2024-0992"},{id:5,date:"2024-05-28",type:"used",points:-200,description:"Probiotics Advanced indirimi",orderNo:"ORD-2024-0989"}],balanceHistory:[{id:1,date:"2024-06-12",type:"refund",amount:45.5,description:"ORD-2024-0990 iadesi",status:"completed"},{id:2,date:"2024-05-20",type:"gift_card",amount:100,description:"Hediye kartı y\xfcklemesi",status:"completed"},{id:3,date:"2024-05-15",type:"withdrawal",amount:-20,description:"Kargo \xfccreti \xf6demesi",status:"completed"}]},l={iban:{accountHolderName:"Ahmet Mehmet YILMAZ",bankName:"garanti",bankDisplayName:"Garanti BBVA",iban:"TR64 0006 2000 4440 0006 2986 57",branchName:"Kadık\xf6y Şubesi",branchCode:"444"},cards:[{id:1,cardHolderName:"AHMET YILMAZ",cardType:"visa",cardNumber:"**** **** **** 1234",expirationDate:"12/26",isDefault:!0,addedDate:"2023-03-15",color:"blue"},{id:2,cardHolderName:"AHMET YILMAZ",cardType:"mastercard",cardNumber:"**** **** **** 5678",expirationDate:"09/25",isDefault:!1,addedDate:"2023-08-22",color:"green"},{id:3,cardHolderName:"AHMET YILMAZ",cardType:"troy",cardNumber:"**** **** **** 9012",expirationDate:"03/27",isDefault:!1,addedDate:"2024-01-10",color:"purple"}],statistics:{monthlyEarnings:2450,totalTransactions:24,averageTransaction:102}}}},e=>{var t=t=>e(e.s=t);e.O(0,[6408,6874,7323,6766,2327,8713,6681,8441,1684,7358],()=>t(8207)),_N_E=e.O()}]);