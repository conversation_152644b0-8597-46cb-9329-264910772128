(()=>{var e={};e.id=279,e.ids=[279],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},17702:(e,t,s)=>{Promise.resolve().then(s.bind(s,54787))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},54787:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\checkout\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\checkout\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67957:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(60687),r=s(28253),i=s(43210),l=s(26001),n=s(30474),d=s(85814),c=s.n(d),o=s(16189),m=s(88920);function x({isOpen:e,onClose:t}){return(0,a.jsx)(m.N,{children:e&&(0,a.jsxs)(l.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},children:[(0,a.jsx)(l.P.div,{className:"absolute inset-0 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:t}),(0,a.jsxs)(l.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.7,opacity:0,y:50},transition:{type:"spring",stiffness:300,damping:25,duration:.5},children:[(0,a.jsxs)(l.P.div,{className:"text-center mb-6",initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},children:[(0,a.jsx)("div",{className:"mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(l.P.svg,{className:"w-10 h-10 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",initial:{pathLength:0},animate:{pathLength:1},transition:{delay:.4,duration:.6},children:(0,a.jsx)(l.P.path,{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7",initial:{pathLength:0},animate:{pathLength:1},transition:{delay:.4,duration:.6}})})}),(0,a.jsx)(l.P.h2,{className:"text-2xl font-bold text-gray-800 mb-2",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:"Siparişiniz Alındı!"}),(0,a.jsx)(l.P.p,{className:"text-gray-600",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4},children:"Siparişiniz başarıyla oluşturuldu. En kısa s\xfcrede hazırlanacak ve size ulaştırılacaktır."})]}),(0,a.jsx)(l.P.div,{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},children:(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-green-700 font-medium",children:"Sipariş Numarası:"}),(0,a.jsxs)("span",{className:"text-green-800 font-bold",children:["#SG",Math.random().toString(36).substr(2,9).toUpperCase()]})]})}),(0,a.jsxs)(l.P.div,{className:"flex flex-col space-y-3",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},children:[(0,a.jsx)(c(),{href:"/",children:(0,a.jsx)(l.P.button,{className:"w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},onClick:t,children:"Ana Sayfaya D\xf6n"})}),(0,a.jsx)(c(),{href:"/products",children:(0,a.jsx)(l.P.button,{className:"w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},onClick:t,children:"Alışverişe Devam Et"})})]}),(0,a.jsx)(l.P.button,{onClick:t,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors",initial:{opacity:0},animate:{opacity:1},transition:{delay:.7},whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]})})}function p(){let{items:e,getTotalPrice:t,getTotalPoints:s,clearCart:d}=(0,r._)();(0,o.useRouter)();let[m,p]=(0,i.useState)(1),[u,h]=(0,i.useState)(!1),[g,y]=(0,i.useState)({firstName:"",lastName:"",email:"",phone:"",address:"",city:"",district:"",postalCode:"",paymentMethod:"creditCard",cardNumber:"",cardName:"",expiryDate:"",cvv:"",billingAddress:"",billingCity:"",billingDistrict:"",billingPostalCode:"",sameAsDelivery:!0}),[b,j]=(0,i.useState)({});if(0===e.length)return(0,a.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-4",children:"Sepetiniz Boş"}),(0,a.jsx)("p",{className:"text-gray-300 mb-8",children:"\xd6deme yapabilmek i\xe7in sepetinizde \xfcr\xfcn bulunmalıdır."}),(0,a.jsx)(c(),{href:"/products",className:"bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300",children:"Alışverişe Başla"})]})})});let f=e=>{let{name:t,value:s,type:a}=e.target;if("checkbox"===a){let s=e.target.checked;y(e=>({...e,[t]:s}))}else y(e=>({...e,[t]:s}));b[t]&&j(e=>({...e,[t]:""}))},N=e=>{let t={};return 1===e&&(g.firstName.trim()||(t.firstName="Ad gereklidir"),g.lastName.trim()||(t.lastName="Soyad gereklidir"),g.email.trim()||(t.email="E-posta gereklidir"),g.phone.trim()||(t.phone="Telefon gereklidir"),g.address.trim()||(t.address="Adres gereklidir"),g.city.trim()||(t.city="Şehir gereklidir"),g.district.trim()||(t.district="İl\xe7e gereklidir"),g.postalCode.trim()||(t.postalCode="Posta kodu gereklidir")),2===e&&"creditCard"===g.paymentMethod&&(g.cardNumber.trim()||(t.cardNumber="Kart numarası gereklidir"),g.cardName.trim()||(t.cardName="Kart sahibi adı gereklidir"),g.expiryDate.trim()||(t.expiryDate="Son kullanma tarihi gereklidir"),g.cvv.trim()||(t.cvv="CVV gereklidir")),j(t),0===Object.keys(t).length},v=async()=>{N(2)&&h(!0)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-4",children:"\xd6deme"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${m>=1?"bg-purple-600 text-white":"bg-gray-300 text-gray-600"}`,children:"1"}),(0,a.jsx)("span",{className:`text-sm font-medium ${m>=1?"text-white":"text-gray-400"}`,children:"Teslimat"})]}),(0,a.jsx)("div",{className:`h-0.5 w-16 ${m>=2?"bg-purple-600":"bg-gray-300"}`}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${m>=2?"bg-purple-600 text-white":"bg-gray-300 text-gray-600"}`,children:"2"}),(0,a.jsx)("span",{className:`text-sm font-medium ${m>=2?"text-white":"text-gray-400"}`,children:"\xd6deme"})]}),(0,a.jsx)("div",{className:`h-0.5 w-16 ${m>=3?"bg-purple-600":"bg-gray-300"}`}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${m>=3?"bg-purple-600 text-white":"bg-gray-300 text-gray-600"}`,children:"3"}),(0,a.jsx)("span",{className:`text-sm font-medium ${m>=3?"text-white":"text-gray-400"}`,children:"Onay"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[1===m&&(0,a.jsxs)(l.P.div,{className:"bg-white rounded-lg p-6 shadow-md",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-6",children:"Teslimat Bilgileri"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ad *"}),(0,a.jsx)("input",{type:"text",name:"firstName",value:g.firstName,onChange:f,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${b.firstName?"border-red-500":"border-gray-300"}`}),b.firstName&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.firstName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Soyad *"}),(0,a.jsx)("input",{type:"text",name:"lastName",value:g.lastName,onChange:f,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${b.lastName?"border-red-500":"border-gray-300"}`}),b.lastName&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.lastName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"E-posta *"}),(0,a.jsx)("input",{type:"email",name:"email",value:g.email,onChange:f,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${b.email?"border-red-500":"border-gray-300"}`}),b.email&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Telefon *"}),(0,a.jsx)("input",{type:"tel",name:"phone",value:g.phone,onChange:f,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${b.phone?"border-red-500":"border-gray-300"}`}),b.phone&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.phone})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Adres *"}),(0,a.jsx)("input",{type:"text",name:"address",value:g.address,onChange:f,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${b.address?"border-red-500":"border-gray-300"}`,placeholder:"Mahalle, Sokak, No"}),b.address&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.address})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Şehir *"}),(0,a.jsx)("input",{type:"text",name:"city",value:g.city,onChange:f,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${b.city?"border-red-500":"border-gray-300"}`}),b.city&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.city})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"İl\xe7e *"}),(0,a.jsx)("input",{type:"text",name:"district",value:g.district,onChange:f,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${b.district?"border-red-500":"border-gray-300"}`}),b.district&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.district})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Posta Kodu *"}),(0,a.jsx)("input",{type:"text",name:"postalCode",value:g.postalCode,onChange:f,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${b.postalCode?"border-red-500":"border-gray-300"}`}),b.postalCode&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.postalCode})]})]})]}),2===m&&(0,a.jsxs)(l.P.div,{className:"bg-white rounded-lg p-6 shadow-md",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-6",children:"\xd6deme Bilgileri"}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"\xd6deme Y\xf6ntemi"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,a.jsx)("input",{type:"radio",name:"paymentMethod",value:"creditCard",checked:"creditCard"===g.paymentMethod,onChange:f,className:"mr-3"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("svg",{className:"w-6 h-6 text-blue-600",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"})}),(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Kredi/Banka Kartı"})]})]}),(0,a.jsxs)("label",{className:"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,a.jsx)("input",{type:"radio",name:"paymentMethod",value:"bankTransfer",checked:"bankTransfer"===g.paymentMethod,onChange:f,className:"mr-3"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("svg",{className:"w-6 h-6 text-green-600",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M11.5,1L2,6V8H21V6M16,10V17H19V10M2,22H21V19H2M10,10V17H13V10"})}),(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Banka Havalesi/EFT"})]})]}),(0,a.jsxs)("label",{className:"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,a.jsx)("input",{type:"radio",name:"paymentMethod",value:"cashOnDelivery",checked:"cashOnDelivery"===g.paymentMethod,onChange:f,className:"mr-3"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("svg",{className:"w-6 h-6 text-orange-600",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"})}),(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Kapıda \xd6deme"})]})]})]})]}),"creditCard"===g.paymentMethod&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kart Numarası *"}),(0,a.jsx)("input",{type:"text",name:"cardNumber",value:g.cardNumber.replace(/\D/g,"").replace(/(\d{4})(?=\d)/g,"$1 "),onChange:e=>{let t=e.target.value.replace(/\s/g,"");t.length<=16&&f({...e,target:{...e.target,value:t}})},placeholder:"1234 5678 9012 3456",className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${b.cardNumber?"border-red-500":"border-gray-300"}`}),b.cardNumber&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.cardNumber})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kart Sahibi Adı *"}),(0,a.jsx)("input",{type:"text",name:"cardName",value:g.cardName,onChange:f,placeholder:"John Doe",className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${b.cardName?"border-red-500":"border-gray-300"}`}),b.cardName&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.cardName})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Son Kullanma Tarihi *"}),(0,a.jsx)("input",{type:"text",name:"expiryDate",value:g.expiryDate.replace(/\D/g,"").replace(/(\d{2})(\d{2})/,"$1/$2"),onChange:e=>{let t=e.target.value.replace(/\D/g,"");t.length<=4&&f({...e,target:{...e.target,value:t}})},placeholder:"MM/YY",className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${b.expiryDate?"border-red-500":"border-gray-300"}`}),b.expiryDate&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.expiryDate})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CVV *"}),(0,a.jsx)("input",{type:"text",name:"cvv",value:g.cvv,onChange:e=>{let t=e.target.value.replace(/\D/g,"");t.length<=3&&f({...e,target:{...e.target,value:t}})},placeholder:"123",className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${b.cvv?"border-red-500":"border-gray-300"}`}),b.cvv&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.cvv})]})]})]}),"bankTransfer"===g.paymentMethod&&(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Banka Hesap Bilgileri"}),(0,a.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Banka:"})," T\xfcrkiye İş Bankası"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Hesap Sahibi:"})," Say Global Ltd. Şti."]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"IBAN:"})," TR12 0006 4000 0011 2345 6789 01"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"A\xe7ıklama:"})," Sipariş numaranızı belirtiniz"]})]})]}),"cashOnDelivery"===g.paymentMethod&&(0,a.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-medium text-orange-800 mb-2",children:"Kapıda \xd6deme"}),(0,a.jsx)("p",{className:"text-sm text-orange-700",children:"Siparişiniz adresinize teslim edilirken nakit olarak \xf6deme yapabilirsiniz. Kapıda \xf6deme i\xe7in ek \xfccret alınmamaktadır."})]})]}),3===m&&(0,a.jsxs)(l.P.div,{className:"bg-white rounded-lg p-6 shadow-md",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-6",children:"Sipariş Onayı"}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-800 mb-3",children:"Teslimat Adresi"}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-sm text-gray-700",children:[(0,a.jsxs)("p",{className:"font-medium",children:[g.firstName," ",g.lastName]}),(0,a.jsx)("p",{children:g.address}),(0,a.jsxs)("p",{children:[g.district,", ",g.city," ",g.postalCode]}),(0,a.jsx)("p",{children:g.phone}),(0,a.jsx)("p",{children:g.email})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-800 mb-3",children:"\xd6deme Y\xf6ntemi"}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-sm text-gray-700",children:["creditCard"===g.paymentMethod&&(0,a.jsxs)("p",{children:["Kredi/Banka Kartı (**** **** **** ",g.cardNumber.slice(-4),")"]}),"bankTransfer"===g.paymentMethod&&(0,a.jsx)("p",{children:"Banka Havalesi/EFT"}),"cashOnDelivery"===g.paymentMethod&&(0,a.jsx)("p",{children:"Kapıda \xd6deme"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-800 mb-3",children:"Sipariş Detayları"}),(0,a.jsx)("div",{className:"space-y-3",children:e.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"relative w-12 h-12 flex-shrink-0",children:(0,a.jsx)(n.default,{src:e.thumbnail,alt:e.title,fill:!0,className:"object-cover rounded"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-800 text-sm",children:e.title}),(0,a.jsxs)("p",{className:"text-gray-600 text-xs",children:["Adet: ",e.quantity]})]})]}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("p",{className:"font-medium text-purple-700",children:[(e.price*e.quantity).toFixed(2)," ₺"]})})]},e.id))})]})]}),(0,a.jsxs)("div",{className:"flex justify-between mt-6",children:[(0,a.jsx)(l.P.button,{onClick:()=>{p(e=>e-1)},className:`px-6 py-2 rounded-lg font-medium ${1===m?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-gray-600 text-white hover:bg-gray-700"}`,disabled:1===m,whileHover:m>1?{scale:1.02}:{},whileTap:m>1?{scale:.98}:{},children:"Geri"}),m<3?(0,a.jsx)(l.P.button,{onClick:()=>{N(m)&&p(e=>e+1)},className:"px-6 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg font-medium hover:shadow-lg",whileHover:{scale:1.02},whileTap:{scale:.98},children:"İleri"}):(0,a.jsx)(l.P.button,{onClick:v,className:"px-6 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg font-medium hover:shadow-lg",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Siparişi Tamamla"})]})]}),(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)(l.P.div,{className:"bg-white rounded-lg p-6 shadow-md sticky top-8",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.3,duration:.6},children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-6",children:"Sipariş \xd6zeti"}),(0,a.jsx)("div",{className:"space-y-3 mb-6",children:e.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"relative w-12 h-12 flex-shrink-0",children:(0,a.jsx)(n.default,{src:e.thumbnail,alt:e.title,fill:!0,className:"object-cover rounded"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-800 truncate",children:e.title}),(0,a.jsxs)("p",{className:"text-xs text-gray-600",children:["x",e.quantity]})]})]}),(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-800",children:[(e.price*e.quantity).toFixed(2)," ₺"]})]},e.id))}),(0,a.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsx)("span",{children:"\xdcr\xfcn Toplamı:"}),(0,a.jsxs)("span",{children:[t().toFixed(2)," ₺"]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsx)("span",{children:"Kargo:"}),(0,a.jsx)("span",{className:"text-green-600",children:"\xdccretsiz"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center text-purple-600 bg-purple-50 p-3 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,a.jsx)("span",{className:"font-medium",children:"Toplam Puan:"})]}),(0,a.jsxs)("span",{className:"font-bold text-lg",children:[s()," puan"]})]}),(0,a.jsx)("div",{className:"border-t pt-3",children:(0,a.jsxs)("div",{className:"flex justify-between text-lg font-bold text-gray-800",children:[(0,a.jsx)("span",{children:"Toplam:"}),(0,a.jsxs)("span",{className:"text-purple-700",children:[t().toFixed(2)," ₺"]})]})})]}),(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-green-700",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"})}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"256-bit SSL ile G\xfcvenli \xd6deme"})]})})]})})]})]})}),u&&(0,a.jsx)(x,{isOpen:u,onClose:()=>{h(!1),d()}})]})}},68687:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var a=s(65239),r=s(48088),i=s(88170),l=s.n(i),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let c={children:["",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,54787)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\checkout\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\checkout\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/checkout/page",pathname:"/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87438:(e,t,s)=>{Promise.resolve().then(s.bind(s,67957))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,181,658,85],()=>s(68687));module.exports=a})();