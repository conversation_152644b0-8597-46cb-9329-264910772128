(()=>{var e={};e.id=571,e.ids=[571],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5442:(e,t,s)=>{Promise.resolve().then(s.bind(s,8547))},6943:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},8547:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\products\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},13150:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(60687),r=s(26001),l=s(13964);function i({checked:e,onChange:t,label:s,disabled:i=!1,size:n="md",className:o=""}){let d={sm:{checkbox:"w-4 h-4",text:"text-xs",icon:"w-2.5 h-2.5"},md:{checkbox:"w-5 h-5",text:"text-sm",icon:"w-3 h-3"},lg:{checkbox:"w-6 h-6",text:"text-base",icon:"w-4 h-4"}}[n];return(0,a.jsxs)(r.P.label,{className:`flex items-center space-x-3 cursor-pointer select-none ${i?"opacity-50 cursor-not-allowed":""} ${o}`,whileHover:i?{}:{scale:1.01},whileTap:i?{}:{scale:.99},children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"checkbox",checked:e,onChange:e=>!i&&t(e.target.checked),disabled:i,className:"sr-only"}),(0,a.jsx)(r.P.div,{className:`
                        ${d.checkbox}
                        rounded-md
                        border-2
                        flex
                        items-center
                        justify-center
                        transition-all
                        duration-200
                        ${e?"bg-gradient-to-br from-purple-500 to-purple-700 border-purple-600 shadow-lg shadow-purple-500/25":"bg-white border-gray-300 hover:border-purple-400"}
                        ${!i&&"hover:shadow-md"}
                    `,initial:!1,animate:{scale:e?1.05:1,boxShadow:e?"0 10px 25px -5px rgba(147, 51, 234, 0.25), 0 4px 6px -2px rgba(147, 51, 234, 0.05)":"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"},transition:{duration:.2,ease:"easeInOut"},children:(0,a.jsx)(r.P.div,{initial:!1,animate:{scale:+!!e,opacity:+!!e},transition:{duration:.15,ease:"easeInOut"},children:(0,a.jsx)(l.A,{className:`${d.icon} text-white stroke-[3]`})})})]}),(0,a.jsx)("span",{className:`
                    ${d.text} 
                    text-gray-700 
                    font-medium 
                    truncate 
                    flex-1
                    ${!i&&"group-hover:text-gray-900"}
                `,children:s})]})}},13943:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},13964:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23594:(e,t,s)=>{Promise.resolve().then(s.bind(s,87465))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48975:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=s(65239),r=s(48088),l=s(88170),i=s.n(l),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8547)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\products\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\products\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var a=s(43210);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:r,className:l="",children:i,iconNode:c,...u},x)=>(0,a.createElement)("svg",{ref:x,...d,width:t,height:t,stroke:e,strokeWidth:r?24*Number(s)/Number(t):s,className:n("lucide",l),...!i&&!o(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(i)?i:[i]])),u=(e,t)=>{let s=(0,a.forwardRef)(({className:s,...l},o)=>(0,a.createElement)(c,{ref:o,iconNode:t,className:n(`lucide-${r(i(e))}`,`lucide-${e}`,s),...l}));return s.displayName=i(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78259:(e,t,s)=>{"use strict";s.d(t,{F:()=>r,Sz:()=>i,vn:()=>a,w7:()=>l});var a=function(e){return e[e.Default=0]="Default",e[e.PriceAsc=1]="PriceAsc",e[e.PriceDesc=2]="PriceDesc",e[e.RatingDesc=3]="RatingDesc",e}({}),r=function(e){return e[e.None=0]="None",e[e.Baslangic=1]="Baslangic",e[e.Girisimci=2]="Girisimci",e[e.Bronz=3]="Bronz",e[e.Gumus=4]="Gumus",e[e.Altin=5]="Altin",e[e.Platin=6]="Platin",e[e.PlatinMax=7]="PlatinMax",e}({}),l=function(e){return e[e.Unspecified=0]="Unspecified",e[e.Male=1]="Male",e[e.Female=2]="Female",e[e.Other=3]="Other",e}({}),i=function(e){return e[e.Pending=0]="Pending",e[e.Accepted=1]="Accepted",e[e.Rejected=2]="Rejected",e}({})},78272:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87465:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>U});var a=s(60687),r=s(43210),l=s(26001),i=s(16189),n=s(11860),o=s(51423),d=s(64298);let c=()=>(0,o.I)({queryKey:["categories"],queryFn:async()=>{let e=await d.jU.getCategories();if(!e.success)throw Error(e.error||"Kategoriler alınamadı");return e.data},staleTime:6e5,refetchOnWindowFocus:!1,refetchOnMount:!1}),u=e=>(0,o.I)({queryKey:["subcategories",e],queryFn:async()=>{if(!e)return[];let t=await d.jU.getSubCategoriesByCategory(e);if(!t.success)throw Error(t.error||"Alt kategoriler alınamadı");return t.data},enabled:!!e,staleTime:6e5,refetchOnWindowFocus:!1}),x=e=>(0,o.I)({queryKey:["products",e],queryFn:async()=>{let t=await d.jU.filterProducts(e);if(!t.success)throw Error(t.error||"\xdcr\xfcnler alınamadı");return t.data},staleTime:12e4,refetchOnWindowFocus:!0,refetchOnMount:!0,enabled:!0}),p=()=>(0,o.I)({queryKey:["referenceData"],queryFn:async()=>{let e=await d.jU.getReferenceData();if(!e.success)throw Error(e.error||"Reference data alınamadı");return e.data},staleTime:6e5,refetchOnWindowFocus:!1,refetchOnMount:!1});var h=s(26787),m=s(59350),g=s(78259);let y={categories:[],subCategories:{},products:[],isLoadingProducts:!1,brands:[],featureDefinitions:[],featureValues:[],isLoadingReferenceData:!1,selectedCategoryId:null,selectedSubCategoryId:null,selectedBrandIds:[],selectedFeatureValueIds:[],priceRange:[0,1e4],sortBy:g.vn.Default,isFilterOpen:!1,hoveredCategoryId:null,error:null},f=(0,h.v)()((0,m.lt)((e,t)=>({...y,setCategories:t=>{console.log("\uD83C\uDFEA Zustand: Setting categories:",t),e({categories:t},!1,"productsPage/setCategories")},setSubCategories:(t,s)=>{console.log("\uD83C\uDFEA Zustand: Setting subcategories for category",t,":",s),e(e=>({subCategories:{...e.subCategories,[t]:s}}),!1,"productsPage/setSubCategories")},setProducts:t=>{console.log("\uD83C\uDFEA Zustand: Setting products:",t.length,"items"),e({products:t},!1,"productsPage/setProducts")},setLoadingProducts:t=>{e({isLoadingProducts:t},!1,"productsPage/setLoadingProducts")},setBrands:t=>{console.log("\uD83C\uDFEA Zustand: Setting brands:",t),e({brands:t},!1,"productsPage/setBrands")},setFeatureDefinitions:t=>{console.log("\uD83C\uDFEA Zustand: Setting feature definitions:",t),e({featureDefinitions:t},!1,"productsPage/setFeatureDefinitions")},setFeatureValues:t=>{console.log("\uD83C\uDFEA Zustand: Setting feature values:",t),e({featureValues:t},!1,"productsPage/setFeatureValues")},setLoadingReferenceData:t=>{e({isLoadingReferenceData:t},!1,"productsPage/setLoadingReferenceData")},setSelectedCategory:t=>{console.log("\uD83C\uDFEA Zustand: Setting selected category:",t),e({selectedCategoryId:t,selectedSubCategoryId:null},!1,"productsPage/setSelectedCategory")},setSelectedSubCategory:t=>{console.log("\uD83C\uDFEA Zustand: Setting selected subcategory:",t),e({selectedSubCategoryId:t},!1,"productsPage/setSelectedSubCategory")},setSelectedBrandIds:t=>{console.log("\uD83C\uDFEA Zustand: Setting selected brand IDs:",t),e({selectedBrandIds:t},!1,"productsPage/setSelectedBrandIds")},setSelectedFeatureValueIds:t=>{console.log("\uD83C\uDFEA Zustand: Setting selected feature value IDs:",t),e({selectedFeatureValueIds:t},!1,"productsPage/setSelectedFeatureValueIds")},setPriceRange:t=>{console.log("\uD83C\uDFEA Zustand: Setting price range:",t),e({priceRange:t},!1,"productsPage/setPriceRange")},setSortBy:t=>{console.log("\uD83C\uDFEA Zustand: Setting sort by:",t),e({sortBy:t},!1,"productsPage/setSortBy")},resetFilters:()=>{console.log("\uD83C\uDFEA Zustand: Resetting all filters"),e({selectedCategoryId:null,selectedSubCategoryId:null,selectedBrandIds:[],selectedFeatureValueIds:[],priceRange:[0,1e4],sortBy:g.vn.Default},!1,"productsPage/resetFilters")},setFilterOpen:t=>{e({isFilterOpen:t},!1,"productsPage/setFilterOpen")},setHoveredCategory:t=>{e({hoveredCategoryId:t},!1,"productsPage/setHoveredCategory")},setError:t=>{console.log("\uD83C\uDFEA Zustand: Setting error:",t),e({error:t},!1,"productsPage/setError")},getCurrentFilterRequest:()=>{let e=t(),s={sortBy:e.sortBy};return e.selectedCategoryId&&(s.categoryIds=[e.selectedCategoryId]),e.selectedSubCategoryId&&(s.subCategoryIds=[e.selectedSubCategoryId]),e.selectedBrandIds.length>0&&(s.brandIds=e.selectedBrandIds),e.selectedFeatureValueIds.length>0&&(s.featureValueIds=e.selectedFeatureValueIds),e.priceRange[0]>0&&(s.minPrice=e.priceRange[0]),e.priceRange[1]<1e4&&(s.maxPrice=e.priceRange[1]),console.log("\uD83C\uDFEA Zustand: Generated filter request:",s),s}}),{name:"products-page-store",enabled:!1}));var b=s(88920),v=s(62688);let w=(0,v.A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),j=(0,v.A)("laptop",[["path",{d:"M18 5a2 2 0 0 1 2 2v8.526a2 2 0 0 0 .212.897l1.068 2.127a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45l1.068-2.127A2 2 0 0 0 4 15.526V7a2 2 0 0 1 2-2z",key:"1pdavp"}],["path",{d:"M20.054 15.987H3.946",key:"14rxg9"}]]),N=(0,v.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),k=(0,v.A)("shirt",[["path",{d:"M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z",key:"1wgbhj"}]]),P=(0,v.A)("book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]),C=(0,v.A)("gamepad-2",[["line",{x1:"6",x2:"10",y1:"11",y2:"11",key:"1gktln"}],["line",{x1:"8",x2:"8",y1:"9",y2:"13",key:"qnk9ow"}],["line",{x1:"15",x2:"15.01",y1:"12",y2:"12",key:"krot7o"}],["line",{x1:"18",x2:"18.01",y1:"10",y2:"10",key:"1lcuu1"}],["path",{d:"M17.32 5H6.68a4 4 0 0 0-3.978 3.59c-.006.052-.01.101-.017.152C2.604 9.416 2 14.456 2 16a3 3 0 0 0 3 3c1 0 1.5-.5 2-1l1.414-1.414A2 2 0 0 1 9.828 16h4.344a2 2 0 0 1 1.414.586L17 18c.5.5 1 1 2 1a3 3 0 0 0 3-3c0-1.545-.604-6.584-.685-7.258-.007-.05-.011-.1-.017-.151A4 4 0 0 0 17.32 5z",key:"mfqc10"}]]),A=(0,v.A)("car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]]),S=(0,v.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var M=s(6943);let I=(0,v.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]);var F=s(78272),L=s(13943);let z={Elektronik:w,Bilgisayar:j,"Ev & Yaşam":N,Giyim:k,Kitap:P,Oyun:C,Otomotiv:A,Sağlık:S,default:M.A};function R(){let[e,t]=(0,r.useState)(!1),[s,i]=(0,r.useState)(null),[n,o]=(0,r.useState)(null),[d,x]=(0,r.useState)(null),[p,h]=(0,r.useState)(!1),[m,g]=(0,r.useState)(null),y=(0,r.useRef)(null),v=(0,r.useRef)(null),w=(0,r.useRef)(null),{selectedCategoryId:j,selectedSubCategoryId:N}=f(),{setSelectedCategory:k,setSelectedSubCategory:P,resetFilters:C}=f(),{data:A=[],isLoading:S}=c(),{data:R=[]}=u(s),{data:q=[]}=u(j),{data:D=[]}=u(m),T=e=>z[e]||z.default,B=e=>{w.current&&clearTimeout(w.current),w.current=setTimeout(()=>{i(e)},500)},O=()=>{w.current&&clearTimeout(w.current),w.current=setTimeout(()=>{i(null)},800)},H=()=>{w.current&&clearTimeout(w.current)},$=e=>{j===e?k(null):k(e),P(null),t(!1),i(null)},E=e=>{let a=p?m:s;a&&k(a),P(e),t(!1),i(null),x(null),g(null)},U=e=>{console.log("Mobile category toggle:",e,"Current expanded:",d),d===e?(k(e),P(null),x(null),g(null),t(!1)):(x(e),g(e))};return S?(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-4",children:(0,a.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"})})}):(0,a.jsxs)("div",{className:"mb-8 relative",children:[(0,a.jsx)(l.P.div,{className:"bg-white rounded-xl shadow-lg border border-gray-100 relative z-10",initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.5,delay:.1}},whileHover:{scale:1.02,boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"},transition:{duration:.2,ease:"easeOut"},children:(0,a.jsxs)(l.P.button,{ref:v,onClick:()=>{t(!e),i(null)},whileTap:{scale:.98},transition:{duration:.2,ease:"easeOut"},className:"w-full flex items-center justify-between px-6 py-4 text-left rounded-xl",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,a.jsx)(I,{className:"h-5 w-5 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm text-gray-500 block",children:"Kategori Se\xe7in"}),(0,a.jsx)("span",{className:"font-semibold text-gray-900 text-lg",children:(()=>{if(N){let e=q.find(e=>e.id===N);return e?.name||"Alt Kategori"}if(j){let e=A.find(e=>e.id===j);return e?.name||"Kategori"}return"T\xfcm Kategoriler"})()})]})]}),(0,a.jsx)(l.P.div,{animate:{rotate:180*!!e},transition:{duration:.2},children:(0,a.jsx)(F.A,{className:"h-5 w-5 text-gray-400"})})]})}),(0,a.jsx)(b.N,{children:e&&(0,a.jsx)(l.P.div,{ref:y,initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-2xl border border-gray-100 z-50 overflow-hidden",children:(0,a.jsxs)("div",{className:"max-h-96 overflow-y-auto",children:[(0,a.jsx)(l.P.button,{onClick:()=>{k(null),P(null),t(!1),i(null)},whileHover:{backgroundColor:"#f8fafc"},className:`w-full flex items-center px-6 py-4 text-left transition-all duration-200 border-b border-gray-100 ${!j&&!N?"bg-purple-50 text-purple-700":"text-gray-700 hover:bg-gray-50"}`,children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:`p-2 rounded-lg ${!j&&!N?"bg-purple-100":"bg-gray-100"}`,children:(0,a.jsx)(M.A,{className:`h-5 w-5 ${!j&&!N?"text-purple-600":"text-gray-600"}`})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"T\xfcm Kategoriler"}),(0,a.jsx)("span",{className:"text-sm text-gray-500 block",children:"T\xfcm \xfcr\xfcnleri g\xf6r\xfcnt\xfcle"})]})]})}),(0,a.jsx)("div",{className:"py-2",children:A.map(e=>{let t=T(e.name),r=j===e.id;return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(l.P.button,{onClick:()=>{console.log("Category clicked:",e.id,"isMobile:",p),p?U(e.id):$(e.id)},onMouseEnter:()=>{p||(B(e.id),o(e.id))},onMouseLeave:()=>{p||(O(),o(null))},initial:{backgroundColor:r?"#f3f4f6":"transparent"},animate:{backgroundColor:r?"#f3f4f6":"transparent"},whileHover:{backgroundColor:"#e9d5ff",color:"#7c3aed",scale:1.01},whileTap:{scale:.99},transition:{duration:.15,ease:"easeOut"},className:`w-full flex items-center justify-between px-6 py-3 text-left ${r?"text-purple-700":"text-gray-700"}`,children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:`p-2 rounded-lg ${r||n===e.id?"bg-purple-100":"bg-gray-100"}`,children:(0,a.jsx)(t,{className:`h-5 w-5 ${r||n===e.id?"text-purple-600":"text-gray-600"}`})}),(0,a.jsx)("span",{className:"font-medium",children:e.name})]}),(0,a.jsx)(F.A,{className:`h-4 w-4 transition-transform duration-200 ${p?d===e.id?"rotate-180":"":s===e.id?"rotate-180":""}`})]}),(0,a.jsx)(b.N,{children:(p&&d===e.id||!p&&s===e.id)&&(p?D.length>0:R.length>0)&&(0,a.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.2},onMouseEnter:p?void 0:H,onMouseLeave:p?void 0:O,className:"bg-gray-50 border-t border-gray-100",children:(0,a.jsx)("div",{className:"py-2",children:(p?D:R).map(e=>(0,a.jsx)(l.P.button,{onClick:()=>E(e.id),initial:{backgroundColor:N===e.id?"#ffffff":"transparent"},animate:{backgroundColor:N===e.id?"#ffffff":"transparent"},whileHover:{x:4,backgroundColor:"#ffffff",color:"#7c3aed",scale:1.01},whileTap:{scale:.99},transition:{duration:.15,ease:"easeOut"},onMouseEnter:p?void 0:H,className:`w-full flex items-center px-12 py-2 text-left ${N===e.id?"text-purple-700 font-medium shadow-sm":"text-gray-600"}`,children:(0,a.jsx)("span",{className:"text-sm",children:e.name})},e.id))})})})]},e.id)})})]})})}),(j||N)&&(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 flex items-center gap-2 flex-wrap",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Aktif filtre:"}),(0,a.jsxs)(l.P.button,{whileHover:{scale:1.02,transition:{duration:.15,ease:"easeOut"}},whileTap:{scale:.98,transition:{duration:.1,ease:"easeInOut"}},transition:{duration:.15,ease:"easeInOut"},onClick:C,className:"flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-xs font-medium",title:"T\xfcm filtreleri temizle",children:[(0,a.jsx)(L.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"Temizle"})]})]}),j&&(0,a.jsxs)("span",{className:"bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2",children:[A.find(e=>e.id===j)?.name,!N&&(0,a.jsx)("button",{onClick:()=>k(null),className:"text-purple-500 hover:text-purple-700 transition-colors",children:(0,a.jsx)("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),N&&(0,a.jsxs)("span",{className:"bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2",children:[q.find(e=>e.id===N)?.name,(0,a.jsx)("button",{onClick:()=>P(null),className:"text-blue-500 hover:text-blue-700 transition-colors",children:(0,a.jsx)("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]})]})}var q=s(30474),D=s(85814),T=s.n(D),B=s(45840),O=s(42112);function H({product:e}){let{data:t,isLoading:s}=(0,B.P)(),{isCustomerPrice:r}=(0,O.w)(),i=t?.discountRate||null,n=(e,t)=>t?Math.round(t/100*e):0,o=(()=>{let t=e.price;!r&&i&&i>0&&(t*=1-i/100);let s=Number(e.extraDiscount);return s>0&&(t*=1-s/100),t})(),d=!r&&i&&i>0||Number(e.extraDiscount)>0,c=n(e.price,e.pv),u=n(e.price,e.cv);return(0,a.jsx)(l.P.div,{whileHover:{y:-8},transition:{duration:.3},className:"group",children:(0,a.jsx)("div",{className:"bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 h-full flex flex-col",children:(0,a.jsxs)(T(),{href:`/product/${e.id}`,className:"flex-1 flex flex-col",children:[(0,a.jsxs)("div",{className:"relative h-64 overflow-hidden group",children:[(0,a.jsx)(q.default,{src:e.mainImageUrl,alt:e.name,fill:!0,className:"object-cover transition-transform duration-700 group-hover:scale-110"}),(0,a.jsxs)("div",{className:"absolute top-3 right-3 flex flex-col gap-2",children:[!r&&i&&i>0&&(0,a.jsxs)(l.P.div,{className:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg",initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring"},children:["%",i," \xdcye İndirimi"]}),(()=>{let t=Number(e.extraDiscount);return t>0&&(0,a.jsxs)(l.P.div,{className:"bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg",initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring"},children:["%",t," İndirim"]})})()]}),(0,a.jsxs)("div",{className:"absolute top-3 left-3 flex flex-col gap-1",children:[c>0&&(0,a.jsxs)(l.P.div,{className:"bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-2 py-1 rounded-full text-xs font-medium shadow-lg flex items-center gap-1",initial:{scale:0},animate:{scale:1},transition:{delay:.3,type:"spring"},children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,a.jsx)("span",{className:"font-bold",children:"PV"}),(0,a.jsx)("span",{children:c})]}),u>0&&(0,a.jsxs)(l.P.div,{className:"bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-2 py-1 rounded-full text-xs font-medium shadow-lg flex items-center gap-1",initial:{scale:0},animate:{scale:1},transition:{delay:.4,type:"spring"},children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,a.jsx)("span",{className:"font-bold",children:"CV"}),(0,a.jsx)("span",{children:u})]})]})]}),(0,a.jsxs)("div",{className:"p-5 flex-1 flex flex-col",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-1 text-gray-800",children:e.name}),(0,a.jsx)("p",{className:"text-gray-600 mb-2 text-sm",children:e.brandName}),(0,a.jsx)("p",{className:"text-gray-500 text-sm mb-4 line-clamp-2 flex-1",children:e.description}),(0,a.jsx)("div",{className:"mt-auto",children:(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"text-lg font-bold text-purple-700",children:[o.toFixed(2)," ₺"]}),d&&(0,a.jsxs)("span",{className:"text-sm text-gray-500 line-through ml-2",children:[e.price.toFixed(2)," ₺"]})]}),e.averageRating&&e.averageRating>0&&(0,a.jsxs)("div",{className:"flex items-center bg-yellow-50 px-2 py-1 rounded",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),(0,a.jsx)("span",{className:"ml-1 text-sm font-medium text-yellow-700",children:e.averageRating.toFixed(1)}),(0,a.jsxs)("span",{className:"ml-1 text-xs text-gray-500",children:["(",e.totalReviewCount,")"]})]})]})})]})]})})})}var $=s(13150),E=s(15908);function U(){(0,i.useSearchParams)().get("category");let{user:e,isAuthenticated:t}=(0,E.A)(),{data:s,isLoading:r}=(0,B.P)(),{isCustomerPrice:o,setIsCustomerPrice:d,resetCustomerPrice:c,_hasHydrated:u}=(0,O.w)(),{priceRange:h,sortBy:m,isFilterOpen:y,selectedBrandIds:b,selectedFeatureValueIds:v,brands:w,featureDefinitions:j,featureValues:N,getCurrentFilterRequest:k}=f(),{setFilterOpen:P,setPriceRange:C,setSortBy:A,setSelectedBrandIds:S,setSelectedFeatureValueIds:M,setBrands:I,setFeatureDefinitions:F,setFeatureValues:L}=f(),{data:z,isLoading:q,error:D}=x(k()),{data:T,isLoading:U,error:Z}=p(),_=z?.data||[],V={hidden:{opacity:0,y:20},show:{opacity:1,y:0}};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)(l.P.div,{className:"mb-6 text-center",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,a.jsx)("h1",{className:"text-4xl font-bold",children:"\xdcr\xfcnlerimiz"})}),(0,a.jsx)(R,{}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,a.jsx)("div",{className:"lg:hidden mb-4",children:(0,a.jsxs)(l.P.button,{whileTap:{scale:.95},onClick:()=>P(!y),className:"w-full bg-white rounded-lg border border-gray-200 px-4 py-3 flex items-center justify-between shadow-sm",children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Filtreler"}),(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-5 w-5 transition-transform  text-gray-700 ${y?"rotate-180":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]})}),(0,a.jsx)(l.P.aside,{className:`lg:block lg:w-1/4 ${y?"block":"hidden"}`,initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.2},children:(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-md p-6 sticky top-24",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6 pb-4 border-b border-gray-100",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-black",children:"Filtreler"}),(b.length>0||v.length>0)&&(0,a.jsxs)(l.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{S([]),M([])},className:"flex items-center space-x-1 px-2 py-1 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors",title:"Filtreleri Temizle",children:[(0,a.jsx)(n.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"Temizle"})]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h3",{className:"font-semibold mb-4 text-gray-800",children:"Fiyat Aralığı"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-700",children:[(0,a.jsxs)("span",{children:[h[0]," ₺"]}),(0,a.jsxs)("span",{children:[h[1]," ₺"]})]}),(0,a.jsx)("input",{type:"range",min:"0",max:"10000",step:"100",value:h[1],onChange:e=>C([h[0],parseInt(e.target.value)]),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-purple-600"}),(0,a.jsxs)("div",{className:"flex justify-between gap-4",children:[(0,a.jsxs)(l.P.div,{className:"relative flex-1",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,a.jsx)("input",{type:"number",min:"0",max:h[1]||1e4,value:h[0],onChange:e=>{C([parseInt(e.target.value)||0,h[1]])},className:"w-full p-2 pr-4 border border-gray-200 rounded-md text-sm text-gray-500 focus:outline-none focus:ring-1 focus:ring-purple-500"}),(0,a.jsx)("span",{className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-700 text-sm",children:"₺"})]}),(0,a.jsxs)(l.P.div,{className:"relative flex-1",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,a.jsx)("input",{type:"number",min:h[0]||0,max:"10000",value:h[1],onChange:e=>{C([h[0],parseInt(e.target.value)||1e4])},className:"w-full p-2 pr-4 border border-gray-200 rounded-md text-sm text-gray-500 focus:outline-none focus:ring-1 focus:ring-purple-500"}),(0,a.jsx)("span",{className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-700 text-sm",children:"₺"})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:"Marka"}),b.length>0&&(0,a.jsx)(l.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>S([]),className:"flex items-center space-x-1 px-1.5 py-0.5 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors",title:"Marka Filtrelerini Temizle",children:(0,a.jsx)(n.A,{className:"w-3 h-3"})})]}),(0,a.jsx)("div",{className:"space-y-3 max-h-48 overflow-y-auto overflow-x-hidden",children:w.map(e=>(0,a.jsx)($.A,{checked:b.includes(e.id),onChange:t=>{t?S([...b,e.id]):S(b.filter(t=>t!==e.id))},label:e.name,size:"sm",className:"hover:bg-gray-50 p-2 rounded-lg transition-colors"},e.id))})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:"\xd6zellikler"}),v.length>0&&(0,a.jsx)(l.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>M([]),className:"flex items-center space-x-1 px-1.5 py-0.5 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors",title:"\xd6zellik Filtrelerini Temizle",children:(0,a.jsx)(n.A,{className:"w-3 h-3"})})]}),(0,a.jsx)("div",{className:"space-y-4",children:j.map(e=>{let t=N.filter(t=>t.featureDefinitionId===e.id);return 0===t.length?null:(0,a.jsxs)("div",{className:"border-b border-gray-100 pb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-700 mb-3 truncate",children:e.name}),(0,a.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto overflow-x-hidden",children:t.map(e=>(0,a.jsx)($.A,{checked:v.includes(e.id),onChange:t=>{t?M([...v,e.id]):M(v.filter(t=>t!==e.id))},label:e.name,size:"sm",className:"hover:bg-gray-50 p-1.5 rounded-lg transition-colors"},e.id))})]},e.id)})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-3 text-gray-800",children:"Sıralama"}),(0,a.jsxs)(l.P.select,{whileHover:{scale:1.02},whileTap:{scale:.98},value:m,onChange:e=>A(parseInt(e.target.value)),className:"w-full p-2 border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500 text-gray-700",children:[(0,a.jsx)("option",{value:g.vn.Default,children:"\xd6ne \xc7ıkanlar"}),(0,a.jsx)("option",{value:g.vn.PriceAsc,children:"Fiyat: D\xfcş\xfckten Y\xfckseğe"}),(0,a.jsx)("option",{value:g.vn.PriceDesc,children:"Fiyat: Y\xfcksekten D\xfcş\xfcğe"}),(0,a.jsx)("option",{value:g.vn.RatingDesc,children:"Puana G\xf6re"})]})]})]})}),(0,a.jsxs)(l.P.div,{className:"lg:w-3/4",variants:{hidden:{opacity:0},show:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"show",children:[u&&!r&&s?.discountRate&&s.discountRate>0&&(0,a.jsxs)(l.P.div,{className:"mb-6 bg-white rounded-xl shadow-md p-4",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,a.jsx)($.A,{checked:o,onChange:d,label:"M\xfcşteri Fiyatlarını G\xf6ster (\xdcye indirimi uygulanmaz)",size:"md",className:"flex items-center gap-3"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-2 ml-8",children:["Bu se\xe7enek aktif olduğunda \xfcye indiriminiz (%",s.discountRate,") uygulanmaz ve \xfcr\xfcnler m\xfcşteri fiyatları ile g\xf6r\xfcnt\xfclenir."]})]}),q||U?(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-md overflow-hidden animate-pulse",children:[(0,a.jsx)("div",{className:"h-64 bg-gray-200"}),(0,a.jsxs)("div",{className:"p-5",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-2"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded mb-4 w-2/3"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded mb-4"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded"})]})]},t))}):D||Z?(0,a.jsxs)(l.P.div,{className:"bg-white rounded-xl p-8 text-center shadow-md",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 mx-auto text-red-400 mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("h3",{className:"text-xl font-bold mb-2",children:"Hata Oluştu"}),(0,a.jsx)("p",{className:"text-gray-700 mb-4",children:"\xdcr\xfcnler y\xfcklenirken bir hata oluştu. L\xfctfen sayfayı yenileyin."}),(0,a.jsx)("p",{className:"text-sm text-red-600",children:D?.message||Z?.message})]}):0===_.length?(0,a.jsxs)(l.P.div,{className:"bg-white rounded-xl p-8 text-center shadow-md",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 mx-auto text-gray-400 mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("h3",{className:"text-xl font-bold mb-2",children:"\xdcr\xfcn Bulunamadı"}),(0,a.jsx)("p",{className:"text-gray-700 mb-4",children:"Se\xe7tiğiniz filtrelere uygun \xfcr\xfcn bulunmamaktadır. L\xfctfen filtrelerinizi değiştirerek tekrar deneyin."}),(0,a.jsx)(l.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{let{resetFilters:e}=f.getState();e()},className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors",children:"Filtreleri Temizle"})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:_.map(e=>(0,a.jsx)(l.P.div,{variants:V,className:"group",children:(0,a.jsx)(H,{product:e})},e.id))})]})]})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,181,658,85],()=>s(48975));module.exports=a})();