(()=>{var e={};e.id=5,e.ids=[5],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13150:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(60687),a=r(26001),i=r(13964);function n({checked:e,onChange:t,label:r,disabled:n=!1,size:l="md",className:o=""}){let d={sm:{checkbox:"w-4 h-4",text:"text-xs",icon:"w-2.5 h-2.5"},md:{checkbox:"w-5 h-5",text:"text-sm",icon:"w-3 h-3"},lg:{checkbox:"w-6 h-6",text:"text-base",icon:"w-4 h-4"}}[l];return(0,s.jsxs)(a.P.label,{className:`flex items-center space-x-3 cursor-pointer select-none ${n?"opacity-50 cursor-not-allowed":""} ${o}`,whileHover:n?{}:{scale:1.01},whileTap:n?{}:{scale:.99},children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"checkbox",checked:e,onChange:e=>!n&&t(e.target.checked),disabled:n,className:"sr-only"}),(0,s.jsx)(a.P.div,{className:`
                        ${d.checkbox}
                        rounded-md
                        border-2
                        flex
                        items-center
                        justify-center
                        transition-all
                        duration-200
                        ${e?"bg-gradient-to-br from-purple-500 to-purple-700 border-purple-600 shadow-lg shadow-purple-500/25":"bg-white border-gray-300 hover:border-purple-400"}
                        ${!n&&"hover:shadow-md"}
                    `,initial:!1,animate:{scale:e?1.05:1,boxShadow:e?"0 10px 25px -5px rgba(147, 51, 234, 0.25), 0 4px 6px -2px rgba(147, 51, 234, 0.05)":"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"},transition:{duration:.2,ease:"easeInOut"},children:(0,s.jsx)(a.P.div,{initial:!1,animate:{scale:+!!e,opacity:+!!e},transition:{duration:.15,ease:"easeInOut"},children:(0,s.jsx)(i.A,{className:`${d.icon} text-white stroke-[3]`})})})]}),(0,s.jsx)("span",{className:`
                    ${d.text} 
                    text-gray-700 
                    font-medium 
                    truncate 
                    flex-1
                    ${!n&&"group-hover:text-gray-900"}
                `,children:r})]})}},13964:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43184:(e,t,r)=>{Promise.resolve().then(r.bind(r,49483))},48375:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["cart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,70905)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\cart\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\cart\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/cart/page",pathname:"/cart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},49483:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687),a=r(83515),i=r(30474),n=r(85814),l=r.n(n),o=r(26001),d=r(13150);function c(){let{data:e,isLoading:t,error:r,refetch:n}=(0,a.y$)(),{data:c}=(0,a.PL)(),x=(0,a.IM)(),p=(0,a.Yu)(),m=(0,a.vG)(),u=e?.items||[],h=e?.isCustomerPrice||!1,g=c?.discountRate||0;console.log("\uD83D\uDD0D Discount Data Debug:",{discountData:c,discountRate:g,isCustomerPrice:h});let v=async()=>{try{await m.mutateAsync()}catch(e){console.error("Sepet tipi g\xfcncelleme hatası:",e)}},y=async e=>{try{console.log("\uD83D\uDD0D handleRemoveFromCart \xe7ağrıldı, productVariantId:",e),await x.mutateAsync(e)}catch(e){console.error("Sepetten \xfcr\xfcn \xe7ıkarma hatası:",e)}},b=async(e,t)=>{if(t<=0)return void await y(e);try{await p.mutateAsync({productVariantId:e,quantity:t})}catch(e){console.error("Sepet \xfcr\xfcn miktarı g\xfcncelleme hatası:",e)}},f=0===u.length?{totalPrice:0,totalPV:0,totalCV:0,totalSP:0}:u.reduce((e,t)=>{let r=t.quantity,s=t.price;!h&&g&&g>0&&(s*=1-g/100);let a=t.extraDiscount||0;a>0&&(s*=1-a/100);let i=t.price*(t.pv/100),n=t.price*(t.cv/100),l=t.price*(t.sp/100);return{totalPrice:e.totalPrice+s*r,totalPV:e.totalPV+i*r,totalCV:e.totalCV+n*r,totalSP:e.totalSP+l*r}},{totalPrice:0,totalPV:0,totalCV:0,totalSP:0});return t?(0,s.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)(o.P.div,{initial:{opacity:0},animate:{opacity:1},className:"flex flex-col items-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Sepetiniz y\xfckleniyor..."})]})})}):r?(0,s.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto",children:[(0,s.jsx)("div",{className:"text-red-600 mb-4",children:(0,s.jsx)("svg",{className:"h-12 w-12 mx-auto",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:"Sepet Y\xfcklenemedi"}),(0,s.jsx)("p",{className:"text-red-600 mb-4",children:"Sepetiniz y\xfcklenirken bir hata oluştu."}),(0,s.jsx)("button",{onClick:()=>n(),className:"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors",children:"Tekrar Dene"})]})})}):0===u.length?(0,s.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-24 w-24 text-gray-400 mx-auto mb-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Sepetiniz Boş"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8 max-w-md mx-auto",children:"Hen\xfcz sepetinizde \xfcr\xfcn bulunmuyor. Alışverişe başlamak i\xe7in \xfcr\xfcnlerimizi keşfedin."}),(0,s.jsx)(o.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,s.jsxs)(l(),{href:"/products",className:"bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 inline-flex items-center space-x-2",children:[(0,s.jsx)("span",{children:"Alışverişe Başla"}),(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]})})]})})}):(0,s.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("h1",{className:"text-3xl font-bold text-white",children:["Sepetim (",u.length," \xfcr\xfcn)"]})}),(0,s.jsxs)(o.P.div,{className:"mb-6 bg-white rounded-xl shadow-md p-4",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,s.jsx)(d.A,{checked:h,onChange:v,label:`M\xfcşteri Fiyatlarını G\xf6ster ${g>0?"(\xdcye indirimi uygulanmaz)":""}`,size:"md",className:"flex items-center gap-3",disabled:m.isPending}),g>0&&(0,s.jsxs)("p",{className:"text-sm text-gray-600 mt-2 ml-8",children:["Bu se\xe7enek aktif olduğunda \xfcye indiriminiz (%",g,") uygulanmaz ve \xfcr\xfcnler m\xfcşteri fiyatları ile g\xf6r\xfcnt\xfclenir."]}),m.isPending&&(0,s.jsx)("p",{className:"text-sm text-blue-600 mt-2 ml-8",children:"G\xfcncelleniyor..."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsx)("div",{className:"lg:col-span-2 space-y-4",children:u.map((e,t)=>{let r=e.price,a=!1;if(h){let t=e.extraDiscount||0;t>0&&(r*=1-t/100,a=!0)}else{g&&g>0&&(r*=1-g/100,a=!0);let t=e.extraDiscount||0;t>0&&(r*=1-t/100,a=!0)}let n=e.price*(e.pv/100),l=e.price*(e.cv/100),d=e.price*(e.sp/100);return console.log("\uD83D\uDCB0 Fiyat & Puan Debug:",{productName:e.productName,originalPrice:e.price,finalPrice:r,isCustomerPrice:h,discountRate:g,extraDiscount:e.extraDiscount,hasDiscount:a,pv:e.pv,cv:e.cv,sp:e.sp,calculatedPV:n,calculatedCV:l,calculatedSP:d}),(0,s.jsx)(o.P.div,{className:"bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*t,duration:.5},children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"relative w-20 h-20 flex-shrink-0",children:(0,s.jsx)(i.default,{src:e.mainImageUrl,alt:e.productName,fill:!0,className:"object-cover rounded-lg"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:e.productName}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:e.brandName}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,s.jsxs)("span",{className:"text-lg font-bold text-purple-700",children:[(r*e.quantity).toFixed(2)," ₺"]}),a&&(0,s.jsxs)("span",{className:"text-sm text-gray-500 line-through",children:[(e.price*e.quantity).toFixed(2)," ₺"]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[!h&&g&&g>0&&(0,s.jsxs)("span",{className:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-2 py-1 rounded-full text-xs font-medium",children:["%",g," \xdcye İndirimi"]}),(()=>{let t=e.extraDiscount||0;return t>0&&(0,s.jsxs)("span",{className:"bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-medium",children:["%",t," İndirim"]})})()]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[n>0&&(0,s.jsxs)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium",children:["PV: ",(n*e.quantity).toFixed(0)]}),l>0&&(0,s.jsxs)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium",children:["CV: ",(l*e.quantity).toFixed(0)]}),d>0&&(0,s.jsxs)("span",{className:"bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full font-medium",children:["SP: ",(d*e.quantity).toFixed(0)]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)("div",{className:"flex items-center border border-gray-300 rounded-lg",children:[(0,s.jsx)(o.P.button,{onClick:()=>b(e.variantId,e.quantity-1),className:"p-2 hover:bg-gray-100 transition-colors text-purple-800 disabled:opacity-50 disabled:cursor-not-allowed",whileTap:{scale:.9},disabled:e.quantity<=1||p.isPending,children:p.isPending?(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"}):(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 12H4"})})}),(0,s.jsx)("span",{className:"px-4 py-2 text-gray-800 font-medium",children:e.quantity}),(0,s.jsx)(o.P.button,{onClick:()=>b(e.variantId,e.quantity+1),className:"p-2 hover:bg-gray-100 transition-colors text-purple-800 disabled:opacity-50 disabled:cursor-not-allowed",whileTap:{scale:.9},disabled:e.quantity>=e.stock||p.isPending,children:p.isPending?(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"}):(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})})]}),(0,s.jsx)(o.P.button,{onClick:()=>y(e.variantId),className:"text-red-600 hover:text-red-700 p-2",whileHover:{scale:1.1},whileTap:{scale:.9},disabled:x.isPending,children:x.isPending?(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-red-600"}):(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})},e.variantId)})}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsxs)(o.P.div,{className:"bg-white rounded-lg p-6 shadow-md sticky top-8",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.3,duration:.6},children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-6",children:"Sipariş \xd6zeti"}),(0,s.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,s.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,s.jsx)("span",{children:"\xdcr\xfcn Toplamı:"}),(0,s.jsxs)("span",{children:[f.totalPrice.toFixed(2)," ₺"]})]}),(0,s.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,s.jsx)("span",{children:"Kargo:"}),(0,s.jsx)("span",{className:"text-green-600",children:"\xdccretsiz"})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg space-y-2",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Kazanacağınız Puanlar:"}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[f.totalPV>0&&(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium block",children:["PV: ",f.totalPV.toFixed(0)]})}),f.totalCV>0&&(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium block",children:["CV: ",f.totalCV.toFixed(0)]})}),f.totalSP>0&&(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("span",{className:"bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full font-medium block",children:["SP: ",f.totalSP.toFixed(0)]})})]})]}),(0,s.jsx)("div",{className:"border-t pt-3",children:(0,s.jsxs)("div",{className:"flex justify-between text-lg font-bold text-gray-800",children:[(0,s.jsx)("span",{children:"Toplam:"}),(0,s.jsxs)("span",{className:"text-purple-700",children:[f.totalPrice.toFixed(2)," ₺"]})]})})]}),(0,s.jsx)(l(),{href:"/checkout",children:(0,s.jsx)(o.P.button,{className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},children:"\xd6demeye Ge\xe7"})}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsxs)(l(),{href:"/products",className:"text-purple-600 hover:text-purple-700 font-medium inline-flex items-center space-x-1",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),(0,s.jsx)("span",{children:"Alışverişe Devam Et"})]})})]})})]})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var s=r(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:c,...x},p)=>(0,s.createElement)("svg",{ref:p,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:l("lucide",i),...!n&&!o(x)&&{"aria-hidden":"true"},...x},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(n)?n:[n]])),x=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...i},o)=>(0,s.createElement)(c,{ref:o,iconNode:t,className:l(`lucide-${a(n(e))}`,`lucide-${e}`,r),...i}));return r.displayName=n(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70905:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\cart\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\cart\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85040:(e,t,r)=>{Promise.resolve().then(r.bind(r,70905))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,181,658,85],()=>r(48375));module.exports=s})();