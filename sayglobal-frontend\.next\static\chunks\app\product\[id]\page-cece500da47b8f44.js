(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1e3],{9200:(e,t,a)=>{Promise.resolve().then(a.bind(a,60194))},13051:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(95155),s=a(60760),l=a(76408),n=a(6874),i=a.n(n),o=a(66766);function c(e){let{isOpen:t,onClose:a,product:n,isAdded:c}=e;return(0,r.jsx)(s.N,{children:t&&(0,r.jsxs)(l.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:a,children:[(0,r.jsx)("div",{className:"absolute inset-0"}),(0,r.jsxs)(l.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.7,opacity:0,y:50},transition:{type:"spring",stiffness:300,damping:25,duration:.5},onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)(l.P.div,{className:"text-center mb-6",initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},children:[(0,r.jsx)("div",{className:"mx-auto w-20 h-20 ".concat(c?"bg-red-100":"bg-gray-100"," rounded-full flex items-center justify-center mb-4"),children:(0,r.jsx)(l.P.svg,{className:"w-10 h-10 ".concat(c?"text-red-600":"text-gray-500"),fill:c?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24",initial:{scale:0},animate:{scale:1},transition:{delay:.3,type:"spring",stiffness:200},children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})}),(0,r.jsx)(l.P.h2,{className:"text-2xl font-bold text-gray-800 mb-2",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:c?"Favorilere Eklendi!":"Favorilerden \xc7ıkarıldı!"}),(0,r.jsx)(l.P.p,{className:"text-gray-600",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4},children:c?"\xdcr\xfcn favori listenize başarıyla eklendi.":"\xdcr\xfcn favori listenizden \xe7ıkarıldı."})]}),n&&(0,r.jsxs)(l.P.div,{className:"".concat(c?"bg-red-50 border-red-200":"bg-gray-50 border-gray-200"," border rounded-lg p-4 mb-6 flex items-center space-x-3"),initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,r.jsx)("div",{className:"relative w-16 h-16 flex-shrink-0",children:(0,r.jsx)(o.default,{src:n.thumbnail,alt:n.title,fill:!0,className:"object-cover rounded-lg"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 truncate",children:n.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:n.brand}),(0,r.jsx)("p",{className:"text-sm font-medium text-purple-600",children:n.discountPercentage?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("span",{className:"line-through text-gray-400 mr-2",children:["₺",n.price.toFixed(2)]}),"₺",(n.price*(1-n.discountPercentage/100)).toFixed(2)]}):"₺".concat(n.price.toFixed(2))})]})]}),(0,r.jsxs)(l.P.div,{className:"flex flex-col space-y-3",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},children:[(0,r.jsx)(i(),{href:"/account?tab=favorites",children:(0,r.jsx)(l.P.button,{className:"w-full ".concat(c?"bg-gradient-to-r from-red-600 to-pink-600":"bg-gradient-to-r from-gray-600 to-gray-700"," text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300"),whileHover:{scale:1.02},whileTap:{scale:.98},onClick:a,children:"Favorilerime Git"})}),(0,r.jsx)(l.P.button,{className:"w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},onClick:a,children:"Alışverişe Devam Et"})]}),(0,r.jsx)(l.P.button,{onClick:a,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors",initial:{opacity:0},animate:{opacity:1},transition:{delay:.7},whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]})})}},13052:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},13841:(e,t,a)=>{"use strict";a.d(t,{$9:()=>c,AP:()=>u,IM:()=>x,PL:()=>d,Yu:()=>m,vG:()=>h,y$:()=>o});var r=a(32960),s=a(26715),l=a(5041),n=a(80722),i=a(87220);let o=()=>{let{isAuthenticated:e}=(0,i.A)();return(0,r.I)({queryKey:["cartItems",e],queryFn:async()=>{let e=await n.CV.getCartItems();if(e.success)return e.data.data;throw Error(e.error||"Sepet i\xe7erikleri alınamadı")},staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0})},c=()=>{let{isAuthenticated:e}=(0,i.A)();return(0,r.I)({queryKey:["cartCount",e],queryFn:async()=>{let e=await n.CV.getCartCount();if(e.success)return e.data.data;throw Error(e.error||"Sepet \xfcr\xfcn sayısı alınamadı")},staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0})},d=()=>(0,r.I)({queryKey:["discountRate"],queryFn:async()=>{try{let e=await n.Dv.getDiscountRate();if(console.log("\uD83D\uDD0D Discount Rate API Response:",e),e.success)return e.data||{discountRate:0};return console.warn("İndirim oranı alınamadı:",e.error),{discountRate:0}}catch(e){return console.warn("İndirim oranı alınırken hata:",e),{discountRate:0}}},staleTime:3e5,refetchOnWindowFocus:!1,refetchOnMount:!0,retry:!1}),u=()=>{let e=(0,s.jE)();return(0,l.n)({mutationFn:async e=>{let{productVariantId:t,quantity:a,isCustomerPrice:r}=e,s=await n.CV.addToCart(t,a,r);if(!s.success)throw Error(s.error||"\xdcr\xfcn sepete eklenemedi");return s.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepete \xfcr\xfcn ekleme hatası:",e)}})},x=()=>{let e=(0,s.jE)();return(0,l.n)({mutationFn:async e=>{let t=await n.CV.removeFromCart(e);if(!t.success)throw Error(t.error||"\xdcr\xfcn sepetten \xe7ıkarılamadı");return t.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepetten \xfcr\xfcn \xe7ıkarma hatası:",e)}})},m=()=>{let e=(0,s.jE)();return(0,l.n)({mutationFn:async e=>{let{productVariantId:t,quantity:a}=e,r=await n.CV.updateCartQuantity(t,a);if(!r.success)throw Error(r.error||"\xdcr\xfcn miktarı g\xfcncellenemedi");return r.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepet \xfcr\xfcn miktarı g\xfcncelleme hatası:",e)}})},h=()=>{let e=(0,s.jE)();return(0,l.n)({mutationFn:async()=>{let e=await n.Dv.updateCartType();if(!e.success)throw Error(e.error||"Sepet tipi g\xfcncellenemedi");return e.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepet tipi g\xfcncelleme hatası:",e)}})}},19946:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var r=a(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()},o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,t)=>{let{color:a="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:d="",children:u,iconNode:x,...m}=e;return(0,r.createElement)("svg",{ref:t,...c,width:s,height:s,stroke:a,strokeWidth:n?24*Number(l)/Number(s):l,className:i("lucide",d),...!u&&!o(m)&&{"aria-hidden":"true"},...m},[...x.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let a=(0,r.forwardRef)((a,l)=>{let{className:o,...c}=a;return(0,r.createElement)(d,{ref:l,iconNode:t,className:i("lucide-".concat(s(n(e))),"lucide-".concat(e),o),...c})});return a.displayName=n(e),a}},42355:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},54416:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},60194:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>O});var r=a(95155),s=a(12115),l=a(35695),n=a(66766),i=a(6874),o=a.n(i),c=a(76408),d=a(60760),u=a(42355),x=a(13052),m=a(5323),h=a(69848),p=a(61355),g=a(13051),f=a(54416),v=a(65453),w=a(46786);let y=null,j=null,b=null,N=null,k=[],C=null,P=[],D={productData:null,selectedVariantIndex:0,currentImageIndex:0,slideDirection:0,isLoading:!1,error:null,cache:new Map,quantity:1},I=(0,v.v)()((0,w.lt)((e,t)=>({...D,setProductData:a=>{t().productData!==a&&e({productData:a,selectedVariantIndex:0,currentImageIndex:0,slideDirection:0,error:null},!1,"catalogProductDetail/setProductData")},setLoading:t=>{e({isLoading:t},!1,"catalogProductDetail/setLoading")},setError:t=>{e({error:t,isLoading:!1},!1,"catalogProductDetail/setError")},setSelectedVariant:a=>{var r;let s=(null==(r=t().productData)?void 0:r.data.variants.length)||0;a>=0&&a<s&&e({selectedVariantIndex:a,currentImageIndex:0},!1,"catalogProductDetail/setSelectedVariant")},setCurrentImage:a=>{var r;let s=t(),l=null==(r=s.productData)?void 0:r.data.variants[s.selectedVariantIndex],n=(null==l?void 0:l.images.length)||0;a>=0&&a<n&&e({currentImageIndex:a},!1,"catalogProductDetail/setCurrentImage")},nextImage:()=>{var a;let r=t(),s=null==(a=r.productData)?void 0:a.data.variants[r.selectedVariantIndex],l=(null==s?void 0:s.images.length)||0;l>0&&e({currentImageIndex:(r.currentImageIndex+1)%l,slideDirection:1},!1,"catalogProductDetail/nextImage")},prevImage:()=>{var a;let r=t(),s=null==(a=r.productData)?void 0:a.data.variants[r.selectedVariantIndex],l=(null==s?void 0:s.images.length)||0;l>0&&e({currentImageIndex:0===r.currentImageIndex?l-1:r.currentImageIndex-1,slideDirection:-1},!1,"catalogProductDetail/prevImage")},setQuantity:t=>{t>=1&&e({quantity:t},!1,"catalogProductDetail/setQuantity")},increaseQuantity:()=>{e({quantity:t().quantity+1},!1,"catalogProductDetail/increaseQuantity")},decreaseQuantity:()=>{let a=t();a.quantity>1&&e({quantity:a.quantity-1},!1,"catalogProductDetail/decreaseQuantity")},setCachedProduct:(a,r)=>{let s=new Map(t().cache);s.set(a,{data:r,timestamp:Date.now()}),e({cache:s},!1,"catalogProductDetail/setCachedProduct")},getCachedProduct:e=>{let a=t().cache.get(e);return(null==a?void 0:a.data)||null},isCacheValid:function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3e5,r=t().cache.get(e);return!!r&&Date.now()-r.timestamp<a},clearCache:()=>{e({cache:new Map},!1,"catalogProductDetail/clearCache")},clearProductCache:a=>{let r=new Map(t().cache);r.delete(a),e({cache:r},!1,"catalogProductDetail/clearProductCache")},resetState:()=>{y=null,j=null,b=null,N=null,k=P,C=null,e({...D,cache:new Map},!1,"catalogProductDetail/resetState")}}),{name:"catalog-product-detail-store"})),L=()=>I(e=>e.slideDirection),S=()=>I(e=>e.currentImageIndex),E=()=>I(e=>e.quantity),M=()=>I(e=>{let{productData:t,selectedVariantIndex:a}=e;if(!(null==t?void 0:t.data))return null;let r="".concat(t.data.id,"-").concat(a);if(r===j&&null!==y)return y;let s=t.data.variants[a]||null;return y=s,j=r,s}),F=()=>I(e=>{let{productData:t,selectedVariantIndex:a,currentImageIndex:r}=e;if(!(null==t?void 0:t.data))return null;let s="".concat(t.data.id,"-").concat(a,"-").concat(r);if(console.log("\uD83D\uDD0D useCurrentImage called:",{currentKey:s,lastCurrentImageKey:N,hasCache:!!b}),s===N&&null!==b)return console.log("✅ Returning cached image"),b;let l=t.data.variants[a],n=(null==l?void 0:l.images[r])||null;return b=n,N=s,console.log("\uD83D\uDD04 Updating image cache"),n}),A=()=>I(e=>{var t,a;let{productData:r,selectedVariantIndex:s}=e;if(!(null==r?void 0:r.data))return P;let l=r.data.variants[s];if((null==l?void 0:l.id)===C)return k;let n=null!=(t=null==l?void 0:l.images)?t:P;return k=n,C=null!=(a=null==l?void 0:l.id)?a:null,n});function z(e){let{isOpen:t,onClose:a,images:l,currentImageIndex:i,onImageChange:o,productName:m}=e,h=(0,s.useRef)(null),p=L();(0,s.useEffect)(()=>{let e=e=>{if(t)switch(e.key){case"Escape":a();break;case"ArrowLeft":e.preventDefault(),g();break;case"ArrowRight":e.preventDefault(),v()}};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[t,i,l.length]),(0,s.useEffect)(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]);let g=()=>{if(l.length>0){let e=i>0?i-1:l.length-1;I.setState({slideDirection:-1}),setTimeout(()=>{o(e)},0)}},v=()=>{if(l.length>0){let e=i<l.length-1?i+1:0;I.setState({slideDirection:1}),setTimeout(()=>{o(e)},0)}};if(!t||0===l.length)return null;let w=l[i];return(0,r.jsx)(d.N,{children:t&&(0,r.jsxs)(c.P.div,{ref:h,className:"fixed inset-0 z-50 flex items-center justify-center bg-black/90 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},onClick:e=>{e.target===h.current&&a()},children:[(0,r.jsx)(c.P.button,{className:"absolute top-4 right-4 z-10 bg-white/10 hover:bg-white/20 text-white rounded-full p-2 transition-colors",onClick:a,whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsx)(f.A,{className:"h-6 w-6"})}),(0,r.jsxs)("div",{className:"absolute top-4 left-4 z-10 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:[i+1," / ",l.length]}),(0,r.jsxs)("div",{className:"relative w-full h-full max-w-6xl max-h-[90vh] mx-4",children:[(0,r.jsx)(d.N,{mode:"wait",children:(0,r.jsx)(c.P.div,{className:"relative w-full h-full",initial:0!==p?{x:p>0?300:-300,opacity:0}:{opacity:1},animate:{x:0,opacity:1},exit:0!==p?{x:p>0?-300:300,opacity:0}:{opacity:0},transition:{type:"tween",ease:"easeOut",duration:.2*(0!==p),delay:.1*(0!==p)},children:(0,r.jsx)(n.default,{src:w.url,alt:"".concat(m," - ").concat(i+1),fill:!0,className:"object-contain",priority:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"})},i)}),l.length>1&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.P.button,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/10 hover:bg-white/20 text-white rounded-full p-3 transition-colors",onClick:g,whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsx)(u.A,{className:"h-8 w-8"})}),(0,r.jsx)(c.P.button,{className:"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/10 hover:bg-white/20 text-white rounded-full p-3 transition-colors",onClick:v,whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsx)(x.A,{className:"h-8 w-8"})})]})]}),l.length>1&&(0,r.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10",children:(0,r.jsx)("div",{className:"flex space-x-2 bg-black/50 rounded-lg p-2 max-w-xs overflow-x-auto",children:l.map((e,t)=>(0,r.jsx)(c.P.button,{className:"relative w-12 h-12 rounded-md overflow-hidden border-2 transition-all ".concat(t===i?"border-white":"border-transparent opacity-60 hover:opacity-80"),onClick:()=>{let e=t>i?1:t<i?-1:0;I.setState({slideDirection:e}),setTimeout(()=>{o(t)},0)},whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsx)(n.default,{src:e.url,alt:"".concat(m," thumbnail ").concat(t+1),fill:!0,className:"object-cover",sizes:"48px"})},e.id))})}),(0,r.jsx)("div",{className:"absolute bottom-20 left-1/2 transform -translate-x-1/2 z-10 bg-black/50 text-white px-4 py-2 rounded-lg text-center max-w-md",children:(0,r.jsx)("h3",{className:"font-medium truncate",children:m})})]})})}var q=a(32960),V=a(80722);let T=e=>{let t=I(),a=(0,s.useMemo)(()=>e?t.getCachedProduct(e):null,[e,t.getCachedProduct]),r=(0,s.useMemo)(()=>!!e&&t.isCacheValid(e),[e,t.isCacheValid]),l=(0,q.I)({queryKey:["catalogProductDetail",e],queryFn:async()=>{if(!e)throw Error("Product ID is required");if(r&&a)return console.log("\uD83C\uDFAF Using cached catalog product data for ID:",e),a;console.log("\uD83C\uDF10 Fetching catalog product from API for ID:",e),t.setLoading(!0);try{let a=await V.jU.getCatalogProductDetail(e);if(!a.success)throw Error(a.error||"\xdcr\xfcn detayı alınamadı");return t.setCachedProduct(e,a.data),console.log("✅ Catalog product fetched and cached for ID:",e),a.data}catch(e){throw console.error("❌ Error fetching catalog product:",e),t.setError(e instanceof Error?e.message:"\xdcr\xfcn detayı alınamadı"),e}finally{t.setLoading(!1)}},enabled:!!e,staleTime:3e5,gcTime:6e5,refetchOnWindowFocus:!1,retry:(e,t)=>(!t||!("status"in t)||404!==t.status)&&e<2});return(0,s.useEffect)(()=>{l.data?(t.setProductData(l.data),t.setError(null)):l.error&&(t.setError(l.error instanceof Error?l.error.message:"\xdcr\xfcn detayı alınamadı"),t.setProductData(null))},[l.data,l.error]),(0,s.useEffect)(()=>{t.setLoading(l.isLoading)},[l.isLoading]),{data:l.data,isLoading:l.isLoading,error:l.error,isError:l.isError,refetch:l.refetch,isFetching:l.isFetching,isSuccess:l.isSuccess}};var R=a(53234),W=a(13841),B=a(57038);function O(){let{id:e}=(0,l.useParams)(),[t,a]=(0,s.useState)(null),[i,f]=(0,s.useState)(!1),[v,w]=(0,s.useState)(null),[y,j]=(0,s.useState)(!1),[b,N]=(0,s.useState)(!0),[k,C]=(0,s.useState)(!1),{addToCart:P}=(0,m._)(),{addToFavorites:D,removeFromFavorites:q,isFavorite:V}=(0,h.r)(),{data:O,isLoading:H}=(0,R.P)(),Q=(0,W.AP)(),{isCustomerPrice:K}=(0,B.w)(),{setSelectedVariant:_,setCurrentImage:U,increaseQuantity:G,decreaseQuantity:$,resetState:J}=I(),Y=M(),Z=F(),X=A(),ee=E(),et=L(),ea=S(),{data:er,isLoading:es,error:el}=T(t);if((0,s.useEffect)(()=>{if(e){let t=parseInt(e);isNaN(t)||a(t)}},[e]),(0,s.useEffect)(()=>()=>{J()},[]),es)return(0,r.jsx)("div",{className:"container mx-auto px-4 py-16 text-center",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-md max-w-lg mx-auto",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-purple-600 mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"\xdcr\xfcn Y\xfckleniyor..."}),(0,r.jsx)("p",{className:"text-gray-600",children:"\xdcr\xfcn detayları getiriliyor, l\xfctfen bekleyin."})]})});if(el||!er)return(0,r.jsx)("div",{className:"container mx-auto px-4 py-16 text-center",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-md max-w-lg mx-auto",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 mx-auto text-gray-400 mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"\xdcr\xfcn Bulunamadı"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:(null==el?void 0:el.message)||"Aradığınız \xfcr\xfcn bulunamadı veya artık mevcut değil."}),(0,r.jsx)(o(),{href:"/products",className:"inline-block bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300",children:"\xdcr\xfcnlere D\xf6n"})]})});let en=er.data,ei=e=>{switch(e){case 0:return{text:"Stokta yok",color:"text-red-600"};case 1:return{text:"Az stok",color:"text-yellow-600"};case 2:return{text:"Stokta var",color:"text-green-600"};default:return{text:"Bilinmiyor",color:"text-gray-600"}}},eo=(e,t)=>Math.round(e/100*t),ec=function(e,t){let a=!(arguments.length>2)||void 0===arguments[2]||arguments[2],r=e,s=(null==O?void 0:O.discountRate)||null;return a&&!K&&s&&s>0&&(r*=1-s/100),t>0&&(r*=1-t/100),r},ed=function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],a=(null==O?void 0:O.discountRate)||null;return t&&a&&a>0||e>0},eu=e=>{var t;e&&e.stopPropagation();let{setCurrentImage:a}=I.getState(),r=I.getState(),s=null==(t=r.productData)?void 0:t.data.variants[r.selectedVariantIndex],l=(null==s?void 0:s.images.length)||0;if(l>0){let e=0===r.currentImageIndex?l-1:r.currentImageIndex-1;I.setState({slideDirection:-1}),setTimeout(()=>{a(e)},0)}},ex=e=>{var t;e&&e.stopPropagation();let{setCurrentImage:a}=I.getState(),r=I.getState(),s=null==(t=r.productData)?void 0:t.data.variants[r.selectedVariantIndex],l=(null==s?void 0:s.images.length)||0;if(l>0){let e=(r.currentImageIndex+1)%l;I.setState({slideDirection:1}),setTimeout(()=>{a(e)},0)}},em=async()=>{if(en&&Y&&Z)try{await Q.mutateAsync({productVariantId:Y.id,quantity:ee,isCustomerPrice:K});let e=(null==O?void 0:O.discountRate)||null,t=Y.extraDiscount||0,a={id:Y.id,title:en.name,price:Y.price,discountedPrice:ec(Y.price,Y.extraDiscount,!K),thumbnail:Z.url,brand:en.brandName,membershipDiscount:!K&&e&&e>0?e:0,extraDiscount:t,pvPoints:eo(Y.pv,Y.price),cvPoints:eo(Y.cv,Y.price),spPoints:eo(Y.sp,Y.price),quantity:ee};P(a),w(a),f(!0)}catch(e){console.error("Sepete ekleme sırasında hata:",e)}};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)(o(),{href:"/products",className:"inline-flex items-center text-gray-600 hover:text-purple-600 transition-colors",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"\xdcr\xfcnlere D\xf6n"]})}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-10",children:[(0,r.jsxs)(c.P.div,{className:"lg:w-1/2",initial:{opacity:0,x:-30},animate:{opacity:1,x:0},transition:{duration:.5},children:[(0,r.jsx)("div",{className:"bg-white rounded-xl overflow-hidden shadow-md mb-4",children:(0,r.jsxs)("div",{className:"relative h-96 w-full cursor-pointer",onClick:()=>C(!0),children:[(0,r.jsx)(d.N,{mode:"wait",children:Z&&(0,r.jsx)(c.P.div,{initial:0!==et?{x:et>0?300:-300,opacity:0}:{opacity:1},animate:{x:0,opacity:1},exit:0!==et?{x:et>0?-300:300,opacity:0}:{opacity:0},transition:{type:"tween",ease:"easeOut",duration:.2*(0!==et),delay:.1*(0!==et)},className:"absolute inset-0",children:(0,r.jsx)(n.default,{src:Z.url,alt:en.name,fill:!0,className:"object-contain"})},ea)}),X.length>1&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:e=>eu(e),className:"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 hover:text-gray-900 rounded-full p-2 shadow-lg transition-all duration-300 z-10",children:(0,r.jsx)(u.A,{className:"h-6 w-6"})}),(0,r.jsx)(c.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:e=>ex(e),className:"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 hover:text-gray-900 rounded-full p-2 shadow-lg transition-all duration-300 z-10",children:(0,r.jsx)(x.A,{className:"h-6 w-6"})})]}),Y&&Y.extraDiscount>0&&(0,r.jsxs)("div",{className:"absolute top-4 right-4 bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg",children:["%",Y.extraDiscount," İndirim"]}),!K&&(null==O?void 0:O.discountRate)&&O.discountRate>0&&(0,r.jsxs)("div",{className:"absolute ".concat(Y&&Y.extraDiscount>0?"top-12":"top-4"," right-4 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg"),children:["%",O.discountRate," \xdcye İndirimi"]}),Y&&(0,r.jsxs)("div",{className:"absolute top-4 left-4 flex flex-col space-y-2",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,r.jsx)("span",{className:"font-bold",children:"PV"}),(0,r.jsx)("span",{children:eo(Y.pv,Y.price)})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,r.jsx)("span",{className:"font-bold",children:"CV"}),(0,r.jsx)("span",{children:eo(Y.cv,Y.price)})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,r.jsx)("span",{className:"font-bold",children:"SP"}),(0,r.jsx)("span",{children:eo(Y.sp,Y.price)})]})]})]})}),(0,r.jsx)("div",{className:"grid grid-cols-5 gap-2",children:X.map((e,t)=>(0,r.jsx)(c.P.div,{className:"relative border-2 rounded-lg overflow-hidden cursor-pointer transition-all duration-200 ".concat((null==Z?void 0:Z.id)===e.id?"border-purple-500":"border-gray-200"),whileHover:{scale:1.05},onClick:()=>{I.setState({slideDirection:t>ea?1:t<ea?-1:0}),setTimeout(()=>{U(t)},0)},children:(0,r.jsx)("div",{className:"relative h-16 w-full",children:(0,r.jsx)(n.default,{src:e.url,alt:"".concat(en.name," - ").concat(t+1),fill:!0,className:"object-cover"})})},e.id))})]}),(0,r.jsx)(c.P.div,{className:"lg:w-1/2",initial:{opacity:0,x:30},animate:{opacity:1,x:0},transition:{duration:.5,delay:.2},children:(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-md p-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:en.name}),(0,r.jsx)("p",{className:"text-gray-600 mb-2",children:en.brandName})]}),en.averageRating&&(0,r.jsxs)("div",{className:"flex items-center bg-yellow-50 px-3 py-1 rounded-full",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),(0,r.jsx)("span",{className:"ml-1 font-medium text-yellow-700",children:en.averageRating.toFixed(1)})]})]}),(0,r.jsxs)("div",{className:"border-b border-gray-100 pb-6 mb-6",children:[en.variants&&en.variants.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"font-semibold mb-3 text-gray-700",children:"Varyant Se\xe7imi"}),(0,r.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-3",children:en.variants.map((e,t)=>(0,r.jsx)(c.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>_(t),className:"p-3 border-2 rounded-lg text-sm transition-all duration-200 ".concat((null==Y?void 0:Y.id)===e.id?"border-purple-500 bg-purple-50 text-purple-700":"border-gray-200 hover:border-purple-300"),children:(0,r.jsxs)("div",{className:"text-left",children:[e.features.map((e,t)=>(0,r.jsxs)("div",{className:"text-xs text-gray-600",children:[e.featureName,": ",(0,r.jsx)("span",{className:"font-medium",children:e.featureValue})]},t)),(0,r.jsx)("div",{className:"font-semibold mt-1",children:ed(e.extraDiscount,!K)?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("span",{className:(null==Y?void 0:Y.id)===e.id?"text-purple-700":"text-gray-600",children:[ec(e.price,e.extraDiscount).toFixed(2)," ₺"]}),(0,r.jsxs)("span",{className:"text-gray-500 line-through text-xs ml-1",children:[e.price.toFixed(2)," ₺"]})]}):(0,r.jsxs)("span",{className:(null==Y?void 0:Y.id)===e.id?"text-purple-700":"text-gray-600",children:[e.price.toFixed(2)," ₺"]})}),(0,r.jsx)("div",{className:"text-xs mt-1 ".concat(ei(e.stockStatus).color),children:ei(e.stockStatus).text}),(0,r.jsxs)("div",{className:"flex space-x-1 mt-2",children:[(0,r.jsxs)("span",{className:"bg-purple-100 text-purple-700 px-2 py-1 rounded text-xs font-medium",children:["PV ",eo(e.pv,e.price)]}),(0,r.jsxs)("span",{className:"bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium",children:["CV ",eo(e.cv,e.price)]}),(0,r.jsxs)("span",{className:"bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium",children:["SP ",eo(e.sp,e.price)]})]})]})},e.id))})]}),Y&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"flex items-baseline mb-4",children:ed(Y.extraDiscount,!K)?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("span",{className:"text-3xl font-bold text-purple-700 mr-2",children:[ec(Y.price,Y.extraDiscount).toFixed(2)," ₺"]}),(0,r.jsxs)("span",{className:"text-lg text-gray-500 line-through",children:[Y.price.toFixed(2)," ₺"]}),(0,r.jsxs)("div",{className:"flex flex-col gap-1 ml-3",children:[!K&&(null==O?void 0:O.discountRate)&&O.discountRate>0&&(0,r.jsxs)("span",{className:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-2 py-1 rounded-full text-xs font-medium",children:["%",O.discountRate," \xdcye İndirimi"]}),Y.extraDiscount>0&&(0,r.jsxs)("span",{className:"bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-medium",children:["%",Y.extraDiscount," İndirim"]})]})]}):(0,r.jsxs)("span",{className:"text-3xl font-bold text-purple-700 mr-2",children:[Y.price.toFixed(2)," ₺"]})}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Bu \xfcr\xfcn\xfc satın alarak kazanacağınız puanlar:"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,r.jsx)("span",{className:"font-bold",children:"PV"}),(0,r.jsxs)("span",{children:[eo(Y.pv,Y.price)," Puan"]})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,r.jsx)("span",{className:"font-bold",children:"CV"}),(0,r.jsxs)("span",{children:[eo(Y.cv,Y.price)," Puan"]})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,r.jsx)("span",{className:"font-bold",children:"SP"}),(0,r.jsxs)("span",{children:[eo(Y.sp,Y.price)," Puan"]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"text-sm flex items-center ".concat(ei(Y.stockStatus).color),children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),ei(Y.stockStatus).text]}),(0,r.jsxs)("div",{className:"text-sm flex items-center text-gray-600",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})}),"Kategori: ",en.categoryName]})]})]})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center border border-gray-200 rounded-md text-gray-600",children:[(0,r.jsx)(c.P.button,{whileTap:{scale:.9},onClick:$,className:"px-4 py-2 text-gray-600 hover:text-purple-700 focus:outline-none",children:"-"}),(0,r.jsx)("div",{className:"w-12 text-center",children:ee}),(0,r.jsx)(c.P.button,{whileTap:{scale:.9},onClick:G,className:"px-4 py-2 text-gray-600 hover:text-purple-700 focus:outline-none",children:"+"})]}),(0,r.jsxs)(c.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:em,className:"w-full sm:w-auto bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 flex items-center justify-center",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"})}),"Sepete Ekle"]}),(0,r.jsx)(c.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>{if(!en||!Y||!Z)return;let e=(null==O?void 0:O.discountRate)||null,t=0;e&&e>0&&(t+=e),Y.extraDiscount&&Y.extraDiscount>0&&(t=t>0?t+Y.extraDiscount-t*Y.extraDiscount/100:Y.extraDiscount);let a={id:en.id,title:en.name,price:Y.price,thumbnail:Z.url,brand:en.brandName,discountPercentage:t,points:eo(Y.pv,Y.price)};V(en.id)?(q(en.id),N(!1)):(D(a),N(!0)),j(!0)},className:"w-12 h-12 flex items-center justify-center border rounded-full transition-colors ".concat(V(en.id)?"border-red-500 text-red-500 bg-red-50":"border-gray-200 text-gray-400 hover:text-red-500 hover:border-red-500"),children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:V(en.id)?"currentColor":"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold mb-3 text-gray-700",children:"\xdcr\xfcn A\xe7ıklaması"}),(0,r.jsx)("div",{className:"text-gray-700 leading-relaxed",children:en.description})]})]})})]}),(0,r.jsx)(p.A,{isOpen:i,onClose:()=>f(!1),product:v,quantity:ee}),(0,r.jsx)(g.A,{isOpen:y,onClose:()=>j(!1),product:Y&&Z?{id:en.id,title:en.name,price:Y.price,thumbnail:Z.url,brand:en.brandName,discountPercentage:Y.extraDiscount,points:eo(Y.pv,Y.price)}:null,isAdded:b}),(0,r.jsx)(z,{isOpen:k,onClose:()=>C(!1),images:X,currentImageIndex:X.findIndex(e=>e.id===(null==Z?void 0:Z.id)),onImageChange:e=>{U(e)},productName:en.name})]})}},69848:(e,t,a)=>{"use strict";a.d(t,{H:()=>i,r:()=>o});var r=a(95155),s=a(12115),l=a(68318);let n=(0,s.createContext)(void 0);function i(e){let{children:t}=e,[a,i]=(0,s.useState)([]),[o,c]=(0,s.useState)(!1);(0,s.useEffect)(()=>{if(!o){let e=localStorage.getItem("sayGlobalFavorites");if(e)try{let t=JSON.parse(e);i(t)}catch(e){console.error("Favori verileri y\xfcklenirken hata:",e),i(l.I1)}else i(l.I1);c(!0)}},[o]),(0,s.useEffect)(()=>{o&&localStorage.setItem("sayGlobalFavorites",JSON.stringify(a))},[a,o]);let d=(0,s.useCallback)(e=>{i(t=>t.find(t=>t.productId===e.id)?t:[...t,{id:Date.now(),productId:e.id,product:e,addedAt:new Date().toISOString()}])},[]),u=(0,s.useCallback)(e=>{i(t=>t.filter(t=>t.productId!==e))},[]),x=(0,s.useCallback)(e=>a.some(t=>t.productId===e),[a]),m=(0,s.useCallback)(()=>a.length,[a]),h=(0,s.useMemo)(()=>({favorites:a,addToFavorites:d,removeFromFavorites:u,isFavorite:x,getFavoritesCount:m}),[a,d,u,x,m]);return(0,r.jsx)(n.Provider,{value:h,children:t})}function o(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useFavorites must be used within a FavoritesProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[6408,6874,7323,6766,6681,2066,8441,1684,7358],()=>t(9200)),_N_E=e.O()}]);