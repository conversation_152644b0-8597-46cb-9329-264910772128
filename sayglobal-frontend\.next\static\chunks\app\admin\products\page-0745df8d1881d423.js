(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7575],{3005:(e,t,a)=>{Promise.resolve().then(a.bind(a,56912))},45106:(e,t,a)=>{"use strict";a.d(t,{$N:()=>M,EI:()=>P,F0:()=>c,Gc:()=>d,HX:()=>S,JZ:()=>f,OG:()=>k,Ph:()=>p,QK:()=>x,QR:()=>R,S:()=>m,VS:()=>o,Zm:()=>w,_f:()=>D,c6:()=>y,fW:()=>C,gA:()=>g,hg:()=>j,ig:()=>N,lA:()=>u,nb:()=>v,qA:()=>A,u6:()=>h,vQ:()=>b});var s=a(65453),l=a(46786);let r={editPersonalInfo:!1,referenceRegistration:!1,addAddress:!1,setDefaultConfirmation:!1,banking:!1,addCard:!1,setDefaultCard:!1,registerSuccess:!1,successNotification:!1,productCategorySelector:!1,productVariantSetup:!1,productVariant:!1,productDeleteConfirmation:!1},i={editPersonalInfoUser:null,referenceRegistrationData:null,addAddressData:null,setDefaultConfirmationData:null,bankingData:null,addCardData:null,setDefaultCardData:null,successNotificationData:null,productCategorySelectorData:null,productVariantSetupData:null,productVariantData:null,productDeleteConfirmationData:null},n=(0,s.v)()((0,l.lt)(e=>({...r,...i,openModal:(t,a)=>{e(e=>({...e,[t]:!0,...a&&{["".concat(t,"User")]:a}}),!1,"modal/open/".concat(t))},closeModal:t=>{e(e=>({...e,[t]:!1,["".concat(t,"User")]:null}),!1,"modal/close/".concat(t))},closeAllModals:()=>{e({...r,...i},!1,"modal/closeAll")},openEditPersonalInfoModal:t=>{e({editPersonalInfo:!0,editPersonalInfoUser:t},!1,"modal/openEditPersonalInfo")},closeEditPersonalInfoModal:()=>{e({editPersonalInfo:!1,editPersonalInfoUser:null},!1,"modal/closeEditPersonalInfo")},openReferenceRegistrationModal:t=>{e({referenceRegistration:!0,referenceRegistrationData:t},!1,"modal/openReferenceRegistration")},closeReferenceRegistrationModal:()=>{e({referenceRegistration:!1,referenceRegistrationData:null},!1,"modal/closeReferenceRegistration")},openAddAddressModal:t=>{e({addAddress:!0,addAddressData:t},!1,"modal/openAddAddress")},closeAddAddressModal:()=>{e({addAddress:!1,addAddressData:null},!1,"modal/closeAddAddress")},openSetDefaultConfirmationModal:t=>{e({setDefaultConfirmation:!0,setDefaultConfirmationData:t},!1,"modal/openSetDefaultConfirmation")},closeSetDefaultConfirmationModal:()=>{e({setDefaultConfirmation:!1,setDefaultConfirmationData:null},!1,"modal/closeSetDefaultConfirmation")},openBankingModal:t=>{e({banking:!0,bankingData:t},!1,"modal/openBanking")},closeBankingModal:()=>{e({banking:!1,bankingData:null},!1,"modal/closeBanking")},openAddCardModal:t=>{e({addCard:!0,addCardData:t},!1,"modal/openAddCard")},closeAddCardModal:()=>{e({addCard:!1,addCardData:null},!1,"modal/closeAddCard")},openSetDefaultCardModal:t=>{e({setDefaultCard:!0,setDefaultCardData:t},!1,"modal/openSetDefaultCard")},closeSetDefaultCardModal:()=>{e({setDefaultCard:!1,setDefaultCardData:null},!1,"modal/closeSetDefaultCard")},openRegisterSuccessModal:()=>{e({registerSuccess:!0},!1,"modal/openRegisterSuccess")},closeRegisterSuccessModal:()=>{e({registerSuccess:!1},!1,"modal/closeRegisterSuccess")},openSuccessNotificationModal:t=>{e({successNotification:!0,successNotificationData:t},!1,"modal/openSuccessNotification")},closeSuccessNotificationModal:()=>{e({successNotification:!1,successNotificationData:null},!1,"modal/closeSuccessNotification")},openProductCategorySelector:t=>{e({productCategorySelector:!0,productCategorySelectorData:t},!1,"modal/openProductCategorySelector")},closeProductCategorySelector:()=>{e({productCategorySelector:!1,productCategorySelectorData:null},!1,"modal/closeProductCategorySelector")},openProductVariantSetup:t=>{e({productVariantSetup:!0,productVariantSetupData:t},!1,"modal/openProductVariantSetup")},closeProductVariantSetup:()=>{e({productVariantSetup:!1,productVariantSetupData:null},!1,"modal/closeProductVariantSetup")},openProductVariant:t=>{e({productVariant:!0,productVariantData:t},!1,"modal/openProductVariant")},closeProductVariant:()=>{e({productVariant:!1,productVariantData:null},!1,"modal/closeProductVariant")},openProductDeleteConfirmation:t=>{e({productDeleteConfirmation:!0,productDeleteConfirmationData:t},!1,"modal/openProductDeleteConfirmation")},closeProductDeleteConfirmation:()=>{e({productDeleteConfirmation:!1,productDeleteConfirmationData:null},!1,"modal/closeProductDeleteConfirmation")}}),{name:"modal-store",enabled:!1})),o=()=>n(e=>e.editPersonalInfo),d=()=>n(e=>e.editPersonalInfoUser),c=()=>n(e=>e.referenceRegistration),m=()=>n(e=>e.referenceRegistrationData),x=()=>n(e=>e.addAddress),u=()=>n(e=>e.addAddressData),p=()=>n(e=>e.setDefaultConfirmation),h=()=>n(e=>e.setDefaultConfirmationData),f=()=>n(e=>e.banking),g=()=>n(e=>e.bankingData),y=()=>n(e=>e.addCard),j=()=>n(e=>e.addCardData),N=()=>n(e=>e.setDefaultCard),b=()=>n(e=>e.setDefaultCardData),v=()=>n(e=>e.registerSuccess),w=()=>n(e=>e.successNotification),k=()=>n(e=>e.successNotificationData),C=()=>n(e=>e.productCategorySelector),D=()=>n(e=>e.productCategorySelectorData),S=()=>n(e=>e.productVariant),A=()=>n(e=>e.productVariantData),P=()=>n(e=>e.productDeleteConfirmation),M=()=>n(e=>e.productDeleteConfirmationData),R=()=>{let e=n(e=>e.openModal),t=n(e=>e.closeModal),a=n(e=>e.closeAllModals),s=n(e=>e.openEditPersonalInfoModal),l=n(e=>e.closeEditPersonalInfoModal),r=n(e=>e.openReferenceRegistrationModal),i=n(e=>e.closeReferenceRegistrationModal),o=n(e=>e.openAddAddressModal),d=n(e=>e.closeAddAddressModal),c=n(e=>e.openSetDefaultConfirmationModal),m=n(e=>e.closeSetDefaultConfirmationModal),x=n(e=>e.openBankingModal),u=n(e=>e.closeBankingModal),p=n(e=>e.openAddCardModal),h=n(e=>e.closeAddCardModal),f=n(e=>e.openSetDefaultCardModal),g=n(e=>e.closeSetDefaultCardModal),y=n(e=>e.openRegisterSuccessModal),j=n(e=>e.closeRegisterSuccessModal),N=n(e=>e.openSuccessNotificationModal),b=n(e=>e.closeSuccessNotificationModal),v=n(e=>e.openProductCategorySelector),w=n(e=>e.closeProductCategorySelector),k=n(e=>e.openProductVariantSetup),C=n(e=>e.closeProductVariantSetup),D=n(e=>e.openProductVariant),S=n(e=>e.closeProductVariant);return{openModal:e,closeModal:t,closeAllModals:a,openEditPersonalInfoModal:s,closeEditPersonalInfoModal:l,openReferenceRegistrationModal:r,closeReferenceRegistrationModal:i,openAddAddressModal:o,closeAddAddressModal:d,openSetDefaultConfirmationModal:c,closeSetDefaultConfirmationModal:m,openBankingModal:x,closeBankingModal:u,openAddCardModal:p,closeAddCardModal:h,openSetDefaultCardModal:f,closeSetDefaultCardModal:g,openRegisterSuccessModal:y,closeRegisterSuccessModal:j,openSuccessNotificationModal:N,closeSuccessNotificationModal:b,openProductCategorySelector:v,closeProductCategorySelector:w,openProductVariantSetup:k,closeProductVariantSetup:C,openProductVariant:D,closeProductVariant:S,openProductDeleteConfirmation:n(e=>e.openProductDeleteConfirmation),closeProductDeleteConfirmation:n(e=>e.closeProductDeleteConfirmation)}}},56912:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>E});var s=a(95155),l=a(12115),r=a(87220),i=a(35695),n=a(26715),o=a(76408),d=a(56407);let c=(0,a(65453).v)(e=>({searchTerm:"",categoryFilter:"all",statusFilter:"all",setSearchTerm:t=>e({searchTerm:t}),setCategoryFilter:t=>e({categoryFilter:t}),setStatusFilter:t=>e({statusFilter:t}),resetFilters:()=>e({searchTerm:"",categoryFilter:"all",statusFilter:"all"})}));var m=a(45106),x=a(60760);function u(e){let{isOpen:t,onClose:a,onConfirm:r,productId:i,productName:n,brandName:d,imageUrl:c}=e,u=(0,m.EI)(),p=(0,m.$N)(),{closeProductDeleteConfirmation:h}=(0,m.QR)(),f=u||t||!1,g=(null==p?void 0:p.productId)||i,y=(null==p?void 0:p.productName)||n,j=(null==p?void 0:p.brandName)||d,N=(null==p?void 0:p.imageUrl)||c,b=()=>{u&&h(),a&&a()};return(0,l.useEffect)(()=>{if(f){let e=e=>{"Escape"===e.key&&b()};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}},[f]),(0,s.jsx)(x.N,{children:f&&y&&(0,s.jsxs)(o.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:b,children:[(0,s.jsx)("div",{className:"absolute inset-0"}),(0,s.jsxs)(o.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-lg w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.7,opacity:0,y:50},transition:{type:"spring",stiffness:300,damping:25,duration:.5},onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(o.P.div,{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4",initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring",stiffness:200},children:(0,s.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})}),(0,s.jsx)(o.P.h2,{className:"text-2xl font-bold text-gray-800",initial:{opacity:0,x:-10},animate:{opacity:1,x:0},transition:{delay:.2},children:"\xdcr\xfcn\xfc Sil"})]}),(0,s.jsx)(o.P.button,{onClick:b,className:"text-gray-400 hover:text-gray-600 transition-colors",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)(o.P.div,{className:"mb-8",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:[(0,s.jsxs)("p",{className:"text-gray-700 leading-relaxed mb-4",children:[(0,s.jsxs)("span",{className:"font-semibold text-red-600",children:['"',y,'"']})," adlı \xfcr\xfcn\xfc silmek istediğinizden emin misiniz?"]}),(0,s.jsx)(o.P.div,{className:"bg-red-50 border border-red-200 rounded-lg p-4",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{delay:.4},children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-red-500 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-red-800 font-medium text-sm mb-1",children:"Dikkat!"}),(0,s.jsx)("p",{className:"text-red-700 text-sm",children:"Bu işlem geri alınamaz. \xdcr\xfcn kalıcı olarak silinecek ve t\xfcm ilgili veriler kaybolacaktır."})]})]})}),g&&(0,s.jsx)(o.P.div,{className:"mt-4 bg-gray-50 rounded-lg p-4",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-12 h-12 flex-shrink-0",children:N?(0,s.jsx)("img",{src:N,alt:y,className:"w-12 h-12 rounded-lg object-cover"}):(0,s.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})})})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900 truncate",children:y}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:j})]})]})})]}),(0,s.jsxs)(o.P.div,{className:"flex flex-col space-y-3",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},children:[(0,s.jsxs)(o.P.button,{onClick:()=>{(null==p?void 0:p.onConfirm)&&p.onConfirm(),r&&r(),b()},className:"w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),(0,s.jsx)("span",{children:"Evet, \xdcr\xfcn\xfc Sil"})]}),(0,s.jsx)(o.P.button,{onClick:b,className:"w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},children:"İptal Et"})]})]})]})})}var p=a(45934),h=a(54861),f=a(1243),g=a(40646),y=a(35169),j=a(75525),N=a(37108),b=a(47924),v=a(51154),w=a(53904),k=a(84616),C=a(33109),D=a(92657),S=a(13717),A=a(62525),P=a(42355),M=a(13052),R=a(6874),V=a.n(R);let E=()=>{let{user:e,isLoading:t}=(0,r.A)(),a=(0,i.useRouter)(),x=(0,n.jE)(),{openProductDeleteConfirmation:R}=(0,m.QR)(),{searchTerm:E,categoryFilter:L,statusFilter:I,setSearchTerm:T,setCategoryFilter:B,setStatusFilter:F}=c(),[z,U]=(0,l.useState)(1),[Q,Y]=(0,l.useState)(E),[W,_]=(0,l.useState)(null),[H,K]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=setTimeout(()=>{Y(E),U(1)},300);return()=>{clearTimeout(e)}},[E]),(0,l.useEffect)(()=>{t||e&&"admin"===e.role||a.push("/login")},[e,t,a]),(0,l.useEffect)(()=>{e&&"admin"===e.role&&(x.invalidateQueries({queryKey:["adminProductStatistics"]}),x.invalidateQueries({queryKey:["adminProducts"]}),console.log("\uD83D\uDCCA \xdcr\xfcn y\xf6netimi sayfası y\xfcklendi, istatistikler ve \xfcr\xfcn listesi yenileniyor..."))},[e,x]);let{data:O,isLoading:q}=(0,d.tA)(),{data:G,isLoading:$,isFetching:Z}=(0,d.Bv)(z,Q),J=(0,d.W$)(),{refreshProductLists:X}=(0,d.E0)(),ee=(e,t,a,s)=>{R({productId:e,productName:t,brandName:a,imageUrl:s,onConfirm:()=>{J.mutate(e)}})},et=e=>{_(e),K(!0)},ea=G||[],es=ea.filter(e=>{let t="all"===L||e.categoryName===L,a="all"===I||"active"===I&&e.isActive||"inactive"===I&&!e.isActive||"low-stock"===I&&e.stock>0&&e.stock<20||"out-of-stock"===I&&0===e.stock;return t&&a});if(t||q||$&&!Z)return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})});if(!e||"admin"!==e.role)return null;let el=e=>new Intl.NumberFormat("tr-TR",{style:"currency",currency:"TRY"}).format(e),er=e=>e?new Date(e).toLocaleDateString("tr-TR"):"-",ei=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:22;return e.length<=t?e:e.substring(0,t)+"..."},en=e=>0===e?{text:"Stokta Yok",color:"bg-red-100 text-red-800",icon:(0,s.jsx)(h.A,{className:"h-4 w-4"})}:e<20?{text:"Az Stok",color:"bg-yellow-100 text-yellow-800",icon:(0,s.jsx)(f.A,{className:"h-4 w-4"})}:{text:"Stokta",color:"bg-green-100 text-green-800",icon:(0,s.jsx)(g.A,{className:"h-4 w-4"})},eo=[...new Set(ea.map(e=>e.categoryName))],ed=(null==O?void 0:O.totalProductCount)||0,ec=(null==O?void 0:O.activeCount)||0,em=(null==O?void 0:O.lowStockCount)||0,ex=(null==O?void 0:O.outOfStockCount)||0;return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:[(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(V(),{href:"/admin",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,s.jsx)(y.A,{className:"h-5 w-5 mr-2"}),"Admin Paneli"]}),(0,s.jsx)("span",{className:"text-gray-300",children:"/"}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"\xdcr\xfcn Y\xf6netimi"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 bg-red-100 px-4 py-2 rounded-lg",children:[(0,s.jsx)(j.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)("span",{className:"text-red-800 font-medium",children:"Admin Erişimi"})]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,s.jsx)(o.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam \xdcr\xfcn"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:ed})]}),(0,s.jsx)(N.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,s.jsx)(o.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Aktif \xdcr\xfcn"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:ec})]}),(0,s.jsx)(g.A,{className:"h-8 w-8 text-green-600"})]})}),(0,s.jsx)(o.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-yellow-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Az Stok"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:em})]}),(0,s.jsx)(f.A,{className:"h-8 w-8 text-yellow-600"})]})}),(0,s.jsx)(o.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Stokta Yok"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:ex})]}),(0,s.jsx)(h.A,{className:"h-8 w-8 text-red-600"})]})})]}),(0,s.jsx)(o.P.div,{className:"bg-white rounded-xl shadow-lg p-6 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(b.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"}),(0,s.jsx)("input",{type:"text",placeholder:"\xdcr\xfcn ara...",value:E,onChange:e=>T(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder:text-gray-600 text-black"}),Z&&(0,s.jsx)(v.A,{className:"h-5 w-5 text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2 animate-spin"})]}),(0,s.jsxs)("select",{value:L,onChange:e=>B(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-600",children:[(0,s.jsx)("option",{value:"all",children:"T\xfcm Kategoriler"}),eo.map(e=>(0,s.jsx)("option",{value:e,children:e},e))]}),(0,s.jsxs)("select",{value:I,onChange:e=>F(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-600",children:[(0,s.jsx)("option",{value:"all",children:"T\xfcm Durumlar"}),(0,s.jsx)("option",{value:"active",children:"Aktif"}),(0,s.jsx)("option",{value:"inactive",children:"Pasif"}),(0,s.jsx)("option",{value:"low-stock",children:"Az Stok"}),(0,s.jsx)("option",{value:"out-of-stock",children:"Stokta Yok"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("button",{onClick:()=>{console.log("\uD83D\uDD04 Manuel cache yenileme başlatıldı..."),X()},className:"flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",title:"Verileri yenile",children:[(0,s.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Yenile"]}),(0,s.jsxs)(V(),{href:"/admin/products/add",className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,s.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Yeni \xdcr\xfcn"]})]})]})}),(0,s.jsxs)(o.P.div,{className:"bg-white rounded-xl shadow-lg overflow-hidden transition-opacity ".concat(Z?"opacity-70":""),initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 flex justify-between items-center",children:(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"\xdcr\xfcnler"})}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"\xdcr\xfcn"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kategori"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fiyat"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stok"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Satış"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Durum"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Son G\xfcncelleme"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"İşlemler"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:es.map(e=>{let t=en(e.stock);return(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"h-12 w-12 flex-shrink-0",children:e.imageUrl?(0,s.jsx)("img",{src:e.imageUrl,alt:e.name,className:"h-12 w-12 rounded-lg object-cover"}):(0,s.jsx)("div",{className:"h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center",children:(0,s.jsx)(N.A,{className:"h-6 w-6 text-gray-400"})})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",title:e.name,children:ei(e.name)}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.brandName})]})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:e.categoryName})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:el(e.price)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(t.color),children:[t.icon,(0,s.jsxs)("span",{className:"ml-1",children:[e.stock," - ",t.text]})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(C.A,{className:"h-4 w-4 text-green-500 mr-1"}),e.salesCount," adet"]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"Aktif":"Pasif"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:er(e.updatedAt)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>et(e.id),className:"text-blue-600 hover:text-blue-900",title:"Detayları G\xf6r\xfcnt\xfcle",children:(0,s.jsx)(D.A,{className:"h-4 w-4"})}),(0,s.jsx)("button",{onClick:()=>a.push("/admin/products/edit/".concat(e.id,"?from=products")),className:"text-green-600 hover:text-green-900",title:"\xdcr\xfcn\xfc D\xfczenle",children:(0,s.jsx)(S.A,{className:"h-4 w-4"})}),(0,s.jsx)("button",{onClick:()=>ee(e.id,e.name,e.brandName,e.imageUrl||void 0),disabled:J.isPending,className:"text-red-600 hover:text-red-900 disabled:opacity-50 disabled:cursor-not-allowed",children:J.isPending?(0,s.jsx)(v.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(A.A,{className:"h-4 w-4"})})]})})]},e.id)})})]})}),0===es.length&&!Z&&(0,s.jsxs)("div",{className:"px-6 py-12 text-center",children:[(0,s.jsx)(N.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"\xdcr\xfcn bulunamadı"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Arama veya filtreleme kriterlerinizi değiştirerek tekrar deneyin."})]}),(0,s.jsxs)("div",{className:"px-6 py-4 border-t border-gray-200 flex items-center justify-between",children:[(0,s.jsxs)("button",{onClick:()=>U(e=>Math.max(e-1,1)),disabled:1===z,className:"flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"\xd6nceki"]}),(0,s.jsxs)("span",{className:"text-sm text-gray-700",children:["Sayfa ",(0,s.jsx)("span",{className:"font-bold",children:z})]}),(0,s.jsxs)("button",{onClick:()=>U(e=>10===ea.length?e+1:e),disabled:ea.length<10,className:"flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:["Sonraki",(0,s.jsx)(M.A,{className:"h-4 w-4 ml-2"})]})]})]})]}),(0,s.jsx)(u,{}),(0,s.jsx)(p.A,{productId:W,isOpen:H,onClose:()=>{K(!1),_(null),x.invalidateQueries({queryKey:["adminProductStatistics"]}),console.log("\uD83D\uDD04 Modal kapandı, istatistik cache temizlendi")}})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6408,6874,7323,7623,6681,6407,5934,8441,1684,7358],()=>t(3005)),_N_E=e.O()}]);