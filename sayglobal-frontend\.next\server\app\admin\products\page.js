(()=>{var e={};e.id=575,e.ids=[575],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6795:(e,t,s)=>{Promise.resolve().then(s.bind(s,38571))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24651:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=s(65239),r=s(48088),i=s(88170),l=s.n(i),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["admin",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,38571)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\products\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\products\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/products/page",pathname:"/admin/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38571:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\products\\page.tsx","default")},43649:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70352:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>T});var a=s(60687),r=s(43210),i=s(15908),l=s(16189),n=s(8693),o=s(26001),d=s(55245);let c=(0,s(26787).v)(e=>({searchTerm:"",categoryFilter:"all",statusFilter:"all",setSearchTerm:t=>e({searchTerm:t}),setCategoryFilter:t=>e({categoryFilter:t}),setStatusFilter:t=>e({statusFilter:t}),resetFilters:()=>e({searchTerm:"",categoryFilter:"all",statusFilter:"all"})}));var x=s(75068),m=s(88920);function p({isOpen:e,onClose:t,onConfirm:s,productId:r,productName:i,brandName:l,imageUrl:n}){let d=(0,x.EI)(),c=(0,x.$N)(),{closeProductDeleteConfirmation:p}=(0,x.QR)(),u=d||e||!1,h=c?.productId||r,y=c?.productName||i,g=c?.brandName||l,f=c?.imageUrl||n,j=()=>{d&&p(),t&&t()};return(0,a.jsx)(m.N,{children:u&&y&&(0,a.jsxs)(o.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:j,children:[(0,a.jsx)("div",{className:"absolute inset-0"}),(0,a.jsxs)(o.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-lg w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.7,opacity:0,y:50},transition:{type:"spring",stiffness:300,damping:25,duration:.5},onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.P.div,{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4",initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring",stiffness:200},children:(0,a.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})}),(0,a.jsx)(o.P.h2,{className:"text-2xl font-bold text-gray-800",initial:{opacity:0,x:-10},animate:{opacity:1,x:0},transition:{delay:.2},children:"\xdcr\xfcn\xfc Sil"})]}),(0,a.jsx)(o.P.button,{onClick:j,className:"text-gray-400 hover:text-gray-600 transition-colors",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)(o.P.div,{className:"mb-8",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:[(0,a.jsxs)("p",{className:"text-gray-700 leading-relaxed mb-4",children:[(0,a.jsxs)("span",{className:"font-semibold text-red-600",children:['"',y,'"']})," adlı \xfcr\xfcn\xfc silmek istediğinizden emin misiniz?"]}),(0,a.jsx)(o.P.div,{className:"bg-red-50 border border-red-200 rounded-lg p-4",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{delay:.4},children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("svg",{className:"w-5 h-5 text-red-500 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-red-800 font-medium text-sm mb-1",children:"Dikkat!"}),(0,a.jsx)("p",{className:"text-red-700 text-sm",children:"Bu işlem geri alınamaz. \xdcr\xfcn kalıcı olarak silinecek ve t\xfcm ilgili veriler kaybolacaktır."})]})]})}),h&&(0,a.jsx)(o.P.div,{className:"mt-4 bg-gray-50 rounded-lg p-4",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 flex-shrink-0",children:f?(0,a.jsx)("img",{src:f,alt:y,className:"w-12 h-12 rounded-lg object-cover"}):(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center",children:(0,a.jsx)("svg",{className:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900 truncate",children:y}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:g})]})]})})]}),(0,a.jsxs)(o.P.div,{className:"flex flex-col space-y-3",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},children:[(0,a.jsxs)(o.P.button,{onClick:()=>{c?.onConfirm&&c.onConfirm(),s&&s(),j()},className:"w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),(0,a.jsx)("span",{children:"Evet, \xdcr\xfcn\xfc Sil"})]}),(0,a.jsx)(o.P.button,{onClick:j,className:"w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},children:"İptal Et"})]})]})]})})}var u=s(21254),h=s(35071),y=s(43649),g=s(5336),f=s(28559),j=s(99891),b=s(19080),v=s(99270),N=s(41862),w=s(78122),k=s(96474),A=s(25541),P=s(13861),C=s(63143),S=s(88233),q=s(47033),M=s(14952),L=s(85814),z=s.n(L);let T=()=>{let{user:e,isLoading:t}=(0,i.A)(),s=(0,l.useRouter)(),m=(0,n.jE)(),{openProductDeleteConfirmation:L}=(0,x.QR)(),{searchTerm:T,categoryFilter:U,statusFilter:_,setSearchTerm:E,setCategoryFilter:D,setStatusFilter:F}=c(),[R,G]=(0,r.useState)(1),[Y,B]=(0,r.useState)(T),[H,I]=(0,r.useState)(null),[K,V]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=setTimeout(()=>{B(T),G(1)},300);return()=>{clearTimeout(e)}},[T]),(0,r.useEffect)(()=>{t||e&&"admin"===e.role||s.push("/login")},[e,t,s]),(0,r.useEffect)(()=>{e&&"admin"===e.role&&(m.invalidateQueries({queryKey:["adminProductStatistics"]}),m.invalidateQueries({queryKey:["adminProducts"]}),console.log("\uD83D\uDCCA \xdcr\xfcn y\xf6netimi sayfası y\xfcklendi, istatistikler ve \xfcr\xfcn listesi yenileniyor..."))},[e,m]);let{data:W,isLoading:$}=(0,d.tA)(),{data:Q,isLoading:O,isFetching:X}=(0,d.Bv)(R,Y),J=(0,d.W$)(),{refreshProductLists:Z}=(0,d.E0)(),ee=(e,t,s,a)=>{L({productId:e,productName:t,brandName:s,imageUrl:a,onConfirm:()=>{J.mutate(e)}})},et=e=>{I(e),V(!0)},es=Q||[],ea=es.filter(e=>{let t="all"===U||e.categoryName===U,s="all"===_||"active"===_&&e.isActive||"inactive"===_&&!e.isActive||"low-stock"===_&&e.stock>0&&e.stock<20||"out-of-stock"===_&&0===e.stock;return t&&s});if(t||$||O&&!X)return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})});if(!e||"admin"!==e.role)return null;let er=e=>new Intl.NumberFormat("tr-TR",{style:"currency",currency:"TRY"}).format(e),ei=e=>e?new Date(e).toLocaleDateString("tr-TR"):"-",el=(e,t=22)=>e.length<=t?e:e.substring(0,t)+"...",en=e=>0===e?{text:"Stokta Yok",color:"bg-red-100 text-red-800",icon:(0,a.jsx)(h.A,{className:"h-4 w-4"})}:e<20?{text:"Az Stok",color:"bg-yellow-100 text-yellow-800",icon:(0,a.jsx)(y.A,{className:"h-4 w-4"})}:{text:"Stokta",color:"bg-green-100 text-green-800",icon:(0,a.jsx)(g.A,{className:"h-4 w-4"})},eo=[...new Set(es.map(e=>e.categoryName))],ed=W?.totalProductCount||0,ec=W?.activeCount||0,ex=W?.lowStockCount||0,em=W?.outOfStockCount||0;return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(z(),{href:"/admin",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,a.jsx)(f.A,{className:"h-5 w-5 mr-2"}),"Admin Paneli"]}),(0,a.jsx)("span",{className:"text-gray-300",children:"/"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"\xdcr\xfcn Y\xf6netimi"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 bg-red-100 px-4 py-2 rounded-lg",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"text-red-800 font-medium",children:"Admin Erişimi"})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)(o.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam \xdcr\xfcn"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:ed})]}),(0,a.jsx)(b.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,a.jsx)(o.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Aktif \xdcr\xfcn"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:ec})]}),(0,a.jsx)(g.A,{className:"h-8 w-8 text-green-600"})]})}),(0,a.jsx)(o.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-yellow-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Az Stok"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:ex})]}),(0,a.jsx)(y.A,{className:"h-8 w-8 text-yellow-600"})]})}),(0,a.jsx)(o.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Stokta Yok"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:em})]}),(0,a.jsx)(h.A,{className:"h-8 w-8 text-red-600"})]})})]}),(0,a.jsx)(o.P.div,{className:"bg-white rounded-xl shadow-lg p-6 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(v.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"}),(0,a.jsx)("input",{type:"text",placeholder:"\xdcr\xfcn ara...",value:T,onChange:e=>E(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder:text-gray-600 text-black"}),X&&(0,a.jsx)(N.A,{className:"h-5 w-5 text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2 animate-spin"})]}),(0,a.jsxs)("select",{value:U,onChange:e=>D(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-600",children:[(0,a.jsx)("option",{value:"all",children:"T\xfcm Kategoriler"}),eo.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]}),(0,a.jsxs)("select",{value:_,onChange:e=>F(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-600",children:[(0,a.jsx)("option",{value:"all",children:"T\xfcm Durumlar"}),(0,a.jsx)("option",{value:"active",children:"Aktif"}),(0,a.jsx)("option",{value:"inactive",children:"Pasif"}),(0,a.jsx)("option",{value:"low-stock",children:"Az Stok"}),(0,a.jsx)("option",{value:"out-of-stock",children:"Stokta Yok"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>{console.log("\uD83D\uDD04 Manuel cache yenileme başlatıldı..."),Z()},className:"flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",title:"Verileri yenile",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Yenile"]}),(0,a.jsxs)(z(),{href:"/admin/products/add",className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Yeni \xdcr\xfcn"]})]})]})}),(0,a.jsxs)(o.P.div,{className:`bg-white rounded-xl shadow-lg overflow-hidden transition-opacity ${X?"opacity-70":""}`,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 flex justify-between items-center",children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"\xdcr\xfcnler"})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"\xdcr\xfcn"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kategori"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fiyat"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stok"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Satış"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Durum"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Son G\xfcncelleme"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"İşlemler"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:ea.map(e=>{let t=en(e.stock);return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-12 w-12 flex-shrink-0",children:e.imageUrl?(0,a.jsx)("img",{src:e.imageUrl,alt:e.name,className:"h-12 w-12 rounded-lg object-cover"}):(0,a.jsx)("div",{className:"h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center",children:(0,a.jsx)(b.A,{className:"h-6 w-6 text-gray-400"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",title:e.name,children:el(e.name)}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.brandName})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:e.categoryName})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:er(e.price)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${t.color}`,children:[t.icon,(0,a.jsxs)("span",{className:"ml-1",children:[e.stock," - ",t.text]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 text-green-500 mr-1"}),e.salesCount," adet"]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isActive?"Aktif":"Pasif"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:ei(e.updatedAt)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>et(e.id),className:"text-blue-600 hover:text-blue-900",title:"Detayları G\xf6r\xfcnt\xfcle",children:(0,a.jsx)(P.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>s.push(`/admin/products/edit/${e.id}?from=products`),className:"text-green-600 hover:text-green-900",title:"\xdcr\xfcn\xfc D\xfczenle",children:(0,a.jsx)(C.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>ee(e.id,e.name,e.brandName,e.imageUrl||void 0),disabled:J.isPending,className:"text-red-600 hover:text-red-900 disabled:opacity-50 disabled:cursor-not-allowed",children:J.isPending?(0,a.jsx)(N.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(S.A,{className:"h-4 w-4"})})]})})]},e.id)})})]})}),0===ea.length&&!X&&(0,a.jsxs)("div",{className:"px-6 py-12 text-center",children:[(0,a.jsx)(b.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"\xdcr\xfcn bulunamadı"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Arama veya filtreleme kriterlerinizi değiştirerek tekrar deneyin."})]}),(0,a.jsxs)("div",{className:"px-6 py-4 border-t border-gray-200 flex items-center justify-between",children:[(0,a.jsxs)("button",{onClick:()=>G(e=>Math.max(e-1,1)),disabled:1===R,className:"flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"\xd6nceki"]}),(0,a.jsxs)("span",{className:"text-sm text-gray-700",children:["Sayfa ",(0,a.jsx)("span",{className:"font-bold",children:R})]}),(0,a.jsxs)("button",{onClick:()=>G(e=>10===es.length?e+1:e),disabled:es.length<10,className:"flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:["Sonraki",(0,a.jsx)(M.A,{className:"h-4 w-4 ml-2"})]})]})]})]}),(0,a.jsx)(p,{}),(0,a.jsx)(u.A,{productId:H,isOpen:K,onClose:()=>{V(!1),I(null),m.invalidateQueries({queryKey:["adminProductStatistics"]}),console.log("\uD83D\uDD04 Modal kapandı, istatistik cache temizlendi")}})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82819:(e,t,s)=>{Promise.resolve().then(s.bind(s,70352))},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99891:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,181,658,85,32,313],()=>s(24651));module.exports=a})();