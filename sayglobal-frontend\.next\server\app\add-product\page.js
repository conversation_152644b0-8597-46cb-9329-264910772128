(()=>{var e={};e.id=904,e.ids=[904],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4963:(e,a,t)=>{"use strict";t.d(a,{t3:()=>d});var r=t(26787),s=t(59350);let i={formData:{name:"",description:"",brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{},price:0,stock:0,extraDiscount:0,ratios:{pvRatio:0,cvRatio:0,spRatio:0},points:{pv:0,cv:0,sp:0},hasVariants:!1,variants:[],images:[],isActive:!0},selectedNames:{brandName:"",categoryName:"",subCategoryName:""},selectedFeatureDetails:[],variants:[],availableFeatures:[],error:null},l=(e,a)=>({pv:Math.round(e*(a.pvRatio/100)),cv:Math.round(e*(a.cvRatio/100)),sp:Math.round(e*(a.spRatio/100))}),n=e=>{let a={},t=[];return e.forEach(e=>{e.featureDetails.forEach(e=>{a[e.featureDefinitionId]||(a[e.featureDefinitionId]=[]),a[e.featureDefinitionId].includes(e.featureValueId)||a[e.featureDefinitionId].push(e.featureValueId),t.some(a=>a.featureName===e.featureName&&a.featureValue===e.featureValue)||t.push({featureName:e.featureName,featureValue:e.featureValue})})}),{newSelectedFeatures:a,newSelectedFeatureDetails:t}},d=(0,r.v)()((0,s.lt)((e,a)=>({...i,setFormData:a=>e(e=>({formData:{...e.formData,...a}})),handleInputChange:(t,r)=>{let s={...a().formData,[t]:r};if(("price"===t||"ratios"===t)&&!s.hasVariants){let e=l(s.price,s.ratios);s={...s,points:e}}e({formData:s})},handleRatioChange:(t,r)=>{let s=a().formData,i={...s.ratios,[t]:r},n={...s,ratios:i};if(!n.hasVariants){let e=l(n.price,i);n={...n,points:e}}e({formData:n})},setCategorySelection:t=>{e({formData:{...a().formData,brandId:t.brandId,categoryId:t.categoryId,subCategoryId:t.subCategoryId,selectedFeatures:t.selectedFeatures,hasVariants:!1},selectedNames:{brandName:t.brandName,categoryName:t.categoryName,subCategoryName:t.subCategoryName},selectedFeatureDetails:t.selectedFeatureDetails,error:null})},clearAllSelections:()=>{e({formData:{...a().formData,brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{},hasVariants:!1},selectedNames:{brandName:"",categoryName:"",subCategoryName:""},selectedFeatureDetails:[],availableFeatures:[],variants:[],error:null})},setAvailableFeatures:a=>e({availableFeatures:a}),setVariants:a=>e({variants:a}),saveVariant:(t,r)=>{let s,{variants:i}=a(),{newSelectedFeatures:l,newSelectedFeatureDetails:d}=n(s=r?i.map(e=>e.id===r?t:e):[...i,{...t,id:Date.now()}]);e(e=>({variants:s,formData:{...e.formData,selectedFeatures:l,hasVariants:s.length>1},selectedFeatureDetails:d}))},deleteVariant:t=>{let{variants:r}=a(),s=r.filter(e=>e.id!==t),{newSelectedFeatures:i,newSelectedFeatureDetails:l}=n(s);e(e=>({variants:s,formData:{...e.formData,selectedFeatures:i,hasVariants:s.length>1},selectedFeatureDetails:l}))},generateVariants:a=>{let{newSelectedFeatures:t,newSelectedFeatureDetails:r}=n(a);e(e=>({formData:{...e.formData,hasVariants:!0,selectedFeatures:t},selectedFeatureDetails:r,variants:a,error:null}))},handleImageUpload:t=>{let{formData:r}=a(),s=Array.from(t).map((e,a)=>({url:URL.createObjectURL(e),isMain:0===r.images.length&&0===a,sortOrder:r.images.length+a,file:e}));e({formData:{...r,images:[...r.images,...s]}})},removeImage:t=>{let{formData:r}=a(),s=r.images.filter((e,a)=>a!==t);r.images[t].isMain&&s.length>0&&(s[0].isMain=!0),e({formData:{...r,images:s}})},setMainImage:t=>{let{formData:r}=a(),s=r.images.map((e,a)=>({...e,isMain:a===t}));e({formData:{...r,images:s}})},setError:a=>e({error:a}),reset:()=>e({...i})}),{name:"add-product-store",enabled:!1}))},8903:(e,a,t)=>{Promise.resolve().then(t.bind(t,28362))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28362:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\add-product\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\add-product\\page.tsx","default")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62084:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>D});var r=t(60687),s=t(43210),i=t(15908),l=t(16189),n=t(26001),d=t(28559),o=t(99891),c=t(96882),m=t(37360),u=t(88233),p=t(45583),x=t(9005),g=t(8819),h=t(85814),b=t.n(h),y=t(64298),f=t(21852),v=t(10577),N=t(4963),j=t(75068),k=t(8693),w=t(54050);let D=()=>{let{user:e,isLoading:a}=(0,i.A)(),t=(0,l.useRouter)(),h=(0,k.jE)(),{formData:D,selectedNames:I,variants:C,error:S,availableFeatures:V}=(0,N.t3)(),{handleInputChange:F,setCategorySelection:A,clearAllSelections:P,deleteVariant:q,setError:E,reset:M,setVariants:_,saveVariant:z}=(0,N.t3)(e=>e),{openProductCategorySelector:$,closeProductCategorySelector:R,openProductVariant:U,closeProductVariant:K}=(0,j.QR)(),O=(0,j.fW)(),G=(0,j._f)(),T=(0,j.HX)(),Y=(0,j.qA)(),H=e=>{let a={};return e.forEach(e=>{e.featureDetails.forEach(e=>{a[e.featureDefinitionId]||(a[e.featureDefinitionId]=[]),a[e.featureDefinitionId].includes(e.featureValueId)||a[e.featureDefinitionId].push(e.featureValueId)})}),a},{mutate:L,isPending:Q}=(0,w.n)({mutationFn:e=>y.jU.createDealershipProduct(e),onSuccess:()=>{console.log("✅ \xdcr\xfcn başarıyla eklendi. Cache temizleniyor..."),h.invalidateQueries({queryKey:["myProducts"]}),h.invalidateQueries({queryKey:["products"]}),t.push("/pending-products"),M()},onError:e=>{E(e.message||"\xdcr\xfcn eklenirken bir hata oluştu")}});(0,s.useEffect)(()=>()=>{M()},[M]),(0,s.useEffect)(()=>{e&&"dealership"!==e.role&&"admin"!==e.role&&t.push("/"),a||e||t.push("/login")},[e,a,t]);let B=e=>{U({editingVariant:e,availableFeatures:V,existingVariants:C})},X=async e=>{e.preventDefault(),E(null);try{if(!D.name.trim())throw Error("\xdcr\xfcn adı gereklidir");if(!D.description.trim())throw Error("\xdcr\xfcn a\xe7ıklaması gereklidir");if(D.brandId<=0)throw Error("Marka ve kategori se\xe7imi gereklidir");if(0===C.length)throw Error("En az bir varyant oluşturmalısınız.");for(let e of C){if(e.pricing.price<=0)throw Error(`${e.name} varyantı i\xe7in fiyat 0'dan b\xfcy\xfck olmalıdır`);if(e.pricing.stock<0)throw Error(`${e.name} varyantı i\xe7in stok miktarı negatif olamaz`)}let e=new FormData;e.append("Product.Name",D.name),e.append("Product.Description",D.description),e.append("Product.BrandId",D.brandId.toString()),e.append("Product.SubCategoryId",D.subCategoryId.toString()),e.append("Product.Stock",D.stock.toString()),C.forEach((a,t)=>{e.append(`Variant[${t}].stock`,a.pricing.stock.toString()),e.append(`Variant[${t}].price`,a.pricing.price.toString()),Object.values(a.selectedFeatures).flat().forEach((a,r)=>{e.append(`Variant[${t}].featureValueIds[${r}]`,a.toString())}),a.images.forEach((a,r)=>{a.file&&(e.append(`Variant[${t}].images[${r}].file`,a.file),e.append(`Variant[${t}].images[${r}].isMain`,a.isMain.toString()),e.append(`Variant[${t}].images[${r}].sortOrder`,a.sortOrder.toString()))})}),L(e)}catch(e){E(e.message||"\xdcr\xfcn eklenirken bir hata oluştu")}};return a?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):e&&("dealership"===e.role||"admin"===e.role)?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:[(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(b(),{href:"/pending-products",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 mr-2"}),"\xdcr\xfcn Listesi"]}),(0,r.jsx)("span",{className:"text-gray-300",children:"/"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Yeni \xdcr\xfcn Ekle"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 bg-blue-100 px-4 py-2 rounded-lg",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 text-blue-600"}),(0,r.jsx)("span",{className:"text-blue-800 font-medium",children:"Satıcı Erişimi"})]})]})}),S&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsx)("p",{className:"text-red-800",children:S}),(0,r.jsx)("button",{onClick:()=>E(null),className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Kapat"})]}),(0,r.jsxs)("form",{onSubmit:X,className:"space-y-8",children:[(0,r.jsxs)(n.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(c.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Temel Bilgiler"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xdcr\xfcn Adı *"}),(0,r.jsx)("input",{type:"text",value:D.name,onChange:e=>F("name",e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black",placeholder:"\xdcr\xfcn adını girin...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"A\xe7ıklama *"}),(0,r.jsx)("textarea",{value:D.description,onChange:e=>F("description",e.target.value),rows:4,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black",placeholder:"\xdcr\xfcn a\xe7ıklamasını girin...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Marka, Kategori ve Detaylar"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>$({initialData:D?{brandId:D.brandId,categoryId:D.categoryId,subCategoryId:D.subCategoryId,selectedFeatures:H(C)}:{brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{}}}),className:"flex-grow px-4 py-3 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 transition-colors flex items-center justify-center text-gray-600 hover:text-blue-600",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2"}),D.brandId>0?"Kategori Se\xe7imini D\xfczenle":"Marka, Kategori ve Detaylar Se\xe7in"]}),D.brandId>0&&(0,r.jsx)("button",{type:"button",onClick:P,title:"T\xfcm se\xe7imleri temizle",className:"flex-shrink-0 p-3 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors",children:(0,r.jsx)(u.A,{className:"h-5 w-5"})})]}),D.brandId>0&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-gray-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"Se\xe7ili:"})," Marka: ",I.brandName,D.categoryId>0&&`, Kategori: ${I.categoryName}`,D.subCategoryId>0&&`, Alt Kategori: ${I.subCategoryName}`]})})]})]})]}),(0,r.jsxs)(n.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Varyant Y\xf6netimi"})]})}),D.subCategoryId<=0&&(0,r.jsx)("div",{className:"p-4 text-center bg-gray-50 rounded-lg",children:(0,r.jsx)("p",{className:"text-gray-500 font-medium",children:"Tekli \xfcr\xfcn eklemek veya varyant oluşturmak i\xe7in l\xfctfen \xf6nce marka, kategori ve alt kategori se\xe7in."})}),D.subCategoryId>0&&0===C.length&&(0,r.jsxs)("div",{className:"p-8 text-center bg-gray-50 rounded-lg",children:[(0,r.jsx)(p.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-3"}),(0,r.jsx)("p",{className:"text-gray-500 font-medium",children:"Hen\xfcz varyant oluşturulmadı."}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1 mb-4",children:'Yukarıdaki "Marka, Kategori ve Detaylar Se\xe7in" kısmı ile d\xfczenleyebilirsiniz.'})]}),C.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-blue-600 mr-2 mt-0.5"}),(0,r.jsxs)("p",{className:"text-sm text-blue-800",children:["Oluşturulan her bir varyant kombinasyonu i\xe7in Fiyat ve Stok bilgilerini girin. Tek bir \xfcr\xfcn ekliyorsanız bile, bu \xfcr\xfcn bir varyant olarak kabul edilir.",(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Not:"})," PV, CV, SP ve indirim oranları admin tarafından belirlenecektir."]})]})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Varyant"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fiyat (₺)"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stok"}),(0,r.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,r.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:C.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.featureDetails.map(e=>e.featureValue).join(", ")})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.price})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.stock})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,r.jsx)("button",{type:"button",onClick:()=>B(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:"Detay"}),(0,r.jsx)("button",{type:"button",onClick:()=>q("number"==typeof e.id?e.id:0),className:"text-gray-500 hover:text-gray-800",children:"Sil"})]})]},e.id))})]})})]})]}),(0,r.jsxs)(n.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(x.A,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"\xdcr\xfcn Fotoğrafları"})]}),(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(c.A,{className:"h-5 w-5 text-blue-400"})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-blue-700",children:'Her bir varyantın fotoğraflarını, varyant tablosundaki "Detay" butonuna tıklayarak a\xe7ılan pencereden y\xf6netebilirsiniz.'})})]})}),C.some(e=>e.images.length>0)&&(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"T\xfcm Varyant G\xf6rselleri (\xd6nizleme)"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:C.flatMap(e=>e.images.map(a=>({...a,variantName:e.name}))).map((e,a)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg overflow-hidden",children:(0,r.jsx)("img",{src:e.url,alt:`${e.variantName} g\xf6rseli`,className:"w-full h-full object-cover"})}),(0,r.jsx)("div",{className:"absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded",children:e.variantName}),e.isMain&&(0,r.jsx)("div",{className:"absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded",children:"Ana"})]},a))})]})]}),(0,r.jsxs)(n.P.div,{className:"flex justify-end space-x-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,r.jsx)(b(),{href:"/pending-products",className:"px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"İptal"}),(0,r.jsx)("button",{type:"submit",disabled:Q,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:Q?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Kaydediliyor..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"\xdcr\xfcn\xfc Kaydet"]})})]})]})]}),(0,r.jsx)(f.A,{isOpen:O,onClose:R,onSelect:e=>{A({brandId:e.brandId,categoryId:e.categoryId,subCategoryId:e.subCategoryId,brandName:e.brandName,categoryName:e.categoryName,subCategoryName:e.subCategoryName,selectedFeatures:e.selectedFeatures,selectedFeatureDetails:e.selectedFeatureDetails}),_(e.generatedVariants),R(),E(null)},initialData:G?.initialData&&"object"==typeof G.initialData&&"selectedFeatures"in G.initialData?G.initialData:{brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{}},colorScheme:"blue"}),(0,r.jsx)(v.A,{isOpen:T,onClose:K,onSave:e=>{z(e,"number"==typeof e.id?e.id:void 0),K()},editingVariant:Y?.editingVariant,availableFeatures:V,existingVariants:C,hidePvCvSp:!0,colorScheme:"blue"})]}):null}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74399:(e,a,t)=>{Promise.resolve().then(t.bind(t,62084))},77529:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var r=t(65239),s=t(48088),i=t(88170),l=t.n(i),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(a,d);let o={children:["",{children:["add-product",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,28362)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\add-product\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\add-product\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/add-product/page",pathname:"/add-product",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[447,181,658,85,112],()=>t(77529));module.exports=r})();