(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4700],{35695:(e,r,a)=>{"use strict";var s=a(18999);a.o(s,"useParams")&&a.d(r,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(r,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(r,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(r,{useSearchParams:function(){return s.useSearchParams}})},64622:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>d});var s=a(95155),t=a(6874),l=a.n(t),n=a(76408),i=a(12115),o=a(35695);function d(){let e=(0,o.useSearchParams)().get("token"),[r,a]=(0,i.useState)(""),[t,d]=(0,i.useState)(""),[c,m]=(0,i.useState)(!1),[x,h]=(0,i.useState)(!1),[u,f]=(0,i.useState)({});(0,i.useEffect)(()=>{e||f(e=>({...e,token:"Ge\xe7ersiz veya eksik sıfırlama bağlantısı. L\xfctfen e-postanızdaki bağlantıyı kontrol edin."}))},[e]);let p=()=>{let a={};return e||(a.token="Ge\xe7ersiz sıfırlama bağlantısı"),r?r.length<8&&(a.password="Şifre en az 8 karakter olmalıdır"):a.password="Şifre gereklidir",r!==t&&(a.confirmPassword="Şifreler eşleşmiyor"),f(a),0===Object.keys(a).length},g=async e=>{if(e.preventDefault(),p()){m(!0);try{await new Promise(e=>setTimeout(e,1500)),h(!0)}catch(e){f(e=>({...e,general:"Bir hata oluştu. L\xfctfen daha sonra tekrar deneyin."}))}finally{m(!1)}}},b=(()=>{if(!r)return{strength:0,label:"",color:""};let e=0;r.length>=8&&(e+=1),r.length>=12&&(e+=1),/[A-Z]/.test(r)&&(e+=1),/[a-z]/.test(r)&&(e+=1),/[0-9]/.test(r)&&(e+=1),/[^A-Za-z0-9]/.test(r)&&(e+=1);let a=[{label:"\xc7ok zayıf",color:"bg-red-500"},{label:"Zayıf",color:"bg-orange-500"},{label:"Orta",color:"bg-yellow-500"},{label:"İyi",color:"bg-lime-500"},{label:"G\xfc\xe7l\xfc",color:"bg-green-500"},{label:"\xc7ok g\xfc\xe7l\xfc",color:"bg-emerald-500"}];return{strength:Math.min(e,5),label:a[e].label,color:a[e].color}})();return x?(0,s.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,s.jsx)("div",{className:"max-w-md mx-auto",children:(0,s.jsx)(n.P.div,{className:"bg-white rounded-2xl shadow-lg overflow-hidden",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,s.jsxs)("div",{className:"p-8 text-center",children:[(0,s.jsx)("div",{className:"w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-10 w-10 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,s.jsx)("h1",{className:"text-3xl font-bold mb-4 text-gray-800",children:"Şifreniz Sıfırlandı!"}),(0,s.jsx)("p",{className:"text-gray-700 mb-8",children:"Şifreniz başarıyla değiştirildi. Artık yeni şifrenizle giriş yapabilirsiniz."}),(0,s.jsx)(n.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,s.jsx)(l(),{href:"/login",className:"inline-block bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300",children:"Giriş Yap"})})]})})})}):(0,s.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,s.jsx)("div",{className:"max-w-md mx-auto",children:(0,s.jsx)(n.P.div,{className:"bg-white rounded-2xl shadow-lg overflow-hidden",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,s.jsxs)("div",{className:"p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-2 text-gray-800",children:"Şifrenizi Sıfırlayın"}),(0,s.jsx)("p",{className:"text-gray-700",children:"L\xfctfen hesabınız i\xe7in yeni bir şifre belirleyin"})]}),u.token?(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6",children:[(0,s.jsx)("p",{children:u.token}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)(l(),{href:"/forgot-password",className:"text-purple-600 font-medium hover:text-purple-800 transition",children:"Yeni bir şifre sıfırlama bağlantısı iste"})})]}):(0,s.jsxs)("form",{onSubmit:g,children:[u.general&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6",children:(0,s.jsx)("p",{children:u.general})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Yeni Şifre"}),(0,s.jsx)("input",{id:"password",type:"password",value:r,onChange:e=>{a(e.target.value),u.password&&f(e=>({...e,password:void 0}))},className:"w-full px-4 py-3 rounded-lg border ".concat(u.password?"border-red-500":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500"),placeholder:"En az 8 karakter"}),u.password&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.password}),r&&(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsx)("div",{className:"h-2 flex-1 bg-gray-200 rounded-full overflow-hidden",children:(0,s.jsx)("div",{className:"h-full ".concat(b.color),style:{width:"".concat((b.strength+1)*16.67,"%")}})}),(0,s.jsx)("span",{className:"text-xs text-gray-500 ml-2 min-w-[70px]",children:b.label})]}),(0,s.jsxs)("ul",{className:"text-xs text-gray-600 mt-2 space-y-1",children:[(0,s.jsx)("li",{className:"".concat(r.length>=8?"text-green-500":""),children:"• En az 8 karakter"}),(0,s.jsx)("li",{className:"".concat(/[A-Z]/.test(r)?"text-green-500":""),children:"• En az bir b\xfcy\xfck harf"}),(0,s.jsx)("li",{className:"".concat(/[0-9]/.test(r)?"text-green-500":""),children:"• En az bir rakam"}),(0,s.jsx)("li",{className:"".concat(/[^A-Za-z0-9]/.test(r)?"text-green-500":""),children:"• En az bir \xf6zel karakter"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Şifre Tekrar"}),(0,s.jsx)("input",{id:"confirmPassword",type:"password",value:t,onChange:e=>{d(e.target.value),u.confirmPassword&&f(e=>({...e,confirmPassword:void 0}))},className:"w-full px-4 py-3 rounded-lg border ".concat(u.confirmPassword?"border-red-500":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500"),placeholder:"Şifrenizi tekrar girin"}),u.confirmPassword&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.confirmPassword})]}),(0,s.jsx)(n.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:c,className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 ".concat(c?"opacity-70 cursor-not-allowed":""),children:c?(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"İşleniyor..."]}):"Şifreyi Sıfırla"})]})]}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsx)("p",{className:"text-gray-700",children:(0,s.jsx)(l(),{href:"/login",className:"text-purple-600 font-medium hover:text-purple-800 transition",children:"Giriş sayfasına d\xf6n"})})})]})})})})}},92177:(e,r,a)=>{Promise.resolve().then(a.bind(a,64622))}},e=>{var r=r=>e(e.s=r);e.O(0,[6408,6874,8441,1684,7358],()=>r(92177)),_N_E=e.O()}]);