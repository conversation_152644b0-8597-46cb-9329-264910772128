"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/cart/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBU1VTXFxEZXNrdG9wXFxTYXlnbG9iYWxcXHNheWdsb2JhbC1mcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxhcGlcXG5hdmlnYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vY2xpZW50L2NvbXBvbmVudHMvbmF2aWdhdGlvbic7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW5hdmlnYXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs":
/*!*************************************************!*\
  !*** ./node_modules/zustand/esm/middleware.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   combine: () => (/* binding */ combine),\n/* harmony export */   createJSONStorage: () => (/* binding */ createJSONStorage),\n/* harmony export */   devtools: () => (/* binding */ devtools),\n/* harmony export */   persist: () => (/* binding */ persist),\n/* harmony export */   redux: () => (/* binding */ redux),\n/* harmony export */   subscribeWithSelector: () => (/* binding */ subscribeWithSelector)\n/* harmony export */ });\nconst reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...args) => api.dispatch(...args), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst removeStoreFromTrackedConnections = (name, store) => {\n  if (store === void 0) return;\n  const connectionInfo = trackedConnections.get(name);\n  if (!connectionInfo) return;\n  delete connectionInfo.stores[store];\n  if (Object.keys(connectionInfo.stores).length === 0) {\n    trackedConnections.delete(name);\n  }\n};\nconst findCallerName = (stack) => {\n  var _a, _b;\n  if (!stack) return void 0;\n  const traceLines = stack.split(\"\\n\");\n  const apiSetStateLineIndex = traceLines.findIndex(\n    (traceLine) => traceLine.includes(\"api.setState\")\n  );\n  if (apiSetStateLineIndex < 0) return void 0;\n  const callerLine = ((_a = traceLines[apiSetStateLineIndex + 1]) == null ? void 0 : _a.trim()) || \"\";\n  return (_b = /.+ (.+) .+/.exec(callerLine)) == null ? void 0 : _b[1];\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : ( false ? 0 : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extensionConnector) {\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const action = nameOrAction === void 0 ? {\n      type: anonymousActionType || findCallerName(new Error().stack) || \"anonymous\"\n    } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  api.devtools = {\n    cleanup: () => {\n      if (connection && typeof connection.unsubscribe === \"function\") {\n        connection.unsubscribe();\n      }\n      removeStoreFromTrackedConnections(options.name, store);\n    }\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...args) => {\n      if (( false ? 0 : void 0) !== \"production\" && args[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...args);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format.\n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, fn) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) fn(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nfunction combine(initialState, create) {\n  return (...args) => Object.assign({}, initialState, create(...args));\n}\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(name, JSON.stringify(newValue, options == null ? void 0 : options.replacer)),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst persistImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            const migration = options.migrate(\n              deserializedStorageValue.state,\n              deserializedStorageValue.version\n            );\n            if (migration instanceof Promise) {\n              return migration.then((result) => [true, result]);\n            }\n            return [true, migration];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persist = persistImpl;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/auth/AuthContext.tsx":
/*!*********************************************!*\
  !*** ./src/components/auth/AuthContext.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/authStore */ \"(app-pages-browser)/./src/stores/authStore.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // 💡 Tek doğruluk kaynağımız artık useUserInfo hook'u.\n    const { data: user, isLoading, isSuccess } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useUserInfo)();\n    // Zustand'dan sadece action'ları ve error state'ini alabiliriz.\n    const { error, clearError } = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    // Login ve Logout için TanStack Query mutation'larını kullanıyoruz.\n    const loginMutation = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useLoginMutation)();\n    const logoutMutation = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useLogoutMutation)();\n    const registerMutation = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRegisterMutation)();\n    // 🚨 Yeni Eklenen Kısım: Force Logout Event Listener'ı\n    // api.ts'deki interceptor refresh token'da başarısız olursa bu event fırlatılır.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const handleForceLogout = {\n                \"AuthProvider.useEffect.handleForceLogout\": ()=>{\n                    // Zaten bir logout işlemi devam etmiyorsa logout yap.\n                    if (!logoutMutation.isPending) {\n                        logoutMutation.mutate();\n                    }\n                }\n            }[\"AuthProvider.useEffect.handleForceLogout\"];\n            window.addEventListener('auth:force-logout', handleForceLogout);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    window.removeEventListener('auth:force-logout', handleForceLogout);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        logoutMutation\n    ]);\n    const login = async (credentials)=>{\n        try {\n            await loginMutation.mutateAsync(credentials);\n            // Başarılı olduğunda, useUserInfo otomatik olarak yeniden tetiklenecek ve user state'i güncellenecek.\n            return true;\n        } catch (error) {\n            console.error('❌ AuthContext login error:', error);\n            // Hata yönetimi zaten useLoginMutation içinde yapılıyor.\n            return false;\n        }\n    };\n    const register = async (data)=>{\n        try {\n            await registerMutation.mutateAsync(data);\n            return true;\n        } catch (error) {\n            console.error('❌ AuthContext register error:', error);\n            return false;\n        }\n    };\n    const logout = async ()=>{\n        await logoutMutation.mutateAsync();\n    // Başarılı olduğunda, useUserInfo query'si devre dışı kalacak ve cache temizlenecek.\n    };\n    // Bu fonksiyon artık mock olduğu için ve konumuz dışında olduğu için şimdilik dokunmuyoruz.\n    const updateUserRole = async (userId, newRole, isDealershipApproved, applicationStatus)=>{\n        console.log('User role update requested:', {\n            userId,\n            newRole,\n            isDealershipApproved,\n            applicationStatus\n        });\n    };\n    const contextValue = {\n        // `isSuccess` user'ın başarılı bir şekilde yüklendiğini gösterir.\n        // Bu, `isAuthenticated`'in yerini alır.\n        isAuthenticated: isSuccess && !!user,\n        user: user || null,\n        login,\n        register,\n        logout,\n        updateUserRole,\n        isLoading: loginMutation.isPending || registerMutation.isPending || logoutMutation.isPending || isLoading,\n        error\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\components\\\\auth\\\\AuthContext.tsx\",\n        lineNumber: 89,\n        columnNumber: 9\n    }, undefined);\n};\n_s(AuthProvider, \"25RP9aXl1ZALQT4VYO3BLTPtchw=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useUserInfo,\n        _stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore,\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useLoginMutation,\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useLogoutMutation,\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRegisterMutation\n    ];\n});\n_c = AuthProvider;\nconst useAuth = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAddresses.ts":
/*!***********************************!*\
  !*** ./src/hooks/useAddresses.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addressKeys: () => (/* binding */ addressKeys),\n/* harmony export */   useAddressCacheUtils: () => (/* binding */ useAddressCacheUtils),\n/* harmony export */   useAddresses: () => (/* binding */ useAddresses),\n/* harmony export */   useAddressesAuthSync: () => (/* binding */ useAddressesAuthSync),\n/* harmony export */   useCreateAddress: () => (/* binding */ useCreateAddress),\n/* harmony export */   useDeleteAddress: () => (/* binding */ useDeleteAddress),\n/* harmony export */   useRefreshAddresses: () => (/* binding */ useRefreshAddresses),\n/* harmony export */   useSetDefaultAddress: () => (/* binding */ useSetDefaultAddress)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/authStore */ \"(app-pages-browser)/./src/stores/authStore.ts\");\n\n\n\n\n// 🏭 Query Key Factory - Organized and type-safe\nconst addressKeys = {\n    all: [\n        'addresses'\n    ],\n    lists: ()=>[\n            ...addressKeys.all,\n            'list'\n        ],\n    list: (userId)=>[\n            ...addressKeys.lists(),\n            {\n                userId\n            }\n        ],\n    detail: (id)=>[\n            ...addressKeys.all,\n            'detail',\n            id\n        ],\n    // Future endpoints için hazır\n    favorites: ()=>[\n            ...addressKeys.all,\n            'favorites'\n        ],\n    search: (query)=>[\n            ...addressKeys.all,\n            'search',\n            {\n                query\n            }\n        ]\n};\n// 🔄 Auth State Sync Helper - Login sonrası state senkronizasyonu için\nconst useAddressesAuthSync = ()=>{\n    // 🎯 CRITICAL FIX: Auth store'dan direkt bilgi al - TanStack Query race condition'ını bypas et\n    const authStore = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { data: userData, isLoading: userLoading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useUserInfo)();\n    // 🔄 IMPROVED: Auth store priority - race condition'ı önle\n    const user = authStore.user || userData;\n    // 🎯 Auth durumunu kontrol et - auth store'dan authenticated ise hazır sayılır\n    const isAuthReady = authStore.isAuthenticated && !authStore.isLoading && !!(user === null || user === void 0 ? void 0 : user.id);\n    if (true) {\n        console.log('🔄 Auth sync status:', {\n            isAuthenticated: authStore.isAuthenticated,\n            authLoading: authStore.isLoading,\n            userLoading,\n            hasUser: !!user,\n            userId: user === null || user === void 0 ? void 0 : user.id,\n            isAuthReady,\n            sourceUser: authStore.user ? 'authStore' : 'tanstackQuery'\n        });\n    }\n    return {\n        isAuthReady,\n        user\n    };\n};\n// 📡 GET - Adres listesi (Optimized)\nconst useAddresses = ()=>{\n    // 🚀 CRITICAL FIX: Auth state senkronizasyonu için helper kullan\n    const { isAuthReady, user } = useAddressesAuthSync();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    // 🔍 Enhanced debug info (only in development)\n    if (true) {\n        console.log('🔍 useAddresses hook status:', {\n            hasUser: !!user,\n            userId: user === null || user === void 0 ? void 0 : user.id,\n            isAuthReady\n        });\n    }\n    // TanStack Query otomatik olarak user ID'ye göre cache'i yönetiyor\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: (user === null || user === void 0 ? void 0 : user.id) ? addressKeys.list(user.id) : [\n            'addresses',\n            'no-user'\n        ],\n        queryFn: {\n            \"useAddresses.useQuery\": async ()=>{\n                if (!(user === null || user === void 0 ? void 0 : user.id)) {\n                    console.log('⚠️ useAddresses: No user ID available, returning empty array');\n                    return [];\n                }\n                console.log('📍 Fetching addresses for user:', user.id);\n                const result = await _services_api__WEBPACK_IMPORTED_MODULE_0__.addressService.getAddresses();\n                if (!result.success) {\n                    const error = new Error(result.error || 'Adresler yüklenemedi');\n                    error.name = 'AddressLoadError';\n                    throw error;\n                }\n                // ✅ ID'ye göre sıralama (küçük ID'ler önce - eklenme sırasına göre)\n                const addresses = result.data || [];\n                const sortedAddresses = addresses.sort({\n                    \"useAddresses.useQuery.sortedAddresses\": (a, b)=>{\n                        // 🏠 1. ÖNCE: Varsayılan adres kontrolü\n                        if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce\n                        if (!a.isDefault && b.isDefault) return 1; // b varsayılan ise önce\n                        // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)\n                        if (!a.id && !b.id) return 0;\n                        if (!a.id) return 1;\n                        if (!b.id) return -1;\n                        return a.id - b.id; // Ascending sort (küçük ID'ler önce)\n                    }\n                }[\"useAddresses.useQuery.sortedAddresses\"]);\n                console.log('📍 Addresses sorted (default first, then by ID):', sortedAddresses.map({\n                    \"useAddresses.useQuery\": (addr)=>({\n                            id: addr.id,\n                            title: addr.title,\n                            isDefault: addr.isDefault\n                        })\n                }[\"useAddresses.useQuery\"]));\n                return sortedAddresses;\n            }\n        }[\"useAddresses.useQuery\"],\n        enabled: isAuthReady,\n        staleTime: 5 * 60 * 1000,\n        gcTime: 10 * 60 * 1000,\n        refetchOnMount: true,\n        refetchOnWindowFocus: false,\n        refetchOnReconnect: true,\n        retry: {\n            \"useAddresses.useQuery\": (failureCount, error)=>{\n                // Smart retry logic\n                if (error.name === 'AddressLoadError') {\n                    return failureCount < 2; // Max 2 retry for API errors\n                }\n                return failureCount < 3; // Max 3 retry for other errors\n            }\n        }[\"useAddresses.useQuery\"],\n        retryDelay: {\n            \"useAddresses.useQuery\": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)\n        }[\"useAddresses.useQuery\"]\n    });\n};\n// ➕ POST - Adres ekleme (Optimized)\nconst useCreateAddress = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    // 🎯 CRITICAL FIX: Auth store'dan direkt bilgi al\n    const authStore = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { data: userData } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useUserInfo)();\n    // 🔄 IMPROVED: Auth store priority - race condition'ı önle\n    const user = authStore.user || userData;\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: {\n            \"useCreateAddress.useMutation\": async (addressData)=>{\n                console.log('🔄 TanStack Query: Creating address...', addressData);\n                // 🔐 Auth check - prevent creating address for unauthenticated users\n                if (!authStore.isAuthenticated || !(user === null || user === void 0 ? void 0 : user.id)) {\n                    console.error('❌ Authentication Error:', {\n                        isAuthenticated: authStore.isAuthenticated,\n                        hasUser: !!user,\n                        userId: user === null || user === void 0 ? void 0 : user.id\n                    });\n                    const error = new Error('Kullanıcı oturum açmamış veya kullanıcı bilgileri yüklenmemiş');\n                    error.name = 'AuthenticationError';\n                    throw error;\n                }\n                // 🏠 İlk adres kontrolü - mevcut adresleri al\n                const currentAddresses = queryClient.getQueryData(addressKeys.list(user.id)) || [];\n                // ✅ Eğer hiç adres yoksa, bu adres varsayılan olarak ayarlanır\n                const isFirstAddress = currentAddresses.length === 0;\n                const finalAddressData = {\n                    ...addressData,\n                    isDefault: isFirstAddress ? true : addressData.isDefault\n                };\n                if (isFirstAddress) {\n                    console.log('🏠 İlk adres ekleniyor - otomatik varsayılan olarak ayarlandı');\n                }\n                const result = await _services_api__WEBPACK_IMPORTED_MODULE_0__.addressService.createAddress(finalAddressData);\n                if (!result.success) {\n                    const error = new Error(result.error || 'Adres eklenemedi');\n                    error.name = 'AddressCreateError';\n                    throw error;\n                }\n                console.log('✅ TanStack Query: Address created successfully');\n                return result.data;\n            }\n        }[\"useCreateAddress.useMutation\"],\n        // 🚀 Optimistic Update\n        onMutate: {\n            \"useCreateAddress.useMutation\": async (newAddressData)=>{\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                // Cancel any outgoing refetches\n                await queryClient.cancelQueries({\n                    queryKey\n                });\n                // Snapshot the previous value for rollback\n                const previousAddresses = queryClient.getQueryData(queryKey);\n                // Optimistically update to the new value\n                if (previousAddresses) {\n                    // 🏠 İlk adres kontrolü (optimistic update için)\n                    const isFirstAddress = previousAddresses.length === 0;\n                    const optimisticAddress = {\n                        id: Date.now(),\n                        title: newAddressData.title,\n                        fullAddress: newAddressData.fullAddress,\n                        city: newAddressData.city,\n                        district: newAddressData.district,\n                        postalCode: newAddressData.postalCode,\n                        isDefault: isFirstAddress ? true : newAddressData.isDefault\n                    };\n                    if (isFirstAddress) {\n                        console.log('🏠 Optimistic: İlk adres - otomatik varsayılan olarak ayarlandı');\n                    }\n                    // ✅ Doğru sıralama algoritması ile optimistic ekle\n                    const updatedAddresses = [\n                        ...previousAddresses,\n                        optimisticAddress\n                    ];\n                    const sortedAddresses = updatedAddresses.sort({\n                        \"useCreateAddress.useMutation.sortedAddresses\": (a, b)=>{\n                            // 🏠 1. ÖNCE: Varsayılan adres kontrolü\n                            if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce\n                            if (!a.isDefault && b.isDefault) return 1; // b varsayılan ise önce\n                            // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)\n                            if (!a.id && !b.id) return 0;\n                            if (!a.id) return 1;\n                            if (!b.id) return -1;\n                            return a.id - b.id; // Ascending sort\n                        }\n                    }[\"useCreateAddress.useMutation.sortedAddresses\"]);\n                    queryClient.setQueryData(queryKey, sortedAddresses);\n                    console.log('🚀 Optimistic update: Address added to cache with smart sorting (default first)');\n                }\n                // Return context for rollback\n                return {\n                    previousAddresses\n                };\n            }\n        }[\"useCreateAddress.useMutation\"],\n        onSuccess: {\n            \"useCreateAddress.useMutation\": (newAddress, variables, context)=>{\n                console.log('✅ TanStack Query: Address creation confirmed by server');\n                // Update cache with real server data\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                queryClient.setQueryData(queryKey, {\n                    \"useCreateAddress.useMutation\": function() {\n                        let oldAddresses = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];\n                        // Remove optimistic entry and add real one\n                        const withoutOptimistic = oldAddresses.filter({\n                            \"useCreateAddress.useMutation.withoutOptimistic\": (addr)=>typeof addr.id === 'number' && addr.id > Date.now() - 10000 ? false : true\n                        }[\"useCreateAddress.useMutation.withoutOptimistic\"]);\n                        // ✅ Doğru sıralama algoritması ile ekle\n                        const updatedAddresses = [\n                            ...withoutOptimistic,\n                            newAddress\n                        ];\n                        const sortedAddresses = updatedAddresses.sort({\n                            \"useCreateAddress.useMutation.sortedAddresses\": (a, b)=>{\n                                // 🏠 1. ÖNCE: Varsayılan adres kontrolü\n                                if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce\n                                if (!a.isDefault && b.isDefault) return 1; // b varsayılan ise önce\n                                // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)\n                                if (!a.id && !b.id) return 0;\n                                if (!a.id) return 1;\n                                if (!b.id) return -1;\n                                return a.id - b.id; // Ascending sort\n                            }\n                        }[\"useCreateAddress.useMutation.sortedAddresses\"]);\n                        console.log('📍 Cache updated with smart sorted addresses:', sortedAddresses.map({\n                            \"useCreateAddress.useMutation\": (addr)=>({\n                                    id: addr.id,\n                                    title: addr.title,\n                                    isDefault: addr.isDefault\n                                })\n                        }[\"useCreateAddress.useMutation\"]));\n                        return sortedAddresses;\n                    }\n                }[\"useCreateAddress.useMutation\"]);\n            }\n        }[\"useCreateAddress.useMutation\"],\n        onError: {\n            \"useCreateAddress.useMutation\": (error, variables, context)=>{\n                console.error('❌ TanStack Query: Address creation failed:', error);\n                // Rollback optimistic update\n                if (context === null || context === void 0 ? void 0 : context.previousAddresses) {\n                    const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                    queryClient.setQueryData(queryKey, context.previousAddresses);\n                    console.log('🔄 Rollback: Optimistic update reverted');\n                }\n            }\n        }[\"useCreateAddress.useMutation\"],\n        onSettled: {\n            \"useCreateAddress.useMutation\": ()=>{\n                // Always refetch to ensure consistency\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                queryClient.invalidateQueries({\n                    queryKey\n                });\n            }\n        }[\"useCreateAddress.useMutation\"]\n    });\n};\n// 🗑️ DELETE - Adres silme (Optimized)\nconst useDeleteAddress = (onDefaultRemoved)=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const { data: userData } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useUserInfo)();\n    const user = userData;\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: {\n            \"useDeleteAddress.useMutation\": async (addressId)=>{\n                console.log('🔄 TanStack Query: Deleting address:', addressId);\n                const result = await _services_api__WEBPACK_IMPORTED_MODULE_0__.addressService.deleteAddress(addressId, (user === null || user === void 0 ? void 0 : user.id) || 0);\n                if (!result.success) {\n                    const error = new Error(result.error || 'Adres silinemedi');\n                    error.name = 'AddressDeleteError';\n                    throw error;\n                }\n                console.log('✅ TanStack Query: Address deleted successfully');\n                return addressId;\n            }\n        }[\"useDeleteAddress.useMutation\"],\n        // 🚀 Optimistic Update\n        onMutate: {\n            \"useDeleteAddress.useMutation\": async (addressId)=>{\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                // Cancel any outgoing refetches\n                await queryClient.cancelQueries({\n                    queryKey\n                });\n                // Snapshot the previous value\n                const previousAddresses = queryClient.getQueryData(queryKey);\n                // Find removed address info\n                const removedAddress = previousAddresses === null || previousAddresses === void 0 ? void 0 : previousAddresses.find({\n                    \"useDeleteAddress.useMutation\": (addr)=>addr.id === addressId\n                }[\"useDeleteAddress.useMutation\"]);\n                // 🏠 Check if removing default address\n                const isRemovingDefault = removedAddress === null || removedAddress === void 0 ? void 0 : removedAddress.isDefault;\n                const remainingAddresses = (previousAddresses === null || previousAddresses === void 0 ? void 0 : previousAddresses.filter({\n                    \"useDeleteAddress.useMutation\": (addr)=>addr.id !== addressId\n                }[\"useDeleteAddress.useMutation\"])) || [];\n                // Optimistically remove from cache\n                if (previousAddresses) {\n                    let optimisticAddresses = previousAddresses.filter({\n                        \"useDeleteAddress.useMutation.optimisticAddresses\": (addr)=>addr.id !== addressId\n                    }[\"useDeleteAddress.useMutation.optimisticAddresses\"]);\n                    // 🚨 If we're removing the default address and there are other addresses\n                    if (isRemovingDefault && optimisticAddresses.length > 0) {\n                        // Make the first remaining address default (by ID order)\n                        const nextDefaultAddress = optimisticAddresses.sort({\n                            \"useDeleteAddress.useMutation\": (a, b)=>(a.id || 0) - (b.id || 0)\n                        }[\"useDeleteAddress.useMutation\"])[0];\n                        optimisticAddresses = optimisticAddresses.map({\n                            \"useDeleteAddress.useMutation\": (addr)=>({\n                                    ...addr,\n                                    isDefault: addr.id === nextDefaultAddress.id\n                                })\n                        }[\"useDeleteAddress.useMutation\"]);\n                        console.log('🏠 Default address removed, setting next address as default:', nextDefaultAddress.title);\n                    }\n                    // Apply sorting\n                    const sortedAddresses = optimisticAddresses.sort({\n                        \"useDeleteAddress.useMutation.sortedAddresses\": (a, b)=>{\n                            if (a.isDefault && !b.isDefault) return -1;\n                            if (!a.isDefault && b.isDefault) return 1;\n                            return (a.id || 0) - (b.id || 0);\n                        }\n                    }[\"useDeleteAddress.useMutation.sortedAddresses\"]);\n                    queryClient.setQueryData(queryKey, sortedAddresses);\n                    console.log('🚀 Optimistic update: Address removed from cache with auto-default handling');\n                }\n                return {\n                    previousAddresses,\n                    removedAddress,\n                    wasDefault: isRemovingDefault,\n                    remainingAddresses\n                };\n            }\n        }[\"useDeleteAddress.useMutation\"],\n        onSuccess: {\n            \"useDeleteAddress.useMutation\": (deletedAddressId, addressId, context)=>{\n                console.log('✅ TanStack Query: Address deletion confirmed by server');\n                // 🏠 Check if we removed a default address and need to set a new one\n                if ((context === null || context === void 0 ? void 0 : context.wasDefault) && (context === null || context === void 0 ? void 0 : context.remainingAddresses.length) > 0) {\n                    // Find the next default address (first by ID order)\n                    const nextDefaultAddress = context.remainingAddresses.sort({\n                        \"useDeleteAddress.useMutation\": (a, b)=>(a.id || 0) - (b.id || 0)\n                    }[\"useDeleteAddress.useMutation\"])[0];\n                    console.log('🏠 Default address was removed, next default should be:', nextDefaultAddress.title);\n                    // Call the callback if provided with both addresses\n                    if (onDefaultRemoved && nextDefaultAddress) {\n                        onDefaultRemoved(nextDefaultAddress, context.removedAddress);\n                    }\n                }\n            }\n        }[\"useDeleteAddress.useMutation\"],\n        onError: {\n            \"useDeleteAddress.useMutation\": (error, addressId, context)=>{\n                console.error('❌ TanStack Query: Address deletion failed:', error);\n                // Rollback optimistic update\n                if (context === null || context === void 0 ? void 0 : context.previousAddresses) {\n                    const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                    queryClient.setQueryData(queryKey, context.previousAddresses);\n                    console.log('🔄 Rollback: Deleted address restored to cache');\n                }\n            }\n        }[\"useDeleteAddress.useMutation\"],\n        onSettled: {\n            \"useDeleteAddress.useMutation\": ()=>{\n                // Always refetch to ensure consistency\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                queryClient.invalidateQueries({\n                    queryKey\n                });\n            }\n        }[\"useDeleteAddress.useMutation\"]\n    });\n};\n// 🏠 SET DEFAULT - Adres varsayılan yapma (Optimized)\nconst useSetDefaultAddress = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const { data: userData } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useUserInfo)();\n    const user = userData;\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: {\n            \"useSetDefaultAddress.useMutation\": async (addressId)=>{\n                console.log('🔄 TanStack Query: Setting address as default:', addressId);\n                const result = await _services_api__WEBPACK_IMPORTED_MODULE_0__.addressService.setDefaultAddress(addressId);\n                if (!result.success) {\n                    const error = new Error(result.error || 'Varsayılan adres ayarlanamadı');\n                    error.name = 'SetDefaultAddressError';\n                    throw error;\n                }\n                console.log('✅ TanStack Query: Address set as default successfully');\n                return result.data; // Updated address with isDefault: true\n            }\n        }[\"useSetDefaultAddress.useMutation\"],\n        // 🚀 Optimistic Update\n        onMutate: {\n            \"useSetDefaultAddress.useMutation\": async (addressId)=>{\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                // Cancel any outgoing refetches\n                await queryClient.cancelQueries({\n                    queryKey\n                });\n                // Snapshot the previous value\n                const previousAddresses = queryClient.getQueryData(queryKey);\n                // Optimistically update addresses\n                if (previousAddresses) {\n                    const optimisticAddresses = previousAddresses.map({\n                        \"useSetDefaultAddress.useMutation.optimisticAddresses\": (addr)=>({\n                                ...addr,\n                                isDefault: addr.id === addressId\n                            })\n                    }[\"useSetDefaultAddress.useMutation.optimisticAddresses\"]);\n                    // ✅ Apply sorting to optimistic data\n                    const sortedAddresses = optimisticAddresses.sort({\n                        \"useSetDefaultAddress.useMutation.sortedAddresses\": (a, b)=>{\n                            // 🏠 1. ÖNCE: Varsayılan adres kontrolü\n                            if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce\n                            if (!a.isDefault && b.isDefault) return 1; // b varsayılan ise önce\n                            // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)\n                            if (!a.id && !b.id) return 0;\n                            if (!a.id) return 1;\n                            if (!b.id) return -1;\n                            return a.id - b.id; // Ascending sort\n                        }\n                    }[\"useSetDefaultAddress.useMutation.sortedAddresses\"]);\n                    queryClient.setQueryData(queryKey, sortedAddresses);\n                    console.log('🚀 Optimistic update: Default address changed with smart sorting');\n                }\n                return {\n                    previousAddresses\n                };\n            }\n        }[\"useSetDefaultAddress.useMutation\"],\n        onSuccess: {\n            \"useSetDefaultAddress.useMutation\": (updatedAddress, addressId, context)=>{\n                console.log('✅ TanStack Query: Set default address confirmed by server');\n                // Update cache with real server data\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                queryClient.setQueryData(queryKey, {\n                    \"useSetDefaultAddress.useMutation\": function() {\n                        let oldAddresses = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];\n                        const updatedAddresses = oldAddresses.map({\n                            \"useSetDefaultAddress.useMutation.updatedAddresses\": (addr)=>({\n                                    ...addr,\n                                    isDefault: addr.id === addressId\n                                })\n                        }[\"useSetDefaultAddress.useMutation.updatedAddresses\"]);\n                        // ✅ Apply sorting to server data\n                        const sortedAddresses = updatedAddresses.sort({\n                            \"useSetDefaultAddress.useMutation.sortedAddresses\": (a, b)=>{\n                                // 🏠 1. ÖNCE: Varsayılan adres kontrolü\n                                if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce\n                                if (!a.isDefault && b.isDefault) return 1; // b varsayılan ise önce\n                                // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)\n                                if (!a.id && !b.id) return 0;\n                                if (!a.id) return 1;\n                                if (!b.id) return -1;\n                                return a.id - b.id; // Ascending sort\n                            }\n                        }[\"useSetDefaultAddress.useMutation.sortedAddresses\"]);\n                        console.log('📍 Cache updated with new default address (smart sorted):', sortedAddresses.map({\n                            \"useSetDefaultAddress.useMutation\": (addr)=>({\n                                    id: addr.id,\n                                    title: addr.title,\n                                    isDefault: addr.isDefault\n                                })\n                        }[\"useSetDefaultAddress.useMutation\"]));\n                        return sortedAddresses;\n                    }\n                }[\"useSetDefaultAddress.useMutation\"]);\n            }\n        }[\"useSetDefaultAddress.useMutation\"],\n        onError: {\n            \"useSetDefaultAddress.useMutation\": (error, addressId, context)=>{\n                console.error('❌ TanStack Query: Set default address failed:', error);\n                // Rollback optimistic update\n                if (context === null || context === void 0 ? void 0 : context.previousAddresses) {\n                    const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                    queryClient.setQueryData(queryKey, context.previousAddresses);\n                    console.log('🔄 Rollback: Default address change reverted');\n                }\n            }\n        }[\"useSetDefaultAddress.useMutation\"],\n        onSettled: {\n            \"useSetDefaultAddress.useMutation\": ()=>{\n                // Always refetch to ensure consistency\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                queryClient.invalidateQueries({\n                    queryKey\n                });\n            }\n        }[\"useSetDefaultAddress.useMutation\"]\n    });\n};\n// 🔄 Manual refetch helper\nconst useRefreshAddresses = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const { data: userData } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useUserInfo)();\n    const user = userData;\n    return ()=>{\n        queryClient.invalidateQueries({\n            queryKey: addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0)\n        });\n    };\n};\n// 📊 Cache utilities\nconst useAddressCacheUtils = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const { data: userData } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useUserInfo)();\n    const user = userData;\n    return {\n        // Prefetch addresses\n        prefetchAddresses: ()=>{\n            return queryClient.prefetchQuery({\n                queryKey: addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0),\n                queryFn: async ()=>{\n                    const result = await _services_api__WEBPACK_IMPORTED_MODULE_0__.addressService.getAddresses();\n                    return result.success ? result.data || [] : [];\n                },\n                staleTime: 5 * 60 * 1000\n            });\n        },\n        // Get cached addresses without triggering fetch\n        getCachedAddresses: ()=>{\n            return queryClient.getQueryData(addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0));\n        },\n        // Set addresses manually (for testing or initial data)\n        setCachedAddresses: (addresses)=>{\n            queryClient.setQueryData(addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0), addresses);\n        },\n        // Clear address cache\n        clearAddressCache: ()=>{\n            queryClient.removeQueries({\n                queryKey: addressKeys.all\n            });\n        }\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAddresses.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authKeys: () => (/* binding */ authKeys),\n/* harmony export */   useAuthCacheUtils: () => (/* binding */ useAuthCacheUtils),\n/* harmony export */   useLoginMutation: () => (/* binding */ useLoginMutation),\n/* harmony export */   useLogoutMutation: () => (/* binding */ useLogoutMutation),\n/* harmony export */   useProfileInfo: () => (/* binding */ useProfileInfo),\n/* harmony export */   useRegisterMutation: () => (/* binding */ useRegisterMutation),\n/* harmony export */   useUserInfo: () => (/* binding */ useUserInfo)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/authService */ \"(app-pages-browser)/./src/services/authService.ts\");\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/authStore */ \"(app-pages-browser)/./src/stores/authStore.ts\");\n/* harmony import */ var _useAddresses__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useAddresses */ \"(app-pages-browser)/./src/hooks/useAddresses.ts\");\n/* harmony import */ var _useDiscountRate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useDiscountRate */ \"(app-pages-browser)/./src/hooks/useDiscountRate.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _stores_customerPriceStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/stores/customerPriceStore */ \"(app-pages-browser)/./src/stores/customerPriceStore.ts\");\n\n\n\n\n\n\n\n\n// 🏭 Query Key Factory\nconst authKeys = {\n    all: [\n        'auth'\n    ],\n    user: ()=>[\n            ...authKeys.all,\n            'user'\n        ],\n    profile: ()=>[\n            ...authKeys.all,\n            'profile'\n        ],\n    profileInfo: ()=>[\n            ...authKeys.all,\n            'profileInfo'\n        ]\n};\n// 🔍 User Info Query - Optimized caching\nconst useUserInfo = ()=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const query = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: authKeys.user(),\n        queryFn: {\n            \"useUserInfo.useQuery[query]\": async ()=>{\n                try {\n                    const userData = await _services_authService__WEBPACK_IMPORTED_MODULE_1__.authService.getUserInfo();\n                    if (!userData) {\n                        return null;\n                    }\n                    // Map backend data to frontend format\n                    let user = userData.user || userData;\n                    // Handle missing user data - Backend artık userId kullanıyor\n                    if (!user.userId && !user.id && !user.email) {\n                        user = {\n                            userId: 1,\n                            email: '<EMAIL>',\n                            firstName: 'User',\n                            lastName: 'User',\n                            phoneNumber: '',\n                            isActive: true,\n                            registeredAt: new Date().toISOString(),\n                            membershipLevelId: 1,\n                            careerRankId: 1,\n                            referenceId: 0,\n                            roles: [\n                                'Customer'\n                            ]\n                        };\n                    }\n                    // Map to AuthUser type properly - Backend artık userId kullanıyor\n                    const mappedUser = {\n                        id: user.userId !== undefined ? user.userId : user.id !== undefined ? user.id : 1,\n                        firstName: user.firstName || 'User',\n                        lastName: user.lastName || 'User',\n                        email: user.email || '<EMAIL>',\n                        phoneNumber: user.phoneNumber || '',\n                        isActive: user.isActive !== undefined ? user.isActive : true,\n                        registeredAt: user.registeredAt || new Date().toISOString(),\n                        membershipLevelId: user.membershipLevelId !== undefined ? user.membershipLevelId : 1,\n                        careerRankId: user.careerRankId !== undefined ? user.careerRankId : 1,\n                        referenceId: user.referenceId !== undefined ? user.referenceId : user.referanceId !== undefined ? user.referanceId : 0,\n                        roles: user.roles || (user.role ? [\n                            user.role\n                        ] : [\n                            'Customer'\n                        ]),\n                        role: user.role ? user.role.toLowerCase() : user.roles && user.roles.includes('Admin') ? 'admin' : user.roles && user.roles.includes('Dealership') ? 'dealership' : 'customer',\n                        membershipLevel: user.membershipLevelId !== undefined ? user.membershipLevelId : 0,\n                        joinDate: user.registeredAt ? new Date(user.registeredAt).toISOString().split('T')[0] : '',\n                        isDealershipApproved: user.roles && user.roles.includes('Dealership')\n                    };\n                    return mappedUser;\n                } catch (error) {\n                    throw error;\n                }\n            }\n        }[\"useUserInfo.useQuery[query]\"],\n        // Query her zaman aktif - JWT sistem artık doğru çalışıyor\n        enabled: true,\n        // 📝 Cache Strategy - VERY IMPORTANT for performance\n        staleTime: 15 * 60 * 1000,\n        gcTime: 30 * 60 * 1000,\n        // 🔄 Refetch Strategy - Solve focus problems\n        refetchOnWindowFocus: false,\n        refetchOnMount: 'always',\n        refetchOnReconnect: true,\n        // ⚡ Background Updates\n        refetchInterval: false,\n        refetchIntervalInBackground: false,\n        // 🛡️ Error Handling\n        retry: {\n            \"useUserInfo.useQuery[query]\": (failureCount, error)=>{\n                var _error_response, _error_response1;\n                // Don't retry on auth errors (401, 403)\n                if ((error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401 || (error === null || error === void 0 ? void 0 : (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n                    return false;\n                }\n                return failureCount < 2;\n            }\n        }[\"useUserInfo.useQuery[query]\"],\n        retryDelay: {\n            \"useUserInfo.useQuery[query]\": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)\n        }[\"useUserInfo.useQuery[query]\"],\n        throwOnError: {\n            \"useUserInfo.useQuery[query]\": (error)=>{\n                var _error_response;\n                // Sadece 401 (Unauthorized) hatası DIŞINDAKİ hataları fırlat.\n                // 401 hatası bizim için \"kullanıcı giriş yapmamış\" demek, bu bir çökme hatası değil.\n                // Böylece Next.js'in geliştirme overlay'i gereksiz yere tetiklenmez.\n                return ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) !== 401;\n            }\n        }[\"useUserInfo.useQuery[query]\"]\n    });\n    // 📊 Handle query state changes with useEffect (TanStack Query v5 best practice)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUserInfo.useEffect\": ()=>{\n            if (query.isSuccess && query.data) {\n                _stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore.setState({\n                    user: query.data,\n                    isAuthenticated: true,\n                    error: null\n                });\n            }\n        }\n    }[\"useUserInfo.useEffect\"], [\n        query.isSuccess,\n        query.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUserInfo.useEffect\": ()=>{\n            if (query.isError && query.error) {\n                var _query_error_response, _query_error;\n                // Handle auth errors\n                if (((_query_error = query.error) === null || _query_error === void 0 ? void 0 : (_query_error_response = _query_error.response) === null || _query_error_response === void 0 ? void 0 : _query_error_response.status) === 401) {\n                    _stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore.setState({\n                        user: null,\n                        isAuthenticated: false\n                    });\n                }\n            }\n        }\n    }[\"useUserInfo.useEffect\"], [\n        query.isError,\n        query.error\n    ]);\n    return query;\n};\n// 🔐 Login Mutation - Optimized\nconst useLoginMutation = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.useQueryClient)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const { user, setUser, clearAuth, setLoading } = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    // Login mutation'ı artık bir AuthUser değil, işlemin başarısını (boolean) döndürür.\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.useMutation)({\n        mutationFn: {\n            \"useLoginMutation.useMutation\": async (credentials)=>{\n                const success = await _services_authService__WEBPACK_IMPORTED_MODULE_1__.authService.login(credentials.email, credentials.password);\n                if (!success) {\n                    throw new Error('Login failed: Invalid credentials from service');\n                }\n                return success; // Sadece true döner\n            }\n        }[\"useLoginMutation.useMutation\"],\n        // Login başarılı olunca (true dönünce) bu blok çalışır.\n        onSuccess: {\n            \"useLoginMutation.useMutation\": async ()=>{\n                try {\n                    // 1. User query'sini geçersiz kıl (stale olarak işaretle).\n                    await queryClient.invalidateQueries({\n                        queryKey: authKeys.user()\n                    });\n                    // 2. Geçersiz kılınan query'yi hemen fetch et ve gerçek kullanıcı verisini al.\n                    const userFromApi = await queryClient.fetchQuery({\n                        queryKey: authKeys.user()\n                    });\n                    if (!userFromApi || !userFromApi.id) {\n                        throw new Error('Fetched user data is invalid or missing ID.');\n                    }\n                    // 3. Alınan gerçek veriyle AuthStore'u güncelle.\n                    _stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore.setState({\n                        isAuthenticated: true,\n                        error: null,\n                        isLoading: false,\n                        user: userFromApi\n                    });\n                    // 4. Yeni kullanıcı için adres cache'ini temizle.\n                    queryClient.invalidateQueries({\n                        queryKey: _useAddresses__WEBPACK_IMPORTED_MODULE_3__.addressKeys.all\n                    });\n                    if (userFromApi.id) {\n                        queryClient.invalidateQueries({\n                            queryKey: _useAddresses__WEBPACK_IMPORTED_MODULE_3__.addressKeys.list(userFromApi.id)\n                        });\n                        queryClient.removeQueries({\n                            queryKey: _useAddresses__WEBPACK_IMPORTED_MODULE_3__.addressKeys.list(userFromApi.id)\n                        });\n                    }\n                    // 5. Yeni kullanıcı için discount rate cache'ini temizle ve yeniden çek.\n                    queryClient.invalidateQueries({\n                        queryKey: _useDiscountRate__WEBPACK_IMPORTED_MODULE_4__.discountRateKeys.all\n                    });\n                    if (userFromApi.id) {\n                        queryClient.invalidateQueries({\n                            queryKey: _useDiscountRate__WEBPACK_IMPORTED_MODULE_4__.discountRateKeys.user(userFromApi.id)\n                        });\n                        queryClient.removeQueries({\n                            queryKey: _useDiscountRate__WEBPACK_IMPORTED_MODULE_4__.discountRateKeys.user(userFromApi.id)\n                        });\n                    }\n                    // 6. Customer price state'ini resetle\n                    _stores_customerPriceStore__WEBPACK_IMPORTED_MODULE_6__.useCustomerPriceStore.getState().resetCustomerPrice();\n                    // 7. Sepet cache'ini temizle ve yeniden çek (giriş sonrası sepet sayısı güncellensin)\n                    queryClient.invalidateQueries({\n                        queryKey: [\n                            'cartCount'\n                        ]\n                    });\n                    queryClient.invalidateQueries({\n                        queryKey: [\n                            'cartItems'\n                        ]\n                    });\n                    try {\n                        // await get().checkAuth(); // 🗑️ Silindi: Artık store'da checkAuth yok. Invalidate yeterli.\n                        console.log('✅ Background checkAuth başarılı - user bilgisi güncellendi');\n                    } catch (checkAuthError) {\n                        console.log('⚠️ Background checkAuth başarısız - mevcut user bilgisi korunuyor:', checkAuthError.message);\n                    }\n                } catch (error) {\n                    _stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore.setState({\n                        error: 'Giriş başarılı fakat kullanıcı verileri alınamadı.',\n                        isAuthenticated: false,\n                        user: null\n                    });\n                }\n            }\n        }[\"useLoginMutation.useMutation\"],\n        onError: {\n            \"useLoginMutation.useMutation\": (error)=>{\n                var _error_response_data, _error_response;\n                _stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore.setState({\n                    error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Giriş başarısız',\n                    isAuthenticated: false,\n                    user: null\n                });\n            }\n        }[\"useLoginMutation.useMutation\"]\n    });\n};\n// 🚪 Logout Mutation - Optimized  \nconst useLogoutMutation = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.useMutation)({\n        mutationFn: {\n            \"useLogoutMutation.useMutation\": async ()=>{\n                await _services_authService__WEBPACK_IMPORTED_MODULE_1__.authService.logout();\n            }\n        }[\"useLogoutMutation.useMutation\"],\n        onSuccess: {\n            \"useLogoutMutation.useMutation\": ()=>{\n                // Tüm cache'i temizle (discount rate dahil)\n                queryClient.clear();\n                _stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore.setState({\n                    user: null,\n                    isAuthenticated: false,\n                    error: null\n                });\n                // Customer price state'ini resetle\n                _stores_customerPriceStore__WEBPACK_IMPORTED_MODULE_6__.useCustomerPriceStore.getState().resetCustomerPrice();\n            }\n        }[\"useLogoutMutation.useMutation\"],\n        onError: {\n            \"useLogoutMutation.useMutation\": (error)=>{\n                // Hata durumunda da tüm cache'i temizle\n                queryClient.clear();\n                _stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore.setState({\n                    user: null,\n                    isAuthenticated: false\n                });\n                // Customer price state'ini resetle\n                _stores_customerPriceStore__WEBPACK_IMPORTED_MODULE_6__.useCustomerPriceStore.getState().resetCustomerPrice();\n            }\n        }[\"useLogoutMutation.useMutation\"]\n    });\n};\n// 📝 Register Mutation\nconst useRegisterMutation = ()=>{\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.useMutation)({\n        mutationFn: {\n            \"useRegisterMutation.useMutation\": async (data)=>{\n                if (data.password !== data.confirmPassword) {\n                    throw new Error('Şifreler eşleşmiyor');\n                }\n                const response = await _services_authService__WEBPACK_IMPORTED_MODULE_1__.authService.register({\n                    firstName: data.firstName,\n                    lastName: data.lastName,\n                    email: data.email,\n                    password: data.password,\n                    phoneNumber: data.phoneNumber || '',\n                    referansCode: data.referansCode\n                });\n                // Başarı durumunu backend'den gelen response'a göre belirle\n                // Örnek: return response.success;\n                return response.success;\n            }\n        }[\"useRegisterMutation.useMutation\"],\n        onSuccess: {\n            \"useRegisterMutation.useMutation\": ()=>{\n                // Kayıt başarılı olunca ne yapılacağına burada karar verilir.\n                // Şimdilik sadece başarılı kabul ediyoruz, otomatik login yapmıyoruz.\n                // İstenirse burada login mutation'ı tetiklenebilir.\n                _stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore.setState({\n                    error: null\n                });\n            }\n        }[\"useRegisterMutation.useMutation\"],\n        onError: {\n            \"useRegisterMutation.useMutation\": (error)=>{\n                _stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore.setState({\n                    error: error.message || 'Kayıt başarısız'\n                });\n            }\n        }[\"useRegisterMutation.useMutation\"]\n    });\n};\n// 🔄 Manual Cache Utils\nconst useAuthCacheUtils = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.useQueryClient)();\n    return {\n        // Force refresh user data\n        refreshUser: ()=>{\n            return queryClient.invalidateQueries({\n                queryKey: authKeys.user()\n            });\n        },\n        // Get cached user data\n        getCachedUser: ()=>{\n            return queryClient.getQueryData(authKeys.user()) || null;\n        },\n        // Update cached user data\n        updateCachedUser: (userData)=>{\n            queryClient.setQueryData(authKeys.user(), userData);\n        },\n        // Clear all auth cache\n        clearAuthCache: ()=>{\n            queryClient.removeQueries({\n                queryKey: authKeys.all\n            });\n        }\n    };\n};\n// 🎯 Profile Info Query - Detaylı profil bilgileri (fotoğraf URL'si dahil)\nconst useProfileInfo = ()=>{\n    const query = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: authKeys.profileInfo(),\n        queryFn: {\n            \"useProfileInfo.useQuery[query]\": async ()=>{\n                try {\n                    const profileData = await _services_authService__WEBPACK_IMPORTED_MODULE_1__.authService.getProfileInfo();\n                    console.log('📋 Profile Info Data:', profileData);\n                    return profileData;\n                } catch (error) {\n                    console.error('❌ Profile Info Error:', error);\n                    throw error;\n                }\n            }\n        }[\"useProfileInfo.useQuery[query]\"],\n        // Query her zaman aktif - JWT sistem artık doğru çalışıyor\n        enabled: true,\n        // Cache Strategy\n        staleTime: 5 * 60 * 1000,\n        gcTime: 15 * 60 * 1000,\n        // Refetch Strategy\n        refetchOnWindowFocus: false,\n        refetchOnMount: 'always',\n        refetchOnReconnect: true,\n        // Background Updates\n        refetchInterval: false,\n        refetchIntervalInBackground: false,\n        // Error Handling\n        retry: {\n            \"useProfileInfo.useQuery[query]\": (failureCount, error)=>{\n                var _error_response, _error_response1;\n                if ((error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401 || (error === null || error === void 0 ? void 0 : (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n                    return false;\n                }\n                return failureCount < 2;\n            }\n        }[\"useProfileInfo.useQuery[query]\"],\n        retryDelay: {\n            \"useProfileInfo.useQuery[query]\": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)\n        }[\"useProfileInfo.useQuery[query]\"],\n        throwOnError: {\n            \"useProfileInfo.useQuery[query]\": (error)=>{\n                var _error_response;\n                return ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) !== 401;\n            }\n        }[\"useProfileInfo.useQuery[query]\"]\n    });\n    return query;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useCart.ts":
/*!******************************!*\
  !*** ./src/hooks/useCart.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateDiscountedPrice: () => (/* binding */ calculateDiscountedPrice),\n/* harmony export */   calculatePoints: () => (/* binding */ calculatePoints),\n/* harmony export */   useAddToCart: () => (/* binding */ useAddToCart),\n/* harmony export */   useCartCount: () => (/* binding */ useCartCount),\n/* harmony export */   useCartItems: () => (/* binding */ useCartItems),\n/* harmony export */   useDiscountRate: () => (/* binding */ useDiscountRate),\n/* harmony export */   useRemoveFromCart: () => (/* binding */ useRemoveFromCart),\n/* harmony export */   useUpdateCartQuantity: () => (/* binding */ useUpdateCartQuantity),\n/* harmony export */   useUpdateCartType: () => (/* binding */ useUpdateCartType)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _components_auth_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/auth/AuthContext */ \"(app-pages-browser)/./src/components/auth/AuthContext.tsx\");\n\n\n\n// Sepet içeriklerini getir\nconst useCartItems = ()=>{\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({\n        queryKey: [\n            'cartItems'\n        ],\n        queryFn: {\n            \"useCartItems.useQuery\": async ()=>{\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.cartService.getCartItems();\n                if (response.success) {\n                    return response.data.data; // API response'u data wrapper'ı içinde geliyor\n                }\n                throw new Error(response.error || 'Sepet içerikleri alınamadı');\n            }\n        }[\"useCartItems.useQuery\"],\n        staleTime: 30 * 1000,\n        refetchOnWindowFocus: true,\n        refetchOnMount: true\n    });\n};\n// Sepetteki ürün sayısını getir\nconst useCartCount = ()=>{\n    const { isAuthenticated } = (0,_components_auth_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({\n        queryKey: [\n            'cartCount',\n            isAuthenticated\n        ],\n        queryFn: {\n            \"useCartCount.useQuery\": async ()=>{\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.cartService.getCartCount();\n                if (response.success) {\n                    return response.data.data; // API response'u data wrapper'ı içinde geliyor\n                }\n                throw new Error(response.error || 'Sepet ürün sayısı alınamadı');\n            }\n        }[\"useCartCount.useQuery\"],\n        staleTime: 30 * 1000,\n        refetchOnWindowFocus: true,\n        refetchOnMount: true\n    });\n};\n// İndirim oranını getir\nconst useDiscountRate = ()=>{\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({\n        queryKey: [\n            'discountRate'\n        ],\n        queryFn: {\n            \"useDiscountRate.useQuery\": async ()=>{\n                try {\n                    const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.userService.getDiscountRate();\n                    console.log('🔍 Discount Rate API Response:', response);\n                    if (response.success) {\n                        // API'den direkt {discountRate: 10} geliyor\n                        return response.data || {\n                            discountRate: 0\n                        };\n                    }\n                    // Hata durumunda 0 döndür, throw etme\n                    console.warn('İndirim oranı alınamadı:', response.error);\n                    return {\n                        discountRate: 0\n                    };\n                } catch (error) {\n                    // Network hatası vs. durumunda da 0 döndür\n                    console.warn('İndirim oranı alınırken hata:', error);\n                    return {\n                        discountRate: 0\n                    };\n                }\n            }\n        }[\"useDiscountRate.useQuery\"],\n        staleTime: 5 * 60 * 1000,\n        refetchOnWindowFocus: false,\n        refetchOnMount: true,\n        retry: false\n    });\n};\n// Sepete ürün ekleme mutation'ı\nconst useAddToCart = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: {\n            \"useAddToCart.useMutation\": async (param)=>{\n                let { productVariantId, quantity, isCustomerPrice } = param;\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.cartService.addToCart(productVariantId, quantity, isCustomerPrice);\n                if (!response.success) {\n                    throw new Error(response.error || 'Ürün sepete eklenemedi');\n                }\n                return response.data;\n            }\n        }[\"useAddToCart.useMutation\"],\n        onSuccess: {\n            \"useAddToCart.useMutation\": ()=>{\n                // Sepet verilerini yenile\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartItems'\n                    ]\n                });\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartCount'\n                    ]\n                });\n            }\n        }[\"useAddToCart.useMutation\"],\n        onError: {\n            \"useAddToCart.useMutation\": (error)=>{\n                console.error('Sepete ürün ekleme hatası:', error);\n            }\n        }[\"useAddToCart.useMutation\"]\n    });\n};\n// Sepetten ürün çıkarma mutation'ı\nconst useRemoveFromCart = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: {\n            \"useRemoveFromCart.useMutation\": async (productVariantId)=>{\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.cartService.removeFromCart(productVariantId);\n                if (!response.success) {\n                    throw new Error(response.error || 'Ürün sepetten çıkarılamadı');\n                }\n                return response.data;\n            }\n        }[\"useRemoveFromCart.useMutation\"],\n        onSuccess: {\n            \"useRemoveFromCart.useMutation\": ()=>{\n                // Sepet verilerini yenile\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartItems'\n                    ]\n                });\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartCount'\n                    ]\n                });\n            }\n        }[\"useRemoveFromCart.useMutation\"],\n        onError: {\n            \"useRemoveFromCart.useMutation\": (error)=>{\n                console.error('Sepetten ürün çıkarma hatası:', error);\n            }\n        }[\"useRemoveFromCart.useMutation\"]\n    });\n};\n// Sepet ürün miktarını güncelleme mutation'ı\nconst useUpdateCartQuantity = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: {\n            \"useUpdateCartQuantity.useMutation\": async (param)=>{\n                let { productVariantId, quantity } = param;\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.cartService.updateCartQuantity(productVariantId, quantity);\n                if (!response.success) {\n                    throw new Error(response.error || 'Ürün miktarı güncellenemedi');\n                }\n                return response.data;\n            }\n        }[\"useUpdateCartQuantity.useMutation\"],\n        onSuccess: {\n            \"useUpdateCartQuantity.useMutation\": ()=>{\n                // Sepet verilerini yenile\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartItems'\n                    ]\n                });\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartCount'\n                    ]\n                });\n            }\n        }[\"useUpdateCartQuantity.useMutation\"],\n        onError: {\n            \"useUpdateCartQuantity.useMutation\": (error)=>{\n                console.error('Sepet ürün miktarı güncelleme hatası:', error);\n            }\n        }[\"useUpdateCartQuantity.useMutation\"]\n    });\n};\n// Sepet tipini güncelleme mutation'ı (customer price toggle)\nconst useUpdateCartType = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: {\n            \"useUpdateCartType.useMutation\": async ()=>{\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.userService.updateCartType();\n                if (!response.success) {\n                    throw new Error(response.error || 'Sepet tipi güncellenemedi');\n                }\n                return response.data;\n            }\n        }[\"useUpdateCartType.useMutation\"],\n        onSuccess: {\n            \"useUpdateCartType.useMutation\": ()=>{\n                // Sepet verilerini yenile\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartItems'\n                    ]\n                });\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartCount'\n                    ]\n                });\n            }\n        }[\"useUpdateCartType.useMutation\"],\n        onError: {\n            \"useUpdateCartType.useMutation\": (error)=>{\n                console.error('Sepet tipi güncelleme hatası:', error);\n            }\n        }[\"useUpdateCartType.useMutation\"]\n    });\n};\n// Puan hesaplama fonksiyonu\nconst calculatePoints = (ratio, price)=>{\n    return Math.round(ratio / 100 * price);\n};\n// Fiyat hesaplama fonksiyonu (indirim dahil)\nconst calculateDiscountedPrice = (originalPrice, discountRate, isCustomerPrice)=>{\n    if (isCustomerPrice || !discountRate || discountRate <= 0) {\n        return originalPrice;\n    }\n    return originalPrice * (1 - discountRate / 100);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useCart.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useDiscountRate.ts":
/*!**************************************!*\
  !*** ./src/hooks/useDiscountRate.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   discountRateKeys: () => (/* binding */ discountRateKeys),\n/* harmony export */   useDiscountRate: () => (/* binding */ useDiscountRate)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _components_auth_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/auth/AuthContext */ \"(app-pages-browser)/./src/components/auth/AuthContext.tsx\");\n\n\n\n// Query key factory\nconst discountRateKeys = {\n    all: [\n        'discountRate'\n    ],\n    user: (userId)=>[\n            ...discountRateKeys.all,\n            'user',\n            userId\n        ]\n};\n// Kullanıcı indirim oranını getiren hook\nconst useDiscountRate = ()=>{\n    const { user, isAuthenticated } = (0,_components_auth_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({\n        queryKey: discountRateKeys.user((user === null || user === void 0 ? void 0 : user.id) || null),\n        queryFn: {\n            \"useDiscountRate.useQuery\": async ()=>{\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.userService.getDiscountRate();\n                if (!response.success) {\n                    throw new Error(response.error || 'İndirim oranı alınamadı');\n                }\n                return response.data;\n            }\n        }[\"useDiscountRate.useQuery\"],\n        enabled: isAuthenticated && !!(user === null || user === void 0 ? void 0 : user.id),\n        staleTime: 5 * 60 * 1000,\n        refetchOnWindowFocus: false,\n        refetchOnMount: true,\n        retry: {\n            \"useDiscountRate.useQuery\": (failureCount, error)=>{\n                var _error_response;\n                // 401 hatası alırsak (giriş yapmamış) retry yapma\n                if ((error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                    return false;\n                }\n                return failureCount < 2;\n            }\n        }[\"useDiscountRate.useQuery\"],\n        retryDelay: {\n            \"useDiscountRate.useQuery\": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)\n        }[\"useDiscountRate.useQuery\"]\n    });\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useDiscountRate.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/authService.ts":
/*!*************************************!*\
  !*** ./src/services/authService.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/constants/apiEndpoints */ \"(app-pages-browser)/./src/constants/apiEndpoints.ts\");\n\n\n// Authentication Services\nconst authService = {\n    // Clear authentication cookies\n    clearAuthCookies () {\n        if (true) {\n            console.log('🧹 Auth cookieleri temizleniyor...');\n            // Bilinen auth cookieleri temizle\n            const authCookies = [\n                'AccessToken',\n                'RefreshToken',\n                'AuthToken',\n                'Token'\n            ];\n            authCookies.forEach((cookieName)=>{\n                // Farklı pathlerde temizle\n                document.cookie = cookieName + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';\n                document.cookie = cookieName + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=' + window.location.hostname;\n                document.cookie = cookieName + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.' + window.location.hostname;\n            });\n            console.log('🧹 Auth cookieleri temizlendi');\n        }\n    },\n    // Login\n    async login (email, password) {\n        console.log('🚪 AuthService login başlıyor...', {\n            email\n        });\n        // Login başlangıcında logout flag'ini kontrol et ve temizle\n        const hasLoggedOut =  true ? localStorage.getItem('hasLoggedOut') : 0;\n        console.log('🔍 Login başlangıcında logout flag kontrolü:', {\n            hasLoggedOut\n        });\n        if (hasLoggedOut) {\n            console.log('🧹 Eski logout flag temizleniyor (yeni login)');\n            if (true) {\n                localStorage.removeItem('hasLoggedOut');\n            }\n        }\n        const loginData = {\n            email,\n            password\n        };\n        console.log('📤 Gönderilecek veri:', loginData);\n        console.log('📡 API URL:', _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defaults.baseURL);\n        console.log('🔧 API Config:', {\n            baseURL: _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defaults.baseURL,\n            withCredentials: _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defaults.withCredentials,\n            headers: _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defaults.headers\n        });\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data3, _response_data_message, _response_data4, _response_data_message1, _response_data5, _response_data6, _response_data7, _response_data8, _response_data_message2, _response_data9, _response_data_message3, _response_data10, _response_data_message4, _response_data11, _response_data_message5, _response_data12, _response_data13;\n            console.log(\"\\uD83D\\uDCE1 POST \".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.LOGIN, \" \\xe7ağrısı yapılıyor...\"));\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.LOGIN, loginData);\n            console.log('📡 Login response:', response.data);\n            console.log('📊 Response status:', response.status);\n            console.log('🍪 Response headers:', response.headers);\n            // Backend Set-Cookie ile token gönderdi\n            console.log('🍪 Backend Set-Cookie ile token gönderdi');\n            // Response structure debug\n            console.log('🔍 Response structure debug:', {\n                data: response.data,\n                status: response.status,\n                message: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.message,\n                hasData: !!response.data,\n                statusType: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.statusType,\n                dataKeys: response.data ? Object.keys(response.data) : []\n            });\n            // GÜÇLÜ SUCCESS DETECTION\n            // HTTP status 200 = Backend başarılı response verdi\n            // Set-Cookie header varlığı = Backend token gönderdi\n            const httpSuccess = response.status === 200;\n            const hasSetCookie = response.headers['set-cookie'] || response.headers['Set-Cookie'] || response.headers['SET-COOKIE'];\n            // Çeşitli backend response formatlarını kontrol et\n            const messageSuccess = ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.message) === 'Giriş başarılı.' || ((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.message) === 'Login successful' || ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_message = _response_data4.message) === null || _response_data_message === void 0 ? void 0 : _response_data_message.includes('başarılı')) || ((_response_data5 = response.data) === null || _response_data5 === void 0 ? void 0 : (_response_data_message1 = _response_data5.message) === null || _response_data_message1 === void 0 ? void 0 : _response_data_message1.includes('successful'));\n            const statusSuccess = ((_response_data6 = response.data) === null || _response_data6 === void 0 ? void 0 : _response_data6.status) === 0 || ((_response_data7 = response.data) === null || _response_data7 === void 0 ? void 0 : _response_data7.status) === 200 || ((_response_data8 = response.data) === null || _response_data8 === void 0 ? void 0 : _response_data8.success) === true;\n            // Eğer HTTP 200 dönmüş ve hata mesajı YOK ise başarılı sayalım\n            const noErrorMessage = !((_response_data9 = response.data) === null || _response_data9 === void 0 ? void 0 : (_response_data_message2 = _response_data9.message) === null || _response_data_message2 === void 0 ? void 0 : _response_data_message2.includes('hatalı')) && !((_response_data10 = response.data) === null || _response_data10 === void 0 ? void 0 : (_response_data_message3 = _response_data10.message) === null || _response_data_message3 === void 0 ? void 0 : _response_data_message3.includes('error')) && !((_response_data11 = response.data) === null || _response_data11 === void 0 ? void 0 : (_response_data_message4 = _response_data11.message) === null || _response_data_message4 === void 0 ? void 0 : _response_data_message4.includes('failed')) && !((_response_data12 = response.data) === null || _response_data12 === void 0 ? void 0 : (_response_data_message5 = _response_data12.message) === null || _response_data_message5 === void 0 ? void 0 : _response_data_message5.includes('invalid')) && !((_response_data13 = response.data) === null || _response_data13 === void 0 ? void 0 : _response_data13.error);\n            const isSuccess = httpSuccess && (messageSuccess || statusSuccess || noErrorMessage);\n            console.log('🔍 Success detection:', {\n                httpSuccess,\n                hasSetCookie: !!hasSetCookie,\n                messageSuccess,\n                statusSuccess,\n                noErrorMessage,\n                finalSuccess: isSuccess\n            });\n            if (isSuccess) {\n                console.log('✅ Login başarılı!');\n                console.log('🍪 Backend Set-Cookie header ile token gönderdi');\n                console.log('🔄 Cookie browser tarafından otomatik set edilecek');\n                // Backend zaten HttpOnly cookie set ediyor, biz manuel set etmeye gerek yok\n                // withCredentials: true olduğu için sonraki API çağrıları cookie'yi otomatik gönderecek\n                return true;\n            } else {\n                console.log('❌ Login başarısız - Response criteria not met');\n                // Başarısız login durumunda eski cookie'leri temizle\n                this.clearAuthCookies();\n                return false;\n            }\n        } catch (error) {\n            throw error;\n        }\n    },\n    // Register\n    async register (userData) {\n        console.log('📤 Register request:', userData);\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REGISTER, userData);\n            console.log('✅ Register response:', response.data);\n            const backendResponse = response.data;\n            return {\n                success: backendResponse.status === 0,\n                message: backendResponse.message,\n                user: backendResponse.data ? {\n                    id: 0,\n                    firstName: userData.firstName,\n                    lastName: userData.lastName,\n                    email: userData.email,\n                    phoneNumber: userData.phoneNumber || ''\n                } : undefined\n            };\n        } catch (error) {\n            console.error('❌ Register error:', error);\n            throw error;\n        }\n    },\n    // Refresh Token\n    async refreshToken () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REFRESH_TOKEN);\n        return response.data;\n    },\n    // Logout\n    async logout () {\n        try {\n            // Backend logout çağrısı\n            await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.LOGOUT);\n            console.log('✅ Backend logout başarılı');\n        } catch (error) {\n            console.error('❌ Backend logout hatası:', error);\n        // Backend logout başarısız olsa bile yerel temizlik yap\n        } finally{\n            if (true) {\n                // localStorage'ı tamamen temizle\n                localStorage.clear();\n                console.log('🧹 localStorage temizlendi');\n                // Cookie'leri manuel olarak temizle (mümkün olanları)\n                const cookies = document.cookie.split(\";\");\n                for (let cookie of cookies){\n                    const eqPos = cookie.indexOf(\"=\");\n                    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();\n                    if (name) {\n                        // Cookie'yi farklı path'lerde temizle\n                        document.cookie = \"\".concat(name, \"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/\");\n                        document.cookie = \"\".concat(name, \"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=\").concat(window.location.hostname);\n                        document.cookie = \"\".concat(name, \"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.\").concat(window.location.hostname);\n                    }\n                }\n                console.log('🍪 Cookieler temizlendi');\n                // Session storage'ı da temizle\n                sessionStorage.clear();\n                console.log('🧹 sessionStorage temizlendi');\n            }\n        }\n    },\n    // Get User Info - HttpOnly cookie ile çalışır\n    async getUserInfo () {\n        console.log('🔎 AuthService getUserInfo başlıyor...');\n        console.log('🍪 withCredentials ile cookie otomatik gönderilecek');\n        try {\n            // Sadece ana USER_INFO endpoint'ini dene.\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.USER_INFO);\n            console.log('✅ getUserInfo başarılı:', response.data);\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            // Eğer hata 401 (Unauthorized) ise, bu bir çökme hatası değil,\n            // sadece kullanıcının giriş yapmadığı anlamına gelir.\n            // Bu durumu hata olarak fırlatmak yerine null döndürerek sessizce yönetiyoruz.\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                console.log('ℹ️ Kullanıcı giriş yapmamış. (Bu beklenen bir durumdur)');\n                return null;\n            }\n            // Diğer tüm hatalar (500, ağ hataları vs.) gerçek bir sorundur.\n            // Bunları React Query'nin yakalaması için fırlatıyoruz.\n            console.error('❌ getUserInfo sırasında beklenmedik bir hata oluştu:', error);\n            throw error;\n        }\n    },\n    // Add Reference Code\n    async addReference (referenceCode) {\n        try {\n            await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.ADD_REFERENCE, {\n                referansCode: referenceCode\n            });\n        } catch (error) {\n            console.error('❌ Referans eklenirken hata:', error);\n            throw error;\n        }\n    },\n    // Make Admin (Admin only)\n    async makeAdmin (userIdOrEmail) {\n        try {\n            await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.MAKE_ADMIN, {\n                userIdOrEmail\n            });\n        } catch (error) {\n            console.error('❌ Admin yapma hatası:', error);\n            throw error;\n        }\n    },\n    // Test endpoint\n    async test () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.TEST_AUTH);\n        return response.data;\n    },\n    // Debug Claims\n    async debugClaims () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.DEBUG_CLAIMS);\n        return response.data;\n    },\n    // Get Profile Info - Detaylı profil bilgileri\n    async getProfileInfo () {\n        console.log('🔎 AuthService getProfileInfo başlıyor...');\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.PROFILE_INFO);\n            console.log('✅ getProfileInfo raw response:', response.data);\n            // API response'u data wrapper'ı içinde geliyor\n            if (response.data.status === 0 && response.data.data) {\n                console.log('✅ getProfileInfo başarılı:', response.data.data);\n                return response.data.data;\n            } else {\n                throw new Error(response.data.message || 'Profil bilgileri alınamadı');\n            }\n        } catch (error) {\n            console.error('❌ getProfileInfo sırasında hata oluştu:', error);\n            throw error;\n        }\n    },\n    // Update Profile\n    async updateProfile (profileData) {\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_PROFILE, profileData);\n            return response.data;\n        } catch (error) {\n            console.error('❌ Update profile error:', error);\n            throw error;\n        }\n    },\n    // Update Profile Picture\n    async updateProfilePicture (imageFile) {\n        console.log('📤 Update profile picture request');\n        try {\n            const formData = new FormData();\n            formData.append('file', imageFile);\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.PROFILE_PICTURE, formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Update profile picture response:', response.data);\n            return response.data;\n        } catch (error) {\n            console.error('❌ Update profile picture error:', error);\n            throw error;\n        }\n    },\n    // Delete Profile Picture\n    async deleteProfilePicture () {\n        console.log('🗑️ Delete profile picture request');\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.DELETE_PROFILE_PICTURE);\n            console.log('✅ Delete profile picture response:', response.data);\n            return response.data;\n        } catch (error) {\n            console.error('❌ Delete profile picture error:', error);\n            throw error;\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9hdXRoU2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEI7QUFDMkI7QUF5RnpELDBCQUEwQjtBQUNuQixNQUFNRSxjQUFjO0lBQ3ZCLCtCQUErQjtJQUMvQkM7UUFDSSxJQUFJLElBQTZCLEVBQUU7WUFDL0JDLFFBQVFDLEdBQUcsQ0FBQztZQUVaLGtDQUFrQztZQUNsQyxNQUFNQyxjQUFjO2dCQUFDO2dCQUFlO2dCQUFnQjtnQkFBYTthQUFRO1lBRXpFQSxZQUFZQyxPQUFPLENBQUNDLENBQUFBO2dCQUNoQiwyQkFBMkI7Z0JBQzNCQyxTQUFTQyxNQUFNLEdBQUdGLGFBQWE7Z0JBQy9CQyxTQUFTQyxNQUFNLEdBQUdGLGFBQWEsMkRBQTJERyxPQUFPQyxRQUFRLENBQUNDLFFBQVE7Z0JBQ2xISixTQUFTQyxNQUFNLEdBQUdGLGFBQWEsNERBQTRERyxPQUFPQyxRQUFRLENBQUNDLFFBQVE7WUFDdkg7WUFFQVQsUUFBUUMsR0FBRyxDQUFDO1FBQ2hCO0lBQ0o7SUFDQSxRQUFRO0lBQ1IsTUFBTVMsT0FBTUMsS0FBYSxFQUFFQyxRQUFnQjtRQUN2Q1osUUFBUUMsR0FBRyxDQUFDLG9DQUFvQztZQUFFVTtRQUFNO1FBRXhELDREQUE0RDtRQUM1RCxNQUFNRSxlQUFlLEtBQTZCLEdBQUdDLGFBQWFDLE9BQU8sQ0FBQyxrQkFBa0IsQ0FBSTtRQUNoR2YsUUFBUUMsR0FBRyxDQUFDLGdEQUFnRDtZQUFFWTtRQUFhO1FBRTNFLElBQUlBLGNBQWM7WUFDZGIsUUFBUUMsR0FBRyxDQUFDO1lBQ1osSUFBSSxJQUE2QixFQUFFO2dCQUMvQmEsYUFBYUUsVUFBVSxDQUFDO1lBQzVCO1FBQ0o7UUFFQSxNQUFNQyxZQUFZO1lBQ2ROO1lBQ0FDO1FBQ0o7UUFFQVosUUFBUUMsR0FBRyxDQUFDLHlCQUF5QmdCO1FBQ3JDakIsUUFBUUMsR0FBRyxDQUFDLGVBQWVMLDRDQUFTQSxDQUFDc0IsUUFBUSxDQUFDQyxPQUFPO1FBQ3JEbkIsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQjtZQUMxQmtCLFNBQVN2Qiw0Q0FBU0EsQ0FBQ3NCLFFBQVEsQ0FBQ0MsT0FBTztZQUNuQ0MsaUJBQWlCeEIsNENBQVNBLENBQUNzQixRQUFRLENBQUNFLGVBQWU7WUFDbkRDLFNBQVN6Qiw0Q0FBU0EsQ0FBQ3NCLFFBQVEsQ0FBQ0csT0FBTztRQUN2QztRQUVBLElBQUk7Z0JBZWFDLGdCQUVHQSxpQkFhT0EsaUJBQ25CQSxpQkFDQUEsd0JBQUFBLGlCQUNBQSx5QkFBQUEsaUJBRWtCQSxpQkFDbEJBLGlCQUNBQSxpQkFHb0JBLHlCQUFBQSxpQkFDbkJBLHlCQUFBQSxrQkFDQUEseUJBQUFBLGtCQUNBQSx5QkFBQUEsa0JBQ0FBO1lBM0NMdEIsUUFBUUMsR0FBRyxDQUFDLHFCQUErQixPQUFwQkosa0VBQWFBLENBQUMwQixLQUFLLEVBQUM7WUFDM0MsTUFBTUQsV0FBVyxNQUFNMUIsNENBQVNBLENBQUM0QixJQUFJLENBQUMzQixrRUFBYUEsQ0FBQzBCLEtBQUssRUFBRU47WUFFM0RqQixRQUFRQyxHQUFHLENBQUMsc0JBQXNCcUIsU0FBU0csSUFBSTtZQUMvQ3pCLFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUJxQixTQUFTSSxNQUFNO1lBQ2xEMUIsUUFBUUMsR0FBRyxDQUFDLHdCQUF3QnFCLFNBQVNELE9BQU87WUFFcEQsd0NBQXdDO1lBQ3hDckIsUUFBUUMsR0FBRyxDQUFDO1lBRVosMkJBQTJCO1lBQzNCRCxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDO2dCQUN4Q3dCLE1BQU1ILFNBQVNHLElBQUk7Z0JBQ25CQyxRQUFRSixTQUFTSSxNQUFNO2dCQUN2QkMsT0FBTyxHQUFFTCxpQkFBQUEsU0FBU0csSUFBSSxjQUFiSCxxQ0FBQUEsZUFBZUssT0FBTztnQkFDL0JDLFNBQVMsQ0FBQyxDQUFDTixTQUFTRyxJQUFJO2dCQUN4QkksVUFBVSxHQUFFUCxrQkFBQUEsU0FBU0csSUFBSSxjQUFiSCxzQ0FBQUEsZ0JBQWVPLFVBQVU7Z0JBQ3JDQyxVQUFVUixTQUFTRyxJQUFJLEdBQUdNLE9BQU9DLElBQUksQ0FBQ1YsU0FBU0csSUFBSSxJQUFJLEVBQUU7WUFDN0Q7WUFFQSwwQkFBMEI7WUFDMUIsb0RBQW9EO1lBQ3BELHFEQUFxRDtZQUNyRCxNQUFNUSxjQUFjWCxTQUFTSSxNQUFNLEtBQUs7WUFDeEMsTUFBTVEsZUFBZVosU0FBU0QsT0FBTyxDQUFDLGFBQWEsSUFDL0NDLFNBQVNELE9BQU8sQ0FBQyxhQUFhLElBQzlCQyxTQUFTRCxPQUFPLENBQUMsYUFBYTtZQUVsQyxtREFBbUQ7WUFDbkQsTUFBTWMsaUJBQWlCYixFQUFBQSxrQkFBQUEsU0FBU0csSUFBSSxjQUFiSCxzQ0FBQUEsZ0JBQWVLLE9BQU8sTUFBSyxxQkFDOUNMLEVBQUFBLGtCQUFBQSxTQUFTRyxJQUFJLGNBQWJILHNDQUFBQSxnQkFBZUssT0FBTyxNQUFLLHdCQUMzQkwsa0JBQUFBLFNBQVNHLElBQUksY0FBYkgsdUNBQUFBLHlCQUFBQSxnQkFBZUssT0FBTyxjQUF0QkwsNkNBQUFBLHVCQUF3QmMsUUFBUSxDQUFDLGtCQUNqQ2Qsa0JBQUFBLFNBQVNHLElBQUksY0FBYkgsdUNBQUFBLDBCQUFBQSxnQkFBZUssT0FBTyxjQUF0QkwsOENBQUFBLHdCQUF3QmMsUUFBUSxDQUFDO1lBRXJDLE1BQU1DLGdCQUFnQmYsRUFBQUEsa0JBQUFBLFNBQVNHLElBQUksY0FBYkgsc0NBQUFBLGdCQUFlSSxNQUFNLE1BQUssS0FDNUNKLEVBQUFBLGtCQUFBQSxTQUFTRyxJQUFJLGNBQWJILHNDQUFBQSxnQkFBZUksTUFBTSxNQUFLLE9BQzFCSixFQUFBQSxrQkFBQUEsU0FBU0csSUFBSSxjQUFiSCxzQ0FBQUEsZ0JBQWVnQixPQUFPLE1BQUs7WUFFL0IsK0RBQStEO1lBQy9ELE1BQU1DLGlCQUFpQixHQUFDakIsa0JBQUFBLFNBQVNHLElBQUksY0FBYkgsdUNBQUFBLDBCQUFBQSxnQkFBZUssT0FBTyxjQUF0QkwsOENBQUFBLHdCQUF3QmMsUUFBUSxDQUFDLGNBQ3JELEdBQUNkLG1CQUFBQSxTQUFTRyxJQUFJLGNBQWJILHdDQUFBQSwwQkFBQUEsaUJBQWVLLE9BQU8sY0FBdEJMLDhDQUFBQSx3QkFBd0JjLFFBQVEsQ0FBQyxhQUNsQyxHQUFDZCxtQkFBQUEsU0FBU0csSUFBSSxjQUFiSCx3Q0FBQUEsMEJBQUFBLGlCQUFlSyxPQUFPLGNBQXRCTCw4Q0FBQUEsd0JBQXdCYyxRQUFRLENBQUMsY0FDbEMsR0FBQ2QsbUJBQUFBLFNBQVNHLElBQUksY0FBYkgsd0NBQUFBLDBCQUFBQSxpQkFBZUssT0FBTyxjQUF0QkwsOENBQUFBLHdCQUF3QmMsUUFBUSxDQUFDLGVBQ2xDLEdBQUNkLG1CQUFBQSxTQUFTRyxJQUFJLGNBQWJILHVDQUFBQSxpQkFBZWtCLEtBQUs7WUFFekIsTUFBTUMsWUFBWVIsZUFBZ0JFLENBQUFBLGtCQUFrQkUsaUJBQWlCRSxjQUFhO1lBRWxGdkMsUUFBUUMsR0FBRyxDQUFDLHlCQUF5QjtnQkFDakNnQztnQkFDQUMsY0FBYyxDQUFDLENBQUNBO2dCQUNoQkM7Z0JBQ0FFO2dCQUNBRTtnQkFDQUcsY0FBY0Q7WUFDbEI7WUFFQSxJQUFJQSxXQUFXO2dCQUNYekMsUUFBUUMsR0FBRyxDQUFDO2dCQUNaRCxRQUFRQyxHQUFHLENBQUM7Z0JBQ1pELFFBQVFDLEdBQUcsQ0FBQztnQkFFWiw0RUFBNEU7Z0JBQzVFLHdGQUF3RjtnQkFFeEYsT0FBTztZQUNYLE9BQU87Z0JBQ0hELFFBQVFDLEdBQUcsQ0FBQztnQkFFWixxREFBcUQ7Z0JBQ3JELElBQUksQ0FBQ0YsZ0JBQWdCO2dCQUVyQixPQUFPO1lBQ1g7UUFDSixFQUFFLE9BQU95QyxPQUFZO1lBQ2pCLE1BQU1BO1FBQ1Y7SUFDSjtJQUVBLFdBQVc7SUFDWCxNQUFNRyxVQUFTQyxRQUF5QjtRQUNwQzVDLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0IyQztRQUVwQyxJQUFJO1lBQ0EsTUFBTXRCLFdBQVcsTUFBTTFCLDRDQUFTQSxDQUFDNEIsSUFBSSxDQUFDM0Isa0VBQWFBLENBQUNnRCxRQUFRLEVBQUVEO1lBQzlENUMsUUFBUUMsR0FBRyxDQUFDLHdCQUF3QnFCLFNBQVNHLElBQUk7WUFFakQsTUFBTXFCLGtCQUFvQ3hCLFNBQVNHLElBQUk7WUFFdkQsT0FBTztnQkFDSGEsU0FBU1EsZ0JBQWdCcEIsTUFBTSxLQUFLO2dCQUNwQ0MsU0FBU21CLGdCQUFnQm5CLE9BQU87Z0JBQ2hDb0IsTUFBTUQsZ0JBQWdCckIsSUFBSSxHQUFHO29CQUN6QnVCLElBQUk7b0JBQ0pDLFdBQVdMLFNBQVNLLFNBQVM7b0JBQzdCQyxVQUFVTixTQUFTTSxRQUFRO29CQUMzQnZDLE9BQU9pQyxTQUFTakMsS0FBSztvQkFDckJ3QyxhQUFhUCxTQUFTTyxXQUFXLElBQUk7Z0JBQ3pDLElBQUlDO1lBQ1I7UUFDSixFQUFFLE9BQU9aLE9BQVk7WUFDakJ4QyxRQUFRd0MsS0FBSyxDQUFDLHFCQUFxQkE7WUFDbkMsTUFBTUE7UUFDVjtJQUNKO0lBRUEsZ0JBQWdCO0lBQ2hCLE1BQU1hO1FBQ0YsTUFBTS9CLFdBQVcsTUFBTTFCLDRDQUFTQSxDQUFDMEQsR0FBRyxDQUFDekQsa0VBQWFBLENBQUMwRCxhQUFhO1FBQ2hFLE9BQU9qQyxTQUFTRyxJQUFJO0lBQ3hCO0lBRUEsU0FBUztJQUNULE1BQU0rQjtRQUNGLElBQUk7WUFDQSx5QkFBeUI7WUFDekIsTUFBTTVELDRDQUFTQSxDQUFDMEQsR0FBRyxDQUFDekQsa0VBQWFBLENBQUM0RCxNQUFNO1lBQ3hDekQsUUFBUUMsR0FBRyxDQUFDO1FBQ2hCLEVBQUUsT0FBT3VDLE9BQU87WUFDWnhDLFFBQVF3QyxLQUFLLENBQUMsNEJBQTRCQTtRQUMxQyx3REFBd0Q7UUFDNUQsU0FBVTtZQUNOLElBQUksSUFBNkIsRUFBRTtnQkFDL0IsaUNBQWlDO2dCQUNqQzFCLGFBQWE0QyxLQUFLO2dCQUNsQjFELFFBQVFDLEdBQUcsQ0FBQztnQkFFWixzREFBc0Q7Z0JBQ3RELE1BQU0wRCxVQUFVdEQsU0FBU0MsTUFBTSxDQUFDc0QsS0FBSyxDQUFDO2dCQUN0QyxLQUFLLElBQUl0RCxVQUFVcUQsUUFBUztvQkFDeEIsTUFBTUUsUUFBUXZELE9BQU93RCxPQUFPLENBQUM7b0JBQzdCLE1BQU1DLE9BQU9GLFFBQVEsQ0FBQyxJQUFJdkQsT0FBTzBELE1BQU0sQ0FBQyxHQUFHSCxPQUFPSSxJQUFJLEtBQUszRCxPQUFPMkQsSUFBSTtvQkFDdEUsSUFBSUYsTUFBTTt3QkFDTixzQ0FBc0M7d0JBQ3RDMUQsU0FBU0MsTUFBTSxHQUFHLEdBQVEsT0FBTHlELE1BQUs7d0JBQzFCMUQsU0FBU0MsTUFBTSxHQUFHLEdBQWdFQyxPQUE3RHdELE1BQUssMERBQWlGLE9BQXpCeEQsT0FBT0MsUUFBUSxDQUFDQyxRQUFRO3dCQUMxR0osU0FBU0MsTUFBTSxHQUFHLEdBQWlFQyxPQUE5RHdELE1BQUssMkRBQWtGLE9BQXpCeEQsT0FBT0MsUUFBUSxDQUFDQyxRQUFRO29CQUMvRztnQkFDSjtnQkFDQVQsUUFBUUMsR0FBRyxDQUFDO2dCQUVaLCtCQUErQjtnQkFDL0JpRSxlQUFlUixLQUFLO2dCQUNwQjFELFFBQVFDLEdBQUcsQ0FBQztZQUNoQjtRQUNKO0lBQ0o7SUFFQSw4Q0FBOEM7SUFDOUMsTUFBTWtFO1FBQ0ZuRSxRQUFRQyxHQUFHLENBQUM7UUFDWkQsUUFBUUMsR0FBRyxDQUFDO1FBRVosSUFBSTtZQUNBLDBDQUEwQztZQUMxQyxNQUFNcUIsV0FBVyxNQUFNMUIsNENBQVNBLENBQUMwRCxHQUFHLENBQUN6RCxrRUFBYUEsQ0FBQ3VFLFNBQVM7WUFDNURwRSxRQUFRQyxHQUFHLENBQUMsMkJBQTJCcUIsU0FBU0csSUFBSTtZQUNwRCxPQUFPSCxTQUFTRyxJQUFJO1FBQ3hCLEVBQUUsT0FBT2UsT0FBWTtnQkFJYkE7WUFISiwrREFBK0Q7WUFDL0Qsc0RBQXNEO1lBQ3RELCtFQUErRTtZQUMvRSxJQUFJQSxFQUFBQSxrQkFBQUEsTUFBTWxCLFFBQVEsY0FBZGtCLHNDQUFBQSxnQkFBZ0JkLE1BQU0sTUFBSyxLQUFLO2dCQUNoQzFCLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixPQUFPO1lBQ1g7WUFFQSxnRUFBZ0U7WUFDaEUsd0RBQXdEO1lBQ3hERCxRQUFRd0MsS0FBSyxDQUFDLHdEQUF3REE7WUFDdEUsTUFBTUE7UUFDVjtJQUNKO0lBRUEscUJBQXFCO0lBQ3JCLE1BQU02QixjQUFhQyxhQUFxQjtRQUNwQyxJQUFJO1lBQ0EsTUFBTTFFLDRDQUFTQSxDQUFDNEIsSUFBSSxDQUFDM0Isa0VBQWFBLENBQUMwRSxhQUFhLEVBQUU7Z0JBQUVDLGNBQWNGO1lBQWM7UUFDcEYsRUFBRSxPQUFPOUIsT0FBTztZQUNaeEMsUUFBUXdDLEtBQUssQ0FBQywrQkFBK0JBO1lBQzdDLE1BQU1BO1FBQ1Y7SUFDSjtJQUVBLDBCQUEwQjtJQUMxQixNQUFNaUMsV0FBVUMsYUFBcUI7UUFDakMsSUFBSTtZQUNBLE1BQU05RSw0Q0FBU0EsQ0FBQzRCLElBQUksQ0FBQzNCLGtFQUFhQSxDQUFDOEUsVUFBVSxFQUFFO2dCQUFFRDtZQUFjO1FBQ25FLEVBQUUsT0FBT2xDLE9BQU87WUFDWnhDLFFBQVF3QyxLQUFLLENBQUMseUJBQXlCQTtZQUN2QyxNQUFNQTtRQUNWO0lBQ0o7SUFFQSxnQkFBZ0I7SUFDaEIsTUFBTW9DO1FBQ0YsTUFBTXRELFdBQVcsTUFBTTFCLDRDQUFTQSxDQUFDMEQsR0FBRyxDQUFDekQsa0VBQWFBLENBQUNnRixTQUFTO1FBQzVELE9BQU92RCxTQUFTRyxJQUFJO0lBQ3hCO0lBRUEsZUFBZTtJQUNmLE1BQU1xRDtRQUNGLE1BQU14RCxXQUFXLE1BQU0xQiw0Q0FBU0EsQ0FBQzBELEdBQUcsQ0FBQ3pELGtFQUFhQSxDQUFDa0YsWUFBWTtRQUMvRCxPQUFPekQsU0FBU0csSUFBSTtJQUN4QjtJQUVBLDhDQUE4QztJQUM5QyxNQUFNdUQ7UUFDRmhGLFFBQVFDLEdBQUcsQ0FBQztRQUVaLElBQUk7WUFDQSxNQUFNcUIsV0FBVyxNQUFNMUIsNENBQVNBLENBQUMwRCxHQUFHLENBQW1DekQsa0VBQWFBLENBQUNvRixZQUFZO1lBQ2pHakYsUUFBUUMsR0FBRyxDQUFDLGtDQUFrQ3FCLFNBQVNHLElBQUk7WUFFM0QsK0NBQStDO1lBQy9DLElBQUlILFNBQVNHLElBQUksQ0FBQ0MsTUFBTSxLQUFLLEtBQUtKLFNBQVNHLElBQUksQ0FBQ0EsSUFBSSxFQUFFO2dCQUNsRHpCLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJxQixTQUFTRyxJQUFJLENBQUNBLElBQUk7Z0JBQzVELE9BQU9ILFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtZQUM3QixPQUFPO2dCQUNILE1BQU0sSUFBSXlELE1BQU01RCxTQUFTRyxJQUFJLENBQUNFLE9BQU8sSUFBSTtZQUM3QztRQUNKLEVBQUUsT0FBT2EsT0FBWTtZQUNqQnhDLFFBQVF3QyxLQUFLLENBQUMsMkNBQTJDQTtZQUN6RCxNQUFNQTtRQUNWO0lBQ0o7SUFFQSxpQkFBaUI7SUFDakIsTUFBTTJDLGVBQWNDLFdBQWlDO1FBQ2pELElBQUk7WUFDQSxNQUFNOUQsV0FBVyxNQUFNMUIsNENBQVNBLENBQUM0QixJQUFJLENBQUMzQixrRUFBYUEsQ0FBQ3dGLGNBQWMsRUFBRUQ7WUFDcEUsT0FBTzlELFNBQVNHLElBQUk7UUFDeEIsRUFBRSxPQUFPZSxPQUFZO1lBQ2pCeEMsUUFBUXdDLEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDLE1BQU1BO1FBQ1Y7SUFDSjtJQUVBLHlCQUF5QjtJQUN6QixNQUFNOEMsc0JBQXFCQyxTQUFlO1FBQ3RDdkYsUUFBUUMsR0FBRyxDQUFDO1FBRVosSUFBSTtZQUNBLE1BQU11RixXQUFXLElBQUlDO1lBQ3JCRCxTQUFTRSxNQUFNLENBQUMsUUFBUUg7WUFFeEIsTUFBTWpFLFdBQVcsTUFBTTFCLDRDQUFTQSxDQUFDK0YsR0FBRyxDQUFDOUYsa0VBQWFBLENBQUMrRixlQUFlLEVBQUVKLFVBQVU7Z0JBQzFFbkUsU0FBUztvQkFDTCxnQkFBZ0I7Z0JBQ3BCO1lBQ0o7WUFDQXJCLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0NxQixTQUFTRyxJQUFJO1lBQy9ELE9BQU9ILFNBQVNHLElBQUk7UUFDeEIsRUFBRSxPQUFPZSxPQUFZO1lBQ2pCeEMsUUFBUXdDLEtBQUssQ0FBQyxtQ0FBbUNBO1lBQ2pELE1BQU1BO1FBQ1Y7SUFDSjtJQUVBLHlCQUF5QjtJQUN6QixNQUFNcUQ7UUFDRjdGLFFBQVFDLEdBQUcsQ0FBQztRQUVaLElBQUk7WUFDQSxNQUFNcUIsV0FBVyxNQUFNMUIsNENBQVNBLENBQUNrRyxNQUFNLENBQUNqRyxrRUFBYUEsQ0FBQ2tHLHNCQUFzQjtZQUM1RS9GLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0NxQixTQUFTRyxJQUFJO1lBQy9ELE9BQU9ILFNBQVNHLElBQUk7UUFDeEIsRUFBRSxPQUFPZSxPQUFZO1lBQ2pCeEMsUUFBUXdDLEtBQUssQ0FBQyxtQ0FBbUNBO1lBQ2pELE1BQU1BO1FBQ1Y7SUFDSjtBQUNKLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQVNVU1xcRGVza3RvcFxcU2F5Z2xvYmFsXFxzYXlnbG9iYWwtZnJvbnRlbmRcXHNyY1xcc2VydmljZXNcXGF1dGhTZXJ2aWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBhcGlDbGllbnQgZnJvbSAnLi9hcGknO1xuaW1wb3J0IHsgQVBJX0VORFBPSU5UUyB9IGZyb20gJ0AvY29uc3RhbnRzL2FwaUVuZHBvaW50cyc7XG5cbi8vIEFQSSBSZXF1ZXN0L1Jlc3BvbnNlIHRpcGxlcmlcbmV4cG9ydCBpbnRlcmZhY2UgTG9naW5SZXF1ZXN0IHtcbiAgICBlbWFpbDogc3RyaW5nO1xuICAgIHBhc3N3b3JkOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUmVnaXN0ZXJSZXF1ZXN0IHtcbiAgICBlbWFpbDogc3RyaW5nO1xuICAgIHBhc3N3b3JkOiBzdHJpbmc7XG4gICAgZmlyc3ROYW1lOiBzdHJpbmc7XG4gICAgbGFzdE5hbWU6IHN0cmluZztcbiAgICBwaG9uZU51bWJlcjogc3RyaW5nO1xuICAgIHJlZmVyYW5zQ29kZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBCYWNrZW5kTG9naW5SZXNwb25zZSB7XG4gICAgc3RhdHVzOiBudW1iZXI7XG4gICAgbWVzc2FnZTogc3RyaW5nO1xuICAgIGRhdGE/OiB7XG4gICAgICAgIHRva2VuPzogc3RyaW5nO1xuICAgICAgICByZWZyZXNoVG9rZW4/OiBzdHJpbmc7XG4gICAgICAgIGNhcmVlcklkPzogbnVtYmVyO1xuICAgICAgICBtZW1iZXJzaGlwTGV2ZWxJZD86IG51bWJlcjtcbiAgICAgICAgdXNlcj86IGFueTtcbiAgICB9O1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIExvZ2luUmVzcG9uc2Uge1xuICAgIHN1Y2Nlc3M6IGJvb2xlYW47XG4gICAgbWVzc2FnZTogc3RyaW5nO1xuICAgIHVzZXI/OiBhbnk7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUmVnaXN0ZXJSZXNwb25zZSB7XG4gICAgc3RhdHVzOiBudW1iZXI7XG4gICAgbWVzc2FnZTogc3RyaW5nO1xuICAgIGRhdGE/OiBhbnk7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQmFja2VuZFJlZ2lzdGVyUmVzcG9uc2Uge1xuICAgIHN1Y2Nlc3M6IGJvb2xlYW47XG4gICAgbWVzc2FnZTogc3RyaW5nO1xuICAgIHVzZXI/OiB7XG4gICAgICAgIGlkOiBudW1iZXI7XG4gICAgICAgIGZpcnN0TmFtZTogc3RyaW5nO1xuICAgICAgICBsYXN0TmFtZTogc3RyaW5nO1xuICAgICAgICBlbWFpbDogc3RyaW5nO1xuICAgICAgICBwaG9uZU51bWJlcjogc3RyaW5nO1xuICAgIH07XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQWRkUmVmZXJlbmNlUmVxdWVzdCB7XG4gICAgcmVmZXJhbnNDb2RlOiBzdHJpbmc7XG59XG5cbi8vIFByb2ZpbGUgZ8O8bmNlbGxlbWUgacOnaW4gdGlwbGVyXG5leHBvcnQgaW50ZXJmYWNlIFVwZGF0ZVByb2ZpbGVSZXF1ZXN0IHtcbiAgICBmaXJzdE5hbWU/OiBzdHJpbmc7XG4gICAgbGFzdE5hbWU/OiBzdHJpbmc7XG4gICAgZGF0ZU9mQmlydGg/OiBzdHJpbmc7IC8vIElTTyBkYXRlIHN0cmluZ1xuICAgIGdlbmRlcj86IG51bWJlcjsgLy8gMDogVW5zcGVjaWZpZWQsIDE6IE1hbGUsIDI6IEZlbWFsZSwgMzogT3RoZXJcbiAgICBsb2NhdGlvbj86IHN0cmluZztcbiAgICBwaG9uZU51bWJlcj86IHN0cmluZztcbn1cblxuLy8gUHJvZmlsIGJpbGdpbGVyaSByZXNwb25zZSB0aXBpIC0gQVBJIHJlc3BvbnNlJ3VuYSBnw7ZyZSBnw7xuY2VsbGVubWnFn1xuZXhwb3J0IGludGVyZmFjZSBQcm9maWxlSW5mb1Jlc3BvbnNlIHtcbiAgICBmaXJzdE5hbWU6IHN0cmluZztcbiAgICBsYXN0TmFtZTogc3RyaW5nO1xuICAgIGVtYWlsOiBzdHJpbmc7XG4gICAgcGhvbmVOdW1iZXI/OiBzdHJpbmc7IC8vIEFQSSdkZW4gZ2VsbWl5b3IsIG9wdGlvbmFsIHlhcMSxbGTEsVxuICAgIGRhdGVPZkJpcnRoPzogc3RyaW5nIHwgbnVsbDtcbiAgICBnZW5kZXI/OiBudW1iZXIgfCBudWxsO1xuICAgIGxvY2F0aW9uPzogc3RyaW5nIHwgbnVsbDtcbiAgICBwcm9maWxlUGljdHVyZVVybD86IHN0cmluZyB8IG51bGw7XG4gICAgcmVnaXN0ZXJlZEF0OiBzdHJpbmc7XG4gICAgbWVtYmVyc2hpcExldmVsSWQ6IG51bWJlcjsgLy8gQmFja2VuZCdkZW4gbWVtYmVyc2hpcExldmVsSWQgb2xhcmFrIGdlbGl5b3JcbiAgICBjYXJlZXJSYW5rSWQ6IG51bWJlcjsgLy8gQmFja2VuZCdkZW4gY2FyZWVyUmFua0lkIG9sYXJhayBnZWxpeW9yXG59XG5cbi8vIEFQSSBSZXNwb25zZSB3cmFwcGVyIHRpcGlcbmludGVyZmFjZSBBcGlSZXNwb25zZTxUPiB7XG4gICAgc3RhdHVzOiBudW1iZXI7XG4gICAgbWVzc2FnZTogc3RyaW5nO1xuICAgIGRhdGE6IFQ7XG59XG5cbi8vIEF1dGhlbnRpY2F0aW9uIFNlcnZpY2VzXG5leHBvcnQgY29uc3QgYXV0aFNlcnZpY2UgPSB7XG4gICAgLy8gQ2xlYXIgYXV0aGVudGljYXRpb24gY29va2llc1xuICAgIGNsZWFyQXV0aENvb2tpZXMoKTogdm9pZCB7XG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfp7kgQXV0aCBjb29raWVsZXJpIHRlbWl6bGVuaXlvci4uLicpO1xuXG4gICAgICAgICAgICAvLyBCaWxpbmVuIGF1dGggY29va2llbGVyaSB0ZW1pemxlXG4gICAgICAgICAgICBjb25zdCBhdXRoQ29va2llcyA9IFsnQWNjZXNzVG9rZW4nLCAnUmVmcmVzaFRva2VuJywgJ0F1dGhUb2tlbicsICdUb2tlbiddO1xuXG4gICAgICAgICAgICBhdXRoQ29va2llcy5mb3JFYWNoKGNvb2tpZU5hbWUgPT4ge1xuICAgICAgICAgICAgICAgIC8vIEZhcmtsxLEgcGF0aGxlcmRlIHRlbWl6bGVcbiAgICAgICAgICAgICAgICBkb2N1bWVudC5jb29raWUgPSBjb29raWVOYW1lICsgJz07ZXhwaXJlcz1UaHUsIDAxIEphbiAxOTcwIDAwOjAwOjAwIEdNVDtwYXRoPS8nO1xuICAgICAgICAgICAgICAgIGRvY3VtZW50LmNvb2tpZSA9IGNvb2tpZU5hbWUgKyAnPTtleHBpcmVzPVRodSwgMDEgSmFuIDE5NzAgMDA6MDA6MDAgR01UO3BhdGg9Lztkb21haW49JyArIHdpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZTtcbiAgICAgICAgICAgICAgICBkb2N1bWVudC5jb29raWUgPSBjb29raWVOYW1lICsgJz07ZXhwaXJlcz1UaHUsIDAxIEphbiAxOTcwIDAwOjAwOjAwIEdNVDtwYXRoPS87ZG9tYWluPS4nICsgd2luZG93LmxvY2F0aW9uLmhvc3RuYW1lO1xuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn6e5IEF1dGggY29va2llbGVyaSB0ZW1pemxlbmRpJyk7XG4gICAgICAgIH1cbiAgICB9LFxuICAgIC8vIExvZ2luXG4gICAgYXN5bmMgbG9naW4oZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+aqiBBdXRoU2VydmljZSBsb2dpbiBiYcWfbMSxeW9yLi4uJywgeyBlbWFpbCB9KTtcblxuICAgICAgICAvLyBMb2dpbiBiYcWfbGFuZ8SxY8SxbmRhIGxvZ291dCBmbGFnJ2luaSBrb250cm9sIGV0IHZlIHRlbWl6bGVcbiAgICAgICAgY29uc3QgaGFzTG9nZ2VkT3V0ID0gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnaGFzTG9nZ2VkT3V0JykgOiBudWxsO1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBMb2dpbiBiYcWfbGFuZ8SxY8SxbmRhIGxvZ291dCBmbGFnIGtvbnRyb2zDvDonLCB7IGhhc0xvZ2dlZE91dCB9KTtcblxuICAgICAgICBpZiAoaGFzTG9nZ2VkT3V0KSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+nuSBFc2tpIGxvZ291dCBmbGFnIHRlbWl6bGVuaXlvciAoeWVuaSBsb2dpbiknKTtcbiAgICAgICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdoYXNMb2dnZWRPdXQnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGxvZ2luRGF0YSA9IHtcbiAgICAgICAgICAgIGVtYWlsLFxuICAgICAgICAgICAgcGFzc3dvcmRcbiAgICAgICAgfTtcblxuICAgICAgICBjb25zb2xlLmxvZygn8J+TpCBHw7ZuZGVyaWxlY2VrIHZlcmk6JywgbG9naW5EYXRhKTtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk6EgQVBJIFVSTDonLCBhcGlDbGllbnQuZGVmYXVsdHMuYmFzZVVSTCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SnIEFQSSBDb25maWc6Jywge1xuICAgICAgICAgICAgYmFzZVVSTDogYXBpQ2xpZW50LmRlZmF1bHRzLmJhc2VVUkwsXG4gICAgICAgICAgICB3aXRoQ3JlZGVudGlhbHM6IGFwaUNsaWVudC5kZWZhdWx0cy53aXRoQ3JlZGVudGlhbHMsXG4gICAgICAgICAgICBoZWFkZXJzOiBhcGlDbGllbnQuZGVmYXVsdHMuaGVhZGVyc1xuICAgICAgICB9KTtcblxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coYPCfk6EgUE9TVCAke0FQSV9FTkRQT0lOVFMuTE9HSU59IMOnYcSfcsSxc8SxIHlhcMSxbMSxeW9yLi4uYCk7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0KEFQSV9FTkRQT0lOVFMuTE9HSU4sIGxvZ2luRGF0YSk7XG5cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OhIExvZ2luIHJlc3BvbnNlOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk4ogUmVzcG9uc2Ugc3RhdHVzOicsIHJlc3BvbnNlLnN0YXR1cyk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+NqiBSZXNwb25zZSBoZWFkZXJzOicsIHJlc3BvbnNlLmhlYWRlcnMpO1xuXG4gICAgICAgICAgICAvLyBCYWNrZW5kIFNldC1Db29raWUgaWxlIHRva2VuIGfDtm5kZXJkaVxuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CfjaogQmFja2VuZCBTZXQtQ29va2llIGlsZSB0b2tlbiBnw7ZuZGVyZGknKTtcblxuICAgICAgICAgICAgLy8gUmVzcG9uc2Ugc3RydWN0dXJlIGRlYnVnXG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBSZXNwb25zZSBzdHJ1Y3R1cmUgZGVidWc6Jywge1xuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGEsXG4gICAgICAgICAgICAgICAgc3RhdHVzOiByZXNwb25zZS5zdGF0dXMsXG4gICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2UuZGF0YT8ubWVzc2FnZSxcbiAgICAgICAgICAgICAgICBoYXNEYXRhOiAhIXJlc3BvbnNlLmRhdGEsXG4gICAgICAgICAgICAgICAgc3RhdHVzVHlwZTogcmVzcG9uc2UuZGF0YT8uc3RhdHVzVHlwZSxcbiAgICAgICAgICAgICAgICBkYXRhS2V5czogcmVzcG9uc2UuZGF0YSA/IE9iamVjdC5rZXlzKHJlc3BvbnNlLmRhdGEpIDogW11cbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAvLyBHw5zDh0zDnCBTVUNDRVNTIERFVEVDVElPTlxuICAgICAgICAgICAgLy8gSFRUUCBzdGF0dXMgMjAwID0gQmFja2VuZCBiYcWfYXLEsWzEsSByZXNwb25zZSB2ZXJkaVxuICAgICAgICAgICAgLy8gU2V0LUNvb2tpZSBoZWFkZXIgdmFybMSxxJ/EsSA9IEJhY2tlbmQgdG9rZW4gZ8O2bmRlcmRpXG4gICAgICAgICAgICBjb25zdCBodHRwU3VjY2VzcyA9IHJlc3BvbnNlLnN0YXR1cyA9PT0gMjAwO1xuICAgICAgICAgICAgY29uc3QgaGFzU2V0Q29va2llID0gcmVzcG9uc2UuaGVhZGVyc1snc2V0LWNvb2tpZSddIHx8XG4gICAgICAgICAgICAgICAgcmVzcG9uc2UuaGVhZGVyc1snU2V0LUNvb2tpZSddIHx8XG4gICAgICAgICAgICAgICAgcmVzcG9uc2UuaGVhZGVyc1snU0VULUNPT0tJRSddO1xuXG4gICAgICAgICAgICAvLyDDh2XFn2l0bGkgYmFja2VuZCByZXNwb25zZSBmb3JtYXRsYXLEsW7EsSBrb250cm9sIGV0XG4gICAgICAgICAgICBjb25zdCBtZXNzYWdlU3VjY2VzcyA9IHJlc3BvbnNlLmRhdGE/Lm1lc3NhZ2UgPT09ICdHaXJpxZ8gYmHFn2FyxLFsxLEuJyB8fFxuICAgICAgICAgICAgICAgIHJlc3BvbnNlLmRhdGE/Lm1lc3NhZ2UgPT09ICdMb2dpbiBzdWNjZXNzZnVsJyB8fFxuICAgICAgICAgICAgICAgIHJlc3BvbnNlLmRhdGE/Lm1lc3NhZ2U/LmluY2x1ZGVzKCdiYcWfYXLEsWzEsScpIHx8XG4gICAgICAgICAgICAgICAgcmVzcG9uc2UuZGF0YT8ubWVzc2FnZT8uaW5jbHVkZXMoJ3N1Y2Nlc3NmdWwnKTtcblxuICAgICAgICAgICAgY29uc3Qgc3RhdHVzU3VjY2VzcyA9IHJlc3BvbnNlLmRhdGE/LnN0YXR1cyA9PT0gMCB8fFxuICAgICAgICAgICAgICAgIHJlc3BvbnNlLmRhdGE/LnN0YXR1cyA9PT0gMjAwIHx8XG4gICAgICAgICAgICAgICAgcmVzcG9uc2UuZGF0YT8uc3VjY2VzcyA9PT0gdHJ1ZTtcblxuICAgICAgICAgICAgLy8gRcSfZXIgSFRUUCAyMDAgZMO2bm3DvMWfIHZlIGhhdGEgbWVzYWrEsSBZT0sgaXNlIGJhxZ9hcsSxbMSxIHNheWFsxLFtXG4gICAgICAgICAgICBjb25zdCBub0Vycm9yTWVzc2FnZSA9ICFyZXNwb25zZS5kYXRhPy5tZXNzYWdlPy5pbmNsdWRlcygnaGF0YWzEsScpICYmXG4gICAgICAgICAgICAgICAgIXJlc3BvbnNlLmRhdGE/Lm1lc3NhZ2U/LmluY2x1ZGVzKCdlcnJvcicpICYmXG4gICAgICAgICAgICAgICAgIXJlc3BvbnNlLmRhdGE/Lm1lc3NhZ2U/LmluY2x1ZGVzKCdmYWlsZWQnKSAmJlxuICAgICAgICAgICAgICAgICFyZXNwb25zZS5kYXRhPy5tZXNzYWdlPy5pbmNsdWRlcygnaW52YWxpZCcpICYmXG4gICAgICAgICAgICAgICAgIXJlc3BvbnNlLmRhdGE/LmVycm9yO1xuXG4gICAgICAgICAgICBjb25zdCBpc1N1Y2Nlc3MgPSBodHRwU3VjY2VzcyAmJiAobWVzc2FnZVN1Y2Nlc3MgfHwgc3RhdHVzU3VjY2VzcyB8fCBub0Vycm9yTWVzc2FnZSk7XG5cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFN1Y2Nlc3MgZGV0ZWN0aW9uOicsIHtcbiAgICAgICAgICAgICAgICBodHRwU3VjY2VzcyxcbiAgICAgICAgICAgICAgICBoYXNTZXRDb29raWU6ICEhaGFzU2V0Q29va2llLFxuICAgICAgICAgICAgICAgIG1lc3NhZ2VTdWNjZXNzLFxuICAgICAgICAgICAgICAgIHN0YXR1c1N1Y2Nlc3MsXG4gICAgICAgICAgICAgICAgbm9FcnJvck1lc3NhZ2UsXG4gICAgICAgICAgICAgICAgZmluYWxTdWNjZXNzOiBpc1N1Y2Nlc3NcbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICBpZiAoaXNTdWNjZXNzKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBMb2dpbiBiYcWfYXLEsWzEsSEnKTtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+NqiBCYWNrZW5kIFNldC1Db29raWUgaGVhZGVyIGlsZSB0b2tlbiBnw7ZuZGVyZGknKTtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBDb29raWUgYnJvd3NlciB0YXJhZsSxbmRhbiBvdG9tYXRpayBzZXQgZWRpbGVjZWsnKTtcblxuICAgICAgICAgICAgICAgIC8vIEJhY2tlbmQgemF0ZW4gSHR0cE9ubHkgY29va2llIHNldCBlZGl5b3IsIGJpeiBtYW51ZWwgc2V0IGV0bWV5ZSBnZXJlayB5b2tcbiAgICAgICAgICAgICAgICAvLyB3aXRoQ3JlZGVudGlhbHM6IHRydWUgb2xkdcSfdSBpw6dpbiBzb25yYWtpIEFQSSDDp2HEn3LEsWxhcsSxIGNvb2tpZSd5aSBvdG9tYXRpayBnw7ZuZGVyZWNla1xuXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinYwgTG9naW4gYmHFn2FyxLFzxLF6IC0gUmVzcG9uc2UgY3JpdGVyaWEgbm90IG1ldCcpO1xuXG4gICAgICAgICAgICAgICAgLy8gQmHFn2FyxLFzxLF6IGxvZ2luIGR1cnVtdW5kYSBlc2tpIGNvb2tpZSdsZXJpIHRlbWl6bGVcbiAgICAgICAgICAgICAgICB0aGlzLmNsZWFyQXV0aENvb2tpZXMoKTtcblxuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgIH1cbiAgICB9LFxuXG4gICAgLy8gUmVnaXN0ZXJcbiAgICBhc3luYyByZWdpc3Rlcih1c2VyRGF0YTogUmVnaXN0ZXJSZXF1ZXN0KTogUHJvbWlzZTxCYWNrZW5kUmVnaXN0ZXJSZXNwb25zZT4ge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+TpCBSZWdpc3RlciByZXF1ZXN0OicsIHVzZXJEYXRhKTtcblxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdChBUElfRU5EUE9JTlRTLlJFR0lTVEVSLCB1c2VyRGF0YSk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIFJlZ2lzdGVyIHJlc3BvbnNlOicsIHJlc3BvbnNlLmRhdGEpO1xuXG4gICAgICAgICAgICBjb25zdCBiYWNrZW5kUmVzcG9uc2U6IFJlZ2lzdGVyUmVzcG9uc2UgPSByZXNwb25zZS5kYXRhO1xuXG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGJhY2tlbmRSZXNwb25zZS5zdGF0dXMgPT09IDAsXG4gICAgICAgICAgICAgICAgbWVzc2FnZTogYmFja2VuZFJlc3BvbnNlLm1lc3NhZ2UsXG4gICAgICAgICAgICAgICAgdXNlcjogYmFja2VuZFJlc3BvbnNlLmRhdGEgPyB7XG4gICAgICAgICAgICAgICAgICAgIGlkOiAwLCAvLyBCYWNrZW5kJ2RlbiB1c2VyIGRldGF5bGFyxLEgZ2VsbWVkacSfaSBpw6dpbiBwbGFjZWhvbGRlclxuICAgICAgICAgICAgICAgICAgICBmaXJzdE5hbWU6IHVzZXJEYXRhLmZpcnN0TmFtZSxcbiAgICAgICAgICAgICAgICAgICAgbGFzdE5hbWU6IHVzZXJEYXRhLmxhc3ROYW1lLFxuICAgICAgICAgICAgICAgICAgICBlbWFpbDogdXNlckRhdGEuZW1haWwsXG4gICAgICAgICAgICAgICAgICAgIHBob25lTnVtYmVyOiB1c2VyRGF0YS5waG9uZU51bWJlciB8fCAnJ1xuICAgICAgICAgICAgICAgIH0gOiB1bmRlZmluZWRcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBSZWdpc3RlciBlcnJvcjonLCBlcnJvcik7XG4gICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBSZWZyZXNoIFRva2VuXG4gICAgYXN5bmMgcmVmcmVzaFRva2VuKCk6IFByb21pc2U8YW55PiB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldChBUElfRU5EUE9JTlRTLlJFRlJFU0hfVE9LRU4pO1xuICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgICB9LFxuXG4gICAgLy8gTG9nb3V0XG4gICAgYXN5bmMgbG9nb3V0KCk6IFByb21pc2U8dm9pZD4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gQmFja2VuZCBsb2dvdXQgw6dhxJ9yxLFzxLFcbiAgICAgICAgICAgIGF3YWl0IGFwaUNsaWVudC5nZXQoQVBJX0VORFBPSU5UUy5MT0dPVVQpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBCYWNrZW5kIGxvZ291dCBiYcWfYXLEsWzEsScpO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEJhY2tlbmQgbG9nb3V0IGhhdGFzxLE6JywgZXJyb3IpO1xuICAgICAgICAgICAgLy8gQmFja2VuZCBsb2dvdXQgYmHFn2FyxLFzxLF6IG9sc2EgYmlsZSB5ZXJlbCB0ZW1pemxpayB5YXBcbiAgICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgICAgIC8vIGxvY2FsU3RvcmFnZSfEsSB0YW1hbWVuIHRlbWl6bGVcbiAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2UuY2xlYXIoKTtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+nuSBsb2NhbFN0b3JhZ2UgdGVtaXpsZW5kaScpO1xuXG4gICAgICAgICAgICAgICAgLy8gQ29va2llJ2xlcmkgbWFudWVsIG9sYXJhayB0ZW1pemxlIChtw7xta8O8biBvbGFubGFyxLEpXG4gICAgICAgICAgICAgICAgY29uc3QgY29va2llcyA9IGRvY3VtZW50LmNvb2tpZS5zcGxpdChcIjtcIik7XG4gICAgICAgICAgICAgICAgZm9yIChsZXQgY29va2llIG9mIGNvb2tpZXMpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXFQb3MgPSBjb29raWUuaW5kZXhPZihcIj1cIik7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5hbWUgPSBlcVBvcyA+IC0xID8gY29va2llLnN1YnN0cigwLCBlcVBvcykudHJpbSgpIDogY29va2llLnRyaW0oKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKG5hbWUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIENvb2tpZSd5aSBmYXJrbMSxIHBhdGgnbGVyZGUgdGVtaXpsZVxuICAgICAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuY29va2llID0gYCR7bmFtZX09O2V4cGlyZXM9VGh1LCAwMSBKYW4gMTk3MCAwMDowMDowMCBHTVQ7cGF0aD0vYDtcbiAgICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmNvb2tpZSA9IGAke25hbWV9PTtleHBpcmVzPVRodSwgMDEgSmFuIDE5NzAgMDA6MDA6MDAgR01UO3BhdGg9Lztkb21haW49JHt3aW5kb3cubG9jYXRpb24uaG9zdG5hbWV9YDtcbiAgICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmNvb2tpZSA9IGAke25hbWV9PTtleHBpcmVzPVRodSwgMDEgSmFuIDE5NzAgMDA6MDA6MDAgR01UO3BhdGg9Lztkb21haW49LiR7d2luZG93LmxvY2F0aW9uLmhvc3RuYW1lfWA7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CfjaogQ29va2llbGVyIHRlbWl6bGVuZGknKTtcblxuICAgICAgICAgICAgICAgIC8vIFNlc3Npb24gc3RvcmFnZSfEsSBkYSB0ZW1pemxlXG4gICAgICAgICAgICAgICAgc2Vzc2lvblN0b3JhZ2UuY2xlYXIoKTtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+nuSBzZXNzaW9uU3RvcmFnZSB0ZW1pemxlbmRpJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9LFxuXG4gICAgLy8gR2V0IFVzZXIgSW5mbyAtIEh0dHBPbmx5IGNvb2tpZSBpbGUgw6dhbMSxxZ/EsXJcbiAgICBhc3luYyBnZXRVc2VySW5mbygpOiBQcm9taXNlPGFueT4ge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UjiBBdXRoU2VydmljZSBnZXRVc2VySW5mbyBiYcWfbMSxeW9yLi4uJyk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn42qIHdpdGhDcmVkZW50aWFscyBpbGUgY29va2llIG90b21hdGlrIGfDtm5kZXJpbGVjZWsnKTtcblxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gU2FkZWNlIGFuYSBVU0VSX0lORk8gZW5kcG9pbnQnaW5pIGRlbmUuXG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoQVBJX0VORFBPSU5UUy5VU0VSX0lORk8pO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBnZXRVc2VySW5mbyBiYcWfYXLEsWzEsTonLCByZXNwb25zZS5kYXRhKTtcbiAgICAgICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgICAvLyBFxJ9lciBoYXRhIDQwMSAoVW5hdXRob3JpemVkKSBpc2UsIGJ1IGJpciDDp8O2a21lIGhhdGFzxLEgZGXEn2lsLFxuICAgICAgICAgICAgLy8gc2FkZWNlIGt1bGxhbsSxY8SxbsSxbiBnaXJpxZ8geWFwbWFkxLHEn8SxIGFubGFtxLFuYSBnZWxpci5cbiAgICAgICAgICAgIC8vIEJ1IGR1cnVtdSBoYXRhIG9sYXJhayBmxLFybGF0bWFrIHllcmluZSBudWxsIGTDtm5kw7xyZXJlayBzZXNzaXpjZSB5w7ZuZXRpeW9ydXouXG4gICAgICAgICAgICBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAxKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+KEue+4jyBLdWxsYW7EsWPEsSBnaXJpxZ8geWFwbWFtxLHFny4gKEJ1IGJla2xlbmVuIGJpciBkdXJ1bWR1ciknKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8gRGnEn2VyIHTDvG0gaGF0YWxhciAoNTAwLCBhxJ8gaGF0YWxhcsSxIHZzLikgZ2Vyw6dlayBiaXIgc29ydW5kdXIuXG4gICAgICAgICAgICAvLyBCdW5sYXLEsSBSZWFjdCBRdWVyeSduaW4geWFrYWxhbWFzxLEgacOnaW4gZsSxcmxhdMSxeW9ydXouXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgZ2V0VXNlckluZm8gc8SxcmFzxLFuZGEgYmVrbGVubWVkaWsgYmlyIGhhdGEgb2x1xZ90dTonLCBlcnJvcik7XG4gICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBBZGQgUmVmZXJlbmNlIENvZGVcbiAgICBhc3luYyBhZGRSZWZlcmVuY2UocmVmZXJlbmNlQ29kZTogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBhd2FpdCBhcGlDbGllbnQucG9zdChBUElfRU5EUE9JTlRTLkFERF9SRUZFUkVOQ0UsIHsgcmVmZXJhbnNDb2RlOiByZWZlcmVuY2VDb2RlIH0pO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFJlZmVyYW5zIGVrbGVuaXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBNYWtlIEFkbWluIChBZG1pbiBvbmx5KVxuICAgIGFzeW5jIG1ha2VBZG1pbih1c2VySWRPckVtYWlsOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGF3YWl0IGFwaUNsaWVudC5wb3N0KEFQSV9FTkRQT0lOVFMuTUFLRV9BRE1JTiwgeyB1c2VySWRPckVtYWlsIH0pO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEFkbWluIHlhcG1hIGhhdGFzxLE6JywgZXJyb3IpO1xuICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgIH1cbiAgICB9LFxuXG4gICAgLy8gVGVzdCBlbmRwb2ludFxuICAgIGFzeW5jIHRlc3QoKTogUHJvbWlzZTxhbnk+IHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KEFQSV9FTkRQT0lOVFMuVEVTVF9BVVRIKTtcbiAgICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gICAgfSxcblxuICAgIC8vIERlYnVnIENsYWltc1xuICAgIGFzeW5jIGRlYnVnQ2xhaW1zKCk6IFByb21pc2U8YW55PiB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldChBUElfRU5EUE9JTlRTLkRFQlVHX0NMQUlNUyk7XG4gICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICAgIH0sXG5cbiAgICAvLyBHZXQgUHJvZmlsZSBJbmZvIC0gRGV0YXlsxLEgcHJvZmlsIGJpbGdpbGVyaVxuICAgIGFzeW5jIGdldFByb2ZpbGVJbmZvKCk6IFByb21pc2U8UHJvZmlsZUluZm9SZXNwb25zZT4ge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UjiBBdXRoU2VydmljZSBnZXRQcm9maWxlSW5mbyBiYcWfbMSxeW9yLi4uJyk7XG5cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldDxBcGlSZXNwb25zZTxQcm9maWxlSW5mb1Jlc3BvbnNlPj4oQVBJX0VORFBPSU5UUy5QUk9GSUxFX0lORk8pO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBnZXRQcm9maWxlSW5mbyByYXcgcmVzcG9uc2U6JywgcmVzcG9uc2UuZGF0YSk7XG5cbiAgICAgICAgICAgIC8vIEFQSSByZXNwb25zZSd1IGRhdGEgd3JhcHBlcifEsSBpw6dpbmRlIGdlbGl5b3JcbiAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhLnN0YXR1cyA9PT0gMCAmJiByZXNwb25zZS5kYXRhLmRhdGEpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIGdldFByb2ZpbGVJbmZvIGJhxZ9hcsSxbMSxOicsIHJlc3BvbnNlLmRhdGEuZGF0YSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuZGF0YTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLmRhdGEubWVzc2FnZSB8fCAnUHJvZmlsIGJpbGdpbGVyaSBhbMSxbmFtYWTEsScpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgZ2V0UHJvZmlsZUluZm8gc8SxcmFzxLFuZGEgaGF0YSBvbHXFn3R1OicsIGVycm9yKTtcbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgfSxcblxuICAgIC8vIFVwZGF0ZSBQcm9maWxlXG4gICAgYXN5bmMgdXBkYXRlUHJvZmlsZShwcm9maWxlRGF0YTogVXBkYXRlUHJvZmlsZVJlcXVlc3QpOiBQcm9taXNlPGFueT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdChBUElfRU5EUE9JTlRTLlVQREFURV9QUk9GSUxFLCBwcm9maWxlRGF0YSk7XG4gICAgICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFVwZGF0ZSBwcm9maWxlIGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgfSxcblxuICAgIC8vIFVwZGF0ZSBQcm9maWxlIFBpY3R1cmVcbiAgICBhc3luYyB1cGRhdGVQcm9maWxlUGljdHVyZShpbWFnZUZpbGU6IEZpbGUpOiBQcm9taXNlPGFueT4ge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+TpCBVcGRhdGUgcHJvZmlsZSBwaWN0dXJlIHJlcXVlc3QnKTtcblxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTtcbiAgICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnZmlsZScsIGltYWdlRmlsZSk7XG5cbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnB1dChBUElfRU5EUE9JTlRTLlBST0ZJTEVfUElDVFVSRSwgZm9ybURhdGEsIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YScsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBVcGRhdGUgcHJvZmlsZSBwaWN0dXJlIHJlc3BvbnNlOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBVcGRhdGUgcHJvZmlsZSBwaWN0dXJlIGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgfSxcblxuICAgIC8vIERlbGV0ZSBQcm9maWxlIFBpY3R1cmVcbiAgICBhc3luYyBkZWxldGVQcm9maWxlUGljdHVyZSgpOiBQcm9taXNlPGFueT4ge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+Xke+4jyBEZWxldGUgcHJvZmlsZSBwaWN0dXJlIHJlcXVlc3QnKTtcblxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZGVsZXRlKEFQSV9FTkRQT0lOVFMuREVMRVRFX1BST0ZJTEVfUElDVFVSRSk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIERlbGV0ZSBwcm9maWxlIHBpY3R1cmUgcmVzcG9uc2U6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIERlbGV0ZSBwcm9maWxlIHBpY3R1cmUgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgIH1cbiAgICB9LFxufTsgIl0sIm5hbWVzIjpbImFwaUNsaWVudCIsIkFQSV9FTkRQT0lOVFMiLCJhdXRoU2VydmljZSIsImNsZWFyQXV0aENvb2tpZXMiLCJjb25zb2xlIiwibG9nIiwiYXV0aENvb2tpZXMiLCJmb3JFYWNoIiwiY29va2llTmFtZSIsImRvY3VtZW50IiwiY29va2llIiwid2luZG93IiwibG9jYXRpb24iLCJob3N0bmFtZSIsImxvZ2luIiwiZW1haWwiLCJwYXNzd29yZCIsImhhc0xvZ2dlZE91dCIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJyZW1vdmVJdGVtIiwibG9naW5EYXRhIiwiZGVmYXVsdHMiLCJiYXNlVVJMIiwid2l0aENyZWRlbnRpYWxzIiwiaGVhZGVycyIsInJlc3BvbnNlIiwiTE9HSU4iLCJwb3N0IiwiZGF0YSIsInN0YXR1cyIsIm1lc3NhZ2UiLCJoYXNEYXRhIiwic3RhdHVzVHlwZSIsImRhdGFLZXlzIiwiT2JqZWN0Iiwia2V5cyIsImh0dHBTdWNjZXNzIiwiaGFzU2V0Q29va2llIiwibWVzc2FnZVN1Y2Nlc3MiLCJpbmNsdWRlcyIsInN0YXR1c1N1Y2Nlc3MiLCJzdWNjZXNzIiwibm9FcnJvck1lc3NhZ2UiLCJlcnJvciIsImlzU3VjY2VzcyIsImZpbmFsU3VjY2VzcyIsInJlZ2lzdGVyIiwidXNlckRhdGEiLCJSRUdJU1RFUiIsImJhY2tlbmRSZXNwb25zZSIsInVzZXIiLCJpZCIsImZpcnN0TmFtZSIsImxhc3ROYW1lIiwicGhvbmVOdW1iZXIiLCJ1bmRlZmluZWQiLCJyZWZyZXNoVG9rZW4iLCJnZXQiLCJSRUZSRVNIX1RPS0VOIiwibG9nb3V0IiwiTE9HT1VUIiwiY2xlYXIiLCJjb29raWVzIiwic3BsaXQiLCJlcVBvcyIsImluZGV4T2YiLCJuYW1lIiwic3Vic3RyIiwidHJpbSIsInNlc3Npb25TdG9yYWdlIiwiZ2V0VXNlckluZm8iLCJVU0VSX0lORk8iLCJhZGRSZWZlcmVuY2UiLCJyZWZlcmVuY2VDb2RlIiwiQUREX1JFRkVSRU5DRSIsInJlZmVyYW5zQ29kZSIsIm1ha2VBZG1pbiIsInVzZXJJZE9yRW1haWwiLCJNQUtFX0FETUlOIiwidGVzdCIsIlRFU1RfQVVUSCIsImRlYnVnQ2xhaW1zIiwiREVCVUdfQ0xBSU1TIiwiZ2V0UHJvZmlsZUluZm8iLCJQUk9GSUxFX0lORk8iLCJFcnJvciIsInVwZGF0ZVByb2ZpbGUiLCJwcm9maWxlRGF0YSIsIlVQREFURV9QUk9GSUxFIiwidXBkYXRlUHJvZmlsZVBpY3R1cmUiLCJpbWFnZUZpbGUiLCJmb3JtRGF0YSIsIkZvcm1EYXRhIiwiYXBwZW5kIiwicHV0IiwiUFJPRklMRV9QSUNUVVJFIiwiZGVsZXRlUHJvZmlsZVBpY3R1cmUiLCJkZWxldGUiLCJERUxFVEVfUFJPRklMRV9QSUNUVVJFIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/authService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/stores/authStore.ts":
/*!*********************************!*\
  !*** ./src/stores/authStore.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// Backend'den gelen user verisini frontend'e uygun hale getiren helper fonksiyon\nconst mapBackendUserToFrontend = (backendUser)=>{\n    if (!backendUser) {\n        throw new Error('Backend user data is null or undefined');\n    }\n    // Check for ID field in different formats\n    console.log('🔍 Mapping user data for:', backendUser.email || 'unknown user');\n    console.log('🔍 Backend user fields:', {\n        userId: backendUser.userId,\n        id: backendUser.id,\n        role: backendUser.role,\n        referenceId: backendUser.referenceId,\n        referanceId: backendUser.referanceId\n    });\n    // Roller dizisinden veya tek role alanından ana rolü belirle\n    let role = 'customer';\n    // Önce string role alanını kontrol et\n    if (backendUser.role) {\n        if (backendUser.role.toLowerCase() === 'admin') {\n            role = 'admin';\n        } else if (backendUser.role.toLowerCase() === 'dealership') {\n            role = 'dealership';\n        } else {\n            role = 'customer';\n        }\n    } else if (backendUser.roles && Array.isArray(backendUser.roles) && backendUser.roles.length > 0) {\n        if (backendUser.roles.includes('Admin')) {\n            role = 'admin';\n        } else if (backendUser.roles.includes('Dealership')) {\n            role = 'dealership';\n        }\n    }\n    // 🔍 ID field mapping - Backend artık userId alanını kullanıyor\n    let userId = backendUser.userId || backendUser.id || backendUser.ID || backendUser.user_id;\n    // Eğer hiçbiri yoksa ve email varsa, geçici bir ID ata\n    if (!userId && backendUser.email) {\n        console.warn('⚠️ Backend user has no ID field, generating temporary ID from email hash');\n        // Email'den basit bir hash oluştur\n        userId = Math.abs(backendUser.email.split('').reduce((a, b)=>{\n            a = (a << 5) - a + b.charCodeAt(0);\n            return a & a;\n        }, 0));\n    }\n    const mappedUser = {\n        ...backendUser,\n        id: userId,\n        role,\n        membershipLevel: backendUser.membershipLevelId !== undefined ? backendUser.membershipLevelId : 0,\n        joinDate: backendUser.registeredAt ? new Date(backendUser.registeredAt).toISOString().split('T')[0] : '',\n        isDealershipApproved: role === 'dealership',\n        roles: backendUser.roles || (backendUser.role ? [\n            backendUser.role\n        ] : []),\n        isActive: backendUser.isActive !== undefined ? backendUser.isActive : true,\n        phoneNumber: backendUser.phoneNumber || '',\n        referenceId: backendUser.referenceId || backendUser.referanceId || 0,\n        careerRankId: backendUser.careerRankId || 1\n    };\n    console.log('✅ User mapped successfully with ID:', mappedUser.id);\n    return mappedUser;\n};\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((set, get)=>({\n        // Initial state\n        user: null,\n        isLoading: false,\n        isAuthenticated: false,\n        error: null,\n        // Actions\n        // 🗑️ login fonksiyonu tamamen silindi. Artık useLoginMutation kullanılıyor.\n        // 🗑️ logout fonksiyonu tamamen silindi. Artık useLogoutMutation kullanılıyor.\n        clearError: ()=>set({\n                error: null\n            }),\n        setLoading: (loading)=>set({\n                isLoading: loading\n            })\n    }), {\n    name: 'auth-store'\n})); // 🗑️ Window event listener'ları silindi. Bu mantık artık AuthContext ve interceptor'da. \n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/stores/authStore.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/stores/customerPriceStore.ts":
/*!******************************************!*\
  !*** ./src/stores/customerPriceStore.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCustomerPriceStore: () => (/* binding */ useCustomerPriceStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useCustomerPriceStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set)=>({\n        isCustomerPrice: false,\n        setIsCustomerPrice: (value)=>set({\n                isCustomerPrice: value\n            }),\n        resetCustomerPrice: ()=>set({\n                isCustomerPrice: false\n            }),\n        _hasHydrated: false,\n        setHasHydrated: (state)=>set({\n                _hasHydrated: state\n            })\n    }), {\n    name: 'customer-price-store',\n    onRehydrateStorage: ()=>(state)=>{\n            state === null || state === void 0 ? void 0 : state.setHasHydrated(true);\n        }\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/stores/customerPriceStore.ts\n"));

/***/ })

});