(()=>{var e={};e.id=798,e.ids=[798],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10188:(e,t,r)=>{Promise.resolve().then(r.bind(r,74652))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17201:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),l=r.n(i),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let c={children:["",{children:["admin",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58322)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\orders\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\orders\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/orders/page",pathname:"/admin/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19080:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22757:(e,t,r)=>{"use strict";r.d(t,{bk:()=>a,d2:()=>i});var s=r(78259);let a={totalUsers:1247,totalOrders:2856,totalRevenue:185420.75,totalProducts:156,newUsersThisMonth:87,ordersThisMonth:234,revenueThisMonth:18650.5,monthlyStats:[{month:"Ocak",users:65,orders:180,revenue:12450.75,products:145},{month:"Şubat",users:78,orders:210,revenue:15280.5,products:148},{month:"Mart",users:92,orders:245,revenue:18320.25,products:152},{month:"Nisan",users:68,orders:190,revenue:14680.75,products:154},{month:"Mayıs",users:85,orders:220,revenue:16850,products:155},{month:"Haziran",users:87,orders:234,revenue:18650.5,products:156}]};s.F.None,s.F.Gumus,s.F.None,s.F.Bronz,s.F.Bronz,s.F.None;let i=[{id:"ORD-2024-1001",userId:3,userName:"M\xfcşteri Ali",userEmail:"<EMAIL>",orderDate:"2024-06-15T08:30:00",status:"processing",total:1245.5,itemCount:3,shippingMethod:"Hızlı Kargo",paymentMethod:"Kredi Kartı"},{id:"ORD-2024-1000",userId:4,userName:"Zeynep Kaya",userEmail:"<EMAIL>",orderDate:"2024-06-14T16:20:00",status:"shipped",total:890.25,itemCount:2,shippingMethod:"Standart Kargo",paymentMethod:"Kredi Kartı"},{id:"ORD-2024-0999",userId:5,userName:"Mehmet Demir",userEmail:"<EMAIL>",orderDate:"2024-06-14T12:15:00",status:"delivered",total:2150.75,itemCount:5,shippingMethod:"Express Kargo",paymentMethod:"Havale/EFT"},{id:"ORD-2024-0998",userId:2,userName:"Distrib\xfct\xf6r Ahmet",userEmail:"<EMAIL>",orderDate:"2024-06-13T14:45:00",status:"delivered",total:1650,itemCount:4,shippingMethod:"Hızlı Kargo",paymentMethod:"Kredi Kartı"},{id:"ORD-2024-0997",userId:6,userName:"Ayşe Yılmaz",userEmail:"<EMAIL>",orderDate:"2024-06-12T10:30:00",status:"cancelled",total:450.75,itemCount:1,shippingMethod:"Standart Kargo",paymentMethod:"Kredi Kartı"}]},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28561:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35071:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},46636:(e,t,r)=>{Promise.resolve().then(r.bind(r,58322))},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58322:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\admin\\\\orders\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\orders\\page.tsx","default")},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var s=r(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:i="",children:l,iconNode:o,...x},m)=>(0,s.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:n("lucide",i),...!l&&!d(x)&&{"aria-hidden":"true"},...x},[...o.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(l)?l:[l]])),x=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...i},d)=>(0,s.createElement)(o,{ref:d,iconNode:t,className:n(`lucide-${a(l(e))}`,`lucide-${e}`,r),...i}));return r.displayName=l(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},74652:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>N});var s=r(60687),a=r(43210),i=r(15908),l=r(16189),n=r(26001),d=r(22757),c=r(48730),o=r(19080),x=r(88059),m=r(5336),p=r(35071),u=r(28559),h=r(99891),y=r(28561),g=r(99270),f=r(13861),j=r(63143),b=r(85814),v=r.n(b);let N=()=>{let{user:e,isLoading:t}=(0,i.A)(),r=(0,l.useRouter)(),[b,N]=(0,a.useState)(d.d2),[w,A]=(0,a.useState)(""),[k,M]=(0,a.useState)("all");(0,a.useEffect)(()=>{t||e&&"admin"===e.role||r.push("/login")},[e,t,r]);let P=b.filter(e=>{let t=e.id.toLowerCase().includes(w.toLowerCase())||e.userName.toLowerCase().includes(w.toLowerCase())||e.userEmail.toLowerCase().includes(w.toLowerCase()),r="all"===k||e.status===k;return t&&r});if(t)return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})});if(!e||"admin"!==e.role)return null;let C=e=>{switch(e){case"pending":return(0,s.jsx)(c.A,{className:"h-4 w-4 text-yellow-600"});case"processing":return(0,s.jsx)(o.A,{className:"h-4 w-4 text-blue-600"});case"shipped":return(0,s.jsx)(x.A,{className:"h-4 w-4 text-purple-600"});case"delivered":return(0,s.jsx)(m.A,{className:"h-4 w-4 text-green-600"});case"cancelled":return(0,s.jsx)(p.A,{className:"h-4 w-4 text-red-600"});default:return(0,s.jsx)(c.A,{className:"h-4 w-4 text-gray-600"})}},S=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"processing":return"bg-blue-100 text-blue-800";case"shipped":return"bg-purple-100 text-purple-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},z=e=>{switch(e){case"pending":return"Beklemede";case"processing":return"Hazırlanıyor";case"shipped":return"Kargoda";case"delivered":return"Teslim Edildi";case"cancelled":return"İptal Edildi";default:return"Bilinmiyor"}},D=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),q=e=>new Intl.NumberFormat("tr-TR",{style:"currency",currency:"TRY"}).format(e),E=b.reduce((e,t)=>e+t.total,0);return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(v(),{href:"/admin",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"Admin Paneli"]}),(0,s.jsx)("span",{className:"text-gray-300",children:"/"}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Sipariş Y\xf6netimi"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 bg-red-100 px-4 py-2 rounded-lg",children:[(0,s.jsx)(h.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)("span",{className:"text-red-800 font-medium",children:"Admin Erişimi"})]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-6 mb-8",children:[(0,s.jsx)(n.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Sipariş"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:b.length})]}),(0,s.jsx)(y.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,s.jsx)(n.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-yellow-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Bekleyen"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:b.filter(e=>"pending"===e.status).length})]}),(0,s.jsx)(c.A,{className:"h-8 w-8 text-yellow-600"})]})}),(0,s.jsx)(n.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Hazırlanıyor"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:b.filter(e=>"processing"===e.status).length})]}),(0,s.jsx)(o.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,s.jsx)(n.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Teslim Edildi"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:b.filter(e=>"delivered"===e.status).length})]}),(0,s.jsx)(m.A,{className:"h-8 w-8 text-green-600"})]})}),(0,s.jsx)(n.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Gelir"}),(0,s.jsx)("p",{className:"text-lg font-bold text-gray-900",children:q(E)})]}),(0,s.jsx)("div",{className:"text-purple-600 text-2xl font-bold",children:"₺"})]})})]}),(0,s.jsx)(n.P.div,{className:"bg-white rounded-xl shadow-lg p-6 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:(0,s.jsx)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(g.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"}),(0,s.jsx)("input",{type:"text",placeholder:"Sipariş ara...",value:w,onChange:e=>A(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder:text-gray-600 text-black"})]}),(0,s.jsxs)("select",{value:k,onChange:e=>M(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-600",children:[(0,s.jsx)("option",{value:"all",children:"T\xfcm Durumlar"}),(0,s.jsx)("option",{value:"pending",children:"Beklemede"}),(0,s.jsx)("option",{value:"processing",children:"Hazırlanıyor"}),(0,s.jsx)("option",{value:"shipped",children:"Kargoda"}),(0,s.jsx)("option",{value:"delivered",children:"Teslim Edildi"}),(0,s.jsx)("option",{value:"cancelled",children:"İptal Edildi"})]})]})})}),(0,s.jsxs)(n.P.div,{className:"bg-white rounded-xl shadow-lg overflow-hidden",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Siparişler (",P.length,")"]})}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Sipariş No"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"M\xfcşteri"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tarih"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Durum"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"\xdcr\xfcn Sayısı"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Toplam"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"\xd6deme"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"İşlemler"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:P.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.id})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.userEmail})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:D(e.orderDate)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${S(e.status)}`,children:[C(e.status),(0,s.jsx)("span",{className:"ml-1",children:z(e.status)})]})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.itemCount," \xfcr\xfcn"]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:q(e.total)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.paymentMethod}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.shippingMethod})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{className:"text-blue-600 hover:text-blue-900",children:(0,s.jsx)(f.A,{className:"h-4 w-4"})}),(0,s.jsx)("button",{className:"text-green-600 hover:text-green-900",children:(0,s.jsx)(j.A,{className:"h-4 w-4"})})]})})]},e.id))})]})}),0===P.length&&(0,s.jsxs)("div",{className:"px-6 py-12 text-center",children:[(0,s.jsx)(y.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Sipariş bulunamadı"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Arama kriterlerinizi değiştirerek tekrar deneyin."})]})]})]})})}},78259:(e,t,r)=>{"use strict";r.d(t,{F:()=>a,Sz:()=>l,vn:()=>s,w7:()=>i});var s=function(e){return e[e.Default=0]="Default",e[e.PriceAsc=1]="PriceAsc",e[e.PriceDesc=2]="PriceDesc",e[e.RatingDesc=3]="RatingDesc",e}({}),a=function(e){return e[e.None=0]="None",e[e.Baslangic=1]="Baslangic",e[e.Girisimci=2]="Girisimci",e[e.Bronz=3]="Bronz",e[e.Gumus=4]="Gumus",e[e.Altin=5]="Altin",e[e.Platin=6]="Platin",e[e.PlatinMax=7]="PlatinMax",e}({}),i=function(e){return e[e.Unspecified=0]="Unspecified",e[e.Male=1]="Male",e[e.Female=2]="Female",e[e.Other=3]="Other",e}({}),l=function(e){return e[e.Pending=0]="Pending",e[e.Accepted=1]="Accepted",e[e.Rejected=2]="Rejected",e}({})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88059:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},94735:e=>{"use strict";e.exports=require("events")},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},99891:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,181,658,85],()=>r(17201));module.exports=s})();