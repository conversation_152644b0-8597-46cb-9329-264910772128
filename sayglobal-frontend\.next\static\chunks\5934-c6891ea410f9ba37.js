"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5934],{45934:(e,s,a)=>{a.d(s,{A:()=>v});var t=a(95155),l=a(12115),r=a(76408),i=a(35695),n=a(26715),c=a(85339),d=a(40646),o=a(54861),x=a(54416),m=a(51154),h=a(42355),u=a(13052),g=a(37108),y=a(69074),j=a(71007),p=a(56407),N=a(23903),f=a(83717);let v=e=>{var s,a,v,b;let{productId:w,isOpen:k,onClose:A,showApprovalStatus:S=!1,refreshOnClose:P=!1}=e,D=(0,i.useRouter)(),z=(0,n.jE)(),B=(0,N.Q6)(),C=(0,N.e$)(),R=(0,N.Z9)(e=>e.setCurrentImage),U=(0,N.Z9)(e=>e.setSelectedVariant),V=(0,N.Z9)(e=>e.clearProductCache),{data:M,isLoading:F,error:q}=(0,p.LJ)(w),{data:E,isLoading:O}=(0,p.qr)(w,S&&k);(0,l.useEffect)(()=>{if(M){var e,s,a,t,l,r;console.log("\uD83D\uDD0D Modal ProductDetail Data:",{id:M.id,name:M.name,variantsCount:(null==(e=M.variants)?void 0:e.length)||0,variants:M.variants,hasVariants:M.variants&&M.variants.length>0,firstVariant:null==(s=M.variants)?void 0:s[0],firstVariantPrice:null==(t=M.variants)||null==(a=t[0])?void 0:a.price,firstVariantPriceType:typeof(null==(r=M.variants)||null==(l=r[0])?void 0:l.price)})}},[M]),(0,l.useEffect)(()=>(k?(document.body.style.overflow="hidden",S&&w&&(console.log("\uD83D\uDD04 Onay y\xf6netimi sayfasından modal a\xe7ıldı, cache refresh ediliyor..."),z.invalidateQueries({queryKey:["productDetail",w]}),z.invalidateQueries({queryKey:["productMessage",w]}))):document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[k,S,w,z]),(0,l.useEffect)(()=>{R(0)},[B,R]),(0,l.useEffect)(()=>{k&&w&&(console.log("\uD83D\uDD04 Modal opened, forcing fresh data fetch for product:",w),V(w),z.invalidateQueries({queryKey:["productDetail",w]}))},[k,w,V,z]);let K=()=>{P&&w&&(console.log("\uD83D\uDD04 Modal kapatılıyor, cache refresh ediliyor..."),z.invalidateQueries({queryKey:["productDetail",w]}),z.invalidateQueries({queryKey:["productMessage",w]}),z.invalidateQueries({queryKey:["adminProducts"]})),A()};if(!k)return null;let L=e=>new Intl.NumberFormat("tr-TR",{style:"currency",currency:"TRY"}).format(e),T=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),Q=(null==M?void 0:M.variants)&&M.variants.length>0,G=Q?Math.min(B,M.variants.length-1):0,Y=Q?M.variants[G]:null,_=(null==Y||null==(s=Y.images)?void 0:s.map(e=>e.url))||[],I=Y?(e=>0===e?{text:"Stokta Yok",color:"bg-red-100 text-red-800",icon:(0,t.jsx)(o.A,{className:"h-4 w-4"})}:e<20?{text:"Az Stok",color:"bg-yellow-100 text-yellow-800",icon:(0,t.jsx)(c.A,{className:"h-4 w-4"})}:{text:"Stokta",color:"bg-green-100 text-green-800",icon:(0,t.jsx)(d.A,{className:"h-4 w-4"})})(Y.stock):null;return(0,t.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-lg flex items-center justify-center p-4 z-50",onClick:K,children:(0,t.jsxs)(r.P.div,{className:"bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.2},onClick:e=>e.stopPropagation(),children:[(0,t.jsx)("div",{className:"sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-xl",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex items-center space-x-3",children:(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"\xdcr\xfcn Detayları (Admin)"})}),(0,t.jsx)("button",{onClick:K,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,t.jsx)(x.A,{className:"w-6 h-6"})})]})}),(0,t.jsxs)("div",{className:"p-6",children:[F&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,t.jsx)(m.A,{className:"h-8 w-8 animate-spin text-blue-600"}),(0,t.jsx)("span",{className:"ml-2 text-gray-600",children:"\xdcr\xfcn detayları y\xfckleniyor..."})]}),q&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(c.A,{className:"h-6 w-6 text-red-600 mt-1 mr-3 flex-shrink-0"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-red-800 mb-2",children:"\xdcr\xfcn Detayları Y\xfcklenemedi"}),(0,t.jsx)("p",{className:"text-red-700 mb-4",children:"\xdcr\xfcn detayları y\xfcklenirken bir hata oluştu. L\xfctfen tekrar deneyin."}),(0,t.jsxs)("div",{className:"text-sm text-red-600 bg-red-100 rounded p-2 font-mono",children:["Hata: ",(null==q?void 0:q.message)||"Bilinmeyen hata"]}),(0,t.jsx)("button",{onClick:()=>window.location.reload(),className:"mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Sayfayı Yenile"})]})]})}),M&&(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[Q&&M.variants.length>1&&(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsx)("nav",{className:"-mb-px flex space-x-8",children:M.variants.map((e,s)=>(0,t.jsxs)("button",{onClick:()=>U(s),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(B===s?"border-red-500 text-red-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:["Varyant ",s+1,e.features.length>0&&(0,t.jsxs)("span",{className:"ml-1 text-xs",children:["(",e.features.map(e=>e.featureValue).join(", "),")"]})]},e.id))})}),(0,t.jsx)("div",{className:"relative aspect-square bg-gray-100 rounded-lg overflow-hidden",children:_.length>0?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("img",{src:_[C],alt:M.name,className:"w-full h-full object-cover"}),_.length>1&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("button",{onClick:()=>{var e;if(!Y||!(null==(e=Y.images)?void 0:e.length))return;let s=Y.images.length;R((C-1+s)%s)},className:"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors",children:(0,t.jsx)(h.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>{var e;if(Y&&(null==(e=Y.images)?void 0:e.length))R((C+1)%Y.images.length)},className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors",children:(0,t.jsx)(u.A,{className:"h-4 w-4"})}),(0,t.jsxs)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:[C+1," / ",_.length]})]})]}):(0,t.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,t.jsx)(g.A,{className:"h-16 w-16 text-gray-400"})})}),_.length>1&&(0,t.jsx)("div",{className:"grid grid-cols-4 gap-2",children:_.map((e,s)=>(0,t.jsx)("button",{onClick:()=>R(s),className:"aspect-square rounded-lg overflow-hidden border-2 ".concat(C===s?"border-red-500":"border-gray-200 hover:border-gray-300"),children:(0,t.jsx)("img",{src:e,alt:"".concat(M.name," ").concat(s+1),className:"w-full h-full object-cover"})},s))})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(M.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:M.isActive?"Aktif":"Pasif"}),S&&void 0!==M.status&&(0,t.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full border ".concat((e=>{switch(e){case f.Sz.Pending:return"bg-yellow-100 text-yellow-800 border-yellow-200";case f.Sz.Accepted:return"bg-green-100 text-green-800 border-green-200";case f.Sz.Rejected:return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(M.status)),children:[(e=>{switch(e){case f.Sz.Pending:return(0,t.jsx)(c.A,{className:"h-4 w-4"});case f.Sz.Accepted:return(0,t.jsx)(d.A,{className:"h-4 w-4"});case f.Sz.Rejected:return(0,t.jsx)(o.A,{className:"h-4 w-4"});default:return(0,t.jsx)(c.A,{className:"h-4 w-4"})}})(M.status),(0,t.jsx)("span",{className:"ml-1",children:(e=>{switch(e){case f.Sz.Pending:return"Onay Bekliyor";case f.Sz.Accepted:return"Onaylandı";case f.Sz.Rejected:return"Reddedildi";default:return"Bilinmiyor"}})(M.status)})]})]}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:M.categoryName})]}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:M.name}),(0,t.jsx)("p",{className:"text-lg text-gray-600",children:M.brandName})]}),Y?(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:"Varyant Bilgileri"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Fiyat"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-900",children:(()=>{let e=Y.price;return(console.log("\uD83D\uDCB0 Fiyat Debug:",{price:Y.price,priceType:typeof Y.price,isNull:null===Y.price,isUndefined:void 0===Y.price,isNaN:isNaN(Y.price),variant:Y}),null==e||void 0===e)?"Fiyat Belirtilmemiş":isNaN(e)?"Ge\xe7ersiz Fiyat":L(e)})()})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Stok"}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:I&&(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(I.color),children:[I.icon,(0,t.jsxs)("span",{className:"ml-1",children:[Y.stock," - ",I.text]})]})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"PV"}),(0,t.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:Y.pv})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"CV"}),(0,t.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:Y.cv})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"SP"}),(0,t.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:Y.sp})]})]}),Y.extraDiscount>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Ekstra İndirim"}),(0,t.jsxs)("p",{className:"text-lg font-semibold text-green-600",children:["%",Y.extraDiscount]})]}),Y.features.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"\xd6zellikler"}),(0,t.jsx)("div",{className:"space-y-2",children:Y.features.map((e,s)=>(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[e.featureName,":"]}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.featureValue})]},s))})]})]}):(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"h-5 w-5 text-yellow-600 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-yellow-800",children:"Varyant Bilgileri Eksik"}),(0,t.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"Bu \xfcr\xfcn i\xe7in varyant bilgileri bulunamadı. \xdcr\xfcn\xfc d\xfczenleyerek varyant ekleyebilirsiniz."})]})]})}),M.description&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"A\xe7ıklama"}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:M.description})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-2",children:[(0,t.jsxs)("h3",{className:"font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(y.A,{className:"h-5 w-5 mr-2 text-gray-600"}),"Tarihler"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Oluşturulma:"}),(0,t.jsx)("span",{className:"text-gray-900",children:T(M.createdAt)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Son G\xfcncelleme:"}),(0,t.jsx)("span",{className:"text-gray-900",children:T(M.updatedAt)})]})]})]}),M.createdByUserId&&(0,t.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg border",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[(0,t.jsx)(j.A,{className:"h-5 w-5 mr-2 text-gray-600"}),"Oluşturan"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Kullanıcı Adı:"}),(0,t.jsx)("p",{className:"text-gray-900 font-medium",children:M.createdByUserName})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Rol:"}),(0,t.jsx)("p",{className:"text-gray-900 font-medium",children:(null==(a=M.createdByUserRole)?void 0:a.toLowerCase())==="admin"?"Admin":(null==(v=M.createdByUserRole)?void 0:v.toLowerCase())==="dealership"?"Satıcı":(null==(b=M.createdByUserRole)?void 0:b.toLowerCase())==="customer"?"M\xfcşteri":M.createdByUserRole})]})]})]}),S&&(0,t.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg border",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 mr-2 text-gray-600"}),"Admin \xdcr\xfcn Notu"]}),O?(0,t.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,t.jsx)(m.A,{className:"h-5 w-5 animate-spin text-gray-400 mr-2"}),(0,t.jsx)("span",{className:"text-gray-500",children:"Not y\xfckleniyor..."})]}):E?(0,t.jsx)("div",{className:"space-y-3",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"mb-3",children:E.message?(0,t.jsx)("p",{className:"text-gray-800 font-medium",children:E.message}):(0,t.jsx)("p",{className:"text-gray-500 italic",children:"Admin notu girilmemiş"})}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Değiştiren:"})," ",E.approvedByUser.fullName," (",E.approvedByUser.role,")"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Tarih:"})," ",new Date(E.changedAtUtc).toLocaleString("tr-TR")]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Durum:"}),(0,t.jsx)("span",{className:"ml-1 ".concat(1===E.newStatus?"text-green-600":2===E.newStatus?"text-red-600":"text-yellow-600"),children:1===E.newStatus?"Onaylandı":2===E.newStatus?"Reddedildi":"Beklemede"})]})]})]}),(0,t.jsx)("div",{className:"ml-4",children:1===E.newStatus?(0,t.jsx)(d.A,{className:"h-5 w-5 text-green-500"}):2===E.newStatus?(0,t.jsx)(o.A,{className:"h-5 w-5 text-red-500"}):(0,t.jsx)(c.A,{className:"h-5 w-5 text-yellow-500"})})]})}):(0,t.jsx)("div",{className:"text-center py-4",children:(null==M?void 0:M.status)===f.Sz.Pending?(0,t.jsxs)("div",{className:"flex items-center justify-center text-yellow-600",children:[(0,t.jsx)(c.A,{className:"h-5 w-5 mr-2"}),(0,t.jsx)("span",{children:"Onay bekleniyor"})]}):(0,t.jsxs)("div",{className:"flex items-center justify-center text-gray-500",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 mr-2"}),(0,t.jsx)("span",{children:"Admin notu girilmemiş"})]})})]}),(0,t.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,t.jsx)("button",{onClick:K,className:"flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors",children:"Kapat"}),(0,t.jsx)("button",{className:"flex-1 px-4 py-2 text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors",onClick:()=>{K();let e=S?"pending-products":"products";D.push("/admin/products/edit/".concat(M.id,"?from=").concat(e))},children:"D\xfczenle"})]})]})]})]})]})})}},83717:(e,s,a)=>{a.d(s,{F:()=>l,Sz:()=>i,vn:()=>t,w7:()=>r});var t=function(e){return e[e.Default=0]="Default",e[e.PriceAsc=1]="PriceAsc",e[e.PriceDesc=2]="PriceDesc",e[e.RatingDesc=3]="RatingDesc",e}({}),l=function(e){return e[e.None=0]="None",e[e.Baslangic=1]="Baslangic",e[e.Girisimci=2]="Girisimci",e[e.Bronz=3]="Bronz",e[e.Gumus=4]="Gumus",e[e.Altin=5]="Altin",e[e.Platin=6]="Platin",e[e.PlatinMax=7]="PlatinMax",e}({}),r=function(e){return e[e.Unspecified=0]="Unspecified",e[e.Male=1]="Male",e[e.Female=2]="Female",e[e.Other=3]="Other",e}({}),i=function(e){return e[e.Pending=0]="Pending",e[e.Accepted=1]="Accepted",e[e.Rejected=2]="Rejected",e}({})}}]);