(()=>{var e={};e.id=197,e.ids=[197],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3929:(e,a,t)=>{Promise.resolve().then(t.bind(t,70955))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28597:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>w});var r=t(60687),s=t(43210),i=t(15908),n=t(16189),l=t(26001),o=t(43649),d=t(5336),c=t(28559),u=t(99891),m=t(96882),g=t(37360),p=t(88233),x=t(45583),f=t(9005),h=t(8819),b=t(85814),y=t.n(b),v=t(64298),N=t(21852),j=t(10577),I=t(47602),D=t(75068),k=t(8693),V=t(55245);let w=()=>{let{user:e,isLoading:a}=(0,i.A)(),t=(0,n.useRouter)(),b=(0,n.useParams)();(0,k.jE)();let[w,F]=(0,s.useState)(!1),[S,C]=(0,s.useState)(null),[P,A]=(0,s.useState)(!1),{formData:$,selectedNames:E,variants:z,error:M,availableFeatures:q,originalProductId:R}=(0,I.K_)(),{handleInputChange:_,setCategorySelection:U,clearAllSelections:O,deleteVariant:G,setError:K,reset:T,setVariants:Y,saveVariant:B,initializeWithProduct:H,setOriginalProductId:L}=(0,I.K_)(e=>e),{openProductCategorySelector:W,closeProductCategorySelector:X,openProductVariant:Q,closeProductVariant:J}=(0,D.QR)(),Z=(0,D.fW)(),ee=(0,D._f)(),ea=(0,D.HX)(),et=(0,D.qA)(),er=e=>{let a={};return e.forEach(e=>{e.featureDetails.forEach(e=>{a[e.featureDefinitionId]||(a[e.featureDefinitionId]=[]),a[e.featureDefinitionId].includes(e.featureValueId)||a[e.featureDefinitionId].push(e.featureValueId)})}),a},{mutateAsync:es,isPending:ei}=(0,V.mn)();(0,s.useEffect)(()=>{(async()=>{let e=(await b).id;C(e),L(parseInt(e))})()},[b,L]),(0,s.useEffect)(()=>{(async()=>{if(S)try{let e=await v.jU.getDealershipProductDetail(parseInt(S));e.success&&e.data?H(e.data):K("\xdcr\xfcn bulunamadı veya erişim yetkiniz bulunmuyor.")}catch(e){console.error("\xdcr\xfcn y\xfckleme hatası:",e),K("\xdcr\xfcn y\xfcklenirken bir hata oluştu.")}})()},[S,H,K]),(0,s.useEffect)(()=>()=>{T()},[T]),(0,s.useEffect)(()=>{e&&"dealership"!==e.role&&"admin"!==e.role&&t.push("/"),a||e||t.push("/login")},[e,a,t]);let en=e=>{Q({editingVariant:e,availableFeatures:q,existingVariants:z})},el=async e=>{e.preventDefault(),K(null),console.log("\uD83D\uDE80 Edit Product Submit başladı"),console.log("\uD83D\uDCCB Form Data:",{name:$.name,description:$.description,brandId:$.brandId,subCategoryId:$.subCategoryId,originalProductId:R}),console.log("\uD83D\uDD27 Variants:",z);try{if(!$.name.trim())throw Error("\xdcr\xfcn adı gereklidir");if(!$.description.trim())throw Error("\xdcr\xfcn a\xe7ıklaması gereklidir");if($.brandId<=0)throw Error("Marka ve kategori se\xe7imi gereklidir");if(0===z.length)throw Error("En az bir varyant oluşturmalısınız.");for(let e of z){if(e.pricing.price<=0)throw Error(`${e.name} varyantı i\xe7in fiyat 0'dan b\xfcy\xfck olmalıdır`);if(e.pricing.stock<0)throw Error(`${e.name} varyantı i\xe7in stok miktarı negatif olamaz`)}console.log("✅ Validation ge\xe7ti, FormData oluşturuluyor...");let e=new FormData;for(let[a,t]of(e.append("Product.Id",R.toString()),e.append("Product.Name",$.name),e.append("Product.Description",$.description),e.append("Product.BrandId",$.brandId.toString()),e.append("Product.SubCategoryId",$.subCategoryId.toString()),console.log("\uD83D\uDCE6 Product bilgileri FormData'ya eklendi:",{"Product.Id":R.toString(),"Product.Name":$.name,"Product.Description":$.description,"Product.BrandId":$.brandId.toString(),"Product.SubCategoryId":$.subCategoryId.toString()}),z.forEach((a,t)=>{if(console.log(`🔧 Variant ${t} işleniyor:`,{id:a.id,name:a.name,pricing:a.pricing,selectedFeatures:a.selectedFeatures,imagesCount:a.images.length}),!a.id)throw console.error("❌ Variant ID eksik:",a),Error(`Variant ${t+1} i\xe7in ID eksik`);e.append(`Variant[${t}].Id`,a.id.toString()),e.append(`Variant[${t}].Stock`,a.pricing.stock.toString()),e.append(`Variant[${t}].Price`,a.pricing.price.toString()),console.log(`✅ Variant[${t}] pricing:`,{Id:a.id.toString(),Stock:a.pricing.stock.toString(),Price:a.pricing.price.toString()});let r=[],s=a.featureDetails.filter(e=>e.featureValueId&&e.featureValueId>0).map(e=>e.featureValueId),i=Object.values(a.selectedFeatures).flat(),n=[...s,...i];r=[...new Set(n)],console.log(`🔍 Variant ${t} - featureDetails IDs:`,s),console.log(`🔍 Variant ${t} - selectedFeatures IDs:`,i),console.log(`✅ Variant ${t} - birleştirilmiş featureValueIds:`,r),console.log(`🔍 Variant ${t} final featureValueIds:`,r),console.log(`🔍 Variant ${t} featureDetails:`,a.featureDetails),console.log(`🔍 Variant ${t} selectedFeatures:`,a.selectedFeatures),r.forEach((a,r)=>{e.append(`Variant[${t}].FeatureValueIds[${r}]`,a.toString()),console.log(`✅ Variant[${t}].FeatureValueIds[${r}] = ${a}`)});let l=0;a.images.forEach(a=>{a.file?(e.append(`Variant[${t}].Images[${l}].File`,a.file),e.append(`Variant[${t}].Images[${l}].IsMain`,a.isMain.toString()),e.append(`Variant[${t}].Images[${l}].SortOrder`,l.toString()),console.log("\uD83D\uDCE4 Yeni fotoğraf g\xf6nderiliyor:",a.file.name,"Index:",l),l++):a.id&&a.url&&!a.url.startsWith("blob:")&&(e.append(`Variant[${t}].Images[${l}].Id`,a.id.toString()),e.append(`Variant[${t}].Images[${l}].IsMain`,a.isMain.toString()),e.append(`Variant[${t}].Images[${l}].SortOrder`,l.toString()),console.log("\uD83D\uDD04 Mevcut fotoğraf korunuyor:",a.url,"ID:",a.id,"Index:",l),l++)})}),console.log("\uD83D\uDCCB Final FormData contents:"),e.entries()))t instanceof File?console.log(`${a}: File(${t.name}, ${t.size} bytes)`):console.log(`${a}: ${t}`);console.log("\uD83D\uDE80 API \xe7ağrısı başlatılıyor..."),await es(e),console.log("✅ \xdcr\xfcn başarıyla g\xfcncellendi!"),F(!0),setTimeout(()=>{t.push("/pending-products"),T()},3e3)}catch(e){console.error("❌ \xdcr\xfcn g\xfcncelleme hatası:",e),console.error("❌ Error details:",{message:e.message,response:e.response?.data,status:e.response?.status}),K(e.message||"\xdcr\xfcn g\xfcncellenirken bir hata oluştu")}};return a?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):e&&("dealership"===e.role||"admin"===e.role)?R&&(!R||$.name)?M&&M.includes("bulunamadı")?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-8 text-center max-w-md mx-auto",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3},children:[(0,r.jsx)(o.A,{className:"h-16 w-16 text-red-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"\xdcr\xfcn Bulunamadı"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"D\xfczenlemek istediğiniz \xfcr\xfcn bulunamadı veya erişim yetkiniz bulunmuyor."}),(0,r.jsx)(y(),{href:"/pending-products",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:"\xdcr\xfcn Listesine D\xf6n"})]})}):w?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-8 text-center max-w-md mx-auto",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3},children:[(0,r.jsx)(d.A,{className:"h-16 w-16 text-green-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"\xdcr\xfcn Başarıyla G\xfcncellendi!"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"\xdcr\xfcn\xfcn\xfcz başarıyla g\xfcncellendi ve tekrar admin onayına g\xf6nderildi."}),(0,r.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-green-600 mx-auto"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Y\xf6nlendiriliyorsunuz..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:[(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(y(),{href:"/pending-products",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 mr-2"}),"\xdcr\xfcn Listesi"]}),(0,r.jsx)("span",{className:"text-gray-300",children:"/"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"\xdcr\xfcn D\xfczenle"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 bg-blue-100 px-4 py-2 rounded-lg",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-blue-600"}),(0,r.jsx)("span",{className:"text-blue-800 font-medium",children:"Satıcı Erişimi"})]})]})}),M&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsx)("p",{className:"text-red-800",children:M}),(0,r.jsx)("button",{onClick:()=>K(null),className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Kapat"})]}),(0,r.jsxs)("form",{onSubmit:el,className:"space-y-8",children:[(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(m.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Temel Bilgiler"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xdcr\xfcn Adı *"}),(0,r.jsx)("input",{type:"text",value:$.name,onChange:e=>_("name",e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black",placeholder:"\xdcr\xfcn adını girin...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"A\xe7ıklama *"}),(0,r.jsx)("textarea",{value:$.description,onChange:e=>_("description",e.target.value),rows:4,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black",placeholder:"\xdcr\xfcn a\xe7ıklamasını girin...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Marka, Kategori ve Detaylar"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{type:"button",onClick:async()=>{if($&&$.subCategoryId>0)try{A(!0);let e=z.map(e=>{let a=e.features||[];console.log("\uD83D\uDD0D Variant features:",a);let t=[];return a.forEach(e=>{console.log("\uD83D\uDD0D Processing feature:",e),void 0!==e.featureDefinitionId&&void 0!==e.featureValueId?(console.log("✅ Feature added to details"),t.push({featureDefinitionId:e.featureDefinitionId,featureValueId:e.featureValueId,featureName:e.featureName,featureValue:e.featureValue})):console.log("❌ Feature skipped - missing IDs:",{featureDefinitionId:e.featureDefinitionId,featureValueId:e.featureValueId})}),console.log("\uD83D\uDD0D Final featureDetails for variant:",t),{...e,featureDetails:t}}),a=er(e);console.log("\uD83C\uDFAF Derived selectedFeatures:",a),Y(e),W({initialData:{brandId:$.brandId,categoryId:$.categoryId,subCategoryId:$.subCategoryId,selectedFeatures:a}})}catch(e){console.error("\xd6zellik se\xe7imleri y\xfcklenirken hata:",e),W({initialData:{brandId:$.brandId,categoryId:$.categoryId,subCategoryId:$.subCategoryId,selectedFeatures:{}}})}finally{A(!1)}else W({initialData:{brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{}}})},disabled:P,className:"flex-grow px-4 py-3 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 transition-colors flex items-center justify-center text-gray-600 hover:text-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:P?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"}),"Y\xfckleniyor..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"h-5 w-5 mr-2"}),$.brandId>0?"Kategori Se\xe7imini D\xfczenle":"Marka, Kategori ve Detaylar Se\xe7in"]})}),$.brandId>0&&(0,r.jsx)("button",{type:"button",onClick:O,title:"T\xfcm se\xe7imleri temizle",className:"flex-shrink-0 p-3 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors",children:(0,r.jsx)(p.A,{className:"h-5 w-5"})})]}),$.brandId>0&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-gray-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"Se\xe7ili:"})," Marka: ",E.brandName,$.categoryId>0&&`, Kategori: ${E.categoryName}`,$.subCategoryId>0&&`, Alt Kategori: ${E.subCategoryName}`]})})]})]})]}),(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Varyant Y\xf6netimi"})]})}),$.subCategoryId<=0&&(0,r.jsx)("div",{className:"p-4 text-center bg-gray-50 rounded-lg",children:(0,r.jsx)("p",{className:"text-gray-500 font-medium",children:"Tekli \xfcr\xfcn eklemek veya varyant oluşturmak i\xe7in l\xfctfen \xf6nce marka, kategori ve alt kategori se\xe7in."})}),$.subCategoryId>0&&0===z.length&&(0,r.jsxs)("div",{className:"p-8 text-center bg-gray-50 rounded-lg",children:[(0,r.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-3"}),(0,r.jsx)("p",{className:"text-gray-500 font-medium",children:"Hen\xfcz varyant oluşturulmadı."}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1 mb-4",children:'Yukarıdaki "Marka, Kategori ve Detaylar Se\xe7in" kısmı ile d\xfczenleyebilirsiniz.'})]}),z.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 text-blue-600 mr-2 mt-0.5"}),(0,r.jsxs)("p",{className:"text-sm text-blue-800",children:["Oluşturulan her bir varyant kombinasyonu i\xe7in Fiyat ve Stok bilgilerini girin. Tek bir \xfcr\xfcn ekliyorsanız bile, bu \xfcr\xfcn bir varyant olarak kabul edilir.",(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Not:"})," PV, CV, SP ve indirim oranları admin tarafından belirlenecektir."]})]})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Varyant"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fiyat (₺)"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stok"}),(0,r.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,r.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:z.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.featureDetails.map(e=>e.featureValue).join(", ")})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.price})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.stock})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,r.jsx)("button",{type:"button",onClick:()=>en(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:"Detay"}),(0,r.jsx)("button",{type:"button",onClick:()=>G("number"==typeof e.id?e.id:0),className:"text-gray-500 hover:text-gray-800",children:"Sil"})]})]},e.id))})]})})]})]}),(0,r.jsxs)(l.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(f.A,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"\xdcr\xfcn Fotoğrafları"})]}),(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(m.A,{className:"h-5 w-5 text-blue-400"})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-blue-700",children:'Her bir varyantın fotoğraflarını, varyant tablosundaki "Detay" butonuna tıklayarak a\xe7ılan pencereden y\xf6netebilirsiniz.'})})]})}),z.some(e=>e.images.length>0)&&(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"T\xfcm Varyant G\xf6rselleri (\xd6nizleme)"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:z.flatMap(e=>e.images.map(a=>({...a,variantName:e.name}))).map((e,a)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg overflow-hidden",children:(0,r.jsx)("img",{src:e.url,alt:`${e.variantName} g\xf6rseli`,className:"w-full h-full object-cover"})}),(0,r.jsx)("div",{className:"absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded",children:e.variantName}),e.isMain&&(0,r.jsx)("div",{className:"absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded",children:"Ana"})]},a))})]})]}),(0,r.jsxs)(l.P.div,{className:"flex justify-end space-x-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,r.jsx)(y(),{href:"/pending-products",className:"px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"İptal"}),(0,r.jsx)("button",{type:"submit",disabled:ei,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:ei?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"G\xfcncelleniyor..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"\xdcr\xfcn\xfc G\xfcncelle"]})})]})]})]}),(0,r.jsx)(N.A,{isOpen:Z,onClose:X,onSelect:e=>{U({brandId:e.brandId,categoryId:e.categoryId,subCategoryId:e.subCategoryId,brandName:e.brandName,categoryName:e.categoryName,subCategoryName:e.subCategoryName,selectedFeatures:e.selectedFeatures,selectedFeatureDetails:e.selectedFeatureDetails}),Y(e.generatedVariants),X(),K(null)},initialData:ee?.initialData&&"object"==typeof ee.initialData&&"selectedFeatures"in ee.initialData?ee.initialData:{brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{}},colorScheme:"blue"}),(0,r.jsx)(j.A,{isOpen:ea,onClose:J,onSave:e=>{B(e,"number"==typeof e.id?e.id:void 0),J()},editingVariant:et?.editingVariant,availableFeatures:q,existingVariants:z,hidePvCvSp:!0,colorScheme:"blue"})]}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"\xdcr\xfcn bilgileri y\xfckleniyor..."})]})}):null}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43649:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},47602:(e,a,t)=>{"use strict";t.d(a,{K_:()=>o});var r=t(26787),s=t(59350);let i={formData:{name:"",description:"",brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{},price:0,stock:0,extraDiscount:0,ratios:{pvRatio:0,cvRatio:0,spRatio:0},points:{pv:0,cv:0,sp:0},hasVariants:!1,variants:[],images:[],isActive:!0},selectedNames:{brandName:"",categoryName:"",subCategoryName:""},selectedFeatureDetails:[],variants:[],availableFeatures:[],error:null,originalProductId:null},n=(e,a)=>({pv:Math.round(e*(a.pvRatio/100)),cv:Math.round(e*(a.cvRatio/100)),sp:Math.round(e*(a.spRatio/100))}),l=e=>{let a={},t=[];return e.forEach(e=>{e.featureDetails.forEach(e=>{a[e.featureDefinitionId]||(a[e.featureDefinitionId]=[]),a[e.featureDefinitionId].includes(e.featureValueId)||a[e.featureDefinitionId].push(e.featureValueId),t.some(a=>a.featureName===e.featureName&&a.featureValue===e.featureValue)||t.push({featureName:e.featureName,featureValue:e.featureValue})})}),{newSelectedFeatures:a,newSelectedFeatureDetails:t}},o=(0,r.v)()((0,s.lt)((e,a)=>({...i,setFormData:a=>e(e=>({formData:{...e.formData,...a}})),handleInputChange:(t,r)=>{let s={...a().formData,[t]:r};if(("price"===t||"ratios"===t)&&!s.hasVariants){let e=n(s.price,s.ratios);s={...s,points:e}}e({formData:s})},handleRatioChange:(t,r)=>{let s=a().formData,i={...s.ratios,[t]:r},l={...s,ratios:i};if(!l.hasVariants){let e=n(l.price,i);l={...l,points:e}}e({formData:l})},setCategorySelection:t=>{e({formData:{...a().formData,brandId:t.brandId,categoryId:t.categoryId,subCategoryId:t.subCategoryId,selectedFeatures:t.selectedFeatures,hasVariants:!1},selectedNames:{brandName:t.brandName,categoryName:t.categoryName,subCategoryName:t.subCategoryName},selectedFeatureDetails:t.selectedFeatureDetails,error:null})},clearAllSelections:()=>{e({formData:{...a().formData,brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{},hasVariants:!1},selectedNames:{brandName:"",categoryName:"",subCategoryName:""},selectedFeatureDetails:[],availableFeatures:[],variants:[],error:null})},setAvailableFeatures:a=>e({availableFeatures:a}),setVariants:a=>e({variants:a}),saveVariant:(t,r)=>{let s,{variants:i}=a(),{newSelectedFeatures:n,newSelectedFeatureDetails:o}=l(s=r?i.map(e=>e.id===r?t:e):[...i,{...t,id:Date.now()}]);e(e=>({variants:s,formData:{...e.formData,selectedFeatures:n,hasVariants:s.length>1},selectedFeatureDetails:o}))},deleteVariant:t=>{let{variants:r}=a(),s=r.filter(e=>e.id!==t),{newSelectedFeatures:i,newSelectedFeatureDetails:n}=l(s);e(e=>({variants:s,formData:{...e.formData,selectedFeatures:i,hasVariants:s.length>1},selectedFeatureDetails:n}))},generateVariants:a=>{let{newSelectedFeatures:t,newSelectedFeatureDetails:r}=l(a);e(e=>({formData:{...e.formData,hasVariants:!0,selectedFeatures:t},selectedFeatureDetails:r,variants:a,error:null}))},handleImageUpload:t=>{let{formData:r}=a(),s=Array.from(t).map((e,a)=>({url:URL.createObjectURL(e),isMain:0===r.images.length&&0===a,sortOrder:r.images.length+a,file:e}));e({formData:{...r,images:[...r.images,...s]}})},removeImage:t=>{let{formData:r}=a(),s=r.images.filter((e,a)=>a!==t);r.images[t].isMain&&s.length>0&&(s[0].isMain=!0),e({formData:{...r,images:s}})},setMainImage:t=>{let{formData:r}=a(),s=r.images.map((e,a)=>({...e,isMain:a===t}));e({formData:{...r,images:s}})},setError:a=>e({error:a}),reset:()=>e({...i}),initializeWithProduct:t=>{console.log("\uD83D\uDD04 Initializing product data:",t),console.log("\uD83D\uDDBC️ Variant images from API:",t.variants?.map(e=>({variantId:e.id,hasImages:!!e.images,imagesCount:e.images?.length||0,images:e.images})));let r=t.variants?.map((e,a)=>{let t=e.features?.map(e=>({featureDefinitionId:e.featureDefinitionId,featureValueId:e.featureValueId,featureName:e.featureName||"",featureValue:e.featureValue||""}))||[],r={};t.forEach(e=>{r[e.featureDefinitionId]||(r[e.featureDefinitionId]=[]),r[e.featureDefinitionId].includes(e.featureValueId)||r[e.featureDefinitionId].push(e.featureValueId)});let s=t.length>0?t.map(e=>e.featureValue).join(" - "):`Varyant ${a+1}`,i=e.price||0,l={pvRatio:e.pv||0,cvRatio:e.cv||0,spRatio:e.sp||0};return{id:e.id||Date.now()+a,name:s,pricing:{price:i,stock:e.stock||0,extraDiscount:e.extraDiscount||0,ratios:l,points:n(i,l)},selectedFeatures:r,features:t,featureDetails:t,images:e.images?.map(e=>({id:e.id,url:e.url,isMain:e.isMain,sortOrder:e.sortOrder,file:null}))||[],isActive:void 0===e.isActive||e.isActive}})||[],s=[],i={};r.forEach(e=>{e.featureDetails?.forEach(e=>{i[e.featureDefinitionId]||(i[e.featureDefinitionId]=[]),i[e.featureDefinitionId].includes(e.featureValueId)||i[e.featureDefinitionId].push(e.featureValueId),s.some(a=>a.featureName===e.featureName&&a.featureValue===e.featureValue)||s.push({featureName:e.featureName,featureValue:e.featureValue})})}),console.log("\uD83D\uDD04 Converted variants:",r),console.log("\uD83D\uDD04 All feature details:",s),console.log("\uD83D\uDD04 Global selected features:",i),e({formData:{name:t.name||"",description:t.description||"",brandId:t.brandId||1,categoryId:t.categoryId||1,subCategoryId:t.subCategoryId||1,selectedFeatures:i,price:r[0]?.pricing.price||0,stock:r[0]?.pricing.stock||0,extraDiscount:r[0]?.pricing.extraDiscount||0,ratios:r[0]?.pricing.ratios||{pvRatio:0,cvRatio:0,spRatio:0},points:r[0]?.pricing.points||{pv:0,cv:0,sp:0},hasVariants:r.length>0,variants:[],images:[],isActive:void 0===t.isActive||t.isActive},selectedNames:{brandName:t.brandName||"",categoryName:t.categoryName||"",subCategoryName:t.subCategoryName||""},selectedFeatureDetails:s,variants:r,error:null}),console.log("✅ Product initialized successfully"),console.log("\uD83D\uDCCA Form data:",a().formData),console.log("\uD83C\uDFF7️ Selected names:",a().selectedNames),console.log("\uD83C\uDFAF Variants:",a().variants),console.log("\uD83D\uDD0D Has variants:",a().formData.hasVariants),console.log("\uD83D\uDD0D Variants length:",a().variants.length)},setOriginalProductId:a=>e({originalProductId:a})}),{name:"edit-product-store",enabled:!1}))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63411:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(65239),s=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(a,o);let d={children:["",{children:["edit-product",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,70955)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\edit-product\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\edit-product\\[id]\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/edit-product/[id]/page",pathname:"/edit-product/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},70955:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\edit-product\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\edit-product\\[id]\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},74553:(e,a,t)=>{Promise.resolve().then(t.bind(t,28597))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[447,181,658,85,112,32],()=>t(63411));module.exports=r})();