(()=>{var e={};e.id=733,e.ids=[733],e.modules={2847:(e,t,s)=>{Promise.resolve().then(s.bind(s,52031))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14952:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23026:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35531:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>U});var r=s(60687),a=s(43210),i=s(15908),l=s(16189),n=s(8693),c=s(26001),d=s(51423),o=s(31212),x=s(64298);let m=()=>(0,d.I)({queryKey:["userRoleStatistics"],queryFn:async()=>{let e=await x.Dv.getUserRoleCounts();if(e.success)return e.data.data;throw Error(e.error||"Kullanıcı istatistikleri alınamadı")},staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0,refetchInterval:6e4}),u=(e,t,s=0,r=0)=>(0,d.I)({queryKey:["adminUsers",e,t,s,r],queryFn:async()=>{let a={page:e,pageSize:10,search:t,roleId:s};1===r?a.isActive=!0:2===r&&(a.isActive=!1);let i=await x.Dv.getUsers(a);if(i.success)return i.data.data?.items||[];throw Error(i.error||"Kullanıcılar alınamadı")},placeholderData:o.rX,staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0,refetchInterval:12e4}),h=()=>{let e=(0,n.jE)();return{refreshUserLists:()=>{console.log("\uD83D\uDD04 Kullanıcı cache'leri yenileniyor..."),e.invalidateQueries({queryKey:["userRoleStatistics"]}),e.invalidateQueries({queryKey:["adminUsers"]})}}},p=(0,s(26787).v)(e=>({searchTerm:"",roleFilter:0,statusFilter:0,setSearchTerm:t=>e({searchTerm:t}),setRoleFilter:t=>e({roleFilter:t}),setStatusFilter:t=>e({statusFilter:t}),resetFilters:()=>e({searchTerm:"",roleFilter:0,statusFilter:0})}));var y=s(99891);let g=(0,s(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);var f=s(58869),b=s(28559),j=s(41862),v=s(41312),N=s(99270),w=s(78122),k=s(23026),A=s(13861),S=s(63143),q=s(88233),C=s(47033),M=s(14952),P=s(85814),K=s.n(P);let U=()=>{let{user:e,isLoading:t}=(0,i.A)(),s=(0,l.useRouter)(),d=(0,n.jE)(),[o,x]=(0,a.useState)(1),[P,U]=(0,a.useState)(""),{searchTerm:R,roleFilter:T,statusFilter:E,setSearchTerm:I,setRoleFilter:_,setStatusFilter:D}=p();(0,a.useEffect)(()=>{t||e&&"admin"===e.role||s.push("/login")},[e,t,s]),(0,a.useEffect)(()=>{let e=setTimeout(()=>{U(R),x(1)},500);return()=>clearTimeout(e)},[R]),(0,a.useEffect)(()=>{e&&"admin"===e.role&&(d.invalidateQueries({queryKey:["userRoleStatistics"]}),d.invalidateQueries({queryKey:["adminUsers"]}),console.log("\uD83D\uDCCA Kullanıcı y\xf6netimi sayfası y\xfcklendi, istatistikler ve kullanıcı listesi yenileniyor..."))},[e,d]);let{data:F,isLoading:L}=m(),{data:z,isLoading:H,isFetching:Y}=u(o,P,T,E),{refreshUserLists:G}=h(),$=z||[],O=F?.totalUserCount||0,V=F?.adminCount||0,W=F?.dealershipCount||0,Q=F?.customerCount||0;if(t||L||H&&!Y)return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})});if(!e||"admin"!==e.role)return null;let X=e=>1===e.roleId?(0,r.jsx)(y.A,{className:"h-4 w-4 text-red-600"}):2===e.roleId?(0,r.jsx)(g,{className:"h-4 w-4 text-green-600"}):3===e.roleId?(0,r.jsx)(f.A,{className:"h-4 w-4 text-blue-600"}):(0,r.jsx)(f.A,{className:"h-4 w-4 text-gray-600"}),Z=e=>1===e.roleId?"bg-red-100 text-red-800":2===e.roleId?"bg-green-100 text-green-800":3===e.roleId?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800",B=e=>1===e.roleId?"Y\xf6netici":2===e.roleId?"Satıcı":3===e.roleId?"M\xfcşteri":"Rol Yok",J=e=>new Date(e).toLocaleDateString("tr-TR"),ee=e=>new Intl.NumberFormat("tr-TR",{style:"currency",currency:"TRY"}).format(e);return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(K(),{href:"/admin",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(b.A,{className:"h-5 w-5 mr-2"}),"Admin Paneli"]}),(0,r.jsx)("span",{className:"text-gray-300",children:"/"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Kullanıcı Y\xf6netimi"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 bg-red-100 px-4 py-2 rounded-lg",children:[(0,r.jsx)(y.A,{className:"h-5 w-5 text-red-600"}),(0,r.jsx)("span",{className:"text-red-800 font-medium",children:"Admin Erişimi"})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)(c.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Kullanıcı"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:L?(0,r.jsx)(j.A,{className:"h-6 w-6 animate-spin inline"}):O})]}),(0,r.jsx)(v.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,r.jsx)(c.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Y\xf6neticiler"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:L?(0,r.jsx)(j.A,{className:"h-6 w-6 animate-spin inline"}):V})]}),(0,r.jsx)(y.A,{className:"h-8 w-8 text-red-600"})]})}),(0,r.jsx)(c.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Satıcılar"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:L?(0,r.jsx)(j.A,{className:"h-6 w-6 animate-spin inline"}):W})]}),(0,r.jsx)(g,{className:"h-8 w-8 text-green-600"})]})}),(0,r.jsx)(c.P.div,{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"M\xfcşteriler"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:L?(0,r.jsx)(j.A,{className:"h-6 w-6 animate-spin inline"}):Q})]}),(0,r.jsx)(f.A,{className:"h-8 w-8 text-blue-600"})]})})]}),(0,r.jsx)(c.P.div,{className:"bg-white rounded-xl shadow-lg p-6 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(N.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"}),(0,r.jsx)("input",{type:"text",placeholder:"Kullanıcı ara...",value:R,onChange:e=>I(e.target.value),className:"pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder:text-gray-600 text-black"}),Y&&(0,r.jsx)(j.A,{className:"h-5 w-5 text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2 animate-spin"})]}),(0,r.jsxs)("select",{value:T,onChange:e=>_(Number(e.target.value)),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-600",children:[(0,r.jsx)("option",{value:0,children:"T\xfcm Roller"}),(0,r.jsx)("option",{value:1,children:"Y\xf6netici"}),(0,r.jsx)("option",{value:2,children:"Satıcı"}),(0,r.jsx)("option",{value:3,children:"M\xfcşteri"})]}),(0,r.jsxs)("select",{value:E,onChange:e=>D(Number(e.target.value)),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-600",children:[(0,r.jsx)("option",{value:0,children:"T\xfcm Durumlar"}),(0,r.jsx)("option",{value:1,children:"Aktif"}),(0,r.jsx)("option",{value:2,children:"Pasif"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>{console.log("\uD83D\uDD04 Manuel cache yenileme başlatıldı..."),G()},className:"flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",title:"Verileri yenile",children:[(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Yenile"]}),(0,r.jsxs)("button",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Yeni Kullanıcı"]})]})]})}),(0,r.jsxs)(c.P.div,{className:`bg-white rounded-xl shadow-lg overflow-hidden transition-opacity ${Y?"opacity-70":""}`,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Kullanıcılar (",$.length,")",H&&(0,r.jsx)(j.A,{className:"h-4 w-4 animate-spin inline ml-2"})]})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kullanıcı"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rol"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Katılım Tarihi"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Son Giriş"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Sipariş/Harcama"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Durum"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"İşlemler"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:$.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"h-10 w-10 flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[e.firstName.charAt(0),e.lastName.charAt(0)]})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:[e.firstName," ",e.lastName]}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${Z(e)}`,children:[X(e),(0,r.jsx)("span",{className:"ml-1",children:B(e)})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:J(e.registeredAt)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.lastLoginAt?new Date(e.lastLoginAt).toLocaleDateString("tr-TR"):"Hi\xe7 giriş yok"}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"text-sm font-medium",children:[e.orderCount," sipariş"]}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:ee(e.totalSpent)})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isActive?"Aktif":"Pasif"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-900",children:(0,r.jsx)(A.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{className:"text-green-600 hover:text-green-900",children:(0,r.jsx)(S.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{className:"text-red-600 hover:text-red-900",children:(0,r.jsx)(q.A,{className:"h-4 w-4"})})]})})]},e.id))})]})}),0===$.length&&!Y&&(0,r.jsxs)("div",{className:"px-6 py-12 text-center",children:[(0,r.jsx)(v.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Kullanıcı bulunamadı"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Arama kriterlerinizi değiştirerek tekrar deneyin."})]}),(0,r.jsxs)("div",{className:"px-6 py-4 border-t border-gray-200 flex items-center justify-between",children:[(0,r.jsxs)("button",{onClick:()=>x(e=>Math.max(e-1,1)),disabled:1===o,className:"flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"\xd6nceki"]}),(0,r.jsxs)("span",{className:"text-sm text-gray-700",children:["Sayfa ",(0,r.jsx)("span",{className:"font-bold",children:o})]}),(0,r.jsxs)("button",{onClick:()=>x(e=>10===$.length?e+1:e),disabled:$.length<10,className:"flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:["Sonraki",(0,r.jsx)(M.A,{className:"h-4 w-4 ml-2"})]})]})]})]})})}},39799:(e,t,s)=>{Promise.resolve().then(s.bind(s,35531))},41312:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},47033:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},52031:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\users\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var r=s(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:i="",children:l,iconNode:o,...x},m)=>(0,r.createElement)("svg",{ref:m,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:n("lucide",i),...!l&&!c(x)&&{"aria-hidden":"true"},...x},[...o.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(l)?l:[l]])),x=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...i},c)=>(0,r.createElement)(o,{ref:c,iconNode:t,className:n(`lucide-${a(l(e))}`,`lucide-${e}`,s),...i}));return s.displayName=l(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78122:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94735:e=>{"use strict";e.exports=require("events")},99270:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},99307:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),l=s.n(i),n=s(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let d={children:["",{children:["admin",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,52031)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\users\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\users\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/users/page",pathname:"/admin/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},99891:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,181,658,85],()=>s(99307));module.exports=r})();