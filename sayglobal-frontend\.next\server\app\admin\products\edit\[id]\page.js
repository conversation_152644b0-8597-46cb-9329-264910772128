(()=>{var e={};e.id=718,e.ids=[718],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15594:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\products\\edit\\[id]\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40237:(e,a,t)=>{"use strict";t.d(a,{Q6:()=>l,Z9:()=>n,e$:()=>d});var r=t(26787),s=t(59350);let i={isModalOpen:!1,selectedProductId:null,selectedVariantIndex:0,currentImageIndex:0,cachedProducts:new Map,lastFetchTime:new Map,isLoading:!1,error:null,prefetchQueue:new Set},n=(0,r.v)()((0,s.lt)((e,a)=>({...i,openModal:a=>{e({isModalOpen:!0,selectedProductId:a,selectedVariantIndex:0,currentImageIndex:0,error:null},!1,"productDetail/openModal")},closeModal:()=>{e({isModalOpen:!1,selectedProductId:null,selectedVariantIndex:0,currentImageIndex:0,error:null},!1,"productDetail/closeModal")},setSelectedVariant:a=>{e({selectedVariantIndex:a,currentImageIndex:0},!1,"productDetail/setSelectedVariant")},setCurrentImage:a=>{e({currentImageIndex:a},!1,"productDetail/setCurrentImage")},nextImage:()=>{let{selectedProductId:t,selectedVariantIndex:r,currentImageIndex:s,cachedProducts:i}=a();if(!t)return;let n=i.get(t);if(!n||!n.variants[r])return;let l=n.variants[r].images.length;l>0&&e({currentImageIndex:(s+1)%l},!1,"productDetail/nextImage")},prevImage:()=>{let{selectedProductId:t,selectedVariantIndex:r,currentImageIndex:s,cachedProducts:i}=a();if(!t)return;let n=i.get(t);if(!n||!n.variants[r])return;let l=n.variants[r].images.length;l>0&&e({currentImageIndex:(s-1+l)%l},!1,"productDetail/prevImage")},setCachedProduct:(t,r)=>{let{cachedProducts:s,lastFetchTime:i}=a(),n=new Map(s),l=new Map(i);n.set(t,r),l.set(t,Date.now()),e({cachedProducts:n,lastFetchTime:l},!1,"productDetail/setCachedProduct")},getCachedProduct:e=>{let{cachedProducts:t}=a();return t.get(e)||null},isCacheValid:(e,t=3e5)=>{let{lastFetchTime:r}=a(),s=r.get(e);return!!s&&Date.now()-s<t},clearCache:()=>{e({cachedProducts:new Map,lastFetchTime:new Map},!1,"productDetail/clearCache")},clearProductCache:t=>{let{cachedProducts:r,lastFetchTime:s}=a(),i=new Map(r),n=new Map(s);i.delete(t),n.delete(t),e({cachedProducts:i,lastFetchTime:n},!1,"productDetail/clearProductCache")},addToPrefetchQueue:t=>{let{prefetchQueue:r}=a(),s=new Set(r);s.add(t),e({prefetchQueue:s},!1,"productDetail/addToPrefetchQueue")},removeFromPrefetchQueue:t=>{let{prefetchQueue:r}=a(),s=new Set(r);s.delete(t),e({prefetchQueue:s},!1,"productDetail/removeFromPrefetchQueue")},clearPrefetchQueue:()=>{e({prefetchQueue:new Set},!1,"productDetail/clearPrefetchQueue")},setLoading:a=>{e({isLoading:a},!1,"productDetail/setLoading")},setError:a=>{e({error:a},!1,"productDetail/setError")},reset:()=>{e({...i,cachedProducts:new Map,lastFetchTime:new Map},!1,"productDetail/reset")}}),{name:"product-detail-store",enabled:!1})),l=()=>n(e=>e.selectedVariantIndex),d=()=>n(e=>e.currentImageIndex)},47602:(e,a,t)=>{"use strict";t.d(a,{K_:()=>d});var r=t(26787),s=t(59350);let i={formData:{name:"",description:"",brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{},price:0,stock:0,extraDiscount:0,ratios:{pvRatio:0,cvRatio:0,spRatio:0},points:{pv:0,cv:0,sp:0},hasVariants:!1,variants:[],images:[],isActive:!0},selectedNames:{brandName:"",categoryName:"",subCategoryName:""},selectedFeatureDetails:[],variants:[],availableFeatures:[],error:null,originalProductId:null},n=(e,a)=>({pv:Math.round(e*(a.pvRatio/100)),cv:Math.round(e*(a.cvRatio/100)),sp:Math.round(e*(a.spRatio/100))}),l=e=>{let a={},t=[];return e.forEach(e=>{e.featureDetails.forEach(e=>{a[e.featureDefinitionId]||(a[e.featureDefinitionId]=[]),a[e.featureDefinitionId].includes(e.featureValueId)||a[e.featureDefinitionId].push(e.featureValueId),t.some(a=>a.featureName===e.featureName&&a.featureValue===e.featureValue)||t.push({featureName:e.featureName,featureValue:e.featureValue})})}),{newSelectedFeatures:a,newSelectedFeatureDetails:t}},d=(0,r.v)()((0,s.lt)((e,a)=>({...i,setFormData:a=>e(e=>({formData:{...e.formData,...a}})),handleInputChange:(t,r)=>{let s={...a().formData,[t]:r};if(("price"===t||"ratios"===t)&&!s.hasVariants){let e=n(s.price,s.ratios);s={...s,points:e}}e({formData:s})},handleRatioChange:(t,r)=>{let s=a().formData,i={...s.ratios,[t]:r},l={...s,ratios:i};if(!l.hasVariants){let e=n(l.price,i);l={...l,points:e}}e({formData:l})},setCategorySelection:t=>{e({formData:{...a().formData,brandId:t.brandId,categoryId:t.categoryId,subCategoryId:t.subCategoryId,selectedFeatures:t.selectedFeatures,hasVariants:!1},selectedNames:{brandName:t.brandName,categoryName:t.categoryName,subCategoryName:t.subCategoryName},selectedFeatureDetails:t.selectedFeatureDetails,error:null})},clearAllSelections:()=>{e({formData:{...a().formData,brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{},hasVariants:!1},selectedNames:{brandName:"",categoryName:"",subCategoryName:""},selectedFeatureDetails:[],availableFeatures:[],variants:[],error:null})},setAvailableFeatures:a=>e({availableFeatures:a}),setVariants:a=>e({variants:a}),saveVariant:(t,r)=>{let s,{variants:i}=a(),{newSelectedFeatures:n,newSelectedFeatureDetails:d}=l(s=r?i.map(e=>e.id===r?t:e):[...i,{...t,id:Date.now()}]);e(e=>({variants:s,formData:{...e.formData,selectedFeatures:n,hasVariants:s.length>1},selectedFeatureDetails:d}))},deleteVariant:t=>{let{variants:r}=a(),s=r.filter(e=>e.id!==t),{newSelectedFeatures:i,newSelectedFeatureDetails:n}=l(s);e(e=>({variants:s,formData:{...e.formData,selectedFeatures:i,hasVariants:s.length>1},selectedFeatureDetails:n}))},generateVariants:a=>{let{newSelectedFeatures:t,newSelectedFeatureDetails:r}=l(a);e(e=>({formData:{...e.formData,hasVariants:!0,selectedFeatures:t},selectedFeatureDetails:r,variants:a,error:null}))},handleImageUpload:t=>{let{formData:r}=a(),s=Array.from(t).map((e,a)=>({url:URL.createObjectURL(e),isMain:0===r.images.length&&0===a,sortOrder:r.images.length+a,file:e}));e({formData:{...r,images:[...r.images,...s]}})},removeImage:t=>{let{formData:r}=a(),s=r.images.filter((e,a)=>a!==t);r.images[t].isMain&&s.length>0&&(s[0].isMain=!0),e({formData:{...r,images:s}})},setMainImage:t=>{let{formData:r}=a(),s=r.images.map((e,a)=>({...e,isMain:a===t}));e({formData:{...r,images:s}})},setError:a=>e({error:a}),reset:()=>e({...i}),initializeWithProduct:t=>{console.log("\uD83D\uDD04 Initializing product data:",t),console.log("\uD83D\uDDBC️ Variant images from API:",t.variants?.map(e=>({variantId:e.id,hasImages:!!e.images,imagesCount:e.images?.length||0,images:e.images})));let r=t.variants?.map((e,a)=>{let t=e.features?.map(e=>({featureDefinitionId:e.featureDefinitionId,featureValueId:e.featureValueId,featureName:e.featureName||"",featureValue:e.featureValue||""}))||[],r={};t.forEach(e=>{r[e.featureDefinitionId]||(r[e.featureDefinitionId]=[]),r[e.featureDefinitionId].includes(e.featureValueId)||r[e.featureDefinitionId].push(e.featureValueId)});let s=t.length>0?t.map(e=>e.featureValue).join(" - "):`Varyant ${a+1}`,i=e.price||0,l={pvRatio:e.pv||0,cvRatio:e.cv||0,spRatio:e.sp||0};return{id:e.id||Date.now()+a,name:s,pricing:{price:i,stock:e.stock||0,extraDiscount:e.extraDiscount||0,ratios:l,points:n(i,l)},selectedFeatures:r,features:t,featureDetails:t,images:e.images?.map(e=>({id:e.id,url:e.url,isMain:e.isMain,sortOrder:e.sortOrder,file:null}))||[],isActive:void 0===e.isActive||e.isActive}})||[],s=[],i={};r.forEach(e=>{e.featureDetails?.forEach(e=>{i[e.featureDefinitionId]||(i[e.featureDefinitionId]=[]),i[e.featureDefinitionId].includes(e.featureValueId)||i[e.featureDefinitionId].push(e.featureValueId),s.some(a=>a.featureName===e.featureName&&a.featureValue===e.featureValue)||s.push({featureName:e.featureName,featureValue:e.featureValue})})}),console.log("\uD83D\uDD04 Converted variants:",r),console.log("\uD83D\uDD04 All feature details:",s),console.log("\uD83D\uDD04 Global selected features:",i),e({formData:{name:t.name||"",description:t.description||"",brandId:t.brandId||1,categoryId:t.categoryId||1,subCategoryId:t.subCategoryId||1,selectedFeatures:i,price:r[0]?.pricing.price||0,stock:r[0]?.pricing.stock||0,extraDiscount:r[0]?.pricing.extraDiscount||0,ratios:r[0]?.pricing.ratios||{pvRatio:0,cvRatio:0,spRatio:0},points:r[0]?.pricing.points||{pv:0,cv:0,sp:0},hasVariants:r.length>0,variants:[],images:[],isActive:void 0===t.isActive||t.isActive},selectedNames:{brandName:t.brandName||"",categoryName:t.categoryName||"",subCategoryName:t.subCategoryName||""},selectedFeatureDetails:s,variants:r,error:null}),console.log("✅ Product initialized successfully"),console.log("\uD83D\uDCCA Form data:",a().formData),console.log("\uD83C\uDFF7️ Selected names:",a().selectedNames),console.log("\uD83C\uDFAF Variants:",a().variants),console.log("\uD83D\uDD0D Has variants:",a().formData.hasVariants),console.log("\uD83D\uDD0D Variants length:",a().variants.length)},setOriginalProductId:a=>e({originalProductId:a})}),{name:"edit-product-store",enabled:!1}))},49032:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>C});var r=t(60687),s=t(43210),i=t.n(s),n=t(15908),l=t(16189),d=t(26001),o=t(28559),c=t(99891),u=t(96882),m=t(37360),p=t(88233),g=t(45583),x=t(9005),f=t(8819),h=t(85814),y=t.n(h),b=t(64298),v=t(21852),I=t(10577),N=t(47602),j=t(40237),D=t(75068),w=t(8693),V=t(51423),k=t(54050);let C=({params:e})=>{let{user:a,isLoading:t}=(0,n.A)(),h=(0,l.useRouter)(),C=(0,l.useSearchParams)(),P=(0,w.jE)(),S=(0,j.Z9)(e=>e.clearProductCache),F="pending-products"===(C.get("from")||"products")?{url:"/admin/pending-products",text:"\xdcr\xfcn Onay Listesi"}:{url:"/admin/products",text:"\xdcr\xfcn Listesi"},[M,A]=i().useState(null),[E,$]=i().useState(!1),{formData:q,selectedNames:R,variants:z,error:O,availableFeatures:_,originalProductId:K}=(0,N.K_)(),{handleInputChange:Q,setCategorySelection:U,clearAllSelections:T,deleteVariant:G,removeImage:L,setMainImage:Y,setError:H,reset:W,setVariants:B,saveVariant:X,initializeWithProduct:Z,setOriginalProductId:J}=(0,N.K_)(e=>e),{openProductCategorySelector:ee,closeProductCategorySelector:ea,openProductVariant:et,closeProductVariant:er}=(0,D.QR)(),es=(0,D.fW)(),ei=(0,D._f)(),en=(0,D.HX)(),el=(0,D.qA)();(0,s.useEffect)(()=>{(async()=>{let a=await e;A(a.id),J(parseInt(a.id))})()},[e,J]);let{data:ed,isLoading:eo}=(0,V.I)({queryKey:["productDetail",M],queryFn:()=>b.jU.getProductDetail(parseInt(M)),enabled:!!M});(0,s.useEffect)(()=>{ed?.success&&ed?.data&&(console.log("\uD83D\uDD0D Raw product data from API:",ed.data),Z(ed.data))},[ed,Z]),(0,s.useEffect)(()=>{console.log("\uD83C\uDFAF Edit page variants:",z),console.log("\uD83C\uDFAF Edit page variants length:",z.length),console.log("\uD83C\uDFAF Edit page formData.subCategoryId:",q.subCategoryId),console.log("\uD83C\uDFAF Edit page formData.hasVariants:",q.hasVariants)},[z,q.subCategoryId,q.hasVariants]);let ec=e=>{let a={};return e.forEach(e=>{e.featureDetails.forEach(e=>{a[e.featureDefinitionId]||(a[e.featureDefinitionId]=[]),a[e.featureDefinitionId].includes(e.featureValueId)||a[e.featureDefinitionId].push(e.featureValueId)})}),a},{mutate:eu,isPending:em}=(0,k.n)({mutationFn:e=>b.jU.updateFullProduct(e),onSuccess:()=>{console.log("✅ \xdcr\xfcn başarıyla g\xfcncellendi. Cache temizleniyor..."),P.invalidateQueries({queryKey:["products"]}),P.invalidateQueries({queryKey:["adminProducts"]}),P.invalidateQueries({queryKey:["adminProductStatistics"]}),P.invalidateQueries({queryKey:["productDetail",M]}),M&&S(parseInt(M)),h.push(F.url),W()},onError:e=>{H(e.message||"\xdcr\xfcn g\xfcncellenirken bir hata oluştu")}});(0,s.useEffect)(()=>()=>{W()},[W]),(0,s.useEffect)(()=>{a&&"admin"!==a.role&&h.push("/"),t||a||h.push("/login")},[a,t,h]);let ep=e=>{et({editingVariant:e,availableFeatures:_,existingVariants:z})},eg=async a=>{a.preventDefault(),H(null);try{if(!q.name.trim())throw Error("\xdcr\xfcn adı gereklidir");if(!q.description.trim())throw Error("\xdcr\xfcn a\xe7ıklaması gereklidir");if(q.brandId<=0)throw Error("Marka ve kategori se\xe7imi gereklidir");if(0===z.length)throw Error("En az bir varyant oluşturmalısınız.");for(let e of z){if(e.pricing.price<=0)throw Error(`${e.name} varyantı i\xe7in fiyat 0'dan b\xfcy\xfck olmalıdır`);if(e.pricing.stock<0)throw Error(`${e.name} varyantı i\xe7in stok miktarı negatif olamaz`)}let a=new FormData,t=await e;for(let[e,r]of(a.append("Product.Id",t.id),a.append("Product.Name",q.name),a.append("Product.Description",q.description),a.append("Product.BrandId",q.brandId.toString()),a.append("Product.SubCategoryId",q.subCategoryId.toString()),z.forEach((e,t)=>{if(!e.id)throw console.error("❌ Variant ID eksik:",e),Error(`Variant ${t+1} i\xe7in ID eksik`);a.append(`Variant[${t}].Id`,e.id.toString()),a.append(`Variant[${t}].Stock`,e.pricing.stock.toString()),a.append(`Variant[${t}].Price`,e.pricing.price.toString()),a.append(`Variant[${t}].ExtraDiscount`,Math.round(e.pricing.extraDiscount||0).toString()),a.append(`Variant[${t}].Pv`,Math.round(e.pricing.ratios.pvRatio||0).toString()),a.append(`Variant[${t}].Cv`,Math.round(e.pricing.ratios.cvRatio||0).toString()),a.append(`Variant[${t}].Sp`,Math.round(e.pricing.ratios.spRatio||0).toString());let r=[],s=e.featureDetails.filter(e=>e.featureValueId&&e.featureValueId>0).map(e=>e.featureValueId);s.length>0?(r=s,console.log(`✅ Variant ${t} - featureDetails'den alınan featureValueIds:`,r)):(r=Object.values(e.selectedFeatures).flat(),console.log(`⚠️ Variant ${t} - selectedFeatures'dan alınan featureValueIds:`,r)),console.log(`🔍 Variant ${t} final featureValueIds:`,r),console.log(`🔍 Variant ${t} featureDetails:`,e.featureDetails),console.log(`🔍 Variant ${t} selectedFeatures:`,e.selectedFeatures),r.forEach((e,r)=>{a.append(`Variant[${t}].FeatureValueIds[${r}]`,e.toString())});let i=0;e.images.forEach(e=>{e.file?(a.append(`Variant[${t}].Images[${i}].File`,e.file),a.append(`Variant[${t}].Images[${i}].IsMain`,e.isMain.toString()),a.append(`Variant[${t}].Images[${i}].SortOrder`,i.toString()),console.log("\uD83D\uDCE4 Yeni fotoğraf g\xf6nderiliyor:",e.file.name,"Index:",i),i++):e.id&&e.url&&!e.url.startsWith("blob:")&&(a.append(`Variant[${t}].Images[${i}].Id`,e.id.toString()),a.append(`Variant[${t}].Images[${i}].IsMain`,e.isMain.toString()),a.append(`Variant[${t}].Images[${i}].SortOrder`,i.toString()),console.log("\uD83D\uDD04 Mevcut fotoğraf korunuyor:",e.url,"ID:",e.id,"Index:",i),i++)}),console.log(`✅ Variant ${t+1} (ID: ${e.id}) hazırlandı`)}),console.log("\uD83D\uDCCB FormData i\xe7eriği:"),a.entries()))console.log(`${e}:`,r);console.log("\uD83D\uDE80 \xdcr\xfcn g\xfcncelleme isteği g\xf6nderiliyor..."),eu(a)}catch(e){H(e.message||"\xdcr\xfcn g\xfcncellenirken bir hata oluştu")}};return t||eo?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):a&&"admin"===a.role?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:[(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(y(),{href:F.url,className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 mr-2"}),F.text]}),(0,r.jsx)("span",{className:"text-gray-300",children:"/"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"\xdcr\xfcn D\xfczenle"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 bg-red-100 px-4 py-2 rounded-lg",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-red-600"}),(0,r.jsx)("span",{className:"text-red-800 font-medium",children:"Admin Erişimi"})]})]})}),O&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsx)("p",{className:"text-red-800",children:O}),(0,r.jsx)("button",{onClick:()=>H(null),className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Kapat"})]}),(0,r.jsxs)("form",{onSubmit:eg,className:"space-y-8",children:[(0,r.jsxs)(d.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(u.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Temel Bilgiler"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xdcr\xfcn Adı *"}),(0,r.jsx)("input",{type:"text",value:q.name,onChange:e=>Q("name",e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-black",placeholder:"\xdcr\xfcn adını girin...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"A\xe7ıklama *"}),(0,r.jsx)("textarea",{value:q.description,onChange:e=>Q("description",e.target.value),rows:4,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-black",placeholder:"\xdcr\xfcn a\xe7ıklamasını girin...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Marka, Kategori ve Detaylar"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{type:"button",onClick:async()=>{if(q&&q.subCategoryId>0)try{$(!0);let e=z.map(e=>{let a=e.features||[],t=[];return a.forEach(e=>{void 0!==e.featureDefinitionId&&void 0!==e.featureValueId&&t.push({featureDefinitionId:e.featureDefinitionId,featureValueId:e.featureValueId,featureName:e.featureName,featureValue:e.featureValue})}),{...e,featureDetails:t}}),a=ec(e);B(e),ee({initialData:{brandId:q.brandId,categoryId:q.categoryId,subCategoryId:q.subCategoryId,selectedFeatures:a}})}catch(e){console.error("\xd6zellik se\xe7imleri y\xfcklenirken hata:",e),ee({initialData:{brandId:q.brandId,categoryId:q.categoryId,subCategoryId:q.subCategoryId,selectedFeatures:{}}})}finally{$(!1)}else ee({initialData:{brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{}}})},disabled:E,className:"flex-grow px-4 py-3 border-2 border-dashed border-gray-300 rounded-lg hover:border-red-500 transition-colors flex items-center justify-center text-gray-600 hover:text-red-600 disabled:opacity-50 disabled:cursor-not-allowed",children:E?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"}),"Y\xfckleniyor..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2"}),q.brandId>0?"Kategori Se\xe7imini D\xfczenle":"Marka, Kategori ve Detaylar Se\xe7in"]})}),q.brandId>0&&(0,r.jsx)("button",{type:"button",onClick:T,title:"T\xfcm se\xe7imleri temizle",className:"flex-shrink-0 p-3 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors",children:(0,r.jsx)(p.A,{className:"h-5 w-5"})})]}),q.brandId>0&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-gray-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"Se\xe7ili:"})," Marka: ",R.brandName,q.categoryId>0&&`, Kategori: ${R.categoryName}`,q.subCategoryId>0&&`, Alt Kategori: ${R.subCategoryName}`]})})]})]})]}),(0,r.jsxs)(d.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"h-6 w-6 text-red-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Varyant Y\xf6netimi"})]})}),q.subCategoryId<=0&&(0,r.jsx)("div",{className:"p-4 text-center bg-gray-50 rounded-lg",children:(0,r.jsx)("p",{className:"text-gray-500 font-medium",children:"Tekli \xfcr\xfcn eklemek veya varyant oluşturmak i\xe7in l\xfctfen \xf6nce marka, kategori ve alt kategori se\xe7in."})}),q.subCategoryId>0&&0===z.length&&(0,r.jsxs)("div",{className:"p-8 text-center bg-gray-50 rounded-lg",children:[(0,r.jsx)(g.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-3"}),(0,r.jsx)("p",{className:"text-gray-500 font-medium",children:"Hen\xfcz varyant oluşturulmadı."}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1 mb-4",children:'Yukarıdaki "Marka, Kategori ve Detaylar Se\xe7in" kısmı ile d\xfczenleyebilirsiniz.'})]}),z.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-blue-600 mr-2 mt-0.5"}),(0,r.jsx)("p",{className:"text-sm text-blue-800",children:"Oluşturulan her bir varyant kombinasyonu i\xe7in Fiyat, Stok ve Puan bilgilerini girin. Tek bir \xfcr\xfcn ekliyorsanız bile, bu \xfcr\xfcn bir varyant olarak kabul edilir."})]})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Varyant"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fiyat (₺)"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stok"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"PV Oranı (%)"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"CV Oranı (%)"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"SP Oranı (%)"}),(0,r.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,r.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:z.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.featureDetails.map(e=>e.featureValue).join(", ")})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.price})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.stock})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.ratios.pvRatio})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.ratios.cvRatio})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.pricing.ratios.spRatio})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,r.jsx)("button",{type:"button",onClick:()=>ep(e),className:"text-red-600 hover:text-red-900 mr-3",children:"Detay"}),(0,r.jsx)("button",{type:"button",onClick:()=>G("number"==typeof e.id?e.id:0),className:"text-gray-500 hover:text-gray-800",children:"Sil"})]})]},e.id))})]})})]})]}),(0,r.jsxs)(d.P.div,{className:"bg-white rounded-xl shadow-lg p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(x.A,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"\xdcr\xfcn Fotoğrafları"})]}),(0,r.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(u.A,{className:"h-5 w-5 text-blue-400"})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-blue-700",children:'Her bir varyantın fotoğraflarını, varyant tablosundaki "Detay" butonuna tıklayarak a\xe7ılan pencereden y\xf6netebilirsiniz.'})})]})}),z.some(e=>e.images.length>0)&&(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"T\xfcm Varyant G\xf6rselleri (\xd6nizleme)"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:z.flatMap(e=>e.images.map(a=>({...a,variantName:e.name}))).map((e,a)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg overflow-hidden",children:(0,r.jsx)("img",{src:e.url,alt:`${e.variantName} g\xf6rseli`,className:"w-full h-full object-cover"})}),(0,r.jsx)("div",{className:"absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded",children:e.variantName}),e.isMain&&(0,r.jsx)("div",{className:"absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded",children:"Ana"})]},a))})]})]}),(0,r.jsxs)(d.P.div,{className:"flex justify-end space-x-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,r.jsx)(y(),{href:"/admin/products",className:"px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"İptal"}),(0,r.jsx)("button",{type:"submit",disabled:em,className:"px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:em?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"G\xfcncelleniyor..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"\xdcr\xfcn\xfc G\xfcncelle"]})})]})]})]}),(0,r.jsx)(v.A,{isOpen:es,onClose:ea,onSelect:e=>{U({brandId:e.brandId,categoryId:e.categoryId,subCategoryId:e.subCategoryId,brandName:e.brandName,categoryName:e.categoryName,subCategoryName:e.subCategoryName,selectedFeatures:e.selectedFeatures,selectedFeatureDetails:e.selectedFeatureDetails}),B(e.generatedVariants),ea(),H(null)},initialData:ei?.initialData&&"object"==typeof ei.initialData&&"selectedFeatures"in ei.initialData?ei.initialData:{brandId:0,categoryId:0,subCategoryId:0,selectedFeatures:{}}}),(0,r.jsx)(I.A,{isOpen:en,onClose:er,onSave:e=>{X(e,"number"==typeof e.id?e.id:void 0),er()},editingVariant:el?.editingVariant,availableFeatures:_,existingVariants:z})]}):null}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74812:(e,a,t)=>{Promise.resolve().then(t.bind(t,49032))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88380:(e,a,t)=>{Promise.resolve().then(t.bind(t,15594))},94735:e=>{"use strict";e.exports=require("events")},96769:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=t(65239),s=t(48088),i=t(88170),n=t.n(i),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(a,d);let o={children:["",{children:["admin",{children:["products",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,15594)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\products\\edit\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\admin\\products\\edit\\[id]\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/products/edit/[id]/page",pathname:"/admin/products/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})}};var a=require("../../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[447,181,658,85,112],()=>t(96769));module.exports=r})();