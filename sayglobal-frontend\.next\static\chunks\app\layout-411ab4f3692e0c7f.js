(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{5323:(e,t,a)=>{"use strict";a.d(t,{_:()=>s,e:()=>o});var r=a(95155),i=a(12115);let n=(0,i.createContext)(void 0);function o(e){let{children:t}=e,[a,o]=(0,i.useState)([]);(0,i.useEffect)(()=>{{let e=localStorage.getItem("sayGlobalCart");if(e)try{let t=JSON.parse(e).map(e=>({...e,points:e.points||0}));o(t)}catch(e){console.error("Sepet verileri y\xfcklenirken hata:",e)}}},[]),(0,i.useEffect)(()=>{localStorage.setItem("sayGlobalCart",JSON.stringify(a))},[a]);let s=e=>{o(t=>t.filter(t=>t.id!==e))};return(0,r.jsx)(n.Provider,{value:{items:a,addToCart:e=>{o(t=>{let a=t.find(t=>t.id===e.id),r=e.quantity||1;return a?t.map(t=>t.id===e.id?{...t,quantity:t.quantity+r,points:e.points||t.points||0}:t):[...t,{id:e.id,title:e.title,price:e.price,thumbnail:e.thumbnail,brand:e.brand,quantity:r,discountPercentage:e.discountPercentage,points:e.points||0}]})},removeFromCart:s,updateQuantity:(e,t)=>{if(t<=0)return void s(e);o(a=>a.map(a=>a.id===e?{...a,quantity:t}:a))},clearCart:()=>{o([])},getTotalItems:()=>a.reduce((e,t)=>e+t.quantity,0),getTotalPrice:()=>a.reduce((e,t)=>e+(t.discountPercentage?t.price*(1-t.discountPercentage/100):t.price)*t.quantity,0),getTotalPoints:()=>a.reduce((e,t)=>e+(t.points||0)*t.quantity,0)},children:t})}function s(){let e=(0,i.useContext)(n);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},13841:(e,t,a)=>{"use strict";a.d(t,{$9:()=>c,AP:()=>u,IM:()=>m,PL:()=>d,Yu:()=>p,vG:()=>h,y$:()=>l});var r=a(32960),i=a(26715),n=a(5041),o=a(80722),s=a(87220);let l=()=>{let{isAuthenticated:e}=(0,s.A)();return(0,r.I)({queryKey:["cartItems",e],queryFn:async()=>{let e=await o.CV.getCartItems();if(e.success)return e.data.data;throw Error(e.error||"Sepet i\xe7erikleri alınamadı")},staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0})},c=()=>{let{isAuthenticated:e}=(0,s.A)();return(0,r.I)({queryKey:["cartCount",e],queryFn:async()=>{let e=await o.CV.getCartCount();if(e.success)return e.data.data;throw Error(e.error||"Sepet \xfcr\xfcn sayısı alınamadı")},staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0})},d=()=>(0,r.I)({queryKey:["discountRate"],queryFn:async()=>{try{let e=await o.Dv.getDiscountRate();if(console.log("\uD83D\uDD0D Discount Rate API Response:",e),e.success)return e.data||{discountRate:0};return console.warn("İndirim oranı alınamadı:",e.error),{discountRate:0}}catch(e){return console.warn("İndirim oranı alınırken hata:",e),{discountRate:0}}},staleTime:3e5,refetchOnWindowFocus:!1,refetchOnMount:!0,retry:!1}),u=()=>{let e=(0,i.jE)();return(0,n.n)({mutationFn:async e=>{let{productVariantId:t,quantity:a,isCustomerPrice:r}=e,i=await o.CV.addToCart(t,a,r);if(!i.success)throw Error(i.error||"\xdcr\xfcn sepete eklenemedi");return i.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepete \xfcr\xfcn ekleme hatası:",e)}})},m=()=>{let e=(0,i.jE)();return(0,n.n)({mutationFn:async e=>{let t=await o.CV.removeFromCart(e);if(!t.success)throw Error(t.error||"\xdcr\xfcn sepetten \xe7ıkarılamadı");return t.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepetten \xfcr\xfcn \xe7ıkarma hatası:",e)}})},p=()=>{let e=(0,i.jE)();return(0,n.n)({mutationFn:async e=>{let{productVariantId:t,quantity:a}=e,r=await o.CV.updateCartQuantity(t,a);if(!r.success)throw Error(r.error||"\xdcr\xfcn miktarı g\xfcncellenemedi");return r.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepet \xfcr\xfcn miktarı g\xfcncelleme hatası:",e)}})},h=()=>{let e=(0,i.jE)();return(0,n.n)({mutationFn:async()=>{let e=await o.Dv.updateCartType();if(!e.success)throw Error(e.error||"Sepet tipi g\xfcncellenemedi");return e.data},onSuccess:()=>{e.invalidateQueries({queryKey:["cartItems"]}),e.invalidateQueries({queryKey:["cartCount"]})},onError:e=>{console.error("Sepet tipi g\xfcncelleme hatası:",e)}})}},23533:(e,t,a)=>{Promise.resolve().then(a.bind(a,94750))},30347:()=>{},45106:(e,t,a)=>{"use strict";a.d(t,{$N:()=>z,EI:()=>M,F0:()=>d,Gc:()=>c,HX:()=>D,JZ:()=>f,OG:()=>N,Ph:()=>h,QK:()=>m,QR:()=>A,S:()=>u,VS:()=>l,Zm:()=>j,_f:()=>P,c6:()=>y,fW:()=>C,gA:()=>g,hg:()=>k,ig:()=>w,lA:()=>p,nb:()=>v,qA:()=>S,u6:()=>x,vQ:()=>b});var r=a(65453),i=a(46786);let n={editPersonalInfo:!1,referenceRegistration:!1,addAddress:!1,setDefaultConfirmation:!1,banking:!1,addCard:!1,setDefaultCard:!1,registerSuccess:!1,successNotification:!1,productCategorySelector:!1,productVariantSetup:!1,productVariant:!1,productDeleteConfirmation:!1},o={editPersonalInfoUser:null,referenceRegistrationData:null,addAddressData:null,setDefaultConfirmationData:null,bankingData:null,addCardData:null,setDefaultCardData:null,successNotificationData:null,productCategorySelectorData:null,productVariantSetupData:null,productVariantData:null,productDeleteConfirmationData:null},s=(0,r.v)()((0,i.lt)(e=>({...n,...o,openModal:(t,a)=>{e(e=>({...e,[t]:!0,...a&&{["".concat(t,"User")]:a}}),!1,"modal/open/".concat(t))},closeModal:t=>{e(e=>({...e,[t]:!1,["".concat(t,"User")]:null}),!1,"modal/close/".concat(t))},closeAllModals:()=>{e({...n,...o},!1,"modal/closeAll")},openEditPersonalInfoModal:t=>{e({editPersonalInfo:!0,editPersonalInfoUser:t},!1,"modal/openEditPersonalInfo")},closeEditPersonalInfoModal:()=>{e({editPersonalInfo:!1,editPersonalInfoUser:null},!1,"modal/closeEditPersonalInfo")},openReferenceRegistrationModal:t=>{e({referenceRegistration:!0,referenceRegistrationData:t},!1,"modal/openReferenceRegistration")},closeReferenceRegistrationModal:()=>{e({referenceRegistration:!1,referenceRegistrationData:null},!1,"modal/closeReferenceRegistration")},openAddAddressModal:t=>{e({addAddress:!0,addAddressData:t},!1,"modal/openAddAddress")},closeAddAddressModal:()=>{e({addAddress:!1,addAddressData:null},!1,"modal/closeAddAddress")},openSetDefaultConfirmationModal:t=>{e({setDefaultConfirmation:!0,setDefaultConfirmationData:t},!1,"modal/openSetDefaultConfirmation")},closeSetDefaultConfirmationModal:()=>{e({setDefaultConfirmation:!1,setDefaultConfirmationData:null},!1,"modal/closeSetDefaultConfirmation")},openBankingModal:t=>{e({banking:!0,bankingData:t},!1,"modal/openBanking")},closeBankingModal:()=>{e({banking:!1,bankingData:null},!1,"modal/closeBanking")},openAddCardModal:t=>{e({addCard:!0,addCardData:t},!1,"modal/openAddCard")},closeAddCardModal:()=>{e({addCard:!1,addCardData:null},!1,"modal/closeAddCard")},openSetDefaultCardModal:t=>{e({setDefaultCard:!0,setDefaultCardData:t},!1,"modal/openSetDefaultCard")},closeSetDefaultCardModal:()=>{e({setDefaultCard:!1,setDefaultCardData:null},!1,"modal/closeSetDefaultCard")},openRegisterSuccessModal:()=>{e({registerSuccess:!0},!1,"modal/openRegisterSuccess")},closeRegisterSuccessModal:()=>{e({registerSuccess:!1},!1,"modal/closeRegisterSuccess")},openSuccessNotificationModal:t=>{e({successNotification:!0,successNotificationData:t},!1,"modal/openSuccessNotification")},closeSuccessNotificationModal:()=>{e({successNotification:!1,successNotificationData:null},!1,"modal/closeSuccessNotification")},openProductCategorySelector:t=>{e({productCategorySelector:!0,productCategorySelectorData:t},!1,"modal/openProductCategorySelector")},closeProductCategorySelector:()=>{e({productCategorySelector:!1,productCategorySelectorData:null},!1,"modal/closeProductCategorySelector")},openProductVariantSetup:t=>{e({productVariantSetup:!0,productVariantSetupData:t},!1,"modal/openProductVariantSetup")},closeProductVariantSetup:()=>{e({productVariantSetup:!1,productVariantSetupData:null},!1,"modal/closeProductVariantSetup")},openProductVariant:t=>{e({productVariant:!0,productVariantData:t},!1,"modal/openProductVariant")},closeProductVariant:()=>{e({productVariant:!1,productVariantData:null},!1,"modal/closeProductVariant")},openProductDeleteConfirmation:t=>{e({productDeleteConfirmation:!0,productDeleteConfirmationData:t},!1,"modal/openProductDeleteConfirmation")},closeProductDeleteConfirmation:()=>{e({productDeleteConfirmation:!1,productDeleteConfirmationData:null},!1,"modal/closeProductDeleteConfirmation")}}),{name:"modal-store",enabled:!1})),l=()=>s(e=>e.editPersonalInfo),c=()=>s(e=>e.editPersonalInfoUser),d=()=>s(e=>e.referenceRegistration),u=()=>s(e=>e.referenceRegistrationData),m=()=>s(e=>e.addAddress),p=()=>s(e=>e.addAddressData),h=()=>s(e=>e.setDefaultConfirmation),x=()=>s(e=>e.setDefaultConfirmationData),f=()=>s(e=>e.banking),g=()=>s(e=>e.bankingData),y=()=>s(e=>e.addCard),k=()=>s(e=>e.addCardData),w=()=>s(e=>e.setDefaultCard),b=()=>s(e=>e.setDefaultCardData),v=()=>s(e=>e.registerSuccess),j=()=>s(e=>e.successNotification),N=()=>s(e=>e.successNotificationData),C=()=>s(e=>e.productCategorySelector),P=()=>s(e=>e.productCategorySelectorData),D=()=>s(e=>e.productVariant),S=()=>s(e=>e.productVariantData),M=()=>s(e=>e.productDeleteConfirmation),z=()=>s(e=>e.productDeleteConfirmationData),A=()=>{let e=s(e=>e.openModal),t=s(e=>e.closeModal),a=s(e=>e.closeAllModals),r=s(e=>e.openEditPersonalInfoModal),i=s(e=>e.closeEditPersonalInfoModal),n=s(e=>e.openReferenceRegistrationModal),o=s(e=>e.closeReferenceRegistrationModal),l=s(e=>e.openAddAddressModal),c=s(e=>e.closeAddAddressModal),d=s(e=>e.openSetDefaultConfirmationModal),u=s(e=>e.closeSetDefaultConfirmationModal),m=s(e=>e.openBankingModal),p=s(e=>e.closeBankingModal),h=s(e=>e.openAddCardModal),x=s(e=>e.closeAddCardModal),f=s(e=>e.openSetDefaultCardModal),g=s(e=>e.closeSetDefaultCardModal),y=s(e=>e.openRegisterSuccessModal),k=s(e=>e.closeRegisterSuccessModal),w=s(e=>e.openSuccessNotificationModal),b=s(e=>e.closeSuccessNotificationModal),v=s(e=>e.openProductCategorySelector),j=s(e=>e.closeProductCategorySelector),N=s(e=>e.openProductVariantSetup),C=s(e=>e.closeProductVariantSetup),P=s(e=>e.openProductVariant),D=s(e=>e.closeProductVariant);return{openModal:e,closeModal:t,closeAllModals:a,openEditPersonalInfoModal:r,closeEditPersonalInfoModal:i,openReferenceRegistrationModal:n,closeReferenceRegistrationModal:o,openAddAddressModal:l,closeAddAddressModal:c,openSetDefaultConfirmationModal:d,closeSetDefaultConfirmationModal:u,openBankingModal:m,closeBankingModal:p,openAddCardModal:h,closeAddCardModal:x,openSetDefaultCardModal:f,closeSetDefaultCardModal:g,openRegisterSuccessModal:y,closeRegisterSuccessModal:k,openSuccessNotificationModal:w,closeSuccessNotificationModal:b,openProductCategorySelector:v,closeProductCategorySelector:j,openProductVariantSetup:N,closeProductVariantSetup:C,openProductVariant:P,closeProductVariant:D,openProductDeleteConfirmation:s(e=>e.openProductDeleteConfirmation),closeProductDeleteConfirmation:s(e=>e.closeProductDeleteConfirmation)}}},68318:(e,t,a)=>{"use strict";a.d(t,{I1:()=>i,ZE:()=>r});let r=[{id:1,title:"Vitamin C Serum",description:"Cilt lekelerine karşı etkili, antioksidan \xf6zellikli vitamin C serumu. Cildinizi dış etkenlere karşı korur ve ışıltı verir.",price:349.9,discountPercentage:10,rating:4.7,stock:120,brand:"Say Beauty",category:"Cilt Bakımı",thumbnail:"https://picsum.photos/id/1/200/200",images:["https://picsum.photos/id/1/800/800","https://picsum.photos/id/2/800/800","https://picsum.photos/id/3/800/800"],points:30},{id:2,title:"Kolajen Takviyesi",description:"Eklem ve cilt sağlığı i\xe7in g\xfcnl\xfck kolajen takviyesi. İ\xe7eriğindeki Tip I ve Tip II kolajen ile cildinizin elastikiyetini destekler.",price:299.9,discountPercentage:void 0,rating:4.5,stock:85,brand:"Say Health",category:"Takviye \xdcr\xfcnler",thumbnail:"https://picsum.photos/id/4/200/200",images:["https://picsum.photos/id/4/800/800","https://picsum.photos/id/5/800/800","https://picsum.photos/id/6/800/800"],points:30},{id:3,title:"Argan Yağlı Şampuan",description:"Kuru ve yıpranmış sa\xe7lar i\xe7in onarıcı argan yağı i\xe7eren şampuan. Sa\xe7larınızı besler ve kolay taranmasını sağlar.",price:129.9,discountPercentage:15,rating:4.3,stock:200,brand:"Say Beauty",category:"Sa\xe7 Bakımı",thumbnail:"https://picsum.photos/id/7/200/200",images:["https://picsum.photos/id/7/800/800","https://picsum.photos/id/8/800/800","https://picsum.photos/id/9/800/800"],points:10},{id:4,title:"Probiyotik Kompleks",description:"Bağırsak florası ve sindirim sistemi sağlığı i\xe7in g\xfcnl\xfck probiyotik takviyesi. 10 farklı probiyotik suşu i\xe7erir.",price:199.9,discountPercentage:5,rating:4.8,stock:150,brand:"Say Health",category:"Takviye \xdcr\xfcnler",thumbnail:"https://picsum.photos/id/10/200/200",images:["https://picsum.photos/id/10/800/800","https://picsum.photos/id/11/800/800","https://picsum.photos/id/12/800/800"],points:20},{id:5,title:"Retinol Gece Kremi",description:"Yaşlanma karşıtı, kırışıklık giderici ve cilt yenileyici retinol i\xe7erikli gece kremi. D\xfczenli kullanımda ince \xe7izgilerin g\xf6r\xfcn\xfcm\xfcn\xfc azaltır.",price:399.9,discountPercentage:void 0,rating:4.6,stock:70,brand:"Say Beauty",category:"Cilt Bakımı",thumbnail:"https://picsum.photos/id/13/200/200",images:["https://picsum.photos/id/13/800/800","https://picsum.photos/id/14/800/800","https://picsum.photos/id/15/800/800"],points:40},{id:6,title:"Multivitamin",description:"G\xfcnl\xfck vitamin ve mineral ihtiyacınızı karşılayan multivitamin takviyesi. Adan Zye t\xfcm vitaminleri i\xe7erir.",price:179.9,discountPercentage:10,rating:4.4,stock:250,brand:"Say Health",category:"Takviye \xdcr\xfcnler",thumbnail:"https://picsum.photos/id/16/200/200",images:["https://picsum.photos/id/16/800/800","https://picsum.photos/id/17/800/800","https://picsum.photos/id/18/800/800"],points:15},{id:7,title:"Hyaluronik Asit Serumu",description:"Yoğun nemlendirici hyaluronik asit serumu. Cildin nem bariyerini g\xfc\xe7lendirir ve dolgunluk sağlar.",price:279.9,discountPercentage:void 0,rating:4.9,stock:100,brand:"Say Beauty",category:"Cilt Bakımı",thumbnail:"https://picsum.photos/id/19/200/200",images:["https://picsum.photos/id/19/800/800","https://picsum.photos/id/20/800/800","https://picsum.photos/id/21/800/800"],points:25},{id:8,title:"Yeşil \xc7ay \xd6zl\xfc Tonik",description:"G\xf6zenek sıkılaştırıcı ve yağ dengeleyici yeşil \xe7ay \xf6zl\xfc tonik. Cildi temizler ve ferahlatır.",price:149.9,discountPercentage:5,rating:4.2,stock:180,brand:"Say Beauty",category:"Cilt Bakımı",thumbnail:"https://picsum.photos/id/22/200/200",images:["https://picsum.photos/id/22/800/800","https://picsum.photos/id/23/800/800","https://picsum.photos/id/24/800/800"],points:15}],i=[{id:1,productId:1,product:r[0],addedAt:"2024-01-15T10:30:00Z"},{id:2,productId:3,product:r[2],addedAt:"2024-01-14T15:20:00Z"},{id:3,productId:5,product:r[4],addedAt:"2024-01-13T09:45:00Z"}]},69848:(e,t,a)=>{"use strict";a.d(t,{H:()=>s,r:()=>l});var r=a(95155),i=a(12115),n=a(68318);let o=(0,i.createContext)(void 0);function s(e){let{children:t}=e,[a,s]=(0,i.useState)([]),[l,c]=(0,i.useState)(!1);(0,i.useEffect)(()=>{if(!l){let e=localStorage.getItem("sayGlobalFavorites");if(e)try{let t=JSON.parse(e);s(t)}catch(e){console.error("Favori verileri y\xfcklenirken hata:",e),s(n.I1)}else s(n.I1);c(!0)}},[l]),(0,i.useEffect)(()=>{l&&localStorage.setItem("sayGlobalFavorites",JSON.stringify(a))},[a,l]);let d=(0,i.useCallback)(e=>{s(t=>t.find(t=>t.productId===e.id)?t:[...t,{id:Date.now(),productId:e.id,product:e,addedAt:new Date().toISOString()}])},[]),u=(0,i.useCallback)(e=>{s(t=>t.filter(t=>t.productId!==e))},[]),m=(0,i.useCallback)(e=>a.some(t=>t.productId===e),[a]),p=(0,i.useCallback)(()=>a.length,[a]),h=(0,i.useMemo)(()=>({favorites:a,addToFavorites:d,removeFromFavorites:u,isFavorite:m,getFavoritesCount:p}),[a,d,u,m,p]);return(0,r.jsx)(o.Provider,{value:h,children:t})}function l(){let e=(0,i.useContext)(o);if(void 0===e)throw Error("useFavorites must be used within a FavoritesProvider");return e}},94750:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>M});var r=a(95155),i=a(65356),n=a.n(i),o=a(87017),s=a(26715),l=a(12115);a(30347);var c=a(6874),d=a.n(c),u=a(66766),m=a(76408),p=a(87220),h=a(13841);function x(){let[e,t]=(0,l.useState)(!1),[a,i]=(0,l.useState)(!1),[n,o]=(0,l.useState)(!1),{user:s,isLoading:c,isAuthenticated:x,logout:f}=(0,p.A)(),{data:g=0,isLoading:y}=(0,h.$9)(),k=(0,l.useRef)(null),w=(0,l.useRef)(null);return(0,l.useEffect)(()=>{function t(t){window.innerWidth>=768&&!e&&(k.current&&!k.current.contains(t.target)&&i(!1),w.current&&!w.current.contains(t.target)&&o(!1))}if((a||n)&&!e)return document.addEventListener("mousedown",t),()=>{document.removeEventListener("mousedown",t)}},[a,n,e]),(0,r.jsx)(m.P.header,{className:"bg-white shadow-md sticky top-0 z-50 ",initial:{y:-100},animate:{y:0},transition:{type:"spring",stiffness:100,damping:20},children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-20",children:[(0,r.jsx)(d(),{href:"/",className:"flex items-center",children:(0,r.jsxs)(m.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:17},className:"flex items-center",children:[(0,r.jsx)(u.default,{src:"/assets/sayglobal_logo.png",alt:"Say Global Logo",width:180,height:72,className:"h-16 w-auto object-contain",priority:!0}),(0,r.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 text-transparent bg-clip-text hidden sm:block",children:"Say Global"})]})}),(0,r.jsx)(m.P.button,{type:"button",className:"md:hidden text-gray-600 hover:text-gray-900 focus:outline-none",onClick:()=>t(!e),whileTap:{scale:.9},children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),(0,r.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[[{text:"Anasayfa",route:"/"},{text:"\xdcr\xfcnler",route:"/products"},{text:"Duyurular",route:"/announcements"}].map((e,t)=>(0,r.jsx)(m.P.div,{whileHover:{y:-2},whileTap:{y:0},children:(0,r.jsxs)(d(),{href:e.route,className:"text-gray-700 hover:text-purple-600 font-medium relative overflow-hidden group",children:[e.text,(0,r.jsx)("span",{className:"absolute bottom-0 left-0 w-full h-0.5 bg-purple-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out"})]})},t)),(0,r.jsxs)("div",{className:"relative",ref:k,children:[(0,r.jsx)(m.P.button,{className:"text-gray-700 hover:text-purple-600 font-medium focus:outline-none p-2 rounded-full hover:bg-gray-100",onClick:()=>i(!a),whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),a&&(0,r.jsx)(m.P.div,{className:"absolute right-0 mt-2 w-72 bg-white rounded-lg shadow-xl p-3 border border-gray-100",initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{duration:.2},children:(0,r.jsxs)("div",{className:"flex items-center bg-gray-50 rounded-md",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 ml-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),(0,r.jsx)("input",{type:"text",placeholder:"\xdcr\xfcn ara...",className:"w-full px-3 py-2 text-black bg-transparent border-none focus:outline-none focus:ring-0"})]})})]})]}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,r.jsx)(m.P.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsxs)(d(),{href:"/cart",className:"text-gray-700 hover:text-purple-600 relative p-2",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})}),(0,r.jsx)("span",{className:"absolute top-0 right-0 md:top-4 md:left-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:y?"...":g})]})}),(0,r.jsxs)("div",{className:"relative",ref:w,children:[(0,r.jsxs)(m.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>o(!n),className:"text-gray-700 hover:text-purple-600 p-2 flex items-center space-x-2",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),c?(0,r.jsx)("div",{className:"hidden md:block w-20 h-4 bg-gray-200 rounded animate-pulse"}):x&&s?(0,r.jsxs)("span",{className:"hidden md:block text-sm font-medium",children:[s.firstName," ",s.lastName]}):null]}),n&&(0,r.jsx)(m.P.div,{className:"absolute right-0 mt-2 py-2 w-56 bg-white rounded-lg shadow-xl z-20 border border-gray-100",initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{duration:.2},children:x&&s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"px-4 py-2 border-b border-gray-100",children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[s.firstName," ",s.lastName]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:s.email}),(0,r.jsx)("span",{className:"inline-block mt-1 px-2 py-1 rounded-full text-xs font-medium ".concat("admin"===s.role?"bg-red-100 text-red-800":"dealership"===s.role?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"),children:"admin"===s.role?"Y\xf6netici":"dealership"===s.role?"Satıcı":"M\xfcşteri"})]}),(0,r.jsx)(d(),{href:"/account",className:"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600",onClick:()=>o(!1),children:"Hesabım"}),(0,r.jsx)(d(),{href:"/account?tab=orders",className:"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600",onClick:()=>o(!1),children:"Siparişlerim"}),(0,r.jsx)(d(),{href:"/account?tab=favorites",className:"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600",onClick:()=>o(!1),children:"Favorilerim"}),("customer"===s.role||"dealership"===s.role||"admin"===s.role)&&(0,r.jsx)(d(),{href:"/panel",className:"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600",onClick:()=>o(!1),children:"Kontrol Paneli"}),"admin"===s.role&&(0,r.jsx)(d(),{href:"/admin",className:"block px-4 py-2 text-gray-700 hover:bg-red-50 hover:text-red-600",onClick:()=>o(!1),children:"Y\xf6netici Paneli"}),(0,r.jsx)("div",{className:"border-t border-gray-100 my-2"}),(0,r.jsx)("button",{onClick:()=>{f(),t(!1),o(!1)},className:"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-600 rounded-md",children:"\xc7ıkış Yap"})]}):(0,r.jsxs)("div",{className:"px-2 py-1 space-y-2",children:[(0,r.jsx)(d(),{href:"/login",className:"block w-full text-center rounded-md bg-gradient-to-r from-purple-600 to-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-all duration-300 hover:from-purple-700 hover:to-indigo-700",onClick:()=>o(!1),children:"Giriş Yap"}),(0,r.jsx)(d(),{href:"/register",className:"block w-full text-center rounded-md bg-white px-4 py-2 text-sm font-medium text-gray-800 border border-gray-300 transition-all duration-300 hover:bg-gray-100",onClick:()=>o(!1),children:"Kayıt Ol"})]})})]}),x&&s&&"customer"===s.role&&!s.isDealershipApproved&&(0,r.jsx)(m.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:17},children:(0,r.jsxs)(d(),{href:"/become-dealer",className:"bg-gradient-to-r from-green-600 to-emerald-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow duration-300 flex items-center space-x-1",children:[(0,r.jsx)("span",{children:"Satıcı Ol"}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})]})}),x&&s&&"admin"===s.role&&(0,r.jsx)(m.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:17},children:(0,r.jsxs)(d(),{href:"/admin",className:"bg-gradient-to-r from-red-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow duration-300 flex items-center space-x-1",children:[(0,r.jsx)("span",{children:"Y\xf6netici Paneli"}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]})]}),e&&(0,r.jsx)(m.P.div,{className:"md:hidden pb-4",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,r.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,r.jsx)(d(),{href:"/",className:"text-gray-700 hover:text-purple-600 font-medium py-2",onClick:()=>t(!1),children:"Anasayfa"}),(0,r.jsx)(d(),{href:"/products",className:"text-gray-700 hover:text-purple-600 font-medium py-2",onClick:()=>t(!1),children:"\xdcr\xfcnler"}),(0,r.jsx)(d(),{href:"/announcements",className:"text-gray-700 hover:text-purple-600 font-medium py-2",onClick:()=>t(!1),children:"Duyurular"}),(0,r.jsx)("div",{className:"py-2",children:(0,r.jsxs)("div",{className:"flex items-center bg-gray-50 rounded-md",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 ml-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),(0,r.jsx)("input",{type:"text",placeholder:"\xdcr\xfcn ara...",className:"w-full px-3 py-2 bg-transparent border-none focus:outline-none focus:ring-0 text-black"})]})}),(0,r.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,r.jsxs)(d(),{href:"/cart",className:"text-gray-700 hover:text-purple-600 relative p-3 bg-gray-50 rounded-lg flex items-center justify-center",onClick:()=>t(!1),children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-7 w-7",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})}),(0,r.jsx)("span",{className:"absolute -top-1 -right-1 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-medium shadow-lg",children:y?"...":g})]}),(0,r.jsxs)("button",{onClick:()=>o(!n),className:"text-gray-700 hover:text-purple-600 flex items-center space-x-3 p-2 bg-gray-50 rounded-lg flex-1 ml-4",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),c?(0,r.jsx)("div",{className:"w-20 h-4 bg-gray-200 rounded animate-pulse"}):x&&s?(0,r.jsxs)("span",{className:"text-sm font-medium truncate",children:[s.firstName," ",s.lastName]}):(0,r.jsx)("span",{className:"text-sm font-medium",children:"Giriş Yap"})]})]}),n&&(0,r.jsx)(m.P.div,{className:"bg-gray-50 rounded-lg p-4 mt-3",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:x&&s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-3 mb-3",children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[s.firstName," ",s.lastName]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:s.email}),(0,r.jsx)("span",{className:"inline-block mt-1 px-2 py-1 rounded-full text-xs font-medium ".concat("admin"===s.role?"bg-red-100 text-red-800":"dealership"===s.role?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"),children:"admin"===s.role?"Y\xf6netici":"dealership"===s.role?"Satıcı":"M\xfcşteri"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d(),{href:"/account",className:"block py-2 text-gray-700 hover:text-purple-600 font-medium",onClick:()=>{t(!1),o(!1)},children:"Hesabım"}),(0,r.jsx)(d(),{href:"/account?tab=orders",className:"block py-2 text-gray-700 hover:text-purple-600 font-medium",onClick:()=>{t(!1),o(!1)},children:"Siparişlerim"}),(0,r.jsx)(d(),{href:"/account?tab=favorites",className:"block py-2 text-gray-700 hover:text-purple-600 font-medium",onClick:()=>{t(!1),o(!1)},children:"Favorilerim"}),("customer"===s.role||"dealership"===s.role||"admin"===s.role)&&(0,r.jsx)(d(),{href:"/panel",className:"block py-2 text-gray-700 hover:text-purple-600 font-medium",onClick:()=>{t(!1),o(!1)},children:"Kontrol Paneli"}),"admin"===s.role&&(0,r.jsx)(d(),{href:"/admin",className:"block py-2 text-gray-700 hover:text-red-600 font-medium",onClick:()=>{t(!1),o(!1)},children:"Y\xf6netici Paneli"}),(0,r.jsx)("button",{onClick:()=>{f(),t(!1),o(!1)},className:"w-full text-left py-2 text-red-600 hover:text-red-700 font-medium",children:"\xc7ıkış Yap"})]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d(),{href:"/login",className:"block w-full rounded-lg bg-gradient-to-r from-purple-600 to-indigo-600 px-4 py-3 text-center text-base font-semibold text-white shadow-md transition-all duration-300 hover:from-purple-700 hover:to-indigo-700",onClick:()=>{t(!1),o(!1)},children:"Giriş Yap"}),(0,r.jsx)(d(),{href:"/register",className:"block w-full rounded-lg bg-white px-4 py-3 text-center text-base font-semibold text-gray-800 border border-gray-300 transition-all duration-300 hover:bg-gray-100",onClick:()=>{t(!1),o(!1)},children:"Kayıt Ol"})]})}),x&&s&&"customer"===s.role&&!s.isDealershipApproved&&(0,r.jsx)(d(),{href:"/become-dealer",className:"bg-gradient-to-r from-green-600 to-emerald-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow duration-300 text-center",onClick:()=>t(!1),children:"Satıcı Ol"}),x&&s&&"admin"===s.role&&(0,r.jsx)(d(),{href:"/admin",className:"bg-gradient-to-r from-red-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow duration-300 text-center",onClick:()=>t(!1),children:"Y\xf6netici Paneli"})]})})]})})}function f(){let e={initial:{y:20,opacity:0},animate:{y:0,opacity:1,transition:{duration:.6}}};return(0,r.jsx)("footer",{className:"bg-gradient-to-br from-gray-900 to-gray-800 text-white pt-16 pb-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)(m.P.div,{className:"grid grid-cols-1 md:grid-cols-4 gap-10",variants:{animate:{transition:{staggerChildren:.1}}},initial:"initial",whileInView:"animate",viewport:{once:!0,amount:.3},children:[(0,r.jsxs)(m.P.div,{variants:e,className:"col-span-1",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-indigo-400 text-transparent bg-clip-text",children:"Say Global"}),(0,r.jsx)("p",{className:"text-gray-300 mb-6 text-sm leading-relaxed",children:"Sağlıklı yaşam \xfcr\xfcnleri ve kozmetik alanında T\xfcrkiye'nin lider markası. Doğal i\xe7erikli \xfcr\xfcnlerle sağlığınıza sağlık katın."}),(0,r.jsx)("div",{className:"flex space-x-4",children:[{name:"Facebook",path:"M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z"},{name:"Instagram",path:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"},{name:"Twitter",path:"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"},{name:"YouTube",path:"M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"}].map((e,t)=>(0,r.jsx)(m.P.a,{href:"#",className:"text-gray-400 hover:text-white transition-colors duration-300 transform hover:scale-110",whileHover:{scale:1.2,rotate:5},whileTap:{scale:.9},children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:e.path})})},t))})]}),(0,r.jsxs)(m.P.div,{variants:e,className:"col-span-1",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-6 relative inline-block",children:["Hızlı Linkler",(0,r.jsx)("span",{className:"absolute bottom-0 left-0 w-1/2 h-0.5 bg-gradient-to-r from-purple-400 to-indigo-400"})]}),(0,r.jsx)("ul",{className:"space-y-3",children:[{name:"Anasayfa",path:"/"},{name:"\xdcr\xfcnler",path:"/products"},{name:"Duyurular",path:"/announcements"},{name:"Distrib\xfct\xf6r Paneli",path:"/dashboard"},{name:"Giriş Yap",path:"/login"},{name:"Kayıt Ol",path:"/register"}].map((e,t)=>(0,r.jsx)(m.P.li,{whileHover:{x:5},transition:{type:"spring",stiffness:400,damping:10},children:(0,r.jsxs)(d(),{href:e.path,className:"text-gray-300 hover:text-white group flex items-center",children:[(0,r.jsx)("svg",{className:"h-3 w-3 mr-2 text-purple-400 opacity-0 group-hover:opacity-100 transition-opacity",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),e.name]})},t))})]}),(0,r.jsxs)(m.P.div,{variants:e,className:"col-span-1",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-6 relative inline-block",children:["Kategoriler",(0,r.jsx)("span",{className:"absolute bottom-0 left-0 w-1/2 h-0.5 bg-gradient-to-r from-purple-400 to-indigo-400"})]}),(0,r.jsx)("ul",{className:"space-y-3",children:[{name:"Cilt Bakımı",path:"/products?category=skin-care"},{name:"Sa\xe7 Bakımı",path:"/products?category=hair-care"},{name:"V\xfccut Bakımı",path:"/products?category=body-care"},{name:"Takviye \xdcr\xfcnler",path:"/products?category=supplements"},{name:"Parf\xfcm & Deodorant",path:"/products?category=perfume-deodorant"}].map((e,t)=>(0,r.jsx)(m.P.li,{whileHover:{x:5},transition:{type:"spring",stiffness:400,damping:10},children:(0,r.jsxs)(d(),{href:e.path,className:"text-gray-300 hover:text-white group flex items-center",children:[(0,r.jsx)("svg",{className:"h-3 w-3 mr-2 text-purple-400 opacity-0 group-hover:opacity-100 transition-opacity",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),e.name]})},t))})]}),(0,r.jsxs)(m.P.div,{variants:e,className:"col-span-1",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-6 relative inline-block",children:["İletişim",(0,r.jsx)("span",{className:"absolute bottom-0 left-0 w-1/2 h-0.5 bg-gradient-to-r from-purple-400 to-indigo-400"})]}),(0,r.jsxs)("ul",{className:"space-y-4",children:[(0,r.jsxs)(m.P.li,{className:"flex items-start",whileHover:{scale:1.02},children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-3 text-purple-400 flex-shrink-0 mt-0.5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,r.jsxs)("span",{className:"text-gray-300 text-sm leading-relaxed",children:["Atat\xfcrk Cad. No:123, 34100",(0,r.jsx)("br",{}),"Kadık\xf6y / İstanbul"]})]}),(0,r.jsxs)(m.P.li,{className:"flex items-center",whileHover:{scale:1.02},children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-3 text-purple-400 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),(0,r.jsx)("span",{className:"text-gray-300",children:"0212 123 45 67"})]}),(0,r.jsxs)(m.P.li,{className:"flex items-center",whileHover:{scale:1.02},children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-3 text-purple-400 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),(0,r.jsx)("span",{className:"text-gray-300",children:"<EMAIL>"})]}),(0,r.jsx)(m.P.li,{whileHover:{scale:1.05},children:(0,r.jsx)(m.P.div,{className:"mt-6 bg-gradient-to-r from-purple-600 to-indigo-600 p-0.5 rounded-lg",whileHover:{scale:1.02},children:(0,r.jsxs)("div",{className:"bg-gray-900 rounded-md p-4",children:[(0,r.jsx)("p",{className:"text-sm text-gray-300 mb-3",children:"B\xfcltenimize abone olun"}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("input",{type:"email",placeholder:"E-posta adresiniz",className:"flex-1 bg-gray-800 border-none rounded-l-md text-sm px-3 py-2 focus:outline-none focus:ring-1 focus:ring-purple-500 text-gray-100"}),(0,r.jsx)("button",{className:"bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-r-md px-3 py-2 text-sm font-medium",children:"Abone Ol"})]})]})})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-700 mt-12 pt-8",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,r.jsxs)(m.P.p,{className:"text-gray-400 text-sm mb-4 md:mb-0",initial:{opacity:0},whileInView:{opacity:1},transition:{delay:.5},children:["\xa9 ",new Date().getFullYear()," Say Global. T\xfcm hakları saklıdır."]}),(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-4",children:[{name:"Gizlilik Politikası",path:"/privacy-policy"},{name:"Kullanım Koşulları",path:"/terms-of-use"},{name:"\xc7erez Politikası",path:"/cookie-policy"}].map((e,t)=>(0,r.jsx)(m.P.div,{whileHover:{y:-2},whileTap:{y:0},children:(0,r.jsxs)(d(),{href:e.path,className:"text-gray-400 hover:text-white text-sm relative overflow-hidden group",children:[e.name,(0,r.jsx)("span",{className:"absolute bottom-0 top-6 left-0 w-full h-0.5 bg-purple-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out "})]})},t))})]})})]})})}var g=a(5323),y=a(69848),k=a(60760),w=a(35695),b=a(11518),v=a.n(b),j=a(79023);let N={INITIAL_DELAY:1e3,MAX_DELAY:3e4,MAX_ATTEMPTS:10,BACKOFF_MULTIPLIER:2},C=()=>(0,r.jsxs)("div",{className:"jsx-c1e7004345d34c2d network-status-banner",children:[(0,r.jsxs)("div",{className:"jsx-c1e7004345d34c2d network-status-content",children:[(0,r.jsx)("div",{className:"jsx-c1e7004345d34c2d network-status-icon",children:(0,r.jsx)("span",{className:"jsx-c1e7004345d34c2d network-status-spinner"})}),(0,r.jsxs)("div",{className:"jsx-c1e7004345d34c2d network-status-text",children:[(0,r.jsx)("div",{className:"jsx-c1e7004345d34c2d network-status-title",children:"İnternet bağlantınızda sorun yaşanıyor"}),(0,r.jsx)("div",{className:"jsx-c1e7004345d34c2d network-status-subtitle",children:"Bağlantı kontrol ediliyor..."})]})]}),(0,r.jsx)(v(),{id:"c1e7004345d34c2d",children:".network-status-banner.jsx-c1e7004345d34c2d{position:fixed;top:0;left:0;width:100%;background:-webkit-linear-gradient(315deg,#ffc107 0%,#ffb300 100%);background:-moz-linear-gradient(315deg,#ffc107 0%,#ffb300 100%);background:-o-linear-gradient(315deg,#ffc107 0%,#ffb300 100%);background:linear-gradient(135deg,#ffc107 0%,#ffb300 100%);color:#212529;z-index:9999;-webkit-box-shadow:0 2px 8px rgba(0,0,0,.1);-moz-box-shadow:0 2px 8px rgba(0,0,0,.1);box-shadow:0 2px 8px rgba(0,0,0,.1);-webkit-animation:slideDown.3s ease-out;-moz-animation:slideDown.3s ease-out;-o-animation:slideDown.3s ease-out;animation:slideDown.3s ease-out}.network-status-content.jsx-c1e7004345d34c2d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;padding:12px 20px;gap:12px}.network-status-icon.jsx-c1e7004345d34c2d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.network-status-spinner.jsx-c1e7004345d34c2d{width:20px;height:20px;border:2px solid#212529;border-top:2px solid transparent;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;-webkit-animation:spin 1s linear infinite;-moz-animation:spin 1s linear infinite;-o-animation:spin 1s linear infinite;animation:spin 1s linear infinite}.network-status-text.jsx-c1e7004345d34c2d{text-align:center}.network-status-title.jsx-c1e7004345d34c2d{font-size:14px;font-weight:600;margin-bottom:2px}.network-status-subtitle.jsx-c1e7004345d34c2d{font-size:12px;font-weight:400;opacity:.8}@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes slideDown{from{-webkit-transform:translatey(-100%);transform:translatey(-100%);opacity:0}to{-webkit-transform:translatey(0);transform:translatey(0);opacity:1}}@-moz-keyframes slideDown{from{-moz-transform:translatey(-100%);transform:translatey(-100%);opacity:0}to{-moz-transform:translatey(0);transform:translatey(0);opacity:1}}@-o-keyframes slideDown{from{-o-transform:translatey(-100%);transform:translatey(-100%);opacity:0}to{-o-transform:translatey(0);transform:translatey(0);opacity:1}}@keyframes slideDown{from{-webkit-transform:translatey(-100%);-moz-transform:translatey(-100%);-o-transform:translatey(-100%);transform:translatey(-100%);opacity:0}to{-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0);opacity:1}}@media(max-width:768px){.network-status-content.jsx-c1e7004345d34c2d{padding:10px 16px;gap:8px}.network-status-title.jsx-c1e7004345d34c2d{font-size:13px}.network-status-subtitle.jsx-c1e7004345d34c2d{font-size:11px}}"})]}),P=()=>{let{status:e,setStatus:t}=(0,j.V)(),[a,i]=(0,l.useState)(0),[n,o]=(0,l.useState)(0),s=(0,l.useRef)(null),c=(0,l.useRef)(null);(0,l.useEffect)(()=>{(async()=>{navigator.onLine?(console.log("\uD83C\uDF10 Browser online - status kontrol ediliyor"),"online"!==e&&await u()&&(console.log("✅ Network connectivity confirmed - status online yapılıyor"),t("online"))):(console.log("\uD83C\uDF10 Browser offline - status offline yapılıyor"),t("offline"))})()},[]),(0,l.useEffect)(()=>{let e=()=>{console.log("\uD83C\uDF10 Browser online event detected"),t("online")},a=()=>{console.log("\uD83C\uDF10 Browser offline event detected"),t("offline")};return window.addEventListener("online",e),window.addEventListener("offline",a),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",a)}},[t]);let d=e=>Math.min(N.INITIAL_DELAY*Math.pow(N.BACKOFF_MULTIPLIER,e),N.MAX_DELAY),u=async()=>{try{let e=new AbortController,t=setTimeout(()=>e.abort(),5e3),a=await fetch(window.location.origin,{method:"HEAD",signal:e.signal,cache:"no-cache"});return clearTimeout(t),a.ok||a.status<500}catch(e){return!1}},m=e=>{o(e);let t=()=>{o(e=>e<=1?0:(c.current=setTimeout(t,1e3),e-1))};c.current=setTimeout(t,1e3)},p=async()=>{let e=a+1;i(e),console.log("\uD83D\uDD04 Yeniden bağlanma denemesi ".concat(e,"/").concat(N.MAX_ATTEMPTS));try{if(await u()){console.log("✅ Bağlantı başarılı, online moda ge\xe7iliyor."),t("online"),i(0);return}if(e>=N.MAX_ATTEMPTS){console.log("❌ Maksimum deneme sayısına ulaşıldı, offline modda kalınıyor."),t("offline"),i(0);return}let a=d(e),r=Math.ceil(a/1e3);console.log("❌ Bağlantı denemesi başarısız, ".concat(r,"s sonra yeniden denenecek.")),m(r),s.current=setTimeout(p,a)}catch(a){if(console.log("❌ Bağlantı kontrol hatası:",a),e<N.MAX_ATTEMPTS){let t=d(e);m(Math.ceil(t/1e3)),s.current=setTimeout(p,t)}else t("offline"),i(0)}};return((0,l.useEffect)(()=>{let t=()=>{s.current&&(clearTimeout(s.current),s.current=null),c.current&&(clearTimeout(c.current),c.current=null)};return"reconnecting"===e?(i(0),p()):(t(),i(0),o(0)),t},[e]),"reconnecting"!==e)?null:(0,r.jsx)(C,{})};var D=a(45106);function S(){let e=(0,D.nb)(),{closeRegisterSuccessModal:t}=(0,D.QR)(),a=(0,w.useRouter)(),[i,n]=(0,l.useState)(0);(0,l.useEffect)(()=>{if(e){n(0);let e=setInterval(()=>{n(t=>t>=99?(clearInterval(e),100):t+1)},50);return()=>{clearInterval(e)}}},[e]),(0,l.useEffect)(()=>{if(i>=100&&e){let e=setTimeout(()=>{t(),a.push("/login")},100);return()=>clearTimeout(e)}},[i,e,t,a]);let o=()=>{t(),a.push("/login")};return(0,r.jsx)(k.N,{children:e&&(0,r.jsxs)(m.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:o,children:[(0,r.jsx)("div",{className:"absolute inset-0"}),(0,r.jsxs)(m.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.7,opacity:0,y:50},transition:{type:"spring",stiffness:300,damping:25,duration:.5},onClick:e=>e.stopPropagation(),children:[(0,r.jsx)(m.P.button,{whileHover:{scale:1.1},whileTap:{scale:.95},onClick:o,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})}),(0,r.jsx)("div",{className:"flex justify-center mb-6",children:(0,r.jsx)(m.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1,duration:.3,type:"spring",stiffness:300},className:"w-20 h-20 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(m.P.svg,{initial:{pathLength:0},animate:{pathLength:1},transition:{delay:.2,duration:.5,ease:"easeInOut"},className:"w-12 h-12 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)(m.P.path,{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})})}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(m.P.h2,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.15,duration:.3},className:"text-2xl font-bold text-gray-800 mb-4",children:"Hesabınız Başarıyla Oluşturuldu!"}),(0,r.jsx)(m.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2,duration:.3},className:"text-gray-600 mb-6 leading-relaxed",children:"Kayıt işleminiz tamamlandı. Şimdi giriş yaparak SayGlobal platformunu kullanmaya başlayabilirsiniz."}),(0,r.jsxs)(m.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.25,duration:.3},className:"flex flex-col gap-3",children:[(0,r.jsx)(m.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:o,className:"w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transition-all duration-300",children:"Giriş Yap"}),(0,r.jsx)(m.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:o,className:"w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-6 rounded-lg transition-all duration-300",children:"Kapat"})]})]}),(0,r.jsx)(m.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.35,duration:.3},className:"mt-6",children:(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 overflow-hidden",children:(0,r.jsx)(m.P.div,{className:"h-full bg-gradient-to-r from-purple-600 to-indigo-600 rounded-full",initial:{width:"0%"},animate:{width:"".concat(i,"%")},transition:{duration:.1,ease:"linear"}})})}),(0,r.jsx)(m.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4,duration:.3},className:"mt-3 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[Math.ceil((100-i)/20)," saniye sonra otomatik olarak giriş sayfasına y\xf6nlendirileceksiniz."]})})]})]})})}function M(e){let{children:t}=e,a=(0,w.usePathname)(),[i]=(0,l.useState)(()=>new o.E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:3,refetchOnWindowFocus:!1},mutations:{retry:1}}}));return(0,r.jsx)("html",{lang:"tr",className:"scroll-smooth",children:(0,r.jsx)("body",{className:"".concat(n().className," bg-gray-50 min-h-screen flex flex-col"),children:(0,r.jsxs)(s.Ht,{client:i,children:[(0,r.jsxs)(p.O,{children:[(0,r.jsx)(P,{}),(0,r.jsx)(g.e,{children:(0,r.jsxs)(y.H,{children:[(0,r.jsx)(x,{}),(0,r.jsx)(k.N,{mode:"wait",children:(0,r.jsx)(m.P.main,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:.3},className:"flex-grow",children:t},a)}),(0,r.jsx)(f,{}),(0,r.jsx)(S,{})]})})]}),!1]})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3444,6408,6874,7323,6766,3680,6681,8441,1684,7358],()=>t(23533)),_N_E=e.O()}]);