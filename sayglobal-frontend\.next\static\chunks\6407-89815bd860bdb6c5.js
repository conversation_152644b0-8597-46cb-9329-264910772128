"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6407],{23903:(e,t,r)=>{r.d(t,{Q6:()=>l,Z9:()=>o,e$:()=>u});var a=r(65453),n=r(46786);let c={isModalOpen:!1,selectedProductId:null,selectedVariantIndex:0,currentImageIndex:0,cachedProducts:new Map,lastFetchTime:new Map,isLoading:!1,error:null,prefetchQueue:new Set},o=(0,a.v)()((0,n.lt)((e,t)=>({...c,openModal:t=>{e({isModalOpen:!0,selectedProductId:t,selectedVariantIndex:0,currentImageIndex:0,error:null},!1,"productDetail/openModal")},closeModal:()=>{e({isModalOpen:!1,selectedProductId:null,selectedVariantIndex:0,currentImageIndex:0,error:null},!1,"productDetail/closeModal")},setSelectedVariant:t=>{e({selectedVariantIndex:t,currentImageIndex:0},!1,"productDetail/setSelectedVariant")},setCurrentImage:t=>{e({currentImageIndex:t},!1,"productDetail/setCurrentImage")},nextImage:()=>{let{selectedProductId:r,selectedVariantIndex:a,currentImageIndex:n,cachedProducts:c}=t();if(!r)return;let o=c.get(r);if(!o||!o.variants[a])return;let l=o.variants[a].images.length;l>0&&e({currentImageIndex:(n+1)%l},!1,"productDetail/nextImage")},prevImage:()=>{let{selectedProductId:r,selectedVariantIndex:a,currentImageIndex:n,cachedProducts:c}=t();if(!r)return;let o=c.get(r);if(!o||!o.variants[a])return;let l=o.variants[a].images.length;l>0&&e({currentImageIndex:(n-1+l)%l},!1,"productDetail/prevImage")},setCachedProduct:(r,a)=>{let{cachedProducts:n,lastFetchTime:c}=t(),o=new Map(n),l=new Map(c);o.set(r,a),l.set(r,Date.now()),e({cachedProducts:o,lastFetchTime:l},!1,"productDetail/setCachedProduct")},getCachedProduct:e=>{let{cachedProducts:r}=t();return r.get(e)||null},isCacheValid:function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3e5,{lastFetchTime:a}=t(),n=a.get(e);return!!n&&Date.now()-n<r},clearCache:()=>{e({cachedProducts:new Map,lastFetchTime:new Map},!1,"productDetail/clearCache")},clearProductCache:r=>{let{cachedProducts:a,lastFetchTime:n}=t(),c=new Map(a),o=new Map(n);c.delete(r),o.delete(r),e({cachedProducts:c,lastFetchTime:o},!1,"productDetail/clearProductCache")},addToPrefetchQueue:r=>{let{prefetchQueue:a}=t(),n=new Set(a);n.add(r),e({prefetchQueue:n},!1,"productDetail/addToPrefetchQueue")},removeFromPrefetchQueue:r=>{let{prefetchQueue:a}=t(),n=new Set(a);n.delete(r),e({prefetchQueue:n},!1,"productDetail/removeFromPrefetchQueue")},clearPrefetchQueue:()=>{e({prefetchQueue:new Set},!1,"productDetail/clearPrefetchQueue")},setLoading:t=>{e({isLoading:t},!1,"productDetail/setLoading")},setError:t=>{e({error:t},!1,"productDetail/setError")},reset:()=>{e({...c,cachedProducts:new Map,lastFetchTime:new Map},!1,"productDetail/reset")}}),{name:"product-detail-store",enabled:!1})),l=()=>o(e=>e.selectedVariantIndex),u=()=>o(e=>e.currentImageIndex)},56407:(e,t,r)=>{r.d(t,{Bv:()=>y,E0:()=>D,Ee:()=>s,IG:()=>f,LJ:()=>P,N5:()=>g,W$:()=>p,i5:()=>m,mn:()=>h,qr:()=>I,tA:()=>d});var a=r(32960),n=r(26715),c=r(5041),o=r(80722),l=r(52020),u=r(23903),i=r(12115);let d=()=>(0,a.I)({queryKey:["adminProductStatistics"],queryFn:async()=>{let e=await o.jU.getAdminProductStatistics();if(e.success)return e.data;throw Error(e.error||"İstatistikler alınamadı")},staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0,refetchInterval:6e4}),s=()=>(0,a.I)({queryKey:["myProductStatistics"],queryFn:async()=>{let e=await o.jU.getMyProductStatistics();if(e.success)return e.data;throw Error(e.error||"İstatistikler alınamadı")},staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0,refetchInterval:6e4}),h=()=>{let e=(0,n.jE)();return(0,c.n)({mutationFn:e=>(console.log("\uD83D\uDD04 Hook: updateSimpleProduct mutation başlatılıyor..."),o.jU.updateSimpleProduct(e)),onSuccess:t=>{console.log("✅ Hook: \xdcr\xfcn başarıyla g\xfcncellendi. Cache temizleniyor..."),console.log("\uD83D\uDCC4 Hook: Success data:",t),e.invalidateQueries({queryKey:["myProducts"]}),e.invalidateQueries({queryKey:["myProductStatistics"]}),e.invalidateQueries({queryKey:["products"]})},onError:e=>{var t,r;console.error("❌ Hook: \xdcr\xfcn g\xfcncelleme hatası:",e),console.error("❌ Hook: Error details:",{message:e.message,response:null==(t=e.response)?void 0:t.data,status:null==(r=e.response)?void 0:r.status})}})},y=(e,t)=>(0,a.I)({queryKey:["adminProducts",e,t],queryFn:()=>o.jU.getAdminProducts({pageNumber:e,pageSize:10,productName:t}),placeholderData:l.rX,staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0,refetchInterval:12e4}),m=(e,t,r)=>(0,a.I)({queryKey:["myProducts",e,t,r],queryFn:async()=>{let a={page:e,pageSize:10,name:t};void 0!==r&&-1!==r&&(a.status=r);let n=await o.jU.getMyProducts(a);if(n.success)return n.data;throw Error(n.error||"\xdcr\xfcnler alınamadı")},placeholderData:l.rX,staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0,refetchInterval:12e4}),g=e=>(0,a.I)({queryKey:["dealershipProductDetail",e],queryFn:async()=>{if(!e)return null;let t=await o.jU.getDealershipProductDetail(e);if(t.success)return t.data;throw Error(t.error||"\xdcr\xfcn detayı alınamadı")},enabled:!!e,staleTime:3e4,refetchOnWindowFocus:!0,refetchOnMount:!0}),p=()=>{let{refreshProductLists:e}=D();return(0,c.n)({mutationFn:e=>o.jU.deleteProduct(e),onSuccess:(t,r)=>{console.log("✅ \xdcr\xfcn başarıyla silindi. Listeler g\xfcncelleniyor..."),e(),console.log("\uD83D\uDD04 \xdcr\xfcn ".concat(r," silindi, cache'ler yenilendi"))},onError:e=>{console.error("❌ Silme işlemi sırasında hata:",e)}})},P=e=>{let t=(0,u.Z9)(e=>e.getCachedProduct),r=(0,u.Z9)(e=>e.isCacheValid),n=(0,u.Z9)(e=>e.setCachedProduct),c=(0,u.Z9)(e=>e.setLoading),d=(0,u.Z9)(e=>e.setError),s=(0,i.useMemo)(()=>e?t(e):null,[e,t]),h=(0,i.useMemo)(()=>!!e&&r(e),[e,r]),y=(0,a.I)({queryKey:["productDetail",e],queryFn:async()=>{if(!e)throw Error("Product ID is required");if(h&&s)return console.log("\uD83C\uDFAF Using cached product data for ID:",e),s;console.log("\uD83C\uDF10 Fetching fresh product data for ID:",e),c(!0);try{let t=await o.jU.getProductDetail(e);if(!t.success)throw Error(t.error||"\xdcr\xfcn detayı alınamadı");return n(e,t.data),d(null),t.data}catch(e){throw d(e.message),e}finally{c(!1)}},enabled:!!e,staleTime:6e5,gcTime:9e5,retry:2,retryDelay:e=>Math.min(1e3*2**e,3e4),initialData:h&&s?s:void 0,placeholderData:l.rX});return(0,i.useEffect)(()=>{y.data&&e&&!y.isError&&n(e,y.data)},[y.data,e,y.isError,n]),{...y,isCached:h&&!!s,cacheAge:0}},f=()=>{let{refreshProductLists:e,refreshProductDetail:t}=D();return(0,c.n)({mutationFn:e=>{let{productId:t,isApproved:r,message:a}=e;return o.jU.updateProductStatus(t,r,a)},onSuccess:(r,a)=>{console.log("✅ \xdcr\xfcn durumu başarıyla g\xfcncellendi. Cache temizleniyor..."),e(),t(a.productId),console.log("\uD83D\uDD04 \xdcr\xfcn ".concat(a.productId," i\xe7in t\xfcm cache'ler yenilendi"))},onError:e=>{console.error("❌ \xdcr\xfcn durumu g\xfcncellenirken hata:",e)}})},I=function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return(0,a.I)({queryKey:["productMessage",e],queryFn:async()=>{if(!e)return null;console.log("\uD83D\uDCDD Product message API \xe7ağrısı yapılıyor, productId:",e);let t=await o.jU.getProductMessage(e);return t.success?(console.log("✅ Product message başarıyla alındı:",t.data),t.data):(console.warn("Product message API hatası:",t.error),null)},enabled:t&&!!e,staleTime:6e4,cacheTime:3e5,retry:!1,refetchOnWindowFocus:!0})},D=()=>{let e=(0,n.jE)();return{refreshProductLists:()=>{console.log("\uD83D\uDD04 \xdcr\xfcn listeleri cache'i yenileniyor..."),e.invalidateQueries({queryKey:["adminProducts"]}),e.invalidateQueries({queryKey:["adminProductStatistics"]})},refreshProductDetail:t=>{console.log("\uD83D\uDD04 \xdcr\xfcn detayı cache'i yenileniyor (ID: ".concat(t,")...")),e.invalidateQueries({queryKey:["productDetail",t]}),e.invalidateQueries({queryKey:["productMessage",t]})},refreshAllProductData:()=>{console.log("\uD83D\uDD04 T\xfcm \xfcr\xfcn verileri cache'i yenileniyor..."),e.invalidateQueries({queryKey:["adminProducts"]}),e.invalidateQueries({queryKey:["adminProductStatistics"]}),e.invalidateQueries({queryKey:["productDetail"]}),e.invalidateQueries({queryKey:["productMessage"]})}}}}}]);