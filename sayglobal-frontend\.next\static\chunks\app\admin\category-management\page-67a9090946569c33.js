(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2817],{45106:(e,a,r)=>{"use strict";r.d(a,{$N:()=>E,EI:()=>I,F0:()=>c,Gc:()=>d,HX:()=>A,JZ:()=>y,OG:()=>C,Ph:()=>x,QK:()=>g,QR:()=>z,S:()=>u,VS:()=>o,Zm:()=>N,_f:()=>D,c6:()=>h,fW:()=>w,gA:()=>f,hg:()=>p,ig:()=>j,lA:()=>m,nb:()=>v,qA:()=>S,u6:()=>b,vQ:()=>k});var t=r(65453),l=r(46786);let i={editPersonalInfo:!1,referenceRegistration:!1,addAddress:!1,setDefaultConfirmation:!1,banking:!1,addCard:!1,setDefaultCard:!1,registerSuccess:!1,successNotification:!1,productCategorySelector:!1,productVariantSetup:!1,productVariant:!1,productDeleteConfirmation:!1},s={editPersonalInfoUser:null,referenceRegistrationData:null,addAddressData:null,setDefaultConfirmationData:null,bankingData:null,addCardData:null,setDefaultCardData:null,successNotificationData:null,productCategorySelectorData:null,productVariantSetupData:null,productVariantData:null,productDeleteConfirmationData:null},n=(0,t.v)()((0,l.lt)(e=>({...i,...s,openModal:(a,r)=>{e(e=>({...e,[a]:!0,...r&&{["".concat(a,"User")]:r}}),!1,"modal/open/".concat(a))},closeModal:a=>{e(e=>({...e,[a]:!1,["".concat(a,"User")]:null}),!1,"modal/close/".concat(a))},closeAllModals:()=>{e({...i,...s},!1,"modal/closeAll")},openEditPersonalInfoModal:a=>{e({editPersonalInfo:!0,editPersonalInfoUser:a},!1,"modal/openEditPersonalInfo")},closeEditPersonalInfoModal:()=>{e({editPersonalInfo:!1,editPersonalInfoUser:null},!1,"modal/closeEditPersonalInfo")},openReferenceRegistrationModal:a=>{e({referenceRegistration:!0,referenceRegistrationData:a},!1,"modal/openReferenceRegistration")},closeReferenceRegistrationModal:()=>{e({referenceRegistration:!1,referenceRegistrationData:null},!1,"modal/closeReferenceRegistration")},openAddAddressModal:a=>{e({addAddress:!0,addAddressData:a},!1,"modal/openAddAddress")},closeAddAddressModal:()=>{e({addAddress:!1,addAddressData:null},!1,"modal/closeAddAddress")},openSetDefaultConfirmationModal:a=>{e({setDefaultConfirmation:!0,setDefaultConfirmationData:a},!1,"modal/openSetDefaultConfirmation")},closeSetDefaultConfirmationModal:()=>{e({setDefaultConfirmation:!1,setDefaultConfirmationData:null},!1,"modal/closeSetDefaultConfirmation")},openBankingModal:a=>{e({banking:!0,bankingData:a},!1,"modal/openBanking")},closeBankingModal:()=>{e({banking:!1,bankingData:null},!1,"modal/closeBanking")},openAddCardModal:a=>{e({addCard:!0,addCardData:a},!1,"modal/openAddCard")},closeAddCardModal:()=>{e({addCard:!1,addCardData:null},!1,"modal/closeAddCard")},openSetDefaultCardModal:a=>{e({setDefaultCard:!0,setDefaultCardData:a},!1,"modal/openSetDefaultCard")},closeSetDefaultCardModal:()=>{e({setDefaultCard:!1,setDefaultCardData:null},!1,"modal/closeSetDefaultCard")},openRegisterSuccessModal:()=>{e({registerSuccess:!0},!1,"modal/openRegisterSuccess")},closeRegisterSuccessModal:()=>{e({registerSuccess:!1},!1,"modal/closeRegisterSuccess")},openSuccessNotificationModal:a=>{e({successNotification:!0,successNotificationData:a},!1,"modal/openSuccessNotification")},closeSuccessNotificationModal:()=>{e({successNotification:!1,successNotificationData:null},!1,"modal/closeSuccessNotification")},openProductCategorySelector:a=>{e({productCategorySelector:!0,productCategorySelectorData:a},!1,"modal/openProductCategorySelector")},closeProductCategorySelector:()=>{e({productCategorySelector:!1,productCategorySelectorData:null},!1,"modal/closeProductCategorySelector")},openProductVariantSetup:a=>{e({productVariantSetup:!0,productVariantSetupData:a},!1,"modal/openProductVariantSetup")},closeProductVariantSetup:()=>{e({productVariantSetup:!1,productVariantSetupData:null},!1,"modal/closeProductVariantSetup")},openProductVariant:a=>{e({productVariant:!0,productVariantData:a},!1,"modal/openProductVariant")},closeProductVariant:()=>{e({productVariant:!1,productVariantData:null},!1,"modal/closeProductVariant")},openProductDeleteConfirmation:a=>{e({productDeleteConfirmation:!0,productDeleteConfirmationData:a},!1,"modal/openProductDeleteConfirmation")},closeProductDeleteConfirmation:()=>{e({productDeleteConfirmation:!1,productDeleteConfirmationData:null},!1,"modal/closeProductDeleteConfirmation")}}),{name:"modal-store",enabled:!1})),o=()=>n(e=>e.editPersonalInfo),d=()=>n(e=>e.editPersonalInfoUser),c=()=>n(e=>e.referenceRegistration),u=()=>n(e=>e.referenceRegistrationData),g=()=>n(e=>e.addAddress),m=()=>n(e=>e.addAddressData),x=()=>n(e=>e.setDefaultConfirmation),b=()=>n(e=>e.setDefaultConfirmationData),y=()=>n(e=>e.banking),f=()=>n(e=>e.bankingData),h=()=>n(e=>e.addCard),p=()=>n(e=>e.addCardData),j=()=>n(e=>e.setDefaultCard),k=()=>n(e=>e.setDefaultCardData),v=()=>n(e=>e.registerSuccess),N=()=>n(e=>e.successNotification),C=()=>n(e=>e.successNotificationData),w=()=>n(e=>e.productCategorySelector),D=()=>n(e=>e.productCategorySelectorData),A=()=>n(e=>e.productVariant),S=()=>n(e=>e.productVariantData),I=()=>n(e=>e.productDeleteConfirmation),E=()=>n(e=>e.productDeleteConfirmationData),z=()=>{let e=n(e=>e.openModal),a=n(e=>e.closeModal),r=n(e=>e.closeAllModals),t=n(e=>e.openEditPersonalInfoModal),l=n(e=>e.closeEditPersonalInfoModal),i=n(e=>e.openReferenceRegistrationModal),s=n(e=>e.closeReferenceRegistrationModal),o=n(e=>e.openAddAddressModal),d=n(e=>e.closeAddAddressModal),c=n(e=>e.openSetDefaultConfirmationModal),u=n(e=>e.closeSetDefaultConfirmationModal),g=n(e=>e.openBankingModal),m=n(e=>e.closeBankingModal),x=n(e=>e.openAddCardModal),b=n(e=>e.closeAddCardModal),y=n(e=>e.openSetDefaultCardModal),f=n(e=>e.closeSetDefaultCardModal),h=n(e=>e.openRegisterSuccessModal),p=n(e=>e.closeRegisterSuccessModal),j=n(e=>e.openSuccessNotificationModal),k=n(e=>e.closeSuccessNotificationModal),v=n(e=>e.openProductCategorySelector),N=n(e=>e.closeProductCategorySelector),C=n(e=>e.openProductVariantSetup),w=n(e=>e.closeProductVariantSetup),D=n(e=>e.openProductVariant),A=n(e=>e.closeProductVariant);return{openModal:e,closeModal:a,closeAllModals:r,openEditPersonalInfoModal:t,closeEditPersonalInfoModal:l,openReferenceRegistrationModal:i,closeReferenceRegistrationModal:s,openAddAddressModal:o,closeAddAddressModal:d,openSetDefaultConfirmationModal:c,closeSetDefaultConfirmationModal:u,openBankingModal:g,closeBankingModal:m,openAddCardModal:x,closeAddCardModal:b,openSetDefaultCardModal:y,closeSetDefaultCardModal:f,openRegisterSuccessModal:h,closeRegisterSuccessModal:p,openSuccessNotificationModal:j,closeSuccessNotificationModal:k,openProductCategorySelector:v,closeProductCategorySelector:N,openProductVariantSetup:C,closeProductVariantSetup:w,openProductVariant:D,closeProductVariant:A,openProductDeleteConfirmation:n(e=>e.openProductDeleteConfirmation),closeProductDeleteConfirmation:n(e=>e.closeProductDeleteConfirmation)}}},67693:(e,a,r)=>{Promise.resolve().then(r.bind(r,92860))},92860:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>q});var t=r(95155),l=r(12115),i=r(87220),s=r(35695),n=r(60760),o=r(76408),d=r(40646),c=r(81284),u=r(1243),g=r(54861),m=r(54416),x=r(45106);let b=()=>{let e=(0,x.Zm)(),a=(0,x.OG)(),{closeSuccessNotificationModal:r}=(0,x.QR)(),[i,s]=(0,l.useState)(0);(0,l.useEffect)(()=>{if(e&&(null==a?void 0:a.autoClose)){s(0);let e=a.duration||5e3,r=setInterval(()=>{s(a=>a>=99?(clearInterval(r),100):a+100/(e/50))},50);return()=>{clearInterval(r)}}},[e,a]),(0,l.useEffect)(()=>{if(i>=100&&e&&(null==a?void 0:a.autoClose)){let e=setTimeout(()=>{r()},100);return()=>clearTimeout(e)}},[i,e,a,r]);let b=(()=>{if(!a)return d.A;switch(a.icon){case"success":default:return d.A;case"info":return c.A;case"warning":return u.A;case"error":return g.A}})();return(0,t.jsx)(n.N,{mode:"wait",children:e&&a&&(0,t.jsxs)(o.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:r,children:[(0,t.jsx)("div",{className:"absolute inset-0"}),(0,t.jsxs)(o.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:25,duration:.5}},exit:{scale:.7,opacity:0,y:50,transition:{type:"spring",stiffness:300,damping:25,duration:.5}},onClick:e=>e.stopPropagation(),children:[(0,t.jsx)(o.P.button,{whileHover:{scale:1.1},whileTap:{scale:.95},onClick:r,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors",children:(0,t.jsx)(m.A,{className:"w-6 h-6"})}),(0,t.jsx)("div",{className:"flex justify-center mb-6",children:(0,t.jsx)(o.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1,duration:.3,type:"spring",stiffness:300},className:"w-20 h-20 ".concat((()=>{if(!a)return"bg-green-100";switch(a.icon){case"success":default:return"bg-green-100";case"info":return"bg-blue-100";case"warning":return"bg-yellow-100";case"error":return"bg-red-100"}})()," rounded-full flex items-center justify-center"),children:(0,t.jsx)(o.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,duration:.5,ease:"easeInOut"},children:(0,t.jsx)(b,{className:"w-12 h-12 ".concat((()=>{if(!a)return"text-green-500";switch(a.icon){case"success":default:return"text-green-500";case"info":return"text-blue-500";case"warning":return"text-yellow-500";case"error":return"text-red-500"}})())})})})}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(o.P.h2,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.15,duration:.3},className:"text-2xl font-bold text-gray-800 mb-4",children:a.title}),(0,t.jsx)(o.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2,duration:.3},className:"text-gray-600 mb-6 leading-relaxed",children:a.message}),(0,t.jsx)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.25,duration:.3},className:"flex flex-col gap-3",children:(0,t.jsx)(o.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:r,className:"w-full bg-gradient-to-r ".concat((()=>{if(!a)return"from-green-600 to-emerald-600";switch(a.icon){case"success":default:return"from-green-600 to-emerald-600";case"info":return"from-blue-600 to-indigo-600";case"warning":return"from-yellow-600 to-orange-600";case"error":return"from-red-600 to-pink-600"}})()," text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transition-all duration-300"),children:"Tamam"})})]}),a.autoClose&&(0,t.jsx)(o.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.35,duration:.3},className:"mt-6",children:(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 overflow-hidden",children:(0,t.jsx)(o.P.div,{className:"h-full bg-gradient-to-r ".concat((()=>{if(!a)return"from-green-600 to-emerald-600";switch(a.icon){case"success":default:return"from-green-600 to-emerald-600";case"info":return"from-blue-600 to-indigo-600";case"warning":return"from-yellow-600 to-orange-600";case"error":return"from-red-600 to-pink-600"}})()," rounded-full"),initial:{width:"0%"},animate:{width:"".concat(i,"%")},transition:{duration:.1,ease:"linear"}})})}),a.autoClose&&(0,t.jsx)(o.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4,duration:.3},className:"mt-3 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[Math.ceil((100-i)/20)," saniye sonra otomatik olarak kapanacak."]})})]})]})})};var y=r(32960),f=r(26715),h=r(5041),p=r(80722),j=r(46893);let k=()=>(0,y.I)({queryKey:["brands"],queryFn:async()=>(await p.Ay.get(j.S.GET_BRANDS)).data,staleTime:3e4}),v=()=>(0,y.I)({queryKey:["categories"],queryFn:async()=>(await p.Ay.get(j.S.GET_CATEGORIES)).data,staleTime:3e4}),N=e=>(0,y.I)({queryKey:["subCategories",e],queryFn:async()=>{let a=j.S.GET_SUBCATEGORIES_BY_CATEGORY.replace("{categoryId}",e.toString());return(await p.Ay.get(a)).data},enabled:!!e,staleTime:3e4}),C=e=>(0,y.I)({queryKey:["featureValues",e],queryFn:async()=>{let a=j.S.GET_FEATURE_VALUES_BY_DEFINITION_ID.replace("{definitionId}",e.toString());return(await p.Ay.get(a)).data},enabled:!!e,staleTime:3e4}),w=e=>(0,y.I)({queryKey:["categoriesByBrand",e],queryFn:async()=>{if(!e)return[];let a=j.S.GET_CATEGORIES_BY_BRAND_ID.replace("{brandId}",e.toString());return(await p.Ay.get(a)).data},enabled:!!e,staleTime:3e4}),D=e=>(0,y.I)({queryKey:["subCategoryFeatures",e],queryFn:async()=>{let a=j.S.GET_SUBCATEGORY_FEATURES_BY_ID.replace("{subCategoryId}",e.toString()),r=await p.Ay.get(a);return console.log("SubCategory ".concat(e," features:"),r.data),r.data},enabled:!!e,staleTime:3e4}),A=()=>(0,y.I)({queryKey:["allFeatureDefinitions"],queryFn:async()=>(await p.Ay.get(j.S.GET_ALL_FEATURE_DEFINITIONS)).data,staleTime:3e4}),S=()=>{let e=(0,f.jE)();return(0,h.n)({mutationFn:async e=>(await p.Ay.post(j.S.CREATE_BRAND,e)).data,onSuccess:()=>{e.invalidateQueries({queryKey:["brands"]})},onError:e=>{console.error("Marka oluşturma hatası:",e)}})},I=()=>{let e=(0,f.jE)();return(0,h.n)({mutationFn:async e=>(await p.Ay.post(j.S.CREATE_CATEGORY,e)).data,onSuccess:()=>{e.invalidateQueries({queryKey:["categories"]})},onError:e=>{console.error("Kategori oluşturma hatası:",e)}})},E=()=>{let e=(0,f.jE)();return(0,h.n)({mutationFn:async e=>(await p.Ay.post(j.S.CREATE_SUBCATEGORY,e)).data,onSuccess:(a,r)=>{e.invalidateQueries({queryKey:["subCategories"]}),e.invalidateQueries({queryKey:["subCategories",r.categoryId]})},onError:e=>{console.error("Alt kategori oluşturma hatası:",e)}})},z=()=>{let e=(0,f.jE)();return(0,h.n)({mutationFn:async e=>(await p.Ay.post(j.S.CREATE_FEATURE_DEFINITION,e)).data,onSuccess:()=>{e.invalidateQueries({queryKey:["allFeatureDefinitions"]})},onError:e=>{console.error("\xd6zellik tanımı oluşturma hatası:",e)}})},K=()=>{let e=(0,f.jE)();return(0,h.n)({mutationFn:async e=>(await p.Ay.post(j.S.CREATE_FEATURE_VALUE,e)).data,onSuccess:(a,r)=>{e.invalidateQueries({queryKey:["featureValues"]}),e.invalidateQueries({queryKey:["featureValues",r.featureDefinitionId]})},onError:e=>{console.error("\xd6zellik değeri oluşturma hatası:",e)}})},M=()=>{let e=(0,f.jE)();return(0,h.n)({mutationFn:async e=>(await p.Ay.post(j.S.CREATE_SUBCATEGORY_FEATURE,e)).data,onSuccess:(a,r)=>{e.invalidateQueries({queryKey:["subCategoryFeatures"]}),e.invalidateQueries({queryKey:["subCategoryFeatures",r.subCategoryId]})},onError:e=>{console.error("Alt kategori-\xf6zellik ilişkisi oluşturma hatası:",e)}})},R=()=>{let e=(0,f.jE)();return(0,h.n)({mutationFn:async e=>(await p.Ay.post(j.S.CREATE_BRAND_CATEGORY,e)).data,onSuccess:(a,r)=>{e.invalidateQueries({queryKey:["categoriesByBrand"]}),e.invalidateQueries({queryKey:["categoriesByBrand",r.brandId]})},onError:e=>{console.error("Marka-kategori ilişkisi oluşturma hatası:",e)}})};var T=r(23227),P=r(43332),F=r(54653),V=r(381),B=r(13717),O=r(38164),_=r(13052);let q=()=>{let{user:e,isLoading:a}=(0,i.A)(),r=(0,s.useRouter)(),{openSuccessNotificationModal:n}=(0,x.QR)(),[d,c]=(0,l.useState)("brands"),[u,g]=(0,l.useState)(""),[m,y]=(0,l.useState)(null),[f,h]=(0,l.useState)(null),[p,j]=(0,l.useState)(null),[q,L]=(0,l.useState)(null),[U,Y]=(0,l.useState)(null),[G,Q]=(0,l.useState)(null),[H,Z]=(0,l.useState)(null),[X,J]=(0,l.useState)(null),[W,$]=(0,l.useState)(null),[ee,ea]=(0,l.useState)(null),[er,et]=(0,l.useState)(null),[el,ei]=(0,l.useState)(null),{data:es=[],isLoading:en}=k(),{data:eo=[],isLoading:ed}=v(),{data:ec=[],isLoading:eu}=N(null!=m?m:void 0),{data:eg=[],isLoading:em}=N(null!=p?p:void 0),{data:ex=[],isLoading:eb}=N(null!=U?U:void 0),{data:ey=[],isLoading:ef}=N(null!=H?H:void 0),{data:eh=[],isLoading:ep}=N(null!=W?W:void 0),{data:ej=[],isLoading:ek}=N(null!=er?er:void 0),{data:ev=[],isLoading:eN}=D(null!=G?G:void 0),{data:eC=[],isLoading:ew}=D(null!=X?X:void 0),{data:eD=[],isLoading:eA}=D(null!=el?el:void 0),{data:eS=[],isLoading:eI}=A(),{data:eE=[],isLoading:ez}=C(null!=f?f:void 0),[eK,eM]=(0,l.useState)(null),{data:eR=[],isLoading:eT}=w(eK||void 0),{data:eP=[],isLoading:eF}=D(null!=q?q:void 0),eV=S(),eB=I(),eO=E(),e_=z(),eq=K(),eL=R(),eU=M(),[eY,eG]=(0,l.useState)(!1),[eQ,eH]=(0,l.useState)({brand:{name:"",logoUrl:""},category:{name:""},subCategory:{name:"",categoryId:""},featureDefinition:{name:"",description:"",isRequired:!1,isMultiSelect:!1},featureValue:{featureDefinitionId:"",value:""},brandCategory:{brandId:"",categoryId:""},subCategoryFeature:{subCategoryId:"",featureDefinitionId:""}}),{data:eZ=[]}=w(eQ.brandCategory.brandId?parseInt(eQ.brandCategory.brandId):void 0);if((0,l.useEffect)(()=>{a||e&&"admin"===e.role||r.push("/login")},[e,a,r]),a)return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})});if(!e||"admin"!==e.role)return null;let eX=async e=>{eG(!0);try{switch(e){case"featureDefinition":if(!eQ.featureDefinition.name||!ee){n({title:"Eksik Bilgi",message:"L\xfctfen \xf6zellik adını girin ve bir alt kategori se\xe7in.",icon:"warning",autoClose:!0,duration:5e3}),eG(!1);return}let r=await e_.mutateAsync({name:eQ.featureDefinition.name,description:eQ.featureDefinition.description,isRequired:eQ.featureDefinition.isRequired,isMultiSelect:eQ.featureDefinition.isMultiSelect});if(r&&r.data){var a;await eU.mutateAsync({subCategoryId:ee,featureDefinitionId:Number(r.data)});let e=(null==(a=eh.find(e=>e.id===ee))?void 0:a.name)||"";n({title:"\xd6zellik Tanımı Oluşturuldu!",message:"'".concat(eQ.featureDefinition.name,"' \xf6zelliği oluşturuldu ve '").concat(e,"' alt kategorisine bağlandı."),icon:"success",autoClose:!0,duration:5e3}),eH(e=>({...e,featureDefinition:{name:"",description:"",isRequired:!1,isMultiSelect:!1}}))}else throw Error("\xd6zellik oluşturuldu ancak ID alınamadı.");break;case"subCategoryFeature":await eU.mutateAsync({subCategoryId:parseInt(eQ.subCategoryFeature.subCategoryId),featureDefinitionId:parseInt(eQ.subCategoryFeature.featureDefinitionId)}),eH(e=>({...e,subCategoryFeature:{subCategoryId:"",featureDefinitionId:""}})),n({title:"Kategori-\xd6zellik İlişkisi Oluşturuldu!",message:"Alt kategori ve \xf6zellik arasında ilişki başarıyla kuruldu.",icon:"success",autoClose:!0,duration:5e3});break;case"featureValue":await eq.mutateAsync({featureDefinitionId:parseInt(eQ.featureValue.featureDefinitionId),value:eQ.featureValue.value}),eH(e=>({...e,featureValue:{featureDefinitionId:"",value:""}})),n({title:"\xd6zellik Değeri Oluşturuldu!",message:'"'.concat(eQ.featureValue.value,'" değeri başarıyla eklendi.'),icon:"success",autoClose:!0,duration:5e3});break;case"brand":await eV.mutateAsync(eQ.brand),eH(e=>({...e,brand:{name:"",logoUrl:""}})),n({title:"Marka Oluşturuldu!",message:'"'.concat(eQ.brand.name,'" markası başarıyla oluşturuldu.'),icon:"success",autoClose:!0,duration:5e3});break;case"category":await eB.mutateAsync(eQ.category),eH(e=>({...e,category:{name:""}})),n({title:"Kategori Oluşturuldu!",message:'"'.concat(eQ.category.name,'" kategorisi başarıyla oluşturuldu.'),icon:"success",autoClose:!0,duration:5e3});break;case"subCategory":await eO.mutateAsync({name:eQ.subCategory.name,categoryId:parseInt(eQ.subCategory.categoryId)}),eH(e=>({...e,subCategory:{name:"",categoryId:""}})),n({title:"Alt Kategori Oluşturuldu!",message:'"'.concat(eQ.subCategory.name,'" alt kategorisi başarıyla oluşturuldu.'),icon:"success",autoClose:!0,duration:5e3});break;case"brandCategory":await eL.mutateAsync({brandId:parseInt(eQ.brandCategory.brandId),categoryId:parseInt(eQ.brandCategory.categoryId)}),eH(e=>({...e,brandCategory:{brandId:"",categoryId:""}})),n({title:"Marka-Kategori İlişkisi Oluşturuldu!",message:"Marka ve kategori arasında ilişki başarıyla kuruldu.",icon:"success",autoClose:!0,duration:5e3});break;default:console.log("Unhandled form type: ".concat(e))}}catch(e){console.error("Form g\xf6nderilirken hata:",e),n({title:"İşlem Hatası",message:"İşlem sırasında bir hata oluştu. L\xfctfen tekrar deneyin.",icon:"error",autoClose:!0,duration:5e3})}finally{eG(!1)}},eJ=[{id:"brands",label:"Markalar",icon:T.A},{id:"categories",label:"Kategoriler",icon:P.A},{id:"subCategories",label:"Alt Kategoriler",icon:F.A},{id:"features",label:"\xd6zellikler",icon:V.A},{id:"featureValues",label:"\xd6zellik Değerleri",icon:B.A},{id:"brandCategories",label:"Marka-Kategori İlişkileri",icon:O.A},{id:"subCategoryFeatures",label:"Kategori-\xd6zellik İlişkileri",icon:_.A}],eW=()=>(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(T.A,{className:"w-5 h-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-blue-900 mb-2",children:"Marka Y\xf6netimi Talimatları"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,t.jsx)("li",{children:"• Sistem genelinde kullanılacak markaları buradan ekleyebilirsiniz"}),(0,t.jsx)("li",{children:"• Marka adı zorunlu alan olup, logo URL'si opsiyoneldir"}),(0,t.jsx)("li",{children:"• Eklenen markalar daha sonra marka-kategori ilişkileri tabında kategorilerle eşleştirilebilir"}),(0,t.jsx)("li",{children:"• Marka logosu i\xe7in ge\xe7erli bir URL adresi kullanın (\xf6rn: https://example.com/logo.png)"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Yeni Marka Ekle"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Marka Adı"}),(0,t.jsx)("input",{type:"text",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black",value:eQ.brand.name,onChange:e=>eH(a=>({...a,brand:{...a.brand,name:e.target.value}})),placeholder:"Marka adını girin"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Logo URL (Opsiyonel)"}),(0,t.jsx)("input",{type:"url",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black",value:eQ.brand.logoUrl,onChange:e=>eH(a=>({...a,brand:{...a.brand,logoUrl:e.target.value}})),placeholder:"Logo URL'sini girin"})]})]}),(0,t.jsx)("button",{onClick:()=>eX("brand"),disabled:!eQ.brand.name||eY,className:"mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition",children:eY?"Oluşturuluyor...":"Marka Oluştur"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Mevcut Markalar"}),(0,t.jsx)("div",{className:"space-y-3",children:es.length>0?es.map(e=>(0,t.jsx)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[e.logoUrl?(0,t.jsx)("img",{src:e.logoUrl,alt:e.name,className:"w-12 h-12 object-contain rounded-md bg-white p-1 border"}):(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center",children:(0,t.jsx)(T.A,{className:"w-6 h-6 text-gray-400"})}),(0,t.jsx)("span",{className:"font-semibold text-gray-800",children:e.name})]})},e.id)):(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Hen\xfcz marka eklenmemiş."})})]})]}),e$=()=>(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(P.A,{className:"w-5 h-5 text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-green-900 mb-2",children:"Kategori Y\xf6netimi Talimatları"}),(0,t.jsxs)("ul",{className:"text-sm text-green-800 space-y-1",children:[(0,t.jsx)("li",{children:"• Ana kategorileri buradan oluşturabilirsiniz (\xf6rn: Elektronik, Giyim, Ev & Yaşam)"}),(0,t.jsx)("li",{children:"• Kategori adı zorunlu alan olup, benzersiz olmalıdır"}),(0,t.jsx)("li",{children:"• Oluşturulan kategoriler alt kategoriler tabında ana kategori olarak se\xe7ilebilir"}),(0,t.jsx)("li",{children:"• Kategoriler marka-kategori ilişkileri tabında markalarla eşleştirilebilir"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Yeni Kategori Ekle"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategori Adı"}),(0,t.jsx)("input",{type:"text",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black",value:eQ.category.name,onChange:e=>eH(a=>({...a,category:{...a.category,name:e.target.value}})),placeholder:"Kategori adını girin"})]})}),(0,t.jsx)("button",{onClick:()=>eX("category"),disabled:!eQ.category.name||eY,className:"mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition",children:eY?"Oluşturuluyor...":"Kategori Oluştur"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Mevcut Kategoriler"}),(0,t.jsx)("div",{className:"space-y-3",children:eo.length>0?eo.map(e=>(0,t.jsx)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,t.jsx)("span",{className:"font-semibold text-gray-800",children:e.name})},e.id)):(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Hen\xfcz kategori eklenmemiş."})})]})]}),e0=()=>(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-purple-50 border border-purple-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(F.A,{className:"w-5 h-5 text-purple-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-purple-900 mb-2",children:"Alt Kategori Y\xf6netimi Talimatları"}),(0,t.jsxs)("ul",{className:"text-sm text-purple-800 space-y-1",children:[(0,t.jsx)("li",{children:"• Alt kategoriler bir ana kategoriye bağlı olarak oluşturulur (\xf6rn: Elektronik > Telefon)"}),(0,t.jsx)("li",{children:"• Alt kategori adı ve ana kategori se\xe7imi zorunlu alanlardır"}),(0,t.jsx)("li",{children:"• Alt kategoriler \xfcr\xfcn \xf6zelliklerini tanımlamak i\xe7in kategori-\xf6zellik ilişkileri tabında kullanılır"}),(0,t.jsx)("li",{children:"• Listeleme sırasında filtrelemek i\xe7in ana kategori se\xe7ebilirsiniz"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Yeni Alt Kategori Ekle"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alt Kategori Adı"}),(0,t.jsx)("input",{type:"text",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black",value:eQ.subCategory.name,onChange:e=>eH(a=>({...a,subCategory:{...a.subCategory,name:e.target.value}})),placeholder:"Alt kategori adını girin"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ana Kategori"}),(0,t.jsxs)("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",value:eQ.subCategory.categoryId,onChange:e=>eH(a=>({...a,subCategory:{...a.subCategory,categoryId:e.target.value}})),children:[(0,t.jsx)("option",{value:"",children:"Kategori se\xe7in"}),eo.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]}),(0,t.jsx)("button",{onClick:()=>eX("subCategory"),disabled:!eQ.subCategory.name||!eQ.subCategory.categoryId||eY,className:"mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition",children:eY?"Oluşturuluyor...":"Alt Kategori Oluştur"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Mevcut Alt Kategoriler"}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategoriye G\xf6re Filtrele"}),(0,t.jsxs)("select",{onChange:e=>y(e.target.value?Number(e.target.value):null),value:null!=m?m:"",className:"w-full max-w-xs px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",children:[(0,t.jsx)("option",{value:"",children:"Kategori Se\xe7in"}),eo.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),eu&&(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Alt kategoriler y\xfckleniyor..."}),!eu&&m&&(0,t.jsx)("div",{className:"space-y-3",children:ec.length>0?ec.map(e=>{var a;return(0,t.jsx)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"font-semibold text-gray-800",children:e.name}),(0,t.jsx)(_.A,{className:"h-5 w-5 text-gray-400"}),(0,t.jsx)("span",{className:"text-md text-gray-600",children:null==(a=eo.find(a=>a.id===e.categoryId))?void 0:a.name})]})},e.id)}):(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Bu kategoriye ait alt kategori bulunamadı."})}),!m&&!eu&&(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Alt kategorileri g\xf6r\xfcnt\xfclemek i\xe7in bir kategori se\xe7in."})]})]}),e2=()=>(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(V.A,{className:"w-5 h-5 text-orange-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-orange-900 mb-2",children:"\xd6zellik Tanımları Y\xf6netimi Talimatları"}),(0,t.jsxs)("ul",{className:"text-sm text-orange-800 space-y-1",children:[(0,t.jsx)("li",{children:"• \xd6zellik tanımları \xfcr\xfcn \xf6zelliklerini kategorize etmek i\xe7in kullanılır (\xf6rn: Renk, Beden, Marka)"}),(0,t.jsx)("li",{children:"• \xd6zellik oluşturulurken mutlaka bir alt kategori se\xe7melisiniz"}),(0,t.jsx)("li",{children:'• "Zorunlu \xd6zellik" işaretlenirse, bu \xf6zellik o kategorideki t\xfcm \xfcr\xfcnlerde bulunmak zorundadır'}),(0,t.jsx)("li",{children:'• "\xc7oklu Se\xe7im" aktifse, bu \xf6zellik i\xe7in birden fazla değer se\xe7ilebilir'}),(0,t.jsx)("li",{children:"• \xd6zellik tanımları oluşturulduktan sonra \xf6zellik değerleri tabında değerler eklenebilir"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Yeni \xd6zellik Tanımı Ekle"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Eklenecek Kategori"}),(0,t.jsxs)("select",{value:W||"",onChange:e=>{$(e.target.value?Number(e.target.value):null),ea(null)},className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",children:[(0,t.jsx)("option",{value:"",children:"\xd6nce Kategori Se\xe7in"}),eo.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Eklenecek Alt Kategori"}),(0,t.jsxs)("select",{value:ee||"",onChange:e=>ea(e.target.value?Number(e.target.value):null),disabled:!W||ep,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",children:[(0,t.jsx)("option",{value:"",children:"Alt Kategori Se\xe7in"}),eh.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xd6zellik Adı"}),(0,t.jsx)("input",{type:"text",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black",value:eQ.featureDefinition.name,onChange:e=>eH(a=>({...a,featureDefinition:{...a.featureDefinition,name:e.target.value}})),placeholder:"\xd6rn: Renk, Beden"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"A\xe7ıklama"}),(0,t.jsx)("input",{type:"text",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black",value:eQ.featureDefinition.description,onChange:e=>eH(a=>({...a,featureDefinition:{...a.featureDefinition,description:e.target.value}})),placeholder:"\xd6zellik a\xe7ıklaması"})]})]}),(0,t.jsxs)("div",{className:"mt-6 flex items-center space-x-8",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500",checked:eQ.featureDefinition.isRequired,onChange:e=>eH(a=>({...a,featureDefinition:{...a.featureDefinition,isRequired:e.target.checked}}))}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Zorunlu \xd6zellik"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500",checked:eQ.featureDefinition.isMultiSelect,onChange:e=>eH(a=>({...a,featureDefinition:{...a.featureDefinition,isMultiSelect:e.target.checked}}))}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"\xc7oklu Se\xe7im"})]})]}),(0,t.jsx)("button",{onClick:()=>eX("featureDefinition"),disabled:!eQ.featureDefinition.name||!ee||eY,className:"mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition",children:eY?"Oluşturuluyor...":"\xd6zellik Tanımı Oluştur"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Mevcut \xd6zellik Tanımları"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategoriye G\xf6re Filtrele"}),(0,t.jsxs)("select",{value:U||"",onChange:e=>{Y(e.target.value?Number(e.target.value):null),Q(null)},className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",children:[(0,t.jsx)("option",{value:"",children:"\xd6nce Kategori Se\xe7in"}),eo.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alt Kategoriye G\xf6re Filtrele"}),(0,t.jsxs)("select",{value:G||"",onChange:e=>Q(e.target.value?Number(e.target.value):null),disabled:!U||eb,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",children:[(0,t.jsx)("option",{value:"",children:"Alt Kategori Se\xe7in"}),ex.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:eN?(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"\xd6zellikler y\xfckleniyor..."}):G&&ev.length>0?ev.map(e=>(0,t.jsx)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,t.jsx)("span",{className:"font-semibold text-gray-800",children:e.featureDefinitionName})},e.featureDefinitionId)):(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:G?"Bu alt kategoriye ait \xf6zellik bulunamadı.":"\xd6zellik tanımlarını g\xf6rmek i\xe7in bir kategori ve alt kategori se\xe7in."})})]})]}),e1=()=>(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-teal-50 border border-teal-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(B.A,{className:"w-5 h-5 text-teal-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-teal-900 mb-2",children:"\xd6zellik Değerleri Y\xf6netimi Talimatları"}),(0,t.jsxs)("ul",{className:"text-sm text-teal-800 space-y-1",children:[(0,t.jsx)("li",{children:"• \xd6zellik değerleri, \xf6zellik tanımları altında yer alan se\xe7ilebilir değerlerdir"}),(0,t.jsx)("li",{children:'• \xd6rneğin "Renk" \xf6zelliği i\xe7in "Kırmızı, Mavi, Yeşil" gibi değerler ekleyebilirsiniz'}),(0,t.jsx)("li",{children:"• Değer eklemek i\xe7in \xf6nce kategori ve alt kategori se\xe7in, sonra o alt kategorideki \xf6zellik tanımlarından birini se\xe7in"}),(0,t.jsx)("li",{children:"• Listeleme yaparken de aynı filtre sırasını takip edin: Kategori > Alt Kategori > \xd6zellik Tanımı"}),(0,t.jsx)("li",{children:"• Bu değerler \xfcr\xfcn ekleme sayfasında kullanıcıya se\xe7enek olarak sunulur"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Yeni \xd6zellik Değeri Ekle"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategori"}),(0,t.jsxs)("select",{value:U||"",onChange:e=>{Y(e.target.value?Number(e.target.value):null),Q(null)},className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",children:[(0,t.jsx)("option",{value:"",children:"\xd6nce Kategori Se\xe7in"}),eo.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alt Kategori"}),(0,t.jsxs)("select",{value:G||"",onChange:e=>Q(e.target.value?Number(e.target.value):null),disabled:!U||eb,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",children:[(0,t.jsx)("option",{value:"",children:"Alt Kategori Se\xe7in"}),ex.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xd6zellik Tanımı"}),(0,t.jsxs)("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",value:eQ.featureValue.featureDefinitionId,onChange:e=>eH(a=>({...a,featureValue:{...a.featureValue,featureDefinitionId:e.target.value}})),disabled:!G||eN,children:[(0,t.jsx)("option",{value:"",children:"\xd6zellik se\xe7in"}),ev.map(e=>(0,t.jsx)("option",{value:e.featureDefinitionId,children:e.featureDefinitionName},e.featureDefinitionId))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Değer"}),(0,t.jsx)("input",{type:"text",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black",value:eQ.featureValue.value,onChange:e=>eH(a=>({...a,featureValue:{...a.featureValue,value:e.target.value}})),placeholder:"\xd6rn: Kırmızı, XL"})]})]}),(0,t.jsx)("button",{onClick:()=>eX("featureValue"),disabled:!eQ.featureValue.featureDefinitionId||!eQ.featureValue.value||eY,className:"mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition",children:eY?"Oluşturuluyor...":"Değer Ekle"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Mevcut \xd6zellik Değerleri"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategori"}),(0,t.jsxs)("select",{value:er||"",onChange:e=>{et(e.target.value?Number(e.target.value):null),ei(null),h(null)},className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",children:[(0,t.jsx)("option",{value:"",children:"Kategori Se\xe7in"}),eo.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alt Kategori"}),(0,t.jsxs)("select",{value:el||"",onChange:e=>{ei(e.target.value?Number(e.target.value):null),h(null)},disabled:!er||ek,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",children:[(0,t.jsx)("option",{value:"",children:"Alt Kategori Se\xe7in"}),ej.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"G\xf6r\xfcnt\xfclenecek \xd6zelliği Se\xe7in"}),(0,t.jsxs)("select",{value:f||"",onChange:e=>h(e.target.value?Number(e.target.value):null),disabled:!el||eA,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",children:[(0,t.jsx)("option",{value:"",children:"\xd6zellik Se\xe7in"}),eD.map(e=>(0,t.jsx)("option",{value:e.featureDefinitionId,children:e.featureDefinitionName},e.featureDefinitionId))]})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:ez?(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Değerler y\xfckleniyor..."}):f&&eE.length>0?eE.map(e=>(0,t.jsx)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,t.jsx)("span",{className:"font-semibold text-gray-800",children:e.value})},e.id)):(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:f?"Bu \xf6zelliğe ait değer bulunamadı.":"Değerleri g\xf6rmek i\xe7in yukarıdan bir kategori, alt kategori ve \xf6zellik tanımı se\xe7in."})})]})]}),e6=()=>{let e=eQ.brandCategory.categoryId?parseInt(eQ.brandCategory.categoryId):void 0,a=eZ.some(a=>a.id===e);return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-indigo-50 border border-indigo-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(O.A,{className:"w-5 h-5 text-indigo-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-indigo-900 mb-2",children:"Marka-Kategori İlişkileri Y\xf6netimi Talimatları"}),(0,t.jsxs)("ul",{className:"text-sm text-indigo-800 space-y-1",children:[(0,t.jsx)("li",{children:"• Markalar ve kategoriler arasında ilişki kurmak i\xe7in bu b\xf6l\xfcm\xfc kullanın"}),(0,t.jsx)("li",{children:"• Bu ilişkiler, \xfcr\xfcn ekleme sırasında hangi markanın hangi kategorilerde kullanılabileceğini belirler"}),(0,t.jsx)("li",{children:"• Bir marka birden fazla kategoriyle ilişkilendirilebilir"}),(0,t.jsx)("li",{children:"• Sistem mevcut ilişkileri kontrol eder ve tekrar ilişki kurulmasını engeller"}),(0,t.jsx)("li",{children:"• Marka se\xe7erek o markaya ait kategorileri g\xf6r\xfcnt\xfcleyebilirsiniz"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Yeni Marka-Kategori İlişkisi Ekle"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Marka"}),(0,t.jsxs)("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",value:eQ.brandCategory.brandId,onChange:e=>eH(a=>({...a,brandCategory:{...a.brandCategory,brandId:e.target.value}})),children:[(0,t.jsx)("option",{value:"",children:"Marka se\xe7in"}),es.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategori"}),(0,t.jsxs)("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",value:eQ.brandCategory.categoryId,onChange:e=>eH(a=>({...a,brandCategory:{...a.brandCategory,categoryId:e.target.value}})),children:[(0,t.jsx)("option",{value:"",children:"Kategori se\xe7in"}),eo.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-6",children:[(0,t.jsx)("button",{onClick:()=>eX("brandCategory"),disabled:!eQ.brandCategory.brandId||!eQ.brandCategory.categoryId||eY||a,className:"bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition",children:eY?"Oluşturuluyor...":"İlişki Oluştur"}),a&&(0,t.jsx)("p",{className:"text-sm text-yellow-600 font-medium",children:"Bu ilişki zaten mevcut."})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Marka Kategorilerini G\xf6r\xfcnt\xfcle"}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Marka Se\xe7in"}),(0,t.jsxs)("select",{value:eK||"",onChange:e=>eM(e.target.value?Number(e.target.value):null),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",children:[(0,t.jsx)("option",{value:"",children:"Marka se\xe7in"}),es.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),eT?(0,t.jsx)("div",{className:"text-center py-4 text-gray-500",children:"Y\xfckleniyor..."}):eK?(0,t.jsx)("div",{className:"space-y-3",children:eR.length>0?eR.map(e=>(0,t.jsx)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsx)("span",{className:"font-semibold text-gray-800",children:e.name})})},e.id)):(0,t.jsx)("div",{className:"text-center py-4 text-gray-500",children:"Bu markaya ait kategori bulunamadı."})}):(0,t.jsx)("div",{className:"text-center py-4 text-gray-500",children:"Kategorileri g\xf6r\xfcnt\xfclemek i\xe7in bir marka se\xe7in."})]})]})},e5=()=>{let e=eQ.subCategoryFeature.featureDefinitionId?parseInt(eQ.subCategoryFeature.featureDefinitionId):void 0,a=eC.some(a=>a.featureDefinitionId===e);return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-pink-50 border border-pink-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(_.A,{className:"w-5 h-5 text-pink-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-pink-900 mb-2",children:"Kategori-\xd6zellik İlişkileri Y\xf6netimi Talimatları"}),(0,t.jsxs)("ul",{className:"text-sm text-pink-800 space-y-1",children:[(0,t.jsx)("li",{children:"• Alt kategoriler ve \xf6zellik tanımları arasında ilişki kurmak i\xe7in bu b\xf6l\xfcm\xfc kullanın"}),(0,t.jsx)("li",{children:"• Bu ilişkiler, o alt kategorideki \xfcr\xfcnlerde hangi \xf6zelliklerin kullanılabileceğini belirler"}),(0,t.jsx)("li",{children:"• Bir alt kategori birden fazla \xf6zellik tanımıyla ilişkilendirilebilir"}),(0,t.jsx)("li",{children:"• Sistem mevcut ilişkileri kontrol eder ve tekrar ilişki kurulmasını engeller"}),(0,t.jsx)("li",{children:'• \xd6zellik tanımları "\xd6zellikler" tabından daha kolay bir şekilde alt kategoriye bağlı olarak oluşturulabilir'}),(0,t.jsx)("li",{children:"• Listeleme yaparken kategori ve alt kategori se\xe7erek ilişkileri g\xf6r\xfcnt\xfcleyebilirsiniz"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Yeni Kategori-\xd6zellik İlişkisi Ekle"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategori"}),(0,t.jsxs)("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",value:H||"",onChange:e=>{Z(e.target.value?Number(e.target.value):null),J(null),eH(e=>({...e,subCategoryFeature:{subCategoryId:"",featureDefinitionId:""}}))},children:[(0,t.jsx)("option",{value:"",children:"Kategori se\xe7in"}),eo.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alt Kategori"}),(0,t.jsxs)("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",value:eQ.subCategoryFeature.subCategoryId,disabled:!H||ef,onChange:e=>{let a=e.target.value;J(a?Number(a):null),eH(e=>({...e,subCategoryFeature:{...e.subCategoryFeature,subCategoryId:a,featureDefinitionId:""}}))},children:[(0,t.jsx)("option",{value:"",children:"Alt Kategori se\xe7in"}),ey.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xd6zellik Tanımı"}),(0,t.jsxs)("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",value:eQ.subCategoryFeature.featureDefinitionId,disabled:!X||eI,onChange:e=>eH(a=>({...a,subCategoryFeature:{...a.subCategoryFeature,featureDefinitionId:e.target.value}})),children:[(0,t.jsx)("option",{value:"",children:"\xd6zellik se\xe7in"}),eS.map(e=>(0,t.jsx)("option",{value:e.featureDefinitionId,children:e.featureDefinitionName},e.featureDefinitionId))]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-6",children:[(0,t.jsx)("button",{onClick:()=>eX("subCategoryFeature"),disabled:!eQ.subCategoryFeature.subCategoryId||!eQ.subCategoryFeature.featureDefinitionId||eY||a,className:"bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition",children:eY?"Oluşturuluyor...":"İlişki Oluştur"}),a&&(0,t.jsx)("p",{className:"text-sm text-yellow-600 font-medium",children:"Bu ilişki zaten mevcut."})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow p-6 border border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-6",children:"Mevcut Kategori-\xd6zellik İlişkileri"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kategoriye G\xf6re Filtrele"}),(0,t.jsxs)("select",{value:p||"",onChange:e=>{j(e.target.value?Number(e.target.value):null),L(null)},className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700",children:[(0,t.jsx)("option",{value:"",children:"\xd6nce Kategori Se\xe7in"}),eo.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alt Kategoriye G\xf6re Filtrele"}),(0,t.jsxs)("select",{value:q||"",onChange:e=>L(e.target.value?Number(e.target.value):null),disabled:!p||em,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100",children:[(0,t.jsx)("option",{value:"",children:"Alt Kategori Se\xe7in"}),eg.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:eF?(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"İlişkiler y\xfckleniyor..."}):q&&eP.length>0?eP.map(e=>(0,t.jsx)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"font-semibold text-gray-800",children:e.subCategoryName}),(0,t.jsx)(_.A,{className:"h-5 w-5 text-gray-400"}),(0,t.jsx)("span",{className:"text-md text-gray-600",children:e.featureDefinitionName})]})},e.id)):(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:q?"Bu alt kategoriye ait \xf6zellik ilişkisi bulunamadı.":"İlişkileri g\xf6rmek i\xe7in bir kategori ve alt kategori se\xe7in."})})]})]})};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8",children:[(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Kategori Y\xf6netimi"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Marka, kategori ve \xf6zellik tanımlarını y\xf6netin"})]}),(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsx)("nav",{className:"-mb-px flex space-x-8 overflow-x-auto",children:eJ.map(e=>(0,t.jsx)("button",{onClick:()=>c(e.id),className:"whitespace-nowrap py-3 px-1 border-b-2 font-semibold text-sm transition-colors ".concat(d===e.id?"border-red-500 text-red-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(e.icon,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:e.label})]})},e.id))})})}),(0,t.jsx)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:(()=>{switch(d){case"brands":return eW();case"categories":return e$();case"subCategories":return e0();case"features":return e2();case"featureValues":return e1();case"brandCategories":return e6();case"subCategoryFeatures":return e5();default:return null}})()},d)]}),(0,t.jsx)(b,{})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[6408,7323,4620,6681,8441,1684,7358],()=>a(67693)),_N_E=e.O()}]);