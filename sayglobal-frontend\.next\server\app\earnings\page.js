(()=>{var e={};e.id=76,e.ids=[76],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16600:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\earnings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\earnings\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23928:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},25878:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(60687),a=s(43210),i=s(84299),n=s(31158),l=s(23928),o=s(25541),d=s(64398),c=s(13861),m=s(99270),x=s(40228),p=s(54220),h=s(58869);let u=()=>{let[e,t]=(0,a.useState)(i.Zq),[s,u]=(0,a.useState)("date"),[g,y]=(0,a.useState)("desc"),[v,f]=(0,a.useState)(""),[j,b]=(0,a.useState)("all"),N=r=>{let a=s===r&&"desc"===g?"asc":"desc";u(r),y(a),t([...e].sort((e,t)=>{let s,i;switch(r){case"date":s=new Date(e.date),i=new Date(t.date);break;case"amount":s=e.amount,i=t.amount;break;case"points":s=e.points,i=t.points;break;default:return 0}return"asc"===a?s>i?1:-1:s<i?1:-1}))},w=e=>{f(e);let s=i.Zq;if(e&&(s=s.filter(t=>t.reference.toLowerCase().includes(e.toLowerCase()))),"all"!==j){let e=new Date,t=new Date;switch(j){case"week":t.setDate(e.getDate()-7);break;case"month":t.setMonth(e.getMonth()-1);break;case"quarter":t.setMonth(e.getMonth()-3)}s=s.filter(e=>new Date(e.date)>=t)}t(s)},k=e=>{b(e),w(v)},A=e.reduce((e,t)=>e+t.amount,0),S=e.reduce((e,t)=>e+t.points,0);return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 py-8",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Kazan\xe7 Detayları"}),(0,r.jsx)("p",{className:"text-gray-600",children:"T\xfcm kazan\xe7 ge\xe7mişinizi detaylı olarak g\xf6r\xfcnt\xfcleyin"})]}),(0,r.jsx)("div",{className:"mt-4 sm:mt-0",children:(0,r.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Rapor İndir"]})})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Kazan\xe7"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₺",i.wI.totalEarnings.toLocaleString("tr-TR",{minimumFractionDigits:2})]})]}),(0,r.jsx)("div",{className:"bg-green-100 p-3 rounded-full",children:(0,r.jsx)(l.A,{className:"h-6 w-6 text-green-600"})})]})}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Filtrelenmiş Kazan\xe7"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₺",A.toLocaleString("tr-TR",{minimumFractionDigits:2})]})]}),(0,r.jsx)("div",{className:"bg-blue-100 p-3 rounded-full",children:(0,r.jsx)(o.A,{className:"h-6 w-6 text-blue-600"})})]})}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Toplam Puan"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:S.toLocaleString("tr-TR")})]}),(0,r.jsx)("div",{className:"bg-purple-100 p-3 rounded-full",children:(0,r.jsx)(d.A,{className:"h-6 w-6 text-purple-600"})})]})}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 border-l-4 border-orange-500",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"İşlem Sayısı"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.length})]}),(0,r.jsx)("div",{className:"bg-orange-100 p-3 rounded-full",children:(0,r.jsx)(c.A,{className:"h-6 w-6 text-orange-600"})})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 mb-8",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 sm:space-x-4",children:[(0,r.jsx)("div",{className:"flex-1 max-w-md",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Referans ara...",value:v,onChange:e=>w(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 placeholder-gray-500 text-black"})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(x.A,{className:"h-5 w-5 text-gray-400"}),(0,r.jsxs)("select",{value:j,onChange:e=>k(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500 text-gray-500 ",children:[(0,r.jsx)("option",{value:"all",children:"T\xfcm Zamanlar"}),(0,r.jsx)("option",{value:"week",children:"Son 7 G\xfcn"}),(0,r.jsx)("option",{value:"month",children:"Son 30 G\xfcn"}),(0,r.jsx)("option",{value:"quarter",children:"Son 3 Ay"})]})]}),(0,r.jsx)("button",{onClick:()=>{f(""),b("all"),t(i.Zq)},className:"px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Filtreleri Temizle"})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Kazan\xe7 Ge\xe7mişi"})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>N("date"),children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{children:"Tarih"}),(0,r.jsx)(p.A,{className:"h-4 w-4"})]})}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Referans"})]})}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>N("points"),children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Puan"}),(0,r.jsx)(p.A,{className:"h-4 w-4"})]})}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>N("amount"),children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Miktar"}),(0,r.jsx)(p.A,{className:"h-4 w-4"})]})}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Seviye"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Y\xfczde"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors",children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:new Date(e.date).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric"})}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:new Date(e.date).toLocaleDateString("tr-TR",{weekday:"long"})})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)("span",{className:"text-white font-semibold text-xs",children:e.reference.split(" ").map(e=>e[0]).join("")})}),(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.reference})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-yellow-500 mr-1"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.points})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm font-bold text-green-600",children:["₺",e.amount.toLocaleString("tr-TR",{minimumFractionDigits:2})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${1===e.level?"bg-green-100 text-green-800":2===e.level?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:["Seviye ",e.level]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["%",e.percentage]})})]},e.id))})]})}),0===e.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsxs)("div",{className:"text-gray-500",children:[(0,r.jsx)(o.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Kazan\xe7 bulunamadı"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Se\xe7ilen kriterlere uygun kazan\xe7 kaydı bulunmuyor."})]})}),e.length>0&&(0,r.jsx)("div",{className:"px-6 py-4 bg-gray-50 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:["Toplam ",(0,r.jsx)("span",{className:"font-medium",children:e.length})," kazan\xe7 kaydı g\xf6steriliyor"]}),(0,r.jsxs)("div",{className:"mt-2 sm:mt-0 text-sm font-medium text-gray-900",children:["Toplam: ₺",A.toLocaleString("tr-TR",{minimumFractionDigits:2}),(0,r.jsxs)("span",{className:"text-gray-500 ml-2",children:["• ",S," puan"]})]})]})})]}),(0,r.jsx)("div",{className:"mt-8 text-center",children:(0,r.jsx)("a",{href:"/panel",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors",children:"Distrib\xfct\xf6r Paneline D\xf6n"})})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30775:(e,t,s)=>{Promise.resolve().then(s.bind(s,16600))},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},39185:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["earnings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,16600)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\earnings\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Sayglobal\\sayglobal-frontend\\src\\app\\earnings\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/earnings/page",pathname:"/earnings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},42447:(e,t,s)=>{Promise.resolve().then(s.bind(s,25878))},54220:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:c,...m},x)=>(0,r.createElement)("svg",{ref:x,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:l("lucide",i),...!n&&!o(m)&&{"aria-hidden":"true"},...m},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(n)?n:[n]])),m=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...i},o)=>(0,r.createElement)(c,{ref:o,iconNode:t,className:l(`lucide-${a(n(e))}`,`lucide-${e}`,s),...i}));return s.displayName=n(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84299:(e,t,s)=>{"use strict";s.d(t,{At:()=>n,Zq:()=>r,dt:()=>l,wI:()=>i,x1:()=>a});let r=[{id:1,distributorId:2,date:"2023-04-15",reference:"Aylin Şahin",points:120,amount:240.5,level:1,percentage:8},{id:2,distributorId:2,date:"2023-04-20",reference:"Emre Kılı\xe7",points:85,amount:170,level:2,percentage:5},{id:3,distributorId:2,date:"2023-04-28",reference:"Arda Altun",points:150,amount:300.75,level:1,percentage:8},{id:4,distributorId:2,date:"2023-05-05",reference:"Burak \xd6zt\xfcrk",points:60,amount:120.25,level:3,percentage:3},{id:5,distributorId:2,date:"2023-05-12",reference:"Selin Kara",points:200,amount:400,level:1,percentage:8}],a=[{id:101,firstName:"Aylin",lastName:"Şahin",level:1,joinDate:"2023-02-10",points:450,isActive:!0},{id:102,firstName:"Emre",lastName:"Kılı\xe7",level:2,joinDate:"2023-02-15",points:320,isActive:!0},{id:103,firstName:"Arda",lastName:"Altun",level:1,joinDate:"2023-03-01",points:580,isActive:!0},{id:104,firstName:"Burak",lastName:"\xd6zt\xfcrk",level:3,joinDate:"2023-03-12",points:150,isActive:!1},{id:105,firstName:"Selin",lastName:"Kara",level:1,joinDate:"2023-03-25",points:650,isActive:!0},{id:106,firstName:"Murat",lastName:"Aydın",level:2,joinDate:"2023-04-05",points:280,isActive:!0},{id:107,firstName:"Elif",lastName:"\xc7elik",level:3,joinDate:"2023-04-18",points:120,isActive:!1}],i={totalEarnings:4250.75,monthlyPoints:350,monthlyActivityPercentage:75,teamSize:a.length,monthlyBalance:1230.5,totalBalance:4250.75,totalPoints:2150,organizationPoints:5680,monthlyEarnings:[{month:"Ocak",earnings:850.25,activity:65},{month:"Şubat",earnings:920.5,activity:70},{month:"Mart",earnings:1050.75,activity:80},{month:"Nisan",earnings:1230.5,activity:75},{month:"Mayıs",earnings:980.25,activity:72}],monthlyPointsHistory:[{month:"Ocak",points:280,target:300},{month:"Şubat",points:320,target:300},{month:"Mart",points:390,target:350},{month:"Nisan",points:420,target:350},{month:"Mayıs",points:350,target:400},{month:"Haziran",points:310,target:400}],monthlyActivityTrend:[{month:"Ocak",activityPercentage:65,teamSize:4,newMembers:1},{month:"Şubat",activityPercentage:70,teamSize:5,newMembers:1},{month:"Mart",activityPercentage:80,teamSize:6,newMembers:1},{month:"Nisan",activityPercentage:75,teamSize:6,newMembers:0},{month:"Mayıs",activityPercentage:72,teamSize:7,newMembers:1},{month:"Haziran",activityPercentage:78,teamSize:7,newMembers:0}],nextLevelPoints:500,currentLevel:"G\xfcm\xfcş",nextLevel:"Altın"},n={id:1,name:"Distrib\xfct\xf6r (Sen)",level:3,points:2150,joinDate:"2023-01-01",isActive:!0,totalEarnings:4250.75,monthlyPoints:350,children:{left:{id:101,name:"Aylin Şahin",level:1,points:450,joinDate:"2023-02-10",isActive:!0,parentId:1,position:"left",totalEarnings:1200.5,monthlyPoints:120,children:{left:{id:103,name:"Arda Altun",level:1,points:580,joinDate:"2023-03-01",isActive:!0,parentId:101,position:"left",totalEarnings:850.25,monthlyPoints:95,children:{left:{id:107,name:"Elif \xc7elik",level:3,points:120,joinDate:"2023-04-18",isActive:!1,parentId:103,position:"left",totalEarnings:240,monthlyPoints:25}}},right:{id:105,name:"Selin Kara",level:1,points:650,joinDate:"2023-03-25",isActive:!0,parentId:101,position:"right",totalEarnings:980.75,monthlyPoints:135}}},right:{id:102,name:"Emre Kılı\xe7",level:2,points:320,joinDate:"2023-02-15",isActive:!0,parentId:1,position:"right",totalEarnings:750.25,monthlyPoints:85,children:{left:{id:104,name:"Burak \xd6zt\xfcrk",level:3,points:150,joinDate:"2023-03-12",isActive:!1,parentId:102,position:"left",totalEarnings:320,monthlyPoints:40},right:{id:106,name:"Murat Aydın",level:2,points:280,joinDate:"2023-04-05",isActive:!0,parentId:102,position:"right",totalEarnings:480.5,monthlyPoints:65}}}}},l={totalMembers:7,activeMembers:5,totalLevels:3,totalPoints:2550,totalEarnings:8950,monthlyGrowth:12.5}},94735:e=>{"use strict";e.exports=require("events")},99270:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,181,658,85],()=>s(39185));module.exports=r})();