(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8279],{5323:(e,t,a)=>{"use strict";a.d(t,{_:()=>n,e:()=>l});var s=a(95155),r=a(12115);let i=(0,r.createContext)(void 0);function l(e){let{children:t}=e,[a,l]=(0,r.useState)([]);(0,r.useEffect)(()=>{{let e=localStorage.getItem("sayGlobalCart");if(e)try{let t=JSON.parse(e).map(e=>({...e,points:e.points||0}));l(t)}catch(e){console.error("Sepet verileri y\xfcklenirken hata:",e)}}},[]),(0,r.useEffect)(()=>{localStorage.setItem("sayGlobalCart",JSON.stringify(a))},[a]);let n=e=>{l(t=>t.filter(t=>t.id!==e))};return(0,s.jsx)(i.Provider,{value:{items:a,addToCart:e=>{l(t=>{let a=t.find(t=>t.id===e.id),s=e.quantity||1;return a?t.map(t=>t.id===e.id?{...t,quantity:t.quantity+s,points:e.points||t.points||0}:t):[...t,{id:e.id,title:e.title,price:e.price,thumbnail:e.thumbnail,brand:e.brand,quantity:s,discountPercentage:e.discountPercentage,points:e.points||0}]})},removeFromCart:n,updateQuantity:(e,t)=>{if(t<=0)return void n(e);l(a=>a.map(a=>a.id===e?{...a,quantity:t}:a))},clearCart:()=>{l([])},getTotalItems:()=>a.reduce((e,t)=>e+t.quantity,0),getTotalPrice:()=>a.reduce((e,t)=>e+(t.discountPercentage?t.price*(1-t.discountPercentage/100):t.price)*t.quantity,0),getTotalPoints:()=>a.reduce((e,t)=>e+(t.points||0)*t.quantity,0)},children:t})}function n(){let e=(0,r.useContext)(i);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},32591:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var s=a(95155),r=a(5323),i=a(12115),l=a(76408),n=a(66766),d=a(6874),c=a.n(d),o=a(35695),m=a(60760);function x(e){let{isOpen:t,onClose:a}=e;return(0,s.jsx)(m.N,{children:t&&(0,s.jsxs)(l.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center px-4",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},children:[(0,s.jsx)(l.P.div,{className:"absolute inset-0 bg-black/20 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:a}),(0,s.jsxs)(l.P.div,{className:"relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl",initial:{scale:.7,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.7,opacity:0,y:50},transition:{type:"spring",stiffness:300,damping:25,duration:.5},children:[(0,s.jsxs)(l.P.div,{className:"text-center mb-6",initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},children:[(0,s.jsx)("div",{className:"mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(l.P.svg,{className:"w-10 h-10 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",initial:{pathLength:0},animate:{pathLength:1},transition:{delay:.4,duration:.6},children:(0,s.jsx)(l.P.path,{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7",initial:{pathLength:0},animate:{pathLength:1},transition:{delay:.4,duration:.6}})})}),(0,s.jsx)(l.P.h2,{className:"text-2xl font-bold text-gray-800 mb-2",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:"Siparişiniz Alındı!"}),(0,s.jsx)(l.P.p,{className:"text-gray-600",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4},children:"Siparişiniz başarıyla oluşturuldu. En kısa s\xfcrede hazırlanacak ve size ulaştırılacaktır."})]}),(0,s.jsx)(l.P.div,{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},children:(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-green-700 font-medium",children:"Sipariş Numarası:"}),(0,s.jsxs)("span",{className:"text-green-800 font-bold",children:["#SG",Math.random().toString(36).substr(2,9).toUpperCase()]})]})}),(0,s.jsxs)(l.P.div,{className:"flex flex-col space-y-3",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},children:[(0,s.jsx)(c(),{href:"/",children:(0,s.jsx)(l.P.button,{className:"w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},onClick:a,children:"Ana Sayfaya D\xf6n"})}),(0,s.jsx)(c(),{href:"/products",children:(0,s.jsx)(l.P.button,{className:"w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300",whileHover:{scale:1.02},whileTap:{scale:.98},onClick:a,children:"Alışverişe Devam Et"})})]}),(0,s.jsx)(l.P.button,{onClick:a,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors",initial:{opacity:0},animate:{opacity:1},transition:{delay:.7},whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]})})}function u(){let{items:e,getTotalPrice:t,getTotalPoints:a,clearCart:d}=(0,r._)();(0,o.useRouter)();let[m,u]=(0,i.useState)(1),[p,h]=(0,i.useState)(!1),[g,y]=(0,i.useState)({firstName:"",lastName:"",email:"",phone:"",address:"",city:"",district:"",postalCode:"",paymentMethod:"creditCard",cardNumber:"",cardName:"",expiryDate:"",cvv:"",billingAddress:"",billingCity:"",billingDistrict:"",billingPostalCode:"",sameAsDelivery:!0}),[f,b]=(0,i.useState)({});if(0===e.length)return(0,s.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-4",children:"Sepetiniz Boş"}),(0,s.jsx)("p",{className:"text-gray-300 mb-8",children:"\xd6deme yapabilmek i\xe7in sepetinizde \xfcr\xfcn bulunmalıdır."}),(0,s.jsx)(c(),{href:"/products",className:"bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300",children:"Alışverişe Başla"})]})})});let j=e=>{let{name:t,value:a,type:s}=e.target;if("checkbox"===s){let a=e.target.checked;y(e=>({...e,[t]:a}))}else y(e=>({...e,[t]:a}));f[t]&&b(e=>({...e,[t]:""}))},N=e=>{let t={};return 1===e&&(g.firstName.trim()||(t.firstName="Ad gereklidir"),g.lastName.trim()||(t.lastName="Soyad gereklidir"),g.email.trim()||(t.email="E-posta gereklidir"),g.phone.trim()||(t.phone="Telefon gereklidir"),g.address.trim()||(t.address="Adres gereklidir"),g.city.trim()||(t.city="Şehir gereklidir"),g.district.trim()||(t.district="İl\xe7e gereklidir"),g.postalCode.trim()||(t.postalCode="Posta kodu gereklidir")),2===e&&"creditCard"===g.paymentMethod&&(g.cardNumber.trim()||(t.cardNumber="Kart numarası gereklidir"),g.cardName.trim()||(t.cardName="Kart sahibi adı gereklidir"),g.expiryDate.trim()||(t.expiryDate="Son kullanma tarihi gereklidir"),g.cvv.trim()||(t.cvv="CVV gereklidir")),b(t),0===Object.keys(t).length},v=async()=>{N(2)&&h(!0)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-4",children:"\xd6deme"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat(m>=1?"bg-purple-600 text-white":"bg-gray-300 text-gray-600"),children:"1"}),(0,s.jsx)("span",{className:"text-sm font-medium ".concat(m>=1?"text-white":"text-gray-400"),children:"Teslimat"})]}),(0,s.jsx)("div",{className:"h-0.5 w-16 ".concat(m>=2?"bg-purple-600":"bg-gray-300")}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat(m>=2?"bg-purple-600 text-white":"bg-gray-300 text-gray-600"),children:"2"}),(0,s.jsx)("span",{className:"text-sm font-medium ".concat(m>=2?"text-white":"text-gray-400"),children:"\xd6deme"})]}),(0,s.jsx)("div",{className:"h-0.5 w-16 ".concat(m>=3?"bg-purple-600":"bg-gray-300")}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat(m>=3?"bg-purple-600 text-white":"bg-gray-300 text-gray-600"),children:"3"}),(0,s.jsx)("span",{className:"text-sm font-medium ".concat(m>=3?"text-white":"text-gray-400"),children:"Onay"})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-2",children:[1===m&&(0,s.jsxs)(l.P.div,{className:"bg-white rounded-lg p-6 shadow-md",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-6",children:"Teslimat Bilgileri"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ad *"}),(0,s.jsx)("input",{type:"text",name:"firstName",value:g.firstName,onChange:j,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ".concat(f.firstName?"border-red-500":"border-gray-300")}),f.firstName&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.firstName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Soyad *"}),(0,s.jsx)("input",{type:"text",name:"lastName",value:g.lastName,onChange:j,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ".concat(f.lastName?"border-red-500":"border-gray-300")}),f.lastName&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.lastName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"E-posta *"}),(0,s.jsx)("input",{type:"email",name:"email",value:g.email,onChange:j,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ".concat(f.email?"border-red-500":"border-gray-300")}),f.email&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Telefon *"}),(0,s.jsx)("input",{type:"tel",name:"phone",value:g.phone,onChange:j,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ".concat(f.phone?"border-red-500":"border-gray-300")}),f.phone&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.phone})]})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Adres *"}),(0,s.jsx)("input",{type:"text",name:"address",value:g.address,onChange:j,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ".concat(f.address?"border-red-500":"border-gray-300"),placeholder:"Mahalle, Sokak, No"}),f.address&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.address})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Şehir *"}),(0,s.jsx)("input",{type:"text",name:"city",value:g.city,onChange:j,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ".concat(f.city?"border-red-500":"border-gray-300")}),f.city&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.city})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"İl\xe7e *"}),(0,s.jsx)("input",{type:"text",name:"district",value:g.district,onChange:j,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ".concat(f.district?"border-red-500":"border-gray-300")}),f.district&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.district})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Posta Kodu *"}),(0,s.jsx)("input",{type:"text",name:"postalCode",value:g.postalCode,onChange:j,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ".concat(f.postalCode?"border-red-500":"border-gray-300")}),f.postalCode&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.postalCode})]})]})]}),2===m&&(0,s.jsxs)(l.P.div,{className:"bg-white rounded-lg p-6 shadow-md",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-6",children:"\xd6deme Bilgileri"}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"\xd6deme Y\xf6ntemi"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,s.jsx)("input",{type:"radio",name:"paymentMethod",value:"creditCard",checked:"creditCard"===g.paymentMethod,onChange:j,className:"mr-3"}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("svg",{className:"w-6 h-6 text-blue-600",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"})}),(0,s.jsx)("span",{className:"font-medium text-gray-700",children:"Kredi/Banka Kartı"})]})]}),(0,s.jsxs)("label",{className:"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,s.jsx)("input",{type:"radio",name:"paymentMethod",value:"bankTransfer",checked:"bankTransfer"===g.paymentMethod,onChange:j,className:"mr-3"}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("svg",{className:"w-6 h-6 text-green-600",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M11.5,1L2,6V8H21V6M16,10V17H19V10M2,22H21V19H2M10,10V17H13V10"})}),(0,s.jsx)("span",{className:"font-medium text-gray-700",children:"Banka Havalesi/EFT"})]})]}),(0,s.jsxs)("label",{className:"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,s.jsx)("input",{type:"radio",name:"paymentMethod",value:"cashOnDelivery",checked:"cashOnDelivery"===g.paymentMethod,onChange:j,className:"mr-3"}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("svg",{className:"w-6 h-6 text-orange-600",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"})}),(0,s.jsx)("span",{className:"font-medium text-gray-700",children:"Kapıda \xd6deme"})]})]})]})]}),"creditCard"===g.paymentMethod&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kart Numarası *"}),(0,s.jsx)("input",{type:"text",name:"cardNumber",value:g.cardNumber.replace(/\D/g,"").replace(/(\d{4})(?=\d)/g,"$1 "),onChange:e=>{let t=e.target.value.replace(/\s/g,"");t.length<=16&&j({...e,target:{...e.target,value:t}})},placeholder:"1234 5678 9012 3456",className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ".concat(f.cardNumber?"border-red-500":"border-gray-300")}),f.cardNumber&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.cardNumber})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kart Sahibi Adı *"}),(0,s.jsx)("input",{type:"text",name:"cardName",value:g.cardName,onChange:j,placeholder:"John Doe",className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ".concat(f.cardName?"border-red-500":"border-gray-300")}),f.cardName&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.cardName})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Son Kullanma Tarihi *"}),(0,s.jsx)("input",{type:"text",name:"expiryDate",value:g.expiryDate.replace(/\D/g,"").replace(/(\d{2})(\d{2})/,"$1/$2"),onChange:e=>{let t=e.target.value.replace(/\D/g,"");t.length<=4&&j({...e,target:{...e.target,value:t}})},placeholder:"MM/YY",className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ".concat(f.expiryDate?"border-red-500":"border-gray-300")}),f.expiryDate&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.expiryDate})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CVV *"}),(0,s.jsx)("input",{type:"text",name:"cvv",value:g.cvv,onChange:e=>{let t=e.target.value.replace(/\D/g,"");t.length<=3&&j({...e,target:{...e.target,value:t}})},placeholder:"123",className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ".concat(f.cvv?"border-red-500":"border-gray-300")}),f.cvv&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.cvv})]})]})]}),"bankTransfer"===g.paymentMethod&&(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Banka Hesap Bilgileri"}),(0,s.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Banka:"})," T\xfcrkiye İş Bankası"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Hesap Sahibi:"})," Say Global Ltd. Şti."]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"IBAN:"})," TR12 0006 4000 0011 2345 6789 01"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"A\xe7ıklama:"})," Sipariş numaranızı belirtiniz"]})]})]}),"cashOnDelivery"===g.paymentMethod&&(0,s.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-medium text-orange-800 mb-2",children:"Kapıda \xd6deme"}),(0,s.jsx)("p",{className:"text-sm text-orange-700",children:"Siparişiniz adresinize teslim edilirken nakit olarak \xf6deme yapabilirsiniz. Kapıda \xf6deme i\xe7in ek \xfccret alınmamaktadır."})]})]}),3===m&&(0,s.jsxs)(l.P.div,{className:"bg-white rounded-lg p-6 shadow-md",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-6",children:"Sipariş Onayı"}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-800 mb-3",children:"Teslimat Adresi"}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-sm text-gray-700",children:[(0,s.jsxs)("p",{className:"font-medium",children:[g.firstName," ",g.lastName]}),(0,s.jsx)("p",{children:g.address}),(0,s.jsxs)("p",{children:[g.district,", ",g.city," ",g.postalCode]}),(0,s.jsx)("p",{children:g.phone}),(0,s.jsx)("p",{children:g.email})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-800 mb-3",children:"\xd6deme Y\xf6ntemi"}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-sm text-gray-700",children:["creditCard"===g.paymentMethod&&(0,s.jsxs)("p",{children:["Kredi/Banka Kartı (**** **** **** ",g.cardNumber.slice(-4),")"]}),"bankTransfer"===g.paymentMethod&&(0,s.jsx)("p",{children:"Banka Havalesi/EFT"}),"cashOnDelivery"===g.paymentMethod&&(0,s.jsx)("p",{children:"Kapıda \xd6deme"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-gray-800 mb-3",children:"Sipariş Detayları"}),(0,s.jsx)("div",{className:"space-y-3",children:e.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 rounded-lg p-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"relative w-12 h-12 flex-shrink-0",children:(0,s.jsx)(n.default,{src:e.thumbnail,alt:e.title,fill:!0,className:"object-cover rounded"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-800 text-sm",children:e.title}),(0,s.jsxs)("p",{className:"text-gray-600 text-xs",children:["Adet: ",e.quantity]})]})]}),(0,s.jsx)("div",{className:"text-right",children:(0,s.jsxs)("p",{className:"font-medium text-purple-700",children:[(e.price*e.quantity).toFixed(2)," ₺"]})})]},e.id))})]})]}),(0,s.jsxs)("div",{className:"flex justify-between mt-6",children:[(0,s.jsx)(l.P.button,{onClick:()=>{u(e=>e-1)},className:"px-6 py-2 rounded-lg font-medium ".concat(1===m?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-gray-600 text-white hover:bg-gray-700"),disabled:1===m,whileHover:m>1?{scale:1.02}:{},whileTap:m>1?{scale:.98}:{},children:"Geri"}),m<3?(0,s.jsx)(l.P.button,{onClick:()=>{N(m)&&u(e=>e+1)},className:"px-6 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg font-medium hover:shadow-lg",whileHover:{scale:1.02},whileTap:{scale:.98},children:"İleri"}):(0,s.jsx)(l.P.button,{onClick:v,className:"px-6 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg font-medium hover:shadow-lg",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Siparişi Tamamla"})]})]}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsxs)(l.P.div,{className:"bg-white rounded-lg p-6 shadow-md sticky top-8",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.3,duration:.6},children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-6",children:"Sipariş \xd6zeti"}),(0,s.jsx)("div",{className:"space-y-3 mb-6",children:e.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"relative w-12 h-12 flex-shrink-0",children:(0,s.jsx)(n.default,{src:e.thumbnail,alt:e.title,fill:!0,className:"object-cover rounded"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-800 truncate",children:e.title}),(0,s.jsxs)("p",{className:"text-xs text-gray-600",children:["x",e.quantity]})]})]}),(0,s.jsxs)("div",{className:"text-sm font-medium text-gray-800",children:[(e.price*e.quantity).toFixed(2)," ₺"]})]},e.id))}),(0,s.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,s.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,s.jsx)("span",{children:"\xdcr\xfcn Toplamı:"}),(0,s.jsxs)("span",{children:[t().toFixed(2)," ₺"]})]}),(0,s.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,s.jsx)("span",{children:"Kargo:"}),(0,s.jsx)("span",{className:"text-green-600",children:"\xdccretsiz"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center text-purple-600 bg-purple-50 p-3 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,s.jsx)("span",{className:"font-medium",children:"Toplam Puan:"})]}),(0,s.jsxs)("span",{className:"font-bold text-lg",children:[a()," puan"]})]}),(0,s.jsx)("div",{className:"border-t pt-3",children:(0,s.jsxs)("div",{className:"flex justify-between text-lg font-bold text-gray-800",children:[(0,s.jsx)("span",{children:"Toplam:"}),(0,s.jsxs)("span",{className:"text-purple-700",children:[t().toFixed(2)," ₺"]})]})})]}),(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-green-700",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"})}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"256-bit SSL ile G\xfcvenli \xd6deme"})]})})]})})]})]})}),p&&(0,s.jsx)(x,{isOpen:p,onClose:()=>{h(!1),d()}})]})}},35695:(e,t,a)=>{"use strict";var s=a(18999);a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}})},54698:(e,t,a)=>{Promise.resolve().then(a.bind(a,32591))},60760:(e,t,a)=>{"use strict";a.d(t,{N:()=>f});var s=a(95155),r=a(12115),i=a(90869),l=a(82885),n=a(97494),d=a(80845),c=a(27351),o=a(51508);class m extends r.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,a=(0,c.s)(e)&&e.offsetWidth||0,s=this.props.sizeRef.current;s.height=t.offsetHeight||0,s.width=t.offsetWidth||0,s.top=t.offsetTop,s.left=t.offsetLeft,s.right=a-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}}function x(e){let{children:t,isPresent:a,anchorX:i}=e,l=(0,r.useId)(),n=(0,r.useRef)(null),d=(0,r.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:c}=(0,r.useContext)(o.Q);return(0,r.useInsertionEffect)(()=>{let{width:e,height:t,top:s,left:r,right:o}=d.current;if(a||!n.current||!e||!t)return;n.current.dataset.motionPopId=l;let m=document.createElement("style");return c&&(m.nonce=c),document.head.appendChild(m),m.sheet&&m.sheet.insertRule('\n          [data-motion-pop-id="'.concat(l,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===i?"left: ".concat(r):"right: ".concat(o),"px !important;\n            top: ").concat(s,"px !important;\n          }\n        ")),()=>{document.head.contains(m)&&document.head.removeChild(m)}},[a]),(0,s.jsx)(m,{isPresent:a,childRef:n,sizeRef:d,children:r.cloneElement(t,{ref:n})})}let u=e=>{let{children:t,initial:a,isPresent:i,onExitComplete:n,custom:c,presenceAffectsLayout:o,mode:m,anchorX:u}=e,h=(0,l.M)(p),g=(0,r.useId)(),y=!0,f=(0,r.useMemo)(()=>(y=!1,{id:g,initial:a,isPresent:i,custom:c,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;n&&n()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[i,h,n]);return o&&y&&(f={...f}),(0,r.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[i]),r.useEffect(()=>{i||h.size||!n||n()},[i]),"popLayout"===m&&(t=(0,s.jsx)(x,{isPresent:i,anchorX:u,children:t})),(0,s.jsx)(d.t.Provider,{value:f,children:t})};function p(){return new Map}var h=a(32082);let g=e=>e.key||"";function y(e){let t=[];return r.Children.forEach(e,e=>{(0,r.isValidElement)(e)&&t.push(e)}),t}let f=e=>{let{children:t,custom:a,initial:d=!0,onExitComplete:c,presenceAffectsLayout:o=!0,mode:m="sync",propagate:x=!1,anchorX:p="left"}=e,[f,b]=(0,h.xQ)(x),j=(0,r.useMemo)(()=>y(t),[t]),N=x&&!f?[]:j.map(g),v=(0,r.useRef)(!0),w=(0,r.useRef)(j),k=(0,l.M)(()=>new Map),[C,P]=(0,r.useState)(j),[S,M]=(0,r.useState)(j);(0,n.E)(()=>{v.current=!1,w.current=j;for(let e=0;e<S.length;e++){let t=g(S[e]);N.includes(t)?k.delete(t):!0!==k.get(t)&&k.set(t,!1)}},[S,N.length,N.join("-")]);let T=[];if(j!==C){let e=[...j];for(let t=0;t<S.length;t++){let a=S[t],s=g(a);N.includes(s)||(e.splice(t,0,a),T.push(a))}return"wait"===m&&T.length&&(e=T),M(y(e)),P(j),null}let{forceRender:D}=(0,r.useContext)(i.L);return(0,s.jsx)(s.Fragment,{children:S.map(e=>{let t=g(e),r=(!x||!!f)&&(j===S||N.includes(t));return(0,s.jsx)(u,{isPresent:r,initial:(!v.current||!!d)&&void 0,custom:a,presenceAffectsLayout:o,mode:m,onExitComplete:r?void 0:()=>{if(!k.has(t))return;k.set(t,!0);let e=!0;k.forEach(t=>{t||(e=!1)}),e&&(null==D||D(),M(w.current),x&&(null==b||b()),c&&c())},anchorX:p,children:e},t)})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6408,6874,6766,8441,1684,7358],()=>t(54698)),_N_E=e.O()}]);